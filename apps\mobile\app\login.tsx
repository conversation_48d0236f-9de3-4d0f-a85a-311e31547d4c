
import React, { useState } from 'react';
import { SafeAreaView, View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { Link, useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { authService } from '@/lib/authService';
import { SocialButton } from '@/components/SocialButton';

const LoginScreen = () => {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert("Date Incomplete", "Te rugăm să introduci email-ul și parola.");
      return;
    }
    setIsLoading(true);
    try {
      const result = await authService.login(email, password);
      if (result.success) {
        Alert.alert("Autentificare Reușită", `Bun venit, ${result.user.FullName}!`);
        // TODO: Implement session management (e.g., save token, update user context)
        router.push('/profile'); // Redirect to profile page on success
      }
    } catch (error: any) {
      Alert.alert("Eroare Autentificare", error.message || 'A apărut o eroare la autentificare.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Autentificare</Text>
          <Text style={styles.subtitle}>Introdu datele pentru a accesa contul.</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <MaterialIcons name="email" color="#6B7280" size={20} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Adresă de email"
              placeholderTextColor="#6B7280"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          <View style={styles.inputContainer}>
            <MaterialIcons name="lock" color="#6B7280" size={20} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Parolă"
              placeholderTextColor="#6B7280"
              secureTextEntry={!isPasswordVisible}
              value={password}
              onChangeText={setPassword}
            />
            <TouchableOpacity onPress={() => setIsPasswordVisible(!isPasswordVisible)} style={styles.eyeIcon}>
              {isPasswordVisible ? <MaterialIcons name="visibility-off" color="#6B7280" size={20} /> : <MaterialIcons name="visibility" color="#6B7280" size={20} />}
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.loginButton} onPress={handleLogin} disabled={isLoading}>
            {isLoading ? <ActivityIndicator color="#FFFFFF" /> : <Text style={styles.loginButtonText}>Autentifică-te</Text>}
          </TouchableOpacity>
        </View>

        <View style={styles.separatorContainer}>
          <View style={styles.separatorLine} />
          <Text style={styles.separatorText}>SAU</Text>
          <View style={styles.separatorLine} />
        </View>

        <View style={styles.socialContainer}>
            <SocialButton provider="google" title="Continuă cu Google" onPress={() => Alert.alert('Info', 'Funcționalitate în dezvoltare.')}/>
            <SocialButton provider="facebook" title="Continuă cu Facebook" onPress={() => Alert.alert('Info', 'Funcționalitate în dezvoltare.')}/>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Nu ai cont? </Text>
          <Link href="/register" asChild>
            <TouchableOpacity>
              <Text style={styles.registerLink}>Înregistrează-te</Text>
            </TouchableOpacity>
          </Link>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#FFFFFF' },
  scrollContent: { flexGrow: 1, justifyContent: 'center', padding: 24 },
  header: { alignItems: 'center', marginBottom: 32 },
  title: { fontSize: 32, fontWeight: 'bold', color: '#111827' },
  subtitle: { fontSize: 16, color: '#6B7280', marginTop: 8 },
  form: { width: '100%' },
  inputContainer: { flexDirection: 'row', alignItems: 'center', borderWidth: 1, borderColor: '#D1D5DB', borderRadius: 12, marginBottom: 16 },
  inputIcon: { marginHorizontal: 12 },
  input: { flex: 1, height: 50, paddingHorizontal: 8, fontSize: 16, color: '#111827' },
  eyeIcon: { padding: 12 },
  loginButton: { backgroundColor: '#3F51B5', borderRadius: 12, paddingVertical: 16, alignItems: 'center', marginTop: 8 },
  loginButtonText: { color: '#FFFFFF', fontSize: 16, fontWeight: '600' },
  separatorContainer: { flexDirection: 'row', alignItems: 'center', marginVertical: 24 },
  separatorLine: { flex: 1, height: 1, backgroundColor: '#E5E7EB' },
  separatorText: { marginHorizontal: 12, color: '#6B7280', fontSize: 14 },
  socialContainer: { width: '100%', gap: 16 },
  footer: { flexDirection: 'row', justifyContent: 'center', marginTop: 32 },
  footerText: { color: '#6B7280', fontSize: 14 },
  registerLink: { color: '#3F51B5', fontWeight: '600', fontSize: 14 },
});

export default LoginScreen;
