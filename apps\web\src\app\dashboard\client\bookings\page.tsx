"use client";

import { useEffect, useState } from 'react';
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Link from "next/link";
import { CalendarDays, PlusCircle, Search, Loader2, CheckCircle, Clock, Star, AlertTriangle } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { BookingStatus } from '@prisma/client';
import { BookingCompletionConfirmation } from '@/components/booking/booking-completion-confirmation';
import { ReviewForm } from '@/components/booking/review-form';

const bookingsPageTranslations = {
  pageTitle: { ro: "Rezervările Mele", ru: "Мои бронирования", en: "My Bookings" },
  pageDescription: { ro: "Vezi și gestionează toate rezervările tale active și anterioare.", ru: "Просматривайте и управляйте всеми вашими активными и прошлыми бронированиями.", en: "View and manage all your active and past bookings." },
  searchNewServiceButton: { ro: "Caută un Nou Serviciu", ru: "Найти новую услугу", en: "Search for a New Service" },
  upcomingActiveBookingsTitle: { ro: "Rezervări Viitoare și Active", ru: "Предстоящие и активные бронирования", en: "Upcoming and Active Bookings" },
  providerLabel: { ro: "Cu:", ru: "С:", en: "With:" },
  statusConfirmed: { ro: "Confirmat", ru: "Подтверждено", en: "Confirmed" },
  statusCompleted: { ro: "Finalizat", ru: "Завершено", en: "Completed" },
  statusPending: { ro: "În Așteptare", ru: "В ожидании", en: "Pending" },
  statusCancelled: { ro: "Anulat", ru: "Отменено", en: "Cancelled" },
  statusInProgress: { ro: "În Desfășurare", ru: "В процессе", en: "In Progress" },
  viewDetailsButton: { ro: "Vezi Detalii", ru: "Посмотреть детали", en: "View Details" },
  bookingHistoryTitle: { ro: "Istoricul Rezervărilor", ru: "История бронирований", en: "Booking History" },
  leaveReviewButton: { ro: "Lasă o Recenzie", ru: "Оставить отзыв", en: "Leave a Review" },
  noBookingsMessage: { ro: "Nu ai încă rezervări. Caută servicii pentru a face prima ta rezervare!", ru: "У вас пока нет бронирований. Найдите услуги, чтобы сделать первое бронирование!", en: "You don't have any bookings yet. Search for services to make your first booking!" },
  findServiceButton: { ro: "Găsește un Serviciu", ru: "Найти услугу", en: "Find a Service" },
  errorNoUserId: { ro: "ID utilizator nu a fost găsit în sesiune.", ru: "ID пользователя не найден в сессии.", en: "User ID not found in session." },
  errorFetchingBookings: { ro: "Eroare la preluarea rezervărilor.", ru: "Ошибка при загрузке бронирований.", en: "Error fetching bookings." },
};

interface BookingClientView {
  id: number;
  clientId: number;
  providerId: number;
  advertisedServiceId: number;
  eventStartDateTime: string | null;
  eventEndDateTime: string | null;
  status: BookingStatus;
  clientNotes: string | null;
  providerNotes: string | null;
  createdAt: string;
  updatedAt: string;
  provider?: {
    fullName: string;
  };
  service?: {
    serviceName: string;
  };
}

export default function ClientBookingsPage() {
  const { translate } = useLanguage();
  const { data: session, status: sessionStatus } = useSession();
  const [userBookings, setUserBookings] = useState<BookingClientView[]>([]);
  const [pendingCompletionBookings, setPendingCompletionBookings] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingPendingCompletion, setIsLoadingPendingCompletion] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBookingForReview, setSelectedBookingForReview] = useState<any | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);

  useEffect(() => {
    if (sessionStatus === 'loading') {
      setIsLoading(true);
      return;
    }

    if (sessionStatus === 'unauthenticated' || !session?.user) {
      setError(translate(bookingsPageTranslations, 'errorNoUserId'));
      setIsLoading(false);
      return;
    }

    const fetchBookings = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const userId = (session.user as any).id;
        if (!userId) {
          setError(translate(bookingsPageTranslations, 'errorNoUserId'));
          return;
        }

        const response = await fetch(`/api/proxy/bookings/client/${userId}`);
        if (!response.ok) {
          throw new Error(translate(bookingsPageTranslations, 'errorFetchingBookings'));
        }
        const data = await response.json();
        setUserBookings(data.bookings || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : translate(bookingsPageTranslations, 'errorFetchingBookings'));
      } finally {
        setIsLoading(false);
      }
    };

    const fetchPendingCompletionBookings = async () => {
      setIsLoadingPendingCompletion(true);
      try {
        const response = await fetch('/api/proxy/bookings/pending-completion');
        if (response.ok) {
          const data = await response.json();
          setPendingCompletionBookings(data.bookings || []);
        }
      } catch (error) {
        console.error('Error fetching pending completion bookings:', error);
      } finally {
        setIsLoadingPendingCompletion(false);
      }
    };

    fetchBookings();
    fetchPendingCompletionBookings();
  }, [session, sessionStatus, translate]);

  const handleConfirmCompletion = async (bookingId: number) => {
    try {
      const response = await fetch(`/api/proxy/bookings/${bookingId}/complete`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Remove from pending completion and refresh bookings
        setPendingCompletionBookings(prev => prev.filter(b => b.id !== bookingId));

        // Refresh main bookings list
        const userId = (session?.user as any)?.id;
        if (userId) {
          const bookingsResponse = await fetch(`/api/proxy/bookings/client/${userId}`);
          if (bookingsResponse.ok) {
            const data = await bookingsResponse.json();
            setUserBookings(data.bookings || []);
          }
        }
      } else {
        throw new Error('Failed to confirm completion');
      }
    } catch (error) {
      console.error('Error confirming completion:', error);
      throw error;
    }
  };

  const handleOpenReview = (bookingId: number) => {
    const booking = pendingCompletionBookings.find(b => b.id === bookingId) ||
                   userBookings.find(b => b.id === bookingId);

    if (booking) {
      setSelectedBookingForReview(booking);
      setIsReviewDialogOpen(true);
    }
  };

  const handleReviewSubmitSuccess = () => {
    setIsReviewDialogOpen(false);
    setSelectedBookingForReview(null);
    // Optionally refresh bookings to update review status
  };

  const upcomingBookings = userBookings.filter(b =>
    b.status === BookingStatus.Confirmed ||
    b.status === BookingStatus.Pending ||
    b.status === 'InProgress'
  );

  const pastBookings = userBookings.filter(b =>
    b.status === BookingStatus.Completed ||
    b.status === BookingStatus.Cancelled
  );

  const getTranslatedStatus = (statusKey?: BookingStatus | string) => {
    if (!statusKey) return "Necunoscut";
    switch (statusKey) {
      case BookingStatus.Confirmed: return translate(bookingsPageTranslations, 'statusConfirmed');
      case BookingStatus.Completed: return translate(bookingsPageTranslations, 'statusCompleted');
      case BookingStatus.Pending: return translate(bookingsPageTranslations, 'statusPending');
      case BookingStatus.Cancelled: return translate(bookingsPageTranslations, 'statusCancelled');
      case 'InProgress': return translate(bookingsPageTranslations, 'statusInProgress');
      default: return statusKey;
    }
  };

  if (isLoading || sessionStatus === 'loading') {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-10 text-center text-destructive">
          <p>{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-headline">{translate(bookingsPageTranslations, 'pageTitle')}</CardTitle>
          <CardDescription>{translate(bookingsPageTranslations, 'pageDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button asChild>
            <Link href="/search" className="flex items-center">
              <PlusCircle className="w-5 h-5 mr-2" /> {translate(bookingsPageTranslations, 'searchNewServiceButton')}
            </Link>
          </Button>
        </CardContent>
      </Card>

      {/* Pending Completion Confirmation */}
      <BookingCompletionConfirmation
        bookings={pendingCompletionBookings}
        onConfirmCompletion={handleConfirmCompletion}
        onOpenReview={handleOpenReview}
        isLoading={isLoadingPendingCompletion}
      />

      {upcomingBookings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{translate(bookingsPageTranslations, 'upcomingActiveBookingsTitle')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingBookings.map(booking => (
              <Card key={booking.id} className="p-4 bg-muted/50">
                <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                  <div>
                    <h3 className="font-semibold">{booking.service?.serviceName || "Serviciu Indisponibil"}</h3>
                    <p className="text-sm text-muted-foreground">
                      {translate(bookingsPageTranslations, 'providerLabel')}{' '}
                      <Link href={`/profile/${booking.providerId}/${booking.advertisedServiceId}`} className="text-primary hover:underline">
                        {booking.provider?.fullName || "Prestator Necunoscut"}
                      </Link>
                    </p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <CalendarDays className="w-4 h-4 mr-1.5"/>
                      {booking.eventStartDateTime ? new Date(booking.eventStartDateTime).toLocaleDateString() : 'Data nespecificată'}
                      {booking.eventStartDateTime ? ` la ${new Date(booking.eventStartDateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` : ''}
                    </p>
                  </div>
                  <div className="flex flex-col items-start sm:items-end gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      booking.status === BookingStatus.Confirmed ? "bg-green-100 text-green-800" :
                      booking.status === BookingStatus.Pending ? "bg-yellow-100 text-yellow-800" :
                      booking.status === 'InProgress' ? "bg-blue-100 text-blue-800" :
                      "bg-gray-100 text-gray-800"
                    }`}>
                      {getTranslatedStatus(booking.status)}
                    </span>
                    <Button variant="outline" size="sm">{translate(bookingsPageTranslations, 'viewDetailsButton')}</Button>
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>
      )}

      {pastBookings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{translate(bookingsPageTranslations, 'bookingHistoryTitle')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {pastBookings.map(booking => (
              <Card key={booking.id} className="p-4">
                <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                  <div>
                    <h3 className="font-semibold">{booking.service?.serviceName || "Serviciu Indisponibil"}</h3>
                    <p className="text-sm text-muted-foreground">
                      {translate(bookingsPageTranslations, 'providerLabel')}{' '}
                      <Link href={`/profile/${booking.providerId}/${booking.advertisedServiceId}`} className="text-primary hover:underline">
                        {booking.provider?.fullName || "Prestator Necunoscut"}
                      </Link>
                    </p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <CalendarDays className="w-4 h-4 mr-1.5"/>
                      {booking.eventStartDateTime ? new Date(booking.eventStartDateTime).toLocaleDateString() : 'Data nespecificată'}
                      {booking.eventStartDateTime ? ` la ${new Date(booking.eventStartDateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` : ''}
                    </p>
                  </div>
                  <div className="flex flex-col items-start sm:items-end gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      booking.status === BookingStatus.Completed ? "bg-blue-100 text-blue-800" :
                      booking.status === BookingStatus.Cancelled ? "bg-red-100 text-red-800" :
                      "bg-gray-100 text-gray-800"
                    }`}>
                      {getTranslatedStatus(booking.status)}
                    </span>
                    {booking.status === BookingStatus.Completed && (
                      <Button
                        variant="link"
                        size="sm"
                        className="p-0 h-auto text-yellow-600 hover:text-yellow-700"
                        onClick={() => handleOpenReview(booking.id)}
                      >
                        <Star className="w-3 h-3 mr-1" />
                        {translate(bookingsPageTranslations, 'leaveReviewButton')}
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>
      )}

      {!isLoading && userBookings.length === 0 && !error && (
        <Card>
          <CardContent className="text-center py-12">
            <Search className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{translate(bookingsPageTranslations, 'noBookingsMessage')}</p>
            <Button asChild className="mt-4">
              <Link href="/search">{translate(bookingsPageTranslations, 'findServiceButton')}</Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Review Dialog */}
      {selectedBookingForReview && (
        <ReviewForm
          bookingId={selectedBookingForReview.id}
          providerName={selectedBookingForReview.provider?.fullName || selectedBookingForReview.Provider?.FullName || "Furnizor"}
          serviceName={selectedBookingForReview.service?.serviceName || selectedBookingForReview.AdvertisedService?.ServiceName || "Serviciu"}
          isOpen={isReviewDialogOpen}
          onClose={() => {
            setIsReviewDialogOpen(false);
            setSelectedBookingForReview(null);
          }}
          onSubmitSuccess={handleReviewSubmitSuccess}
        />
      )}
    </div>
  );
}
