import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions, type ExtendedSession } from '@repo/auth';
import jwt from 'jsonwebtoken';
import apiFetch from '@/lib/api-client';
import type { ProfilePhotoUploadResponse } from '@/types/profile-setup';

export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: 'Nu ești autentificat.' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('photo') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, message: 'Nicio fotografie nu a fost încărcată.' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, message: 'Fotografia trebuie să fie în format JPG, PNG sau WebP.' },
        { status: 400 }
      );
    }

    // Validate file size (5MB max)
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSizeBytes) {
      return NextResponse.json(
        { success: false, message: 'Fotografia nu poate depăși 5MB.' },
        { status: 400 }
      );
    }

    // Create JWT token for backend authentication
    const internalApiJwt = jwt.sign(
      {
        id: session.user.id,
        roles: session.user.roles,
        isAdmin: session.user.isAdmin,
      },
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '5m' }
    );

    // Create FormData for the backend API
    const backendFormData = new FormData();
    backendFormData.append('photo', file);

    // Call the backend API to upload the photo
    const apiResponse = await apiFetch(`user/${session.user.id}/profile/photo`, {
      method: 'POST',
      body: backendFormData,
      jwt: internalApiJwt,
    });

    const responseData = await apiResponse.json();

    if (!apiResponse.ok) {
      console.error('[Profile Photo Upload API] Backend error:', responseData);
      return NextResponse.json(
        { success: false, message: responseData.message || 'Eroare la încărcarea fotografiei.' },
        { status: apiResponse.status }
      );
    }

    console.log(`[Profile Photo Upload API] Photo uploaded successfully for user ${session.user.id}`);
    
    const response: ProfilePhotoUploadResponse = {
      success: true,
      message: 'Fotografia a fost încărcată cu succes.',
      avatarUrl: responseData.avatarUrl,
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('[Profile Photo Upload API] Unexpected error:', error);
    return NextResponse.json(
      { success: false, message: 'A apărut o eroare neașteptată.' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: 'Nu ești autentificat.' },
        { status: 401 }
      );
    }

    // Create JWT token for backend authentication
    const internalApiJwt = jwt.sign(
      {
        id: session.user.id,
        roles: session.user.roles,
        isAdmin: session.user.isAdmin,
      },
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '5m' }
    );

    // Call the backend API to delete the photo
    const apiResponse = await apiFetch(`user/${session.user.id}/profile/photo`, {
      method: 'DELETE',
      jwt: internalApiJwt,
    });

    if (!apiResponse.ok) {
      const responseData = await apiResponse.json();
      console.error('[Profile Photo Delete API] Backend error:', responseData);
      return NextResponse.json(
        { success: false, message: responseData.message || 'Eroare la ștergerea fotografiei.' },
        { status: apiResponse.status }
      );
    }

    console.log(`[Profile Photo Delete API] Photo deleted successfully for user ${session.user.id}`);
    
    const response: ProfilePhotoUploadResponse = {
      success: true,
      message: 'Fotografia a fost ștearsă cu succes.',
      avatarUrl: null,
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('[Profile Photo Delete API] Unexpected error:', error);
    return NextResponse.json(
      { success: false, message: 'A apărut o eroare neașteptată.' },
      { status: 500 }
    );
  }
}
