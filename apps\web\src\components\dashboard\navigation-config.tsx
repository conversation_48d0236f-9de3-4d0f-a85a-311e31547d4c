"use client";

import { User, Settings, LayoutDashboard, <PERSON><PERSON><PERSON><PERSON>, Calendar<PERSON>heck, Calendar<PERSON>lock, Briefcase, MessageSquare, MapPin, Star } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { useRoleToggleAnalytics } from '@/lib/analytics/role-toggle-analytics';

export type Role = 'client' | 'provider';

export interface NavigationItem {
  href: string;
  labelKey: keyof typeof commonTranslations;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  badge?: string | number;
}

export interface NavigationConfig {
  client: NavigationItem[];
  provider: NavigationItem[];
}

export function useNavigationConfig(): NavigationConfig {
  const { translate } = useLanguage();

  return {
    client: [
      {
        href: '/dashboard/client',
        labelKey: 'clientDashboardNav',
        icon: User,
        description: 'Overview of your client activities'
      },
      {
        href: '/dashboard/client/bookings',
        labelKey: 'myBookingsClient',
        icon: CalendarCheck,
        description: 'View and manage your bookings'
      },
      {
        href: '/dashboard/client/addresses',
        labelKey: 'myAddresses',
        icon: MapPin,
        description: 'Manage your saved addresses'
      }
    ],
    provider: [
      {
        href: '/dashboard/provider',
        labelKey: 'providerDashboardNav',
        icon: Briefcase,
        description: 'Overview of your provider activities'
      },
      {
        href: '/dashboard/provider/calendar',
        labelKey: 'providerCalendar',
        icon: CalendarClock,
        description: 'Manage your availability and schedule'
      },
      {
        href: '/dashboard/provider/services',
        labelKey: 'myServices',
        icon: ListChecks,
        description: 'Manage your service offerings'
      },
      {
        href: '/dashboard/provider/reviews',
        labelKey: 'myReviews',
        icon: Star,
        description: 'View and manage client reviews'
      }
    ]
  };
}

// Helper function to get navigation items for a specific role
export function getNavigationForRole(role: Role): NavigationItem[] {
  const config = useNavigationConfig();
  return config[role];
}

// Helper function to check if a path is active
export function isNavigationItemActive(itemHref: string, currentPath: string): boolean {
  // Exact match for dashboard root
  if (itemHref.endsWith('/client') || itemHref.endsWith('/provider')) {
    return currentPath === itemHref;
  }
  
  // Prefix match for sub-pages
  return currentPath.startsWith(itemHref);
}

// Helper function to get the current role from pathname
export function getRoleFromPath(pathname: string): Role {
  if (pathname.includes('/dashboard/provider')) {
    return 'provider';
  }
  return 'client';
}

// Helper function to convert old dashboard URLs to new role-based URLs
export function migrateUrl(pathname: string, userRole: Role): string {
  // Handle legacy dashboard URLs
  if (pathname === '/dashboard') {
    return `/dashboard/${userRole}`;
  }
  
  // Handle legacy provider URLs
  if (pathname.startsWith('/dashboard/provider') && !pathname.includes('/dashboard/provider/')) {
    return pathname.replace('/dashboard/provider', '/dashboard/provider');
  }
  
  // Handle other legacy URLs that don't specify role
  if (pathname.startsWith('/dashboard/') && !pathname.includes('/client') && !pathname.includes('/provider')) {
    const subPath = pathname.replace('/dashboard/', '');
    
    // Determine which role this path belongs to
    const providerPaths = ['calendar', 'services'];
    const targetRole = providerPaths.some(path => subPath.startsWith(path)) ? 'provider' : 'client';
    
    return `/dashboard/${targetRole}/${subPath}`;
  }
  
  return pathname;
}

// Navigation analytics helper
export function trackNavigationClick(item: NavigationItem, currentRole: Role) {
  // Import analytics dynamically to avoid hook usage outside component
  if (typeof window !== 'undefined' && (window as any).roleToggleAnalytics) {
    (window as any).roleToggleAnalytics.trackNavigationClick({
      current_role: currentRole,
      destination: item.href,
      label: item.labelKey,
      source: 'sidebar_navigation',
    });
  }
}
