
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { type AuthenticatedRequest } from '../middleware/auth';
import { BookingStatus, NotificationType } from '@prisma/client';

const router = Router();

router.post('/create', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const clientId = parseInt(req.user.id, 10);

    const {
      providerId,
      serviceId,
      eventStartDateTime,
      eventEndDateTime,
      clientNotes,
      selectedAddressId
    } = req.body;

    if (!providerId || !serviceId || !eventStartDateTime) {
      return res.status(400).json({ success: false, message: 'Lipsesc datele necesare.' });
    }

    if (clientId === providerId) {
      return res.status(400).json({ success: false, message: 'Nu poți rezerva propriul serviciu.' });
    }

    const startDate = new Date(eventStartDateTime);
    const endDate = eventEndDateTime ? new Date(eventEndDateTime) : null;

    if (isNaN(startDate.getTime()) || (endDate && isNaN(endDate.getTime()))) {
      return res.status(400).json({ success: false, message: 'Formatul datei sau orei este invalid.' });
    }

    // Check for conflicting bookings
    const conflictingBooking = await prisma.booking.findFirst({
      where: {
        ProviderId: providerId,
        Status: { in: [BookingStatus.Confirmed, BookingStatus.Pending] },
        EventStartDateTime: {
          lte: endDate || startDate,
        },
        EventEndDateTime: {
          gte: startDate,
        },
      },
    });

    if (conflictingBooking) {
      return res.status(409).json({
        success: false,
        message: 'Furnizorul are deja o rezervare în acest interval de timp.'
      });
    }

    // Validate address if provided
    if (selectedAddressId) {
      const address = await prisma.address.findFirst({
        where: {
          Id: selectedAddressId,
          UserId: clientId,
        },
      });

      if (!address) {
        return res.status(400).json({
          success: false,
          message: 'Adresa selectată nu este validă.'
        });
      }
    }

    const newBooking = await prisma.booking.create({
      data: {
        ClientId: clientId,
        ProviderId: providerId,
        AdvertisedServiceId: serviceId,
        EventStartDateTime: startDate,
        EventEndDateTime: endDate,
        Status: BookingStatus.Pending,
        ClientNotes: clientNotes,
      },
      include: {
        Client: { select: { FullName: true, Email: true } },
        AdvertisedService: { select: { ServiceName: true, Category: { select: { NameKey: true } } } },
        Provider: { select: { FullName: true, Email: true } }
      }
    });

    // Create notification for provider
    await prisma.notification.create({
      data: {
        UserId: providerId,
        Message: `Cerere nouă de rezervare de la ${newBooking.Client.FullName} pentru serviciul "${newBooking.AdvertisedService.ServiceName}".`,
        Link: `/dashboard/provider/calendar`,
        Type: NotificationType.NewBookingRequest,
      }
    });

    return res.json({
      success: true,
      booking: {
        id: newBooking.Id,
        clientId: newBooking.ClientId,
        providerId: newBooking.ProviderId,
        advertisedServiceId: newBooking.AdvertisedServiceId,
        eventStartDateTime: newBooking.EventStartDateTime?.toISOString(),
        eventEndDateTime: newBooking.EventEndDateTime?.toISOString(),
        status: newBooking.Status,
        clientNotes: newBooking.ClientNotes,
        createdAt: newBooking.CreatedAt.toISOString(),
        service: {
          serviceName: newBooking.AdvertisedService.ServiceName,
          category: newBooking.AdvertisedService.Category
        },
        provider: {
          fullName: newBooking.Provider.FullName,
          email: newBooking.Provider.Email
        }
      }
    });

  } catch (error) {
    console.error('[API /bookings/create POST] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la crearea rezervării.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

router.get('/client/:clientId', async (req: AuthenticatedRequest, res: Response) => {
  const { clientId } = req.params;

  if (!clientId) {
    return res.status(400).json({ message: 'Client ID is required' });
  }

  const clientIdNum = parseInt(clientId, 10);
  if (isNaN(clientIdNum)) {
    return res.status(400).json({ message: 'Invalid Client ID format' });
  }

  try {
    const bookingsFromDb = await prisma.booking.findMany({
      where: {
        ClientId: clientIdNum, 
      },
      include: {
        AdvertisedService: { 
          select: {
            ServiceName: true, 
            Category: { select: { NameKey: true }} 
          }
        },
        Provider: { 
          select: {
            Id: true, 
            FullName: true, 
            AvatarUrl: true, 
          }
        }
      },
      orderBy: {
        EventStartDateTime: 'desc', 
      },
    });

    // Transform to camelCase for API response
    const formattedBookings = bookingsFromDb.map(booking => ({
      id: booking.Id,
      clientId: booking.ClientId,
      providerId: booking.ProviderId,
      advertisedServiceId: booking.AdvertisedServiceId,
      eventStartDateTime: booking.EventStartDateTime?.toISOString(),
      eventEndDateTime: booking.EventEndDateTime?.toISOString(),
      status: booking.Status,
      clientNotes: booking.ClientNotes,
      providerNotes: booking.ProviderNotes,
      createdAt: booking.CreatedAt.toISOString(),
      updatedAt: booking.UpdatedAt.toISOString(),
      service: booking.AdvertisedService ? {
        serviceName: booking.AdvertisedService.ServiceName,
        category: booking.AdvertisedService.Category ? {
          nameKey: booking.AdvertisedService.Category.NameKey
        } : null
      } : null,
      provider: booking.Provider ? {
        id: booking.Provider.Id,
        fullName: booking.Provider.FullName,
        avatarUrl: booking.Provider.AvatarUrl
      } : null,
    }));

    return res.json({ bookings: formattedBookings });
  } catch (error) {
    console.error(`[API /client/bookings] Failed to fetch bookings for client ${clientId}:`, error);
    return res.status(500).json({ message: 'Failed to fetch bookings' });
  }
});

// Provider response endpoints
router.put('/:bookingId/approve', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const providerId = parseInt(req.user.id, 10);
    const bookingId = parseInt(req.params.bookingId, 10);
    const { providerNotes } = req.body;

    if (isNaN(bookingId)) {
      return res.status(400).json({ success: false, message: 'ID rezervare invalid.' });
    }

    const booking = await prisma.booking.findFirst({
      where: {
        Id: bookingId,
        ProviderId: providerId,
        Status: BookingStatus.Pending,
      },
      include: {
        Client: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Rezervarea nu a fost găsită sau nu poate fi modificată.'
      });
    }

    const updatedBooking = await prisma.booking.update({
      where: { Id: bookingId },
      data: {
        Status: BookingStatus.Confirmed,
        ProviderNotes: providerNotes,
        UpdatedAt: new Date(),
      },
      include: {
        Client: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    // Notify client of approval
    await prisma.notification.create({
      data: {
        UserId: booking.ClientId,
        Message: `Rezervarea ta pentru serviciul "${booking.AdvertisedService.ServiceName}" a fost confirmată.`,
        Link: `/dashboard/client/bookings`,
        Type: NotificationType.BookingStatusChanged,
      }
    });

    return res.json({ success: true, booking: updatedBooking });

  } catch (error) {
    console.error('[API /bookings/:id/approve PUT] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la confirmarea rezervării.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

router.put('/:bookingId/reject', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const providerId = parseInt(req.user.id, 10);
    const bookingId = parseInt(req.params.bookingId, 10);
    const { providerNotes } = req.body;

    if (isNaN(bookingId)) {
      return res.status(400).json({ success: false, message: 'ID rezervare invalid.' });
    }

    const booking = await prisma.booking.findFirst({
      where: {
        Id: bookingId,
        ProviderId: providerId,
        Status: BookingStatus.Pending,
      },
      include: {
        Client: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Rezervarea nu a fost găsită sau nu poate fi modificată.'
      });
    }

    const updatedBooking = await prisma.booking.update({
      where: { Id: bookingId },
      data: {
        Status: BookingStatus.Cancelled,
        ProviderNotes: providerNotes,
        UpdatedAt: new Date(),
      },
      include: {
        Client: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    // Notify client of rejection
    await prisma.notification.create({
      data: {
        UserId: booking.ClientId,
        Message: `Rezervarea ta pentru serviciul "${booking.AdvertisedService.ServiceName}" a fost respinsă.`,
        Link: `/dashboard/client/bookings`,
        Type: NotificationType.BookingStatusChanged,
      }
    });

    return res.json({ success: true, booking: updatedBooking });

  } catch (error) {
    console.error('[API /bookings/:id/reject PUT] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la respingerea rezervării.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Provider pending bookings endpoint
router.get('/provider/:providerId/pending', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }

    const providerId = parseInt(req.params.providerId, 10);
    const requestingUserId = parseInt(req.user.id, 10);

    // Ensure the requesting user is the provider
    if (providerId !== requestingUserId) {
      return res.status(403).json({ success: false, message: 'Acces interzis.' });
    }

    const pendingBookings = await prisma.booking.findMany({
      where: {
        ProviderId: providerId,
        Status: BookingStatus.Pending,
      },
      include: {
        Client: {
          select: {
            Id: true,
            FullName: true,
            Email: true,
            AvatarUrl: true,
          }
        },
        AdvertisedService: {
          select: {
            Id: true,
            ServiceName: true,
            Category: {
              select: {
                NameKey: true,
              }
            }
          }
        }
      },
      orderBy: {
        CreatedAt: 'desc'
      }
    });

    const formattedBookings = pendingBookings.map(booking => ({
      id: booking.Id,
      clientId: booking.ClientId,
      advertisedServiceId: booking.AdvertisedServiceId,
      eventStartDateTime: booking.EventStartDateTime?.toISOString(),
      eventEndDateTime: booking.EventEndDateTime?.toISOString(),
      status: booking.Status,
      clientNotes: booking.ClientNotes,
      createdAt: booking.CreatedAt.toISOString(),
      client: {
        fullName: booking.Client.FullName,
        email: booking.Client.Email,
        avatarUrl: booking.Client.AvatarUrl,
      },
      service: {
        serviceName: booking.AdvertisedService.ServiceName,
        category: booking.AdvertisedService.Category ? {
          nameKey: booking.AdvertisedService.Category.NameKey
        } : null
      }
    }));

    return res.json({
      success: true,
      bookings: formattedBookings
    });

  } catch (error) {
    console.error('[API /bookings/provider/:id/pending GET] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la preluarea rezervărilor în așteptare.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Service completion endpoints
router.put('/:bookingId/complete', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const userId = parseInt(req.user.id, 10);
    const bookingId = parseInt(req.params.bookingId, 10);

    if (isNaN(bookingId)) {
      return res.status(400).json({ success: false, message: 'ID rezervare invalid.' });
    }

    const booking = await prisma.booking.findFirst({
      where: {
        Id: bookingId,
        OR: [
          { ClientId: userId },
          { ProviderId: userId }
        ],
        Status: BookingStatus.Confirmed,
      },
      include: {
        Client: { select: { FullName: true } },
        Provider: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Rezervarea nu a fost găsită sau nu poate fi finalizată.'
      });
    }

    const updatedBooking = await prisma.booking.update({
      where: { Id: bookingId },
      data: {
        Status: BookingStatus.Completed,
        UpdatedAt: new Date(),
      },
      include: {
        Client: { select: { FullName: true } },
        Provider: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    // Notify both parties of completion
    const isClient = booking.ClientId === userId;
    const notificationUserId = isClient ? booking.ProviderId : booking.ClientId;
    const notificationMessage = isClient
      ? `Clientul ${booking.Client.FullName} a confirmat finalizarea serviciului "${booking.AdvertisedService.ServiceName}".`
      : `Furnizorul ${booking.Provider.FullName} a marcat serviciul "${booking.AdvertisedService.ServiceName}" ca finalizat.`;

    await prisma.notification.create({
      data: {
        UserId: notificationUserId,
        Message: notificationMessage,
        Link: isClient ? `/dashboard/provider/calendar` : `/dashboard/client/bookings`,
        Type: NotificationType.BookingStatusChanged,
      }
    });

    return res.json({ success: true, booking: updatedBooking });

  } catch (error) {
    console.error('[API /bookings/:id/complete PUT] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la finalizarea rezervării.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Mark booking as in progress (provider action)
router.put('/:bookingId/start', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const providerId = parseInt(req.user.id, 10);
    const bookingId = parseInt(req.params.bookingId, 10);

    if (isNaN(bookingId)) {
      return res.status(400).json({ success: false, message: 'ID rezervare invalid.' });
    }

    const booking = await prisma.booking.findFirst({
      where: {
        Id: bookingId,
        ProviderId: providerId,
        Status: BookingStatus.Confirmed,
      },
      include: {
        Client: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Rezervarea nu a fost găsită sau nu poate fi modificată.'
      });
    }

    const updatedBooking = await prisma.booking.update({
      where: { Id: bookingId },
      data: {
        Status: BookingStatus.InProgress,
        UpdatedAt: new Date(),
      },
      include: {
        Client: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    // Notify client
    await prisma.notification.create({
      data: {
        UserId: booking.ClientId,
        Message: `Serviciul "${booking.AdvertisedService.ServiceName}" a fost început de furnizor.`,
        Link: `/dashboard/client/bookings`,
        Type: NotificationType.BookingStatusChanged,
      }
    });

    return res.json({ success: true, booking: updatedBooking });

  } catch (error) {
    console.error('[API /bookings/:id/start PUT] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la începerea serviciului.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Get bookings requiring completion confirmation
router.get('/pending-completion', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const userId = parseInt(req.user.id, 10);

    const pendingBookings = await prisma.booking.findMany({
      where: {
        ClientId: userId,
        Status: BookingStatus.InProgress,
        EventStartDateTime: {
          lte: new Date(), // Service time has passed
        },
      },
      include: {
        Provider: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      },
      orderBy: {
        EventStartDateTime: 'desc'
      }
    });

    return res.json({ success: true, bookings: pendingBookings });

  } catch (error) {
    console.error('[API /bookings/pending-completion GET] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la preluarea rezervărilor.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Get confirmed bookings for provider (ready for service delivery)
router.get('/provider/:providerId/confirmed', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }

    const providerId = parseInt(req.params.providerId, 10);
    const requestingUserId = parseInt(req.user.id, 10);

    // Ensure the requesting user is the provider
    if (providerId !== requestingUserId) {
      return res.status(403).json({ success: false, message: 'Acces interzis.' });
    }

    const confirmedBookings = await prisma.booking.findMany({
      where: {
        ProviderId: providerId,
        OR: [
          { Status: BookingStatus.Confirmed },
          { Status: BookingStatus.InProgress }, // Include in-progress bookings
        ],
      },
      include: {
        Client: {
          select: {
            Id: true,
            FullName: true,
            Email: true,
          }
        },
        AdvertisedService: {
          select: {
            Id: true,
            ServiceName: true,
          }
        }
      },
      orderBy: {
        EventStartDateTime: 'asc'
      }
    });

    const formattedBookings = confirmedBookings.map(booking => ({
      id: booking.Id,
      clientId: booking.ClientId,
      advertisedServiceId: booking.AdvertisedServiceId,
      eventStartDateTime: booking.EventStartDateTime?.toISOString(),
      eventEndDateTime: booking.EventEndDateTime?.toISOString(),
      status: booking.Status,
      clientNotes: booking.ClientNotes,
      createdAt: booking.CreatedAt.toISOString(),
      client: {
        fullName: booking.Client.FullName,
        email: booking.Client.Email,
      },
      service: {
        serviceName: booking.AdvertisedService.ServiceName,
      }
    }));

    return res.json({
      success: true,
      bookings: formattedBookings
    });

  } catch (error) {
    console.error('[API /bookings/provider/:id/confirmed GET] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la preluarea rezervărilor confirmate.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

export default router;
