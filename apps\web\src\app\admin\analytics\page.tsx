"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Users,
  TrendingUp,
  Activity,
  BarChart3,
  PieChart,
  Calendar,
  Download,
  RefreshCw,
  Settings,
  Eye,
  UserCheck,
  Briefcase
} from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations, analyticsTranslations } from '@repo/translations';

// Import existing components
import { UserRegistrationChart } from './_components/UserRegistrationChart';
import { ServiceCategoryChart } from './_components/ServiceCategoryChart';
import { ProviderRequestsChart } from './_components/ProviderRequestsChart';
import { BookingsChart } from './_components/BookingsChart';
import { TopProvidersTable } from './_components/TopProvidersTable';
import { RecentActivityFeed } from './_components/RecentActivityFeed';
import { ActiveUsersStats } from './_components/ActiveUsersStats';

// Import new components
import { OverviewDashboard } from './_components/OverviewDashboard';
import { UsersAnalytics } from './_components/UsersAnalytics';
import { ProvidersAnalytics } from './_components/ProvidersAnalytics';
import { ServicesAnalytics } from './_components/ServicesAnalytics';
import { ActivityAnalytics } from './_components/ActivityAnalytics';

const localAnalyticsTranslations = {
  analyticsTitle: { ro: "Analiză și Statistici", ru: "Аналитика и статистика", en: "Analytics & Statistics" },
  analyticsDescription: { ro: "Vizualizează metrici cheie și tendințe pentru platforma ta.", ru: "Просматривайте ключевые показатели и тенденции для вашей платформы.", en: "View key metrics and trends for your platform." },
  timePeriod: { ro: "Perioada de timp", ru: "Период времени", en: "Time Period" },
  last7Days: { ro: "Ultimele 7 zile", ru: "Последние 7 дней", en: "Last 7 days" },
  last30Days: { ro: "Ultimele 30 zile", ru: "Последние 30 дней", en: "Last 30 days" },
  last90Days: { ro: "Ultimele 90 zile", ru: "Последние 90 дней", en: "Last 90 days" },
  lastYear: { ro: "Ultimul an", ru: "Последний год", en: "Last year" },
  refreshData: { ro: "Actualizează datele", ru: "Обновить данные", en: "Refresh data" },
  exportReport: { ro: "Exportă raport", ru: "Экспортировать отчет", en: "Export report" },

  // Tab labels
  overview: { ro: "Prezentare generală", ru: "Обзор", en: "Overview" },
  users: { ro: "Utilizatori", ru: "Пользователи", en: "Users" },
  providers: { ro: "Prestatori", ru: "Поставщики", en: "Providers" },
  services: { ro: "Servicii", ru: "Услуги", en: "Services" },
  activity: { ro: "Activitate", ru: "Активность", en: "Activity" },

  loadingAnalytics: { ro: "Se încarcă analiza...", ru: "Загрузка аналитики...", en: "Loading analytics..." },
  errorLoadingAnalytics: { ro: "Eroare la încărcarea analizei", ru: "Ошибка загрузки аналитики", en: "Error loading analytics" },
  lastUpdated: { ro: "Ultima actualizare", ru: "Последнее обновление", en: "Last updated" },
};

type TimePeriod = '7d' | '30d' | '90d' | '1y';
type AnalyticsTab = 'overview' | 'users' | 'providers' | 'services' | 'activity';

export default function AdminAnalyticsPage() {
  const { translate } = useLanguage();
  const [activeTab, setActiveTab] = useState<AnalyticsTab>('overview');
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('30d');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Trigger refresh for all components
    setLastUpdated(new Date());
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export report for period:', timePeriod, 'tab:', activeTab);
  };

  const getTabIcon = (tab: AnalyticsTab) => {
    switch (tab) {
      case 'overview':
        return <Eye className="w-4 h-4" />;
      case 'users':
        return <Users className="w-4 h-4" />;
      case 'providers':
        return <UserCheck className="w-4 h-4" />;
      case 'services':
        return <Briefcase className="w-4 h-4" />;
      case 'activity':
        return <Activity className="w-4 h-4" />;
      default:
        return <BarChart3 className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div className="flex-1">
          <h1 className="text-2xl lg:text-3xl font-bold text-foreground">
            {translate(localAnalyticsTranslations, 'analyticsTitle')}
          </h1>
          <p className="text-muted-foreground mt-1 text-sm lg:text-base">
            {translate(localAnalyticsTranslations, 'analyticsDescription')}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
          <Select value={timePeriod} onValueChange={(value: TimePeriod) => setTimePeriod(value)}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <Calendar className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">{translate(localAnalyticsTranslations, 'last7Days')}</SelectItem>
              <SelectItem value="30d">{translate(localAnalyticsTranslations, 'last30Days')}</SelectItem>
              <SelectItem value="90d">{translate(localAnalyticsTranslations, 'last90Days')}</SelectItem>
              <SelectItem value="1y">{translate(localAnalyticsTranslations, 'lastYear')}</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">{translate(localAnalyticsTranslations, 'refreshData')}</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              <span className="hidden sm:inline">{translate(localAnalyticsTranslations, 'exportReport')}</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as AnalyticsTab)} className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 h-auto p-1">
          <TabsTrigger value="overview" className="flex items-center gap-2 px-3 py-2">
            {getTabIcon('overview')}
            <span className="hidden sm:inline">{translate(localAnalyticsTranslations, 'overview')}</span>
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2 px-3 py-2">
            {getTabIcon('users')}
            <span className="hidden sm:inline">{translate(localAnalyticsTranslations, 'users')}</span>
          </TabsTrigger>
          <TabsTrigger value="providers" className="flex items-center gap-2 px-3 py-2">
            {getTabIcon('providers')}
            <span className="hidden sm:inline">{translate(localAnalyticsTranslations, 'providers')}</span>
          </TabsTrigger>
          <TabsTrigger value="services" className="flex items-center gap-2 px-3 py-2">
            {getTabIcon('services')}
            <span className="hidden sm:inline">{translate(localAnalyticsTranslations, 'services')}</span>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2 px-3 py-2">
            {getTabIcon('activity')}
            <span className="hidden sm:inline">{translate(localAnalyticsTranslations, 'activity')}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <OverviewDashboard
            timePeriod={timePeriod}
            refreshTrigger={lastUpdated}
          />
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <UsersAnalytics
            timePeriod={timePeriod}
            refreshTrigger={lastUpdated}
          />
        </TabsContent>

        <TabsContent value="providers" className="mt-6">
          <ProvidersAnalytics
            timePeriod={timePeriod}
            refreshTrigger={lastUpdated}
          />
        </TabsContent>

        <TabsContent value="services" className="mt-6">
          <ServicesAnalytics
            timePeriod={timePeriod}
            refreshTrigger={lastUpdated}
          />
        </TabsContent>

        <TabsContent value="activity" className="mt-6">
          <ActivityAnalytics
            timePeriod={timePeriod}
            refreshTrigger={lastUpdated}
          />
        </TabsContent>
      </Tabs>

      {/* Last Updated Info */}
      <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
        <Badge variant="outline" className="text-xs">
          {translate(localAnalyticsTranslations, 'lastUpdated')}: {lastUpdated.toLocaleString()}
        </Badge>
      </div>
    </div>
  );
}
