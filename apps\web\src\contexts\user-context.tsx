
"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import type { ExtendedNextAuthUser } from '@repo/auth';

interface UserContextType {
  user: ExtendedNextAuthUser | null;
  isLoading: boolean;
  revalidateUser: () => void;
  clearUser: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider = ({ children }: { children: ReactNode }) => {
  const { data: session, status } = useSession();
  const [user, setUser] = useState<ExtendedNextAuthUser | null>(null);
  // Initialize loading state as true - only set to false when we have definitive auth state
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();

  // Function to immediately clear user data - useful for logout scenarios
  const clearUser = useCallback(() => {
    console.log('[UserContext] Manually clearing user data');
    setUser(null);
    setIsLoading(false);
  }, []);

  const fetchAndUpdateUser = useCallback(async () => {
    // Keep loading state true while NextAuth is still determining session status
    if (status === 'loading') {
      setIsLoading(true);
      return;
    }

    // IMMEDIATELY clear user data when unauthenticated - this fixes the role persistence issue
    if (status === 'unauthenticated') {
      console.log('[UserContext] Session unauthenticated - clearing user data immediately');
      setUser(null);
      setIsLoading(false);
      return;
    }

    if (status === 'authenticated') {
      setIsLoading(true);
      try {
        const response = await fetch('/api/auth/me');
        if (response.ok) {
          const data = await response.json();
          if (data.authenticated) {
            console.log('[UserContext] User data fetched successfully:', {
              id: data.user.id,
              isAdmin: data.user.isAdmin,
              isProvider: data.user.isProvider
            });
            setUser(data.user);
          } else {
            console.log('[UserContext] API returned unauthenticated - clearing user data');
            setUser(null);
          }
        } else {
          console.log('[UserContext] Failed to fetch user data - clearing user data');
          setUser(null);
        }
      } catch (error) {
        console.error("UserContext: Failed to fetch user data", error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    }
  }, [status]);

  useEffect(() => {
    fetchAndUpdateUser();
  }, [session, fetchAndUpdateUser]); // Removed pathname dependency to prevent excessive refetches

  // Listen for provider approval events to refresh session
  useEffect(() => {
    const handleUserBecameProvider = (event: CustomEvent) => {
      console.log('[UserContext] User became provider event received, refreshing user data');
      fetchAndUpdateUser();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('userBecameProvider', handleUserBecameProvider as EventListener);
      return () => {
        window.removeEventListener('userBecameProvider', handleUserBecameProvider as EventListener);
      };
    }
  }, [fetchAndUpdateUser]);

  // CRITICAL: Immediately clear user data when session becomes unauthenticated
  // This prevents stale role data from persisting during logout->login transitions
  useEffect(() => {
    if (status === 'unauthenticated') {
      console.log('[UserContext] Session status changed to unauthenticated - immediate user data clear');
      setUser(null);
      setIsLoading(false);
    } else if (status === 'loading') {
      setIsLoading(true);
    }
  }, [status]);

  return (
    <UserContext.Provider value={{ user, isLoading, revalidateUser: fetchAndUpdateUser, clearUser }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
