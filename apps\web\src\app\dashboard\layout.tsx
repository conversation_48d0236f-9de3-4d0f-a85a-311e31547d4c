
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@repo/auth';
import type { ExtendedSession } from '@repo/auth';
import { DashboardLayoutClient } from './dashboard-layout-client';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // SERVER-SIDE AUTHENTICATION CHECK - This prevents dashboard content flash for admin users
  const session = await getServerSession(authOptions) as ExtendedSession | null;

  console.log('[DashboardLayout] Server-side auth check:', {
    hasSession: !!session,
    userId: session?.user?.id,
    isAdmin: session?.user?.isAdmin,
    isProvider: session?.user?.isProvider
  });

  // Redirect non-authenticated users to login
  if (!session || !session.user) {
    console.log('[DashboardLayout] No session found - redirecting to login');
    redirect('/login?callbackUrl=/dashboard');
  }

  // CRITICAL: Redirect admin users to admin panel - NO DASHBOARD CONTENT WILL RENDER
  if (session.user.isAdmin) {
    console.log('[DashboardLayout] Admin user attempting dashboard access - redirecting to admin');
    redirect('/admin');
  }

  // Only non-admin users reach this point - safe to render dashboard content
  return (
    <DashboardLayoutClient session={session}>
      {children}
    </DashboardLayoutClient>
  );
}
