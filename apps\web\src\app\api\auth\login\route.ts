
import { type NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { UserRole } from '@prisma/client'; // Import UserRole enum

// This route is largely superseded by NextAuth's CredentialsProvider.
// If you want to keep it for some other purpose, it should NOT set session cookies
// that conflict with NextAuth.js.
// For now, it will just log and return a message indicating to use NextAuth.
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    console.log('[DEPRECATED Login API] Received login request for email:', email);
    console.warn('[DEPRECATED Login API] This route should ideally not be used for primary login. Please use NextAuth CredentialsProvider.');

    // This block is for demonstration and should not be used for production login.
    // The actual login logic is handled by the CredentialsProvider in `packages/auth/index.ts`,
    // which correctly calls the backend API.
    // We are keeping a placeholder here to avoid breaking changes if anything still calls this route,
    // but we are removing the direct `prisma` dependency.
    
    // Example of how it *would* work with an API call (this is already done in NextAuth's authorize function)
    /*
    const userToLogin = await apiFetch('auth/user-by-email', { method: 'POST', body: JSON.stringify({ email }) });
    const user = await userToLogin.json();

    if (user && user.Password) {
      const isPasswordValid = bcrypt.compareSync(password, user.Password);
      if (isPasswordValid) {
        // ... valid credentials logic
      }
    }
    */
    
    console.log('[DEPRECATED Login API] Invalid credentials or user not found for:', email);
    // Return a generic error as this route should not be successfully used.
    return NextResponse.json({ success: false, message: 'Email sau parolă incorectă. Folosiți sistemul de autentificare principal.' }, { status: 401 });

  } catch (error) {
    let responseMessage = 'A apărut o eroare la autentificare (deprecated API).';
    const statusCode = 500;
    const rawErrorMessage = error instanceof Error ? error.message : 'Unknown internal error';

    console.error('[DEPRECATED Login API] Raw Error during login process:', error); 

    // Specific error detection for database connectivity issues can be complex here without direct access.
    // Keeping it generic is safer.
    responseMessage = `A apărut o eroare la autentificare (deprecated API): ${rawErrorMessage.substring(0, 300)}${rawErrorMessage.length > 300 ? "..." : ""}`;
    
    console.log(`[DEPRECATED Login API] Sending error response to client: ${responseMessage}`);
    return NextResponse.json({ success: false, message: responseMessage }, { status: statusCode });
  }
}
