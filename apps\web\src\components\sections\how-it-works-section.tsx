
"use client";
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/language-context';

const howItWorksTranslations = {
  sectionTitle: { ro: "Cum funcționează bonami", ru: "Как работает bonami", en: "How bonami Works" },
  sectionDescription: { ro: "Găsește și conectează-te cu furnizori de servicii de încredere în doar câțiva pași simpli.", ru: "Найдите и свяжитесь с надежными поставщиками услуг всего за несколько простых шагов.", en: "Find and connect with trusted service providers in just a few simple steps." },
  step1Title: { ro: "Creează-ți Profilul", ru: "Создайте свой профиль", en: "Create Your Profile" },
  step1Desc: { ro: "Înscrie-te și creează un profil detaliat specificând nevoile tale sau serviciile pe care le poți oferi.", ru: "Зарегистрируйтесь и создайте подробный профиль, указав свои потребности или услуги, которые вы можете предложить.", en: "Sign up and create a detailed profile specifying your needs or the services you can offer." },
  step2Title: { ro: "Caută & Conectează-te", ru: "Ищите и связывайтесь", en: "Search & Connect" },
  step2Desc: { ro: "Caută printre furnizorii verificați, citește recenzii și conectează-te cu cei care corespund cerințelor tale.", ru: "Ищите среди проверенных поставщиков, читайте отзывы и связывайтесь с теми, кто соответствует вашим требованиям.", en: "Search among verified providers, read reviews, and connect with those who meet your requirements." },
  step3Title: { ro: "Rezervă & Bucură-te", ru: "Бронируйте и наслаждайтесь", en: "Book & Enjoy" },
  step3Desc: { ro: "Programează servicii, efectuează plăți sigure și bucură-te de liniște cu garanția noastră de satisfacție.", ru: "Планируйте услуги, совершайте безопасные платежи и наслаждайтесь спокойствием с нашей гарантией удовлетворения.", en: "Schedule services, make secure payments, and enjoy peace of mind with our satisfaction guarantee." },
  startButton: { ro: "Începe Astăzi", ru: "Начать сегодня", en: "Start Today" },
};

export function HowItWorksSection() {
  const { translate } = useLanguage();
  const steps = [
    { number: "1", titleKey: "step1Title", descriptionKey: "step1Desc" },
    { number: "2", titleKey: "step2Title", descriptionKey: "step2Desc" },
    { number: "3", titleKey: "step3Title", descriptionKey: "step3Desc" },
  ];

  return (
    <section className="py-16 md:py-20 px-6 bg-background">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl font-bold mb-2 font-headline">{translate(howItWorksTranslations, 'sectionTitle')}</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translate(howItWorksTranslations, 'sectionDescription')}
          </p>
        </div>
            
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step) => (
            <Card key={step.number} className="text-center shadow-lg hover:shadow-xl transition-shadow duration-300 bg-card flex flex-col p-6">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6 text-primary-foreground font-bold text-3xl">
                {step.number}
              </div>
              <CardTitle className="text-xl font-semibold font-headline mb-3">{translate(howItWorksTranslations, step.titleKey)}</CardTitle>
              <CardDescription className="text-foreground/80 flex-grow">{translate(howItWorksTranslations, step.descriptionKey)}</CardDescription>
            </Card>
          ))}
        </div>
            
        <div className="mt-12 md:mt-16 text-center">
          <Button size="lg" asChild className="bg-primary hover:bg-secondary transition-colors">
            <Link href="/register">{translate(howItWorksTranslations, 'startButton')}</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
