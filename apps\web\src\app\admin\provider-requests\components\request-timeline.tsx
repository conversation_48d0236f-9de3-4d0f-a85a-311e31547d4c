"use client";

import React from 'react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  FileText
} from "lucide-react";

interface TimelineEvent {
  id: string;
  type: 'created' | 'service_added' | 'service_approved' | 'service_rejected' | 'service_changes_requested' | 'request_approved' | 'request_rejected';
  title: string;
  description: string;
  timestamp: string;
  actor?: string;
  metadata?: any;
}

interface RequestTimelineProps {
  events: TimelineEvent[];
}

const getEventIcon = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'created':
      return <FileText className="h-4 w-4" />;
    case 'service_added':
      return <FileText className="h-4 w-4" />;
    case 'service_approved':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'service_rejected':
      return <XCircle className="h-4 w-4 text-red-600" />;
    case 'service_changes_requested':
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    case 'request_approved':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'request_rejected':
      return <XCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Clock className="h-4 w-4" />;
  }
};

const getEventColor = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'service_approved':
    case 'request_approved':
      return 'border-green-200 bg-green-50';
    case 'service_rejected':
    case 'request_rejected':
      return 'border-red-200 bg-red-50';
    case 'service_changes_requested':
      return 'border-yellow-200 bg-yellow-50';
    default:
      return 'border-gray-200 bg-gray-50';
  }
};

export function RequestTimeline({ events }: RequestTimelineProps) {
  const { translate } = useLanguage();

  if (events.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {translate(commonTranslations, 'adminRequestTimeline')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            {translate(commonTranslations, 'adminNoTimelineEvents')}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          {translate(commonTranslations, 'adminRequestTimeline')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {events.map((event, index) => (
            <div key={event.id} className="flex gap-4">
              {/* Timeline line */}
              <div className="flex flex-col items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${getEventColor(event.type)}`}>
                  {getEventIcon(event.type)}
                </div>
                {index < events.length - 1 && (
                  <div className="w-px h-8 bg-gray-200 mt-2" />
                )}
              </div>
              
              {/* Event content */}
              <div className="flex-1 min-w-0 pb-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900">{event.title}</h4>
                  <time className="text-xs text-gray-500">
                    {new Date(event.timestamp).toLocaleDateString()} {new Date(event.timestamp).toLocaleTimeString()}
                  </time>
                </div>
                <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                {event.actor && (
                  <div className="flex items-center gap-1 mt-2">
                    <User className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500">{event.actor}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
