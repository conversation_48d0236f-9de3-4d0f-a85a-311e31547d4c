
"use client";

import React, { useState, type FormEvent, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON><PERSON>ef<PERSON>, Loader2 } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { useRouter } from 'next/navigation';
import { commonTranslations } from '@repo/translations';
import { allMoldovaLocations } from '@/lib/locations';
import type { ServiceCategorySlug } from '@prisma/client';
import { useSession } from "next-auth/react";
import { ServicesService } from '@repo/services';
import type { AdvertisedServicePayload, TutoringServiceDetailsPayload } from '@repo/types';

const StepInput = React.memo(({ id, value, onChange, label, placeholder, type = "text", className, fieldError }: { id: string; value: string | number; onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; label: string; placeholder?: string; type?: string; className?: string; fieldError?: boolean; }) => (
  <div className="space-y-1.5"><Label htmlFor={id}>{label}</Label><Input id={id} type={type} value={value} onChange={onChange} placeholder={placeholder} className={cn("text-sm", className, fieldError && "border-destructive focus-visible:ring-destructive")} /></div>
));
StepInput.displayName = 'StepInput';

const StepTextarea = React.memo(({ id, value, onChange, label, placeholder, rows = 3, className, fieldError }: { id: string; value: string; onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void; label: string; placeholder?: string; rows?: number; className?: string; fieldError?: boolean; }) => (
  <div className="space-y-1.5"><Label htmlFor={id}>{label}</Label><Textarea id={id} value={value} onChange={onChange} placeholder={placeholder} rows={rows} className={cn("text-sm", className, fieldError && "border-destructive focus-visible:ring-destructive")} /></div>
));
StepTextarea.displayName = 'StepTextarea';

const StepSwitchField = React.memo(({ id, label, checked, onCheckedChange }: { id: string; label: string; checked: boolean | undefined; onCheckedChange: (checked: boolean) => void; }) => (
  <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm bg-card"><Label htmlFor={id} className="cursor-pointer text-sm">{label}</Label><Switch id={id} checked={!!checked} onCheckedChange={onCheckedChange} /></div>
));
StepSwitchField.displayName = 'StepSwitchField';

const TutoringFields = React.memo(({ details, handleDetailChange, translate }: { details: any, handleDetailChange: (field: string, value: any) => void, translate: (t: any, k: string) => string }) => (
    <div className="space-y-4 p-4 border rounded-md bg-muted/20 mt-4">
        <h4 className="font-medium text-md mb-3">{translate(commonTranslations, 'tutoringTitle')}</h4>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'tutoringGradesCoveredLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id="Grades_1_4" label={translate(commonTranslations, 'tutoringGrades_1_4')} checked={details.Grades_1_4} onCheckedChange={(c)=>handleDetailChange('Grades_1_4',c)} /><StepSwitchField id="Grades_5_8" label={translate(commonTranslations, 'tutoringGrades_5_8')} checked={details.Grades_5_8} onCheckedChange={(c)=>handleDetailChange('Grades_5_8',c)} /><StepSwitchField id="Grades_9_12" label={translate(commonTranslations, 'tutoringGrades_9_12')} checked={details.Grades_9_12} onCheckedChange={(c)=>handleDetailChange('Grades_9_12',c)} /></div></div>
    </div>
));
TutoringFields.displayName = "TutoringFields";

export default function AddTutoringServicePage() {
    const { translate } = useLanguage();
    const router = useRouter();
    const { data: session, status: sessionStatus } = useSession();
    const { toast } = useToast();

    const [formData, setFormData] = useState({
        ServiceName: '',
        Description: '',
        Status: 'Activ' as 'Activ' | 'Inactiv',
        Details: { experienceYears: '', description: '', LocationValue: 'all', PricePerHour: '', PricePerDay: '' },
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        if (fieldErrors[name]) setFieldErrors(p => ({...p, [name]: false}));
    };
    
    const handleDetailChange = useCallback((name: string, value: any) => {
        setFormData(prev => ({ ...prev, Details: { ...prev.Details, [name]: value } }));
        if (fieldErrors[name]) setFieldErrors(p => ({...p, [name]: false}));
    }, [fieldErrors]);

    const validateForm = () => {
        const errors: Record<string, boolean> = {};
        if (!formData.ServiceName.trim()) errors.ServiceName = true;
        if (!formData.Description.trim()) errors.Description = true;
        if (!formData.Details.LocationValue) errors.LocationValue = true;
        if (!formData.Details.experienceYears?.trim() || isNaN(parseInt(formData.Details.experienceYears, 10)) || parseInt(formData.Details.experienceYears, 10) < 0) {
            errors.experienceYears = true;
        }
        setFieldErrors(errors);
        if (Object.keys(errors).length > 0) {
            toast({ variant: "destructive", title: "Eroare Validare", description: "Vă rugăm completați câmpurile obligatorii." });
            return false;
        }
        return true;
    }

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        if (!validateForm()) return;
        
        if (sessionStatus !== 'authenticated' || !session?.user) {
            toast({ variant: "destructive", title: "Eroare", description: "Sesiune invalidă." });
            return;
        }

        setIsSubmitting(true);
        const providerId = (session.user as any).id;
        
        const detailsPayload: TutoringServiceDetailsPayload = {
            ...formData.Details,
            ExperienceYears: parseInt(formData.Details.experienceYears, 10) || 0,
            PricePerHour: formData.Details.PricePerHour ? parseFloat(formData.Details.PricePerHour) : null,
            PricePerDay: formData.Details.PricePerDay ? parseFloat(formData.Details.PricePerDay) : null,
        };

        const payload: Partial<AdvertisedServicePayload> = {
            ProviderId: parseInt(providerId, 10),
            ServiceName: formData.ServiceName,
            Description: formData.Description,
            ServiceCategorySlug: 'Tutoring',
            Status: formData.Status,
            TutoringDetails: detailsPayload,
        };
        
        try {
            await ServicesService.createService(payload);
            toast({ title: "Succes!", description: `Serviciul de Meditații a fost adăugat.` });
            router.push('/dashboard/provider/services');
        } catch (err) {
            toast({ variant: "destructive", title: "Eroare", description: err instanceof Error ? err.message : "A apărut o eroare." });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="space-y-6 pb-12">
            <Button variant="outline" asChild>
                <Link href="/dashboard/provider/services/new">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Înapoi la Selecție Categorii
                </Link>
            </Button>
            <Card>
                <CardHeader>
                    <CardTitle>Adaugă Serviciu: Meditații / Educație</CardTitle>
                    <CardDescription>Completează detaliile pentru noul serviciu de meditații.</CardDescription>
                </CardHeader>
                <form onSubmit={handleSubmit}>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="ServiceName">Nume Serviciu</Label>
                            <Input id="ServiceName" name="ServiceName" value={formData.ServiceName} onChange={handleInputChange} className={cn(fieldErrors.ServiceName && "border-destructive")} />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="Description">Descriere Principală Serviciu</Label>
                            <Textarea id="Description" name="Description" value={formData.Description} onChange={handleInputChange} className={cn(fieldErrors.Description && "border-destructive")} />
                        </div>
                        
                        <StepInput id="experienceYears" value={formData.Details.experienceYears} onChange={(e) => handleDetailChange("experienceYears", e.target.value)} label={translate(commonTranslations, 'experienceYearsLabel')} placeholder={translate(commonTranslations, 'experiencePlaceholder')} type="number" fieldError={fieldErrors.experienceYears} />
                        <div className="space-y-2">
                            <Label htmlFor="LocationValue">{translate(commonTranslations, 'locationLabel')}</Label>
                            <Select value={formData.Details.LocationValue} onValueChange={(val) => handleDetailChange('LocationValue', val)}>
                               <SelectTrigger id="LocationValue" className={cn("text-sm", fieldErrors.LocationValue && "border-destructive focus-visible:ring-destructive")}>
                                    <SelectValue placeholder={translate(commonTranslations, 'locationPlaceholder')} />
                                </SelectTrigger>
                                <SelectContent>
                                    {allMoldovaLocations.map(loc => (<SelectItem key={loc.key} value={loc.value}>{translate(commonTranslations, loc.translationKey as keyof typeof commonTranslations)}</SelectItem>))}
                                </SelectContent>
                            </Select>
                        </div>
                        <StepInput id="PricePerHour" value={formData.Details.PricePerHour} onChange={(e) => handleDetailChange("PricePerHour", e.target.value)} label={translate(commonTranslations, 'pricePerHourLabel')} type="number" />
                        <StepInput id="PricePerDay" value={formData.Details.PricePerDay} onChange={(e) => handleDetailChange("PricePerDay", e.target.value)} label={translate(commonTranslations, 'pricePerDayLabel')} type="number" />
                        
                        <TutoringFields details={formData.Details} handleDetailChange={handleDetailChange} translate={translate} />

                    </CardContent>
                    <CardFooter>
                        <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            Adaugă Serviciu
                        </Button>
                    </CardFooter>
                </form>
            </Card>
        </div>
    );
}
