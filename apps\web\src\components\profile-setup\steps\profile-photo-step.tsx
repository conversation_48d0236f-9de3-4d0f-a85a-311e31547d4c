"use client";

import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, Camera, Trash2, User } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { useToast } from '@/hooks/use-toast';
import { PROFILE_SETUP_VALIDATION } from '@/types/profile-setup';
import { getAvatarUrl } from '@/lib/avatar-utils';

interface ProfilePhotoStepProps {
  value: File | null;
  currentPhotoUrl?: string | null;
  onChange: (file: File | null) => void;
  onPhotoUrlChange: (url: string | null) => void;
}

const photoStepTranslations = {
  en: {
    title: "Add a Profile Photo",
    description: "Help others recognize you with a profile picture. This is optional but recommended.",
    uploadButton: "Upload Photo",
    changeButton: "Change Photo",
    removeButton: "Remove Photo",
    dragDropText: "Drag and drop a photo here, or click to select",
    supportedFormats: "Supported formats: JPG, PNG, WebP (max 5MB)",
    photoPreview: "Photo Preview",
    uploadError: "Error uploading photo",
    removeError: "Error removing photo",
    photoRemoved: "Photo removed successfully",
    invalidFileType: "Please select a valid image file (JPG, PNG, or WebP)",
    fileTooLarge: "File is too large. Maximum size is 5MB",
    uploadSuccess: "Photo uploaded successfully"
  },
  ro: {
    title: "Adaugă o Fotografie de Profil",
    description: "Ajută-i pe alții să te recunoască cu o fotografie de profil. Acest pas este opțional dar recomandat.",
    uploadButton: "Încarcă Fotografia",
    changeButton: "Schimbă Fotografia",
    removeButton: "Elimină Fotografia",
    dragDropText: "Trage și plasează o fotografie aici, sau fă clic pentru a selecta",
    supportedFormats: "Formate acceptate: JPG, PNG, WebP (max 5MB)",
    photoPreview: "Previzualizare Fotografie",
    uploadError: "Eroare la încărcarea fotografiei",
    removeError: "Eroare la eliminarea fotografiei",
    photoRemoved: "Fotografia a fost eliminată cu succes",
    invalidFileType: "Te rugăm să selectezi un fișier imagine valid (JPG, PNG sau WebP)",
    fileTooLarge: "Fișierul este prea mare. Dimensiunea maximă este 5MB",
    uploadSuccess: "Fotografia a fost încărcată cu succes"
  },
  ru: {
    title: "Добавьте Фото Профиля",
    description: "Помогите другим узнать вас с фотографией профиля. Этот шаг необязательный, но рекомендуется.",
    uploadButton: "Загрузить Фото",
    changeButton: "Изменить Фото",
    removeButton: "Удалить Фото",
    dragDropText: "Перетащите фото сюда или нажмите для выбора",
    supportedFormats: "Поддерживаемые форматы: JPG, PNG, WebP (макс 5МБ)",
    photoPreview: "Предпросмотр Фото",
    uploadError: "Ошибка загрузки фото",
    removeError: "Ошибка удаления фото",
    photoRemoved: "Фото успешно удалено",
    invalidFileType: "Пожалуйста, выберите действительный файл изображения (JPG, PNG или WebP)",
    fileTooLarge: "Файл слишком большой. Максимальный размер 5МБ",
    uploadSuccess: "Фото успешно загружено"
  }
};

export function ProfilePhotoStep({ value, currentPhotoUrl, onChange, onPhotoUrlChange }: ProfilePhotoStepProps) {
  const { translate } = useLanguage();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Create preview URL when file changes
  React.useEffect(() => {
    if (value instanceof File) {
      const url = URL.createObjectURL(value);
      setPreviewUrl(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setPreviewUrl(null);
    }
  }, [value]);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!PROFILE_SETUP_VALIDATION.profilePhoto.allowedTypes.includes(file.type as any)) {
      return translate(photoStepTranslations, 'invalidFileType');
    }

    // Check file size
    if (file.size > PROFILE_SETUP_VALIDATION.profilePhoto.maxSizeBytes) {
      return translate(photoStepTranslations, 'fileTooLarge');
    }

    return null;
  };

  const handleFileSelect = (file: File) => {
    const error = validateFile(file);
    if (error) {
      toast({
        title: "Eroare!",
        description: error,
        variant: "destructive",
      });
      return;
    }

    onChange(file);
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleRemovePhoto = async () => {
    try {
      setIsUploading(true);

      // If there's a current photo URL, delete it from server
      if (currentPhotoUrl) {
        const response = await fetch('/api/user/profile/photo', {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to remove photo');
        }

        onPhotoUrlChange(null);
      }

      // Clear local state
      onChange(null);
      setPreviewUrl(null);

      toast({
        title: "Succes!",
        description: translate(photoStepTranslations, 'photoRemoved'),
      });

    } catch (error) {
      console.error('Error removing photo:', error);
      toast({
        title: "Eroare!",
        description: translate(photoStepTranslations, 'removeError'),
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const displayPhotoUrl = previewUrl || getAvatarUrl(currentPhotoUrl);
  const hasPhoto = displayPhotoUrl || value;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">
          {translate(photoStepTranslations, 'title')}
        </h3>
        <p className="text-muted-foreground">
          {translate(photoStepTranslations, 'description')}
        </p>
      </div>

      {/* Photo Preview */}
      <div className="flex justify-center">
        <Avatar className="w-32 h-32 border-4 border-muted">
          <AvatarImage src={displayPhotoUrl || undefined} alt="Profile preview" />
          <AvatarFallback className="text-2xl">
            <User className="w-12 h-12" />
          </AvatarFallback>
        </Avatar>
      </div>

      {/* Upload Area */}
      <Card className={`border-2 border-dashed transition-colors ${
        isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
      }`}>
        <CardContent className="p-8">
          <div
            className="text-center space-y-4 cursor-pointer"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleUploadClick}
          >
            <div className="flex justify-center">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Upload className="w-6 h-6 text-primary" />
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium">
                {translate(photoStepTranslations, 'dragDropText')}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {translate(photoStepTranslations, 'supportedFormats')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center gap-3">
        <Button
          variant="outline"
          onClick={handleUploadClick}
          disabled={isUploading}
        >
          <Camera className="w-4 h-4 mr-2" />
          {hasPhoto 
            ? translate(photoStepTranslations, 'changeButton')
            : translate(photoStepTranslations, 'uploadButton')
          }
        </Button>

        {hasPhoto && (
          <Button
            variant="outline"
            onClick={handleRemovePhoto}
            disabled={isUploading}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            {translate(photoStepTranslations, 'removeButton')}
          </Button>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={PROFILE_SETUP_VALIDATION.profilePhoto.allowedTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
}
