"use client";

import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LocationCombobox } from "@/components/ui/location-combobox";
import { useRouter } from 'next/navigation';
import { useState, useMemo, useEffect } from 'react';
import { useLanguage } from '@/contexts/language-context';
import { LocationService, type LocationEntry } from '@repo/services';
import { commonTranslations } from '@repo/translations';
import type { ServiceCategory } from '@prisma/client';

const heroTranslations = {
  title: {
    ro: 'Găsește îngrijire de calitate pentru familia ta',
    ru: 'Найдите качественный уход для вашей семьи',
    en: 'Find quality care for your family',
  },
  subtitle: {
    ro: 'Conectăm familiile cu profesioniști în îngrijirea copiilor, b<PERSON><PERSON><PERSON><PERSON><PERSON>, servicii de curățenie și meditații.',
    ru: 'Мы связываем семьи с профессионалами по уходу за детьми, пожилыми людьми, услугам по уборке и репетиторству.',
    en: 'We connect families with professionals in childcare, elderly care, cleaning services, and tutoring.',
  },
  searchButton: {
    ro: 'Caută acum',
    ru: 'Искать сейчас',
    en: 'Search Now',
  },
  serviceTypePlaceholder: {
    ro: 'Tip serviciu',
    ru: 'Тип услуги',
    en: 'Service type',
  },
  locationPlaceholder: {
    ro: 'Locație',
    ru: 'Местоположение',
    en: 'Location',
  },
  errorFetchingCategories: { ro: "Eroare la preluarea categoriilor.", ru: "Ошибка загрузки категорий.", en: "Error fetching categories." },
};

export function HeroSection() {
  const router = useRouter();
  const { translate } = useLanguage();
  const [selectedService, setSelectedService] = useState("all");
  const [selectedLocationValue, setSelectedLocationValue] = useState("all");
  const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);

  // Locations state
  const [locations, setLocations] = useState<LocationEntry[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const response = await fetch(`/api/proxy/service-categories`);
        if (!response.ok) {
          throw new Error(translate(heroTranslations, 'errorFetchingCategories'));
        }
        const data = await response.json();
        setServiceCategories(data || []);
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoadingCategories(false);
      }
    };
    fetchCategories();
  }, [translate]);

  // Load locations on component mount
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setLocationsLoading(true);
        const locationData = await LocationService.getLocationsForSearch(translate.currentLanguage);
        setLocations(locationData);
      } catch (error) {
        console.error('Failed to load locations:', error);
      } finally {
        setLocationsLoading(false);
      }
    };

    loadLocations();
  }, [translate.currentLanguage]);

  const handleSearch = () => {
    router.push(`/search?service=${selectedService}&location=${selectedLocationValue}`);
  };

  const serviceOptions = useMemo(() => [
    { value: "all", label: translate(commonTranslations, 'serviceAll') },
    ...serviceCategories.map(category => ({
      value: category.Slug, // Corrected: Slug
      label: translate(commonTranslations, category.NameKey as keyof typeof commonTranslations) // Corrected: NameKey
    }))
  ], [serviceCategories, translate]);

  return (
    <header className="hero-gradient text-white py-20 md:py-24 px-6">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight font-headline">
          {translate(heroTranslations, 'title')}
        </h1>
        <p className="text-lg md:text-xl mb-8 opacity-90 max-w-2xl mx-auto">
          {translate(heroTranslations, 'subtitle')}
        </p>
        <div className="flex flex-col md:flex-row justify-center items-stretch gap-2 max-w-2xl mx-auto bg-card p-3 rounded-lg shadow-xl">
          <Select value={selectedService} onValueChange={setSelectedService} disabled={isLoadingCategories}>
            <SelectTrigger className="w-full md:w-1/3 text-foreground border-border focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50">
              <SelectValue placeholder={isLoadingCategories ? translate(commonTranslations, 'loading') : translate(heroTranslations, 'serviceTypePlaceholder')} />
            </SelectTrigger>
            <SelectContent>
              {serviceOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <LocationCombobox
            locations={locations}
            value={selectedLocationValue}
            onValueChange={setSelectedLocationValue}
            placeholder={locationsLoading ? "Loading locations..." : translate(heroTranslations, 'locationPlaceholder')}
            className="w-full md:w-1/3"
          />
          <Button
            onClick={handleSearch}
            className="bg-accent text-accent-foreground px-6 py-3 w-full md:w-auto font-bold hover:bg-accent/90 transition-colors"
            disabled={isLoadingCategories}
          >
            {translate(heroTranslations, 'searchButton')}
          </Button>
        </div>
      </div>
    </header>
  );
}
