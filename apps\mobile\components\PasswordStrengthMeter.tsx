
import React from 'react';
import { View, StyleSheet, Text } from 'react-native';

interface PasswordStrengthMeterProps {
  score: number;
}

export const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({ score }) => {
  const strengthLevels = [
    { text: 'Prea Scurtă', color: '#E5E7EB' },
    { text: 'Foarte Slabă', color: '#EF4444' },
    { text: 'Slabă', color: '#F97316' },
    { text: 'Medie', color: '#F59E0B' },
    { text: 'Puternică', color: '#84CC16' },
    { text: 'Foarte Puternică', color: '#22C55E' },
  ];

  const level = strengthLevels[score] || strengthLevels[0];

  return (
    <View style={styles.container}>
      <View style={styles.meter}>
        {strengthLevels.slice(1).map((lvl, index) => (
          <View
            key={index}
            style={[
              styles.segment,
              { backgroundColor: score > index ? lvl.color : '#E5E7EB' },
            ]}
          />
        ))}
      </View>
      <Text style={[styles.text, { color: level.color }]}>{level.text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 16,
  },
  meter: {
    flexDirection: 'row',
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  segment: {
    flex: 1,
  },
  text: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
});
