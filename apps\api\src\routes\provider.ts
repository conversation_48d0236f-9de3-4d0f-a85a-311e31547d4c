
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { type AuthenticatedRequest } from '../middleware/auth';
import { type Prisma, ServiceCategorySlug, ServiceStatus, AdvertisedService, NannyServiceDetails, ElderCareServiceDetails, CleaningServiceDetails, TutoringServiceDetails, CookingServiceDetails, UserRole, NotificationType } from '@prisma/client';
import type { AdvertisedServicePayload } from '@repo/types';

const router = Router();

// Helper function to parse string to Decimal or null
function parseOptionalDecimal(value: string | number | undefined | null): Prisma.Decimal | null {
  if (value === undefined || value === null || String(value).trim() === '') return null;
  const num = parseFloat(String(value));
  return isNaN(num) ? null : new Prisma.Decimal(num);
}

// Helper function to parse string to Int or null
function parseOptionalInt(value: string | number | undefined | null): number | null {
  if (value === undefined || value === null || String(value).trim() === '') return null;
  const num = parseInt(String(value), 10);
  return isNaN(num) ? null : num;
}

// GET provider's appointments
router.get('/appointments', async (req: AuthenticatedRequest, res: Response) => {
  const providerIdStr = req.query.providerId as string | undefined;

  if (!providerIdStr) {
    return res.status(400).json({ message: 'Provider ID is required' });
  }

  const providerId = parseInt(providerIdStr, 10);
  if (isNaN(providerId)) {
    return res.status(400).json({ message: 'Invalid Provider ID format' });
  }

  try {
    const appointments = await prisma.booking.findMany({
      where: { ProviderId: providerId },
      include: {
        AdvertisedService: { select: { ServiceName: true, Category: { select: { NameKey: true } } } },
        Client: { select: { FullName: true, Email: true } }
      },
      orderBy: { EventStartDateTime: 'asc' },
    });
    
    const formattedAppointments = appointments.map(app => ({
        id: String(app.Id), 
        serviceName: app.AdvertisedService.ServiceName, 
        clientName: app.Client.FullName || 'Client Necunoscut', 
        date: app.EventStartDateTime,
        time: app.EventStartDateTime ? new Date(app.EventStartDateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit'}) : "N/A", 
        status: app.Status, 
    }));

    return res.json({ appointments: formattedAppointments });
  } catch (error) {
    console.error(`[API /provider/appointments] Error:`, error);
    return res.status(500).json({ message: 'Failed to fetch appointments' });
  }
});    

// GET provider's services
router.get('/services', async (req: AuthenticatedRequest, res: Response) => {
  const providerIdStr = req.query.providerId as string | undefined;
  const serviceCategorySlugParam = req.query.serviceCategorySlug as ServiceCategorySlug | undefined;
  
  if (!providerIdStr) {
    return res.status(400).json({ message: 'Provider ID is required' });
  }
  const providerIdNum = parseInt(providerIdStr, 10); 
  if (isNaN(providerIdNum)) {
    return res.status(400).json({ message: 'Invalid Provider ID' });
  }

  try {
    const whereClause: Prisma.AdvertisedServiceWhereInput = { ProviderId: providerIdNum }; 
    if (serviceCategorySlugParam) {
      whereClause.ServiceCategorySlug = serviceCategorySlugParam; 
    }

    const services = await prisma.advertisedService.findMany({
      where: whereClause,
      include: {
        NannyServiceDetails: true, ElderCareServiceDetails: true, CleaningServiceDetails: true, 
        TutoringServiceDetails: true, CookingServiceDetails: true, 
        Category: { select: { Id: true, Slug: true, NameKey: true }}
      },
      orderBy: { CreatedAt: 'desc' }, 
    });
    
    return res.json({ services: services });
  } catch (error) {
    console.error(`[API /provider/services GET] Error:`, error);
    return res.status(500).json({ message: 'Failed to fetch services' });
  }
});

// POST new service for provider
router.post('/services', async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user?.id) {
    return res.status(401).json({ message: 'Neautorizat: ID-ul prestatorului lipsește din sesiune.' });
  }
  const providerId = parseInt(req.user.id, 10);

  try {
    const body = req.body as Partial<AdvertisedServicePayload>;
    const { ServiceName, Description, ServiceCategorySlug, ...details } = body;

    if (!ServiceName || !Description || !ServiceCategorySlug) {
      return res.status(400).json({ message: 'Lipsesc câmpurile de bază ale serviciului (Nume, Descriere, Categorie).' });
    }
    
    const category = await prisma.serviceCategory.findUnique({ where: { Slug: ServiceCategorySlug } });
    if (!category) {
      return res.status(400).json({ message: `Categoria de servicii "${ServiceCategorySlug}" este invalidă.` });
    }

    const newService = await prisma.$transaction(async (tx) => {
      const service = await tx.advertisedService.create({
        data: {
          ProviderId: providerId,
          CategoryId: category.Id,
          ServiceName,
          Description,
          ServiceCategorySlug,
          Status: 'PendingReview', // New services from existing providers must be reviewed
        }
      });

      const detailData = { AdvertisedServiceId: service.Id, ...details[`${ServiceCategorySlug}Details` as keyof typeof details] as any };
      
      switch (ServiceCategorySlug) {
        case 'Nanny': await tx.nannyServiceDetails.create({ data: detailData }); break;
        case 'ElderCare': await tx.elderCareServiceDetails.create({ data: detailData }); break;
        case 'Cleaning': await tx.cleaningServiceDetails.create({ data: detailData }); break;
        case 'Tutoring': await tx.tutoringServiceDetails.create({ data: detailData }); break;
        case 'Cooking': await tx.cookingServiceDetails.create({ data: detailData }); break;
        default: throw new Error(`Tip de serviciu necunoscut: ${ServiceCategorySlug}`);
      }
      return service;
    });

    const admins = await prisma.user.findMany({ where: { Roles: { some: { Name: UserRole.Admin } } } });
    const provider = await prisma.user.findUnique({ where: { Id: providerId }, select: { FullName: true } });
    for (const admin of admins) {
      await prisma.notification.create({
        data: {
          UserId: admin.Id,
          Message: `Serviciu nou ("${newService.ServiceName}") de la ${provider?.FullName} așteaptă aprobare.`,
          Link: `/admin/services`,
          Type: 'ServiceForReview',
        }
      });
    }

    return res.status(201).json({ service: newService, message: 'Serviciu creat cu succes și trimis pentru aprobare.' });
  } catch (error) {
    console.error('[API /provider/services POST] Eroare:', error);
    return res.status(500).json({ message: error instanceof Error ? error.message : 'Eroare la crearea serviciului.' });
  }
});


// GET specific service by ID
router.get('/services/:serviceId', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const serviceIdNum = parseInt(req.params.serviceId, 10);
    if (isNaN(serviceIdNum)) {
      return res.status(400).json({ message: 'Invalid service ID' });
    }

    const service = await prisma.advertisedService.findUnique({
      where: { Id: serviceIdNum }, 
      include: { 
        NannyServiceDetails: true, ElderCareServiceDetails: true, CleaningServiceDetails: true, 
        TutoringServiceDetails: true, CookingServiceDetails: true,
        Category: { select: { NameKey: true, Slug: true, Id: true }}
      },
    });

    if (!service) {
      return res.status(404).json({ message: 'Service not found' });
    }
    
    return res.json({ service: service });
  } catch (error) {
    console.error(`[API /provider/services/:serviceId GET] Error:`, error);
    return res.status(500).json({ message: 'Failed to fetch service' });
  }
});

// PUT update specific service by ID
router.put('/services/:serviceId', async (req: AuthenticatedRequest, res: Response) => {
   if (!req.user?.id) {
    return res.status(401).json({ message: 'Neautorizat: ID-ul prestatorului lipsește din sesiune.' });
  }
  const providerId = parseInt(req.user.id, 10);
  const serviceId = parseInt(req.params.serviceId, 10);
  if (isNaN(serviceId)) {
    return res.status(400).json({ message: 'ID serviciu invalid.' });
  }

  try {
    const body = req.body as Partial<AdvertisedServicePayload>;
    const { ServiceName, Description, ServiceCategorySlug, Status, ...details } = body;
    
    const serviceToUpdate = await prisma.advertisedService.findFirst({
        where: { Id: serviceId, ProviderId: providerId }
    });
    if(!serviceToUpdate) {
        return res.status(404).json({ message: 'Serviciul nu a fost găsit sau nu aveți permisiunea de a-l modifica.' });
    }

    let updatedService;
    await prisma.$transaction(async (tx) => {
        updatedService = await tx.advertisedService.update({
            where: { Id: serviceId },
            data: {
                ServiceName, Description, 
                // Any modification by a provider resets the status to PendingReview
                Status: 'PendingReview',
            }
        });

        const detailData = { ...details[`${serviceToUpdate.ServiceCategorySlug}Details` as keyof typeof details] as any };
        
        const updateDetails = async (model: any, data: any) => {
            const existing = await model.findUnique({ where: { AdvertisedServiceId: serviceId }});
            if (existing) {
                await model.update({ where: { AdvertisedServiceId: serviceId }, data });
            } else {
                await model.create({ data: { AdvertisedServiceId: serviceId, ...data } });
            }
        };

        switch (serviceToUpdate.ServiceCategorySlug) {
            case 'Nanny': await updateDetails(tx.nannyServiceDetails, detailData); break;
            case 'ElderCare': await updateDetails(tx.elderCareServiceDetails, detailData); break;
            case 'Cleaning': await updateDetails(tx.cleaningServiceDetails, detailData); break;
            case 'Tutoring': await updateDetails(tx.tutoringServiceDetails, detailData); break;
            case 'Cooking': await updateDetails(tx.cookingServiceDetails, detailData); break;
            default: throw new Error(`Tip de serviciu necunoscut: ${serviceToUpdate.ServiceCategorySlug}`);
        }
        
        // Notify Admins about the modification needing review
        const admins = await tx.user.findMany({ where: { Roles: { some: { Name: UserRole.Admin } } } });
        const provider = await tx.user.findUnique({ where: { Id: providerId }, select: { FullName: true } });
        for (const admin of admins) {
          await tx.notification.create({
            data: {
              UserId: admin.Id,
              Message: `Serviciul "${updatedService.ServiceName}" de la ${provider?.FullName} a fost modificat și așteaptă aprobare.`,
              Link: `/admin/services`,
              Type: 'ServiceForReview',
            }
          });
        }
    });

    return res.json({ service: updatedService, message: 'Serviciu actualizat și trimis pentru aprobare.' });
  } catch (error) {
    console.error(`[API /provider/services/:serviceId PUT] Eroare:`, error);
    return res.status(500).json({ message: error instanceof Error ? error.message : 'Eroare la actualizarea serviciului.' });
  }
});

// DELETE specific service by ID
router.delete('/services/:serviceId', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const serviceIdNum = parseInt(req.params.serviceId, 10);
    if (isNaN(serviceIdNum)) {
      return res.status(400).json({ message: 'Invalid service ID' });
    }

    // This is a placeholder. A real implementation would check for user ownership
    // and handle related data (bookings, etc.) gracefully.
    await prisma.advertisedService.delete({ where: { Id: serviceIdNum }});

    return res.json({ message: 'Serviciul a fost șters cu succes.' });
  } catch (error) {
    console.error(`[API /provider/services/:serviceId DELETE] Error:`, error);
    return res.status(500).json({ message: 'Failed to delete service' });
  }
});

export default router;
