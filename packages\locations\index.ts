
export interface LocationEntry {
  key: string; // Unique key for React iteration, e.g., "chisinau_botanica"
  value: string; // Value for API query/URL param, e.g., "chisinau-botanica" or "chisinau"
  translationKey: string; // For useLanguage hook, e.g., "locChisinauBotanica"
  displayRo: string; // Full Romanian name for display/storage, e.g., "Chișinău, Botanica" or "Chișinău"
}

// Values for Chisinau's direct suburbs/communes
export const chisinauDirectSuburbValues = [
  'codru', 'cricova', 'durlesti', 'singera', 'stauceni', 
  'vadul-lui-voda', 'vatra', 'bacioi', 'bubuieci', 'gratiesti', 'truseni', 'ghidighici'
];

// Values for Chisinau's city sectors
export const chisinauCitySectorValues = [
  'chisinau-botanica', 'chisinau-buiucani', 'chisinau-centru', 
  'chisinau-ciocana', 'chisinau-rascani', 'chisinau-telecentru'
];


export const allMoldovaLocations: LocationEntry[] = [
  { key: "all", value: "all", translationKey: "locAllMoldova", displayRo: "Toată Moldova" },
  
  // This is the SELECTABLE item for the entire municipality
  { key: "chisinau_municipality_scope", value: "chisinau-municipality-all", translationKey: "locChisinauMunicipalityScope", displayRo: "Municipiul Chișinău" },

  // Chisinau City Overall entry - This is the parent entry for the city sectors
  { key: "chisinau_city_overall", value: "chisinau", translationKey: "locChisinauCity", displayRo: "Chișinău" },
  
  // Chisinau City Sectors
  { key: "chisinau_botanica", value: "chisinau-botanica", translationKey: "locChisinauBotanica", displayRo: "Botanica" },
  { key: "chisinau_buiucani", value: "chisinau-buiucani", translationKey: "locChisinauBuiucani", displayRo: "Buiucani" },
  { key: "chisinau_centru", value: "chisinau-centru", translationKey: "locChisinauCentru", displayRo: "Centru" },
  { key: "chisinau_ciocana", value: "chisinau-ciocana", translationKey: "locChisinauCiocana", displayRo: "Ciocana" },
  { key: "chisinau_rascani", value: "chisinau-rascani", translationKey: "locChisinauRascani", displayRo: "Râșcani" },
  { key: "chisinau_telecentru", value: "chisinau-telecentru", translationKey: "locChisinauTelecentru", displayRo: "Telecentru" },

  // Chisinau Municipality - Suburbs/Towns/Communes (Directly part of the Municipality)
  { key: "codru", value: "codru", translationKey: "locCodru", displayRo: "Codru" },
  { key: "cricova", value: "cricova", translationKey: "locCricova", displayRo: "Cricova" },
  { key: "durlesti", value: "durlesti", translationKey: "locDurlesti", displayRo: "Durlești" },
  { key: "ghidighici", value: "ghidighici", translationKey: "locGhidighici", displayRo: "Ghidighici" },
  { key: "singera", value: "singera", translationKey: "locSingera", displayRo: "Sîngera" },
  { key: "stauceni", value: "stauceni", translationKey: "locStauceni", displayRo: "Stăuceni" },
  { key: "vadul_lui_voda", value: "vadul-lui-voda", translationKey: "locVadulLuiVoda", displayRo: "Vadul lui Vodă" },
  { key: "vatra_loc", value: "vatra", translationKey: "locVatra", displayRo: "Vatra" }, 
  { key: "bacioi", value: "bacioi", translationKey: "locBacioi", displayRo: "Băcioi" },
  { key: "bubuieci", value: "bubuieci", translationKey: "locBubuieci", displayRo: "Bubuieci" },
  { key: "gratiesti", value: "gratiesti", translationKey: "locGratiesti", displayRo: "Grătiești" },
  { key: "truseni", value: "truseni", translationKey: "locTruseni", displayRo: "Trușeni" },
  
  // Other Major Cities/Towns
  { key: "balti", value: "balti", translationKey: "locBalti", displayRo: "Bălți" },
  { key: "tiraspol", value: "tiraspol", translationKey: "locTiraspol", displayRo: "Tiraspol" },
  { key: "bender", value: "bender", translationKey: "locBender", displayRo: "Bender (Tighina)" },
  { key: "ribnita", value: "ribnita", translationKey: "locRibnita", displayRo: "Rîbnița" },
  { key: "cahul", value: "cahul", translationKey: "locCahul", displayRo: "Cahul" },
  { key: "ungheni", value: "ungheni", translationKey: "locUngheni", displayRo: "Ungheni" },
  { key: "soroca", value: "soroca", translationKey: "locSoroca", displayRo: "Soroca" },
  { key: "orhei", value: "orhei", translationKey: "locOrhei", displayRo: "Orhei" },
  { key: "comrat", value: "comrat", translationKey: "locComrat", displayRo: "Comrat" },
  { key: "dubasari", value: "dubasari", translationKey: "locDubasari", displayRo: "Dubăsari" },
  { key: "straseni", value: "straseni", translationKey: "locStraseni", displayRo: "Strășeni" },
  { key: "causeni", value: "causeni", translationKey: "locCauseni", displayRo: "Căușeni" },
  { key: "drochia", value: "drochia", translationKey: "locDrochia", displayRo: "Drochia" },
  { key: "edinet", value: "edinet", translationKey: "locEdinet", displayRo: "Edineț" },
  { key: "hincesti", value: "hincesti", translationKey: "locHincesti", displayRo: "Hîncești" },
  { key: "ialoveni", value: "ialoveni", translationKey: "locIaloveni", displayRo: "Ialoveni" },
  { key: "floresti", value: "floresti", translationKey: "locFloresti", displayRo: "Florești" },
  { key: "cimislia", value: "cimislia", translationKey: "locCimislia", displayRo: "Cimișlia" },
  { key: "rezina", value: "rezina", translationKey: "locRezina", displayRo: "Rezina" },
  { key: "anenii_noi", value: "anenii-noi", translationKey: "locAneniiNoi", displayRo: "Anenii Noi" },
  { key: "basarabeasca", value: "basarabeasca", translationKey: "locBasarabeasca", displayRo: "Basarabeasca" },
  { key: "briceni", value: "briceni", translationKey: "locBriceni", displayRo: "Briceni" },
  { key: "cantemir", value: "cantemir", translationKey: "locCantemir", displayRo: "Cantemir" },
  { key: "calarasi", value: "calarasi", translationKey: "locCalarasi", displayRo: "Călărași" },
  { key: "ceadir_lunga", value: "ceadir-lunga", translationKey: "locCeadirLunga", displayRo: "Ceadîr-Lunga" },
  { key: "criuleni", value: "criuleni", translationKey: "locCriuleni", displayRo: "Criuleni" },
  { key: "donduseni", value: "donduseni", translationKey: "locDonduseni", displayRo: "Dondușeni" },
  { key: "falesti", value: "falesti", translationKey: "locFalesti", displayRo: "Fălești" },
  { key: "glodeni", value: "glodeni", translationKey: "locGlodeni", displayRo: "Glodeni" },
  { key: "leova", value: "leova", translationKey: "locLeova", displayRo: "Leova" },
  { key: "nisporeni", value: "nisporeni", translationKey: "locNisporeni", displayRo: "Nisporeni" },
  { key: "ocnita", value: "ocnita", translationKey: "locOcnita", displayRo: "Ocnița" },
  { key: "singerei", value: "singerei", translationKey: "locSingerei", displayRo: "Sîngerei" },
  { key: "soldanesti", value: "soldanesti", translationKey: "locSoldanesti", displayRo: "Șoldănești" },
  { key: "stefan_voda", value: "stefan-voda", translationKey: "locStefanVoda", displayRo: "Ștefan Vodă" },
  { key: "taraclia", value: "taraclia", translationKey: "locTaraclia", displayRo: "Taraclia" },
  { key: "telenesti", value: "telenesti", translationKey: "locTelenesti", displayRo: "Telenești" },
  { key: "vulcanesti", value: "vulcanesti", translationKey: "locVulcanesti", displayRo: "Vulcănești" },
];

export const getFullLocationPathDisplayHelper = (
  value: string,
  translateFn: (translations: Record<string, Record<string, string>>, key: string, fallback?: string) => string,
  commonTranslationsObj: Record<string, Record<string, string>> // Pass commonTranslations here
): string => {
  const locationEntry = allMoldovaLocations.find(loc => loc.value === value);
  if (locationEntry) {
    let name = translateFn(commonTranslationsObj, locationEntry.translationKey as keyof typeof commonTranslationsObj);
    
    if (value === 'chisinau-municipality-all' || value === 'chisinau' || value === 'all') {
      return name; 
    }
    if (chisinauDirectSuburbValues.includes(value)) {
      const municipalityName = translateFn(commonTranslationsObj, 'locChisinauMunicipalityScope' as keyof typeof commonTranslationsObj);
      return `${municipalityName}, ${name}`;
    } 
    else if (chisinauCitySectorValues.includes(value)) {
      const cityName = translateFn(commonTranslationsObj, 'locChisinauCity' as keyof typeof commonTranslationsObj);
      return `${cityName}, ${name}`;
    }
    return name;
  }
  // Fallback for unknown values or if "all" is passed but somehow not in allMoldovaLocations
  // Or if value is empty/undefined (though Select should handle placeholder)
  if (value === "all") { // Explicitly handle "all" if it somehow misses the find
    return translateFn(commonTranslationsObj, 'locAllMoldova' as keyof typeof commonTranslationsObj);
  }
  return value; 
};

