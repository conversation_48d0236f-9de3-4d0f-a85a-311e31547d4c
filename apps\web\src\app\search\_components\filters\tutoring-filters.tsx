"use client";

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useLanguage } from '@/contexts/language-context';
import { BooleanFilterGroup, FilterBadges, getActiveFilters } from './filter-components';

const tutoringFilterTranslations = {
  serviceTypes: { ro: "Tipuri Servicii", ru: "Типы услуг", en: "Service Types" },
  afterSchool: { ro: "După școală", ru: "После школы", en: "After School" },
  homeworkHelp: { ro: "Ajutor la teme", ru: "Помощь с домашним заданием", en: "Homework Help" },
  individualLessons: { ro: "Lecții individuale", ru: "Индивидуальные уроки", en: "Individual Lessons" },

  gradeLevels: { ro: "<PERSON>vele Clase", ru: "Уровни классов", en: "Grade Levels" },
  grades1to4: { ro: "Clasele 1-4", ru: "Классы 1-4", en: "Grades 1-4" },
  grades5to8: { ro: "Clasele 5-8", ru: "Классы 5-8", en: "Grades 5-8" },
  grades9to12: { ro: "Clasele 9-12", ru: "Классы 9-12", en: "Grades 9-12" },

  subjects: { ro: "Materii", ru: "Предметы", en: "Subjects" },
  romanian: { ro: "Română", ru: "Румынский", en: "Romanian" },
  math: { ro: "Matematică", ru: "Математика", en: "Mathematics" },
  english: { ro: "Engleză", ru: "Английский", en: "English" },

  formats: { ro: "Formate", ru: "Форматы", en: "Formats" },
  online: { ro: "Online", ru: "Онлайн", en: "Online" },
  tutorHome: { ro: "La profesorul acasă", ru: "Дома у преподавателя", en: "At Tutor's Home" },
  childHome: { ro: "La copilul acasă", ru: "Дома у ребенка", en: "At Child's Home" },

  extraServices: { ro: "Servicii Extra", ru: "Дополнительные услуги", en: "Extra Services" },
  games: { ro: "Jocuri educative", ru: "Образовательные игры", en: "Educational Games" },
  snack: { ro: "Gustare", ru: "Перекус", en: "Snack" },
  transport: { ro: "Transport", ru: "Транспорт", en: "Transport" },
  supervisedHomework: { ro: "Teme supravegheate", ru: "Контролируемые домашние задания", en: "Supervised Homework" },
};

interface TutoringFiltersState {
  // Service types
  ServiceAfterSchool: boolean;
  ServiceHomeworkHelp: boolean;
  ServiceIndividualLessons: boolean;

  // Grade levels
  Grades_1_4: boolean;
  Grades_5_8: boolean;
  Grades_9_12: boolean;

  // Subjects
  SubjectRomanian: boolean;
  SubjectMath: boolean;
  SubjectEnglish: boolean;

  // Formats
  FormatOnline: boolean;
  FormatOwnHome: boolean;
  FormatChildHome: boolean;

  // Extra services
  ExtraGames: boolean;
  ExtraSnack: boolean;
  ExtraTransport: boolean;
  ExtraSupervisedHomework: boolean;
}

export function TutoringFilters() {
  const { translate } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<TutoringFiltersState>({
    ServiceAfterSchool: searchParams.get('ServiceAfterSchool') === 'true',
    ServiceHomeworkHelp: searchParams.get('ServiceHomeworkHelp') === 'true',
    ServiceIndividualLessons: searchParams.get('ServiceIndividualLessons') === 'true',
    Grades_1_4: searchParams.get('Grades_1_4') === 'true',
    Grades_5_8: searchParams.get('Grades_5_8') === 'true',
    Grades_9_12: searchParams.get('Grades_9_12') === 'true',
    SubjectRomanian: searchParams.get('SubjectRomanian') === 'true',
    SubjectMath: searchParams.get('SubjectMath') === 'true',
    SubjectEnglish: searchParams.get('SubjectEnglish') === 'true',
    FormatOnline: searchParams.get('FormatOnline') === 'true',
    FormatOwnHome: searchParams.get('FormatOwnHome') === 'true',
    FormatChildHome: searchParams.get('FormatChildHome') === 'true',
    ExtraGames: searchParams.get('ExtraGames') === 'true',
    ExtraSnack: searchParams.get('ExtraSnack') === 'true',
    ExtraTransport: searchParams.get('ExtraTransport') === 'true',
    ExtraSupervisedHomework: searchParams.get('ExtraSupervisedHomework') === 'true',
  });

  // Update URL when filters change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams.toString());

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, 'true');
      } else {
        newParams.delete(key);
      }
    });

    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  }, [filters, router, pathname, searchParams]);

  const handleFilterChange = (key: string, checked: boolean) => {
    setFilters(prev => ({ ...prev, [key]: checked }));
  };

  const handleClearAll = () => {
    setFilters({
      ServiceAfterSchool: false,
      ServiceHomeworkHelp: false,
      ServiceIndividualLessons: false,
      Grades_1_4: false,
      Grades_5_8: false,
      Grades_9_12: false,
      SubjectRomanian: false,
      SubjectMath: false,
      SubjectEnglish: false,
      FormatOnline: false,
      FormatOwnHome: false,
      FormatChildHome: false,
      ExtraGames: false,
      ExtraSnack: false,
      ExtraTransport: false,
      ExtraSupervisedHomework: false,
    });
  };

  const handleRemoveFilter = (key: string) => {
    setFilters(prev => ({ ...prev, [key]: false }));
  };

  // Create filter labels
  const filterLabels = {
    ServiceAfterSchool: translate(tutoringFilterTranslations, 'afterSchool'),
    ServiceHomeworkHelp: translate(tutoringFilterTranslations, 'homeworkHelp'),
    ServiceIndividualLessons: translate(tutoringFilterTranslations, 'individualLessons'),
    Grades_1_4: translate(tutoringFilterTranslations, 'grades1to4'),
    Grades_5_8: translate(tutoringFilterTranslations, 'grades5to8'),
    Grades_9_12: translate(tutoringFilterTranslations, 'grades9to12'),
    SubjectRomanian: translate(tutoringFilterTranslations, 'romanian'),
    SubjectMath: translate(tutoringFilterTranslations, 'math'),
    SubjectEnglish: translate(tutoringFilterTranslations, 'english'),
    FormatOnline: translate(tutoringFilterTranslations, 'online'),
    FormatOwnHome: translate(tutoringFilterTranslations, 'tutorHome'),
    FormatChildHome: translate(tutoringFilterTranslations, 'childHome'),
    ExtraGames: translate(tutoringFilterTranslations, 'games'),
    ExtraSnack: translate(tutoringFilterTranslations, 'snack'),
    ExtraTransport: translate(tutoringFilterTranslations, 'transport'),
    ExtraSupervisedHomework: translate(tutoringFilterTranslations, 'supervisedHomework'),
  };

  // Get active filters for badges
  const activeFilters = getActiveFilters(filters, filterLabels, 'tutoring');

  return (
    <div className="space-y-4">
      <FilterBadges
        activeFilters={activeFilters}
        onRemove={handleRemoveFilter}
        onClearAll={handleClearAll}
      />

      <BooleanFilterGroup
        title={translate(tutoringFilterTranslations, 'serviceTypes')}
        options={[
          { key: 'ServiceAfterSchool', label: filterLabels.ServiceAfterSchool, checked: filters.ServiceAfterSchool },
          { key: 'ServiceHomeworkHelp', label: filterLabels.ServiceHomeworkHelp, checked: filters.ServiceHomeworkHelp },
          { key: 'ServiceIndividualLessons', label: filterLabels.ServiceIndividualLessons, checked: filters.ServiceIndividualLessons },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(tutoringFilterTranslations, 'gradeLevels')}
        options={[
          { key: 'Grades_1_4', label: filterLabels.Grades_1_4, checked: filters.Grades_1_4 },
          { key: 'Grades_5_8', label: filterLabels.Grades_5_8, checked: filters.Grades_5_8 },
          { key: 'Grades_9_12', label: filterLabels.Grades_9_12, checked: filters.Grades_9_12 },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(tutoringFilterTranslations, 'subjects')}
        options={[
          { key: 'SubjectRomanian', label: filterLabels.SubjectRomanian, checked: filters.SubjectRomanian },
          { key: 'SubjectMath', label: filterLabels.SubjectMath, checked: filters.SubjectMath },
          { key: 'SubjectEnglish', label: filterLabels.SubjectEnglish, checked: filters.SubjectEnglish },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(tutoringFilterTranslations, 'formats')}
        options={[
          { key: 'FormatOnline', label: filterLabels.FormatOnline, checked: filters.FormatOnline },
          { key: 'FormatOwnHome', label: filterLabels.FormatOwnHome, checked: filters.FormatOwnHome },
          { key: 'FormatChildHome', label: filterLabels.FormatChildHome, checked: filters.FormatChildHome },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(tutoringFilterTranslations, 'extraServices')}
        options={[
          { key: 'ExtraGames', label: filterLabels.ExtraGames, checked: filters.ExtraGames },
          { key: 'ExtraSnack', label: filterLabels.ExtraSnack, checked: filters.ExtraSnack },
          { key: 'ExtraTransport', label: filterLabels.ExtraTransport, checked: filters.ExtraTransport },
          { key: 'ExtraSupervisedHomework', label: filterLabels.ExtraSupervisedHomework, checked: filters.ExtraSupervisedHomework },
        ]}
        onChange={handleFilterChange}
      />
    </div>
  );
}
