import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_USERS = {
  clientOnly: {
    email: '<EMAIL>',
    password: 'testpass123',
    isProvider: false,
  },
  dualRole: {
    email: '<EMAIL>', 
    password: 'testpass123',
    isProvider: true,
  },
  providerOnly: {
    email: '<EMAIL>',
    password: 'testpass123',
    isProvider: true,
  },
};

// Helper functions
async function loginUser(page: Page, user: typeof TEST_USERS.clientOnly) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('[data-testid="login-button"]');
  await page.waitForURL('/dashboard/**');
}

async function waitForRoleToggle(page: Page) {
  await page.waitForSelector('[role="tablist"]', { timeout: 5000 });
}

// Test Suite: Role Toggle Functionality
test.describe('Role Toggle Functionality', () => {
  
  test.describe('Client-Only Users', () => {
    test('should not see role toggle for client-only users', async ({ page }) => {
      await loginUser(page, TEST_USERS.clientOnly);
      
      // Should not see role toggle
      const roleToggle = page.locator('[role="tablist"]');
      await expect(roleToggle).not.toBeVisible();
      
      // Should see client badge instead
      const clientBadge = page.locator('text=Client');
      await expect(clientBadge).toBeVisible();
      
      // Should be on client dashboard
      await expect(page).toHaveURL(/\/dashboard\/client/);
    });

    test('should redirect legacy URLs to client routes', async ({ page }) => {
      await loginUser(page, TEST_USERS.clientOnly);
      
      // Test legacy URL redirects
      const legacyUrls = [
        '/dashboard/bookings',
        '/dashboard/chat',
        '/dashboard/settings',
      ];
      
      for (const url of legacyUrls) {
        await page.goto(url);
        await expect(page).toHaveURL(new RegExp(`/dashboard/client`));
      }
    });
  });

  test.describe('Dual-Role Users', () => {
    test('should display role toggle for dual-role users', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Should see role toggle
      const roleToggle = page.locator('[role="tablist"]');
      await expect(roleToggle).toBeVisible();
      
      // Should see both client and provider buttons
      const clientButton = page.locator('[role="tab"][aria-selected="true"]');
      const providerButton = page.locator('[role="tab"][aria-selected="false"]');
      
      await expect(clientButton).toContainText('Client');
      await expect(providerButton).toContainText('Provider');
    });

    test('should switch roles successfully', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Start on client role
      await expect(page).toHaveURL(/\/dashboard\/client/);
      
      // Switch to provider role
      const providerButton = page.locator('[role="tab"]:has-text("Provider")');
      await providerButton.click();
      
      // Should navigate to provider dashboard
      await expect(page).toHaveURL(/\/dashboard\/provider/);
      
      // Should update role indicator
      const roleIndicator = page.locator('[data-testid="role-indicator"]');
      await expect(roleIndicator).toContainText('Provider');
      
      // Switch back to client
      const clientButton = page.locator('[role="tab"]:has-text("Client")');
      await clientButton.click();
      
      // Should navigate back to client dashboard
      await expect(page).toHaveURL(/\/dashboard\/client/);
    });

    test('should preserve page context when switching roles', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      
      // Navigate to client messages
      await page.goto('/dashboard/client/messages');
      await expect(page).toHaveURL('/dashboard/client/messages');
      
      // Switch to provider role
      await waitForRoleToggle(page);
      const providerButton = page.locator('[role="tab"]:has-text("Provider")');
      await providerButton.click();
      
      // Should navigate to provider messages (preserving context)
      await expect(page).toHaveURL('/dashboard/provider/messages');
    });

    test('should update navigation menu based on role', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Check client navigation
      const clientNavigation = [
        'Dashboard',
        'My Bookings', 
        'Messages',
        'Settings',
        'Addresses'
      ];
      
      for (const item of clientNavigation) {
        const navItem = page.locator(`nav a:has-text("${item}")`);
        await expect(navItem).toBeVisible();
      }
      
      // Switch to provider role
      const providerButton = page.locator('[role="tab"]:has-text("Provider")');
      await providerButton.click();
      await page.waitForURL(/\/dashboard\/provider/);
      
      // Check provider navigation
      const providerNavigation = [
        'Dashboard',
        'Calendar',
        'Services',
        'Messages',
        'Settings'
      ];
      
      for (const item of providerNavigation) {
        const navItem = page.locator(`nav a:has-text("${item}")`);
        await expect(navItem).toBeVisible();
      }
      
      // Client-specific items should not be visible
      const addressesItem = page.locator('nav a:has-text("Addresses")');
      await expect(addressesItem).not.toBeVisible();
    });
  });

  test.describe('Keyboard Navigation', () => {
    test('should support keyboard navigation for role toggle', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Focus on role toggle
      const roleToggle = page.locator('[role="tablist"]');
      await roleToggle.focus();
      
      // Use arrow keys to navigate
      await page.keyboard.press('ArrowRight');
      
      // Should focus provider tab
      const providerTab = page.locator('[role="tab"]:has-text("Provider")');
      await expect(providerTab).toBeFocused();
      
      // Press Enter to activate
      await page.keyboard.press('Enter');
      
      // Should switch to provider role
      await expect(page).toHaveURL(/\/dashboard\/provider/);
    });

    test('should support tab navigation through interface', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Tab through the interface
      await page.keyboard.press('Tab'); // Skip to main content
      await page.keyboard.press('Tab'); // Role toggle
      await page.keyboard.press('Tab'); // First navigation item
      
      // Should focus on first navigation item
      const firstNavItem = page.locator('nav a').first();
      await expect(firstNavItem).toBeFocused();
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should work on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await loginUser(page, TEST_USERS.dualRole);
      
      // Role toggle should be hidden on mobile in header
      const headerRoleToggle = page.locator('header [role="tablist"]');
      await expect(headerRoleToggle).not.toBeVisible();
      
      // Should have mobile navigation
      const mobileNav = page.locator('[data-testid="mobile-nav"]');
      await expect(mobileNav).toBeVisible();
    });

    test('should handle touch interactions', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginUser(page, TEST_USERS.dualRole);
      
      // Test touch interactions with role switching
      // This would require more specific mobile testing setup
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Simulate network failure
      await page.route('**/dashboard/provider**', route => route.abort());
      
      // Try to switch roles
      const providerButton = page.locator('[role="tab"]:has-text("Provider")');
      await providerButton.click();
      
      // Should show error state or retry mechanism
      // Implementation depends on error handling strategy
    });

    test('should handle permission errors', async ({ page }) => {
      // Test scenario where user loses provider permissions
      // This would require backend simulation
    });
  });

  test.describe('Loading States', () => {
    test('should show loading states during role switching', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Slow down network to see loading states
      await page.route('**/dashboard/provider**', route => {
        setTimeout(() => route.continue(), 1000);
      });
      
      const providerButton = page.locator('[role="tab"]:has-text("Provider")');
      await providerButton.click();
      
      // Should show loading indicator
      const loadingIndicator = page.locator('[data-testid="loading-indicator"]');
      await expect(loadingIndicator).toBeVisible();
      
      // Should hide loading indicator after completion
      await page.waitForURL(/\/dashboard\/provider/);
      await expect(loadingIndicator).not.toBeVisible();
    });
  });

  test.describe('Analytics Tracking', () => {
    test('should track role switching events', async ({ page }) => {
      // Mock analytics endpoint
      let analyticsEvents: any[] = [];
      await page.route('**/api/analytics/events', route => {
        analyticsEvents.push(route.request().postDataJSON());
        route.fulfill({ status: 200, body: '{}' });
      });
      
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Switch roles
      const providerButton = page.locator('[role="tab"]:has-text("Provider")');
      await providerButton.click();
      await page.waitForURL(/\/dashboard\/provider/);
      
      // Should have tracked the role switch
      expect(analyticsEvents).toContainEqual(
        expect.objectContaining({
          event_type: 'role_switch',
          event_data: expect.objectContaining({
            from_role: 'client',
            to_role: 'provider',
            method: 'header_toggle'
          })
        })
      );
    });
  });

  test.describe('Accessibility', () => {
    test('should meet WCAG 2.1 AA standards', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Check for proper ARIA attributes
      const roleToggle = page.locator('[role="tablist"]');
      await expect(roleToggle).toHaveAttribute('aria-label');
      
      const tabs = page.locator('[role="tab"]');
      for (let i = 0; i < await tabs.count(); i++) {
        const tab = tabs.nth(i);
        await expect(tab).toHaveAttribute('aria-selected');
        await expect(tab).toHaveAttribute('aria-controls');
      }
    });

    test('should work with screen readers', async ({ page }) => {
      // This would require screen reader testing tools
      // For now, we check for proper semantic markup
      
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      // Check for live regions
      const liveRegion = page.locator('[aria-live]');
      await expect(liveRegion).toBeAttached();
      
      // Check for proper headings hierarchy
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      expect(await headings.count()).toBeGreaterThan(0);
    });
  });

  test.describe('Performance', () => {
    test('should load quickly', async ({ page }) => {
      const startTime = Date.now();
      
      await loginUser(page, TEST_USERS.dualRole);
      await waitForRoleToggle(page);
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(3000); // Should load in under 3 seconds
    });

    test('should not cause layout shifts', async ({ page }) => {
      await loginUser(page, TEST_USERS.dualRole);
      
      // Monitor for layout shifts
      const cls = await page.evaluate(() => {
        return new Promise((resolve) => {
          let clsValue = 0;
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            });
            setTimeout(() => resolve(clsValue), 2000);
          });
          observer.observe({ entryTypes: ['layout-shift'] });
        });
      });
      
      expect(cls).toBeLessThan(0.1); // CLS should be less than 0.1
    });
  });
});

// Test Suite: Backward Compatibility
test.describe('Backward Compatibility', () => {
  test('should redirect all legacy URLs correctly', async ({ page }) => {
    await loginUser(page, TEST_USERS.dualRole);
    
    const redirectTests = [
      { from: '/dashboard', to: '/dashboard/client' },
      { from: '/dashboard/bookings', to: '/dashboard/client/bookings' },
      { from: '/dashboard/chat', to: '/dashboard/client/messages' },
      { from: '/dashboard/settings', to: '/dashboard/client/settings' },
      { from: '/dashboard/settings/address', to: '/dashboard/client/addresses' },
    ];
    
    for (const { from, to } of redirectTests) {
      await page.goto(from);
      await expect(page).toHaveURL(new RegExp(to.replace(/\//g, '\\/')));
    }
  });

  test('should handle bookmarked URLs', async ({ page }) => {
    // Test that users can bookmark and return to specific role pages
    await loginUser(page, TEST_USERS.dualRole);
    
    // Navigate to provider calendar
    await page.goto('/dashboard/provider/calendar');
    await expect(page).toHaveURL('/dashboard/provider/calendar');
    
    // Refresh page (simulating bookmark return)
    await page.reload();
    await expect(page).toHaveURL('/dashboard/provider/calendar');
    
    // Should still be in provider role
    const roleIndicator = page.locator('[data-testid="role-indicator"]');
    await expect(roleIndicator).toContainText('Provider');
  });
});
