
"use client";
import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Eye, PlusCircle, Search, ShieldAlert, FileText, Info, ExternalLink, Hourglass, CheckCircle } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { useLanguage } from '@/contexts/language-context';
import { clientDashboardTranslations } from '@/lib/translations/dashboard';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2 } from 'lucide-react';
import { commonTranslations } from '@repo/translations';

interface UserDisplayData {
  id: number | string;
  fullName: string | null;
  isProvider: boolean;
  isAdmin: boolean;
  CreatedAt: string;
}

interface ClientStats {
  recentBookingsCount: number;
  pendingReviewsCount: number;
}

interface SimpleRequestInfo {
  Status: 'Pending' | 'Approved' | 'Rejected';
  AdminNotes: string | null;
}

type RequestFetchStatus = 'idle' | 'loading' | 'error' | 'success';

export default function ClientDashboardPage() {
  const { translate } = useLanguage();
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  // Redirect to role-specific dashboard ONLY when on exact /dashboard route
  useEffect(() => {
    if (sessionStatus === "authenticated" && session?.user && pathname === '/dashboard') {
      const userFromSession = session.user as any;
      const defaultRole = userFromSession.isProvider ? 'provider' : 'client';
      router.replace(`/dashboard/${defaultRole}`);
    }
  }, [session, sessionStatus, router, pathname]);
  const [userDisplayData, setUserDisplayData] = useState<UserDisplayData | null>(null);

  const [clientStats, setClientStats] = useState<ClientStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [statsError, setStatsError] = useState<string | null>(null);

  const [providerRequestFetchStatus, setProviderRequestFetchStatus] = useState<RequestFetchStatus>('idle');
  const [providerRequestInfo, setProviderRequestInfo] = useState<SimpleRequestInfo | null>(null);

  useEffect(() => {
    if (sessionStatus === "authenticated" && session?.user) {
      const userFromSession = session.user as any;
      setUserDisplayData({
        id: userFromSession.id,
        fullName: userFromSession.name || null,
        isProvider: userFromSession.isProvider || false,
        isAdmin: userFromSession.isAdmin || false,
        CreatedAt: userFromSession.CreatedAt || new Date().toISOString(),
      });

      // Only fetch status if user is NOT a provider or admin
      if (!userFromSession.isProvider && !userFromSession.isAdmin) {
        const fetchProviderRequestStatus = async () => {
          setProviderRequestFetchStatus('loading');
          setProviderRequestInfo(null);
          try {
            const reqStatusRes = await fetch(`/api/proxy/provider-requests/my-status`);
            if (reqStatusRes.ok) {
              const reqStatusData = await reqStatusRes.json();
              setProviderRequestInfo(reqStatusData.request); // Can be null if no request
              setProviderRequestFetchStatus('success');
            } else {
              setProviderRequestInfo(null);
              setProviderRequestFetchStatus('error');
            }
          } catch (reqErr) {
            console.error("Dashboard: Error fetching provider request status:", reqErr);
            setProviderRequestFetchStatus('error');
          }
        };
        fetchProviderRequestStatus();
      }
    } else if (sessionStatus === "unauthenticated") {
      setUserDisplayData(null);
      setProviderRequestFetchStatus('idle');
      setProviderRequestInfo(null);
    }
  }, [session, sessionStatus, translate]);

  useEffect(() => {
    if (!userDisplayData || !userDisplayData.id || userDisplayData.isAdmin) return;

    const fetchClientStats = async () => {
      setIsLoadingStats(true);
      setStatsError(null);
      try {
        let userIdNum: number;
        if (typeof userDisplayData.id === 'string') {
          userIdNum = parseInt(userDisplayData.id, 10);
          if (isNaN(userIdNum)) throw new Error("Invalid user ID for stats");
        } else {
          userIdNum = userDisplayData.id;
        }

        const clientRes = await fetch(`/api/proxy/dashboard/client-stats?clientId=${userIdNum}`);
        if (!clientRes.ok) throw new Error(translate(clientDashboardTranslations, 'errorFetchingStats'));
        const clientData = await clientRes.json();
        setClientStats(clientData);

      } catch (err) {
        setStatsError(err instanceof Error ? err.message : String(err));
      } finally {
        setIsLoadingStats(false);
      }
    };
    fetchClientStats();
  }, [userDisplayData, translate]);

  const displayedName = userDisplayData?.fullName || 'Utilizator';
  const welcomeMessage = translate(clientDashboardTranslations, 'welcomeMessage').replace('{name}', displayedName);
  const memberSinceDate = userDisplayData?.CreatedAt ? new Date(userDisplayData.CreatedAt).toLocaleDateString('ro-RO', { year: 'numeric', month: 'long' }) : 'N/A';
  const translatedMemberSince = translate(clientDashboardTranslations, 'memberSince').replace('{date}', memberSinceDate);

  const translatedViewBookingsDesc = clientStats ? translate(clientDashboardTranslations, 'viewBookingsCardDesc').replace('{count}', String(clientStats.recentBookingsCount)) : translate(clientDashboardTranslations, 'viewBookingsCardDesc').replace('{count}', '0');
  const translatedAddReviewDesc = clientStats ? translate(clientDashboardTranslations, 'addReviewCardDesc').replace('{count}', String(clientStats.pendingReviewsCount)) : translate(clientDashboardTranslations, 'addReviewCardDesc').replace('{count}', '0');

  if (sessionStatus === "loading") {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="w-12 h-12 animate-spin text-primary" />
        <p className="ml-4 text-lg">{translate(clientDashboardTranslations, 'loadingUserData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-headline">{welcomeMessage}</CardTitle>
          <CardDescription>{translate(clientDashboardTranslations, 'dashboardDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{translatedMemberSince}</p>
        </CardContent>
      </Card>

      {/* Provider Status Section - Moved to top */}
      {!userDisplayData?.isAdmin && (
        <>
          {/* Show provider dashboard link for existing providers */}
          {userDisplayData?.isProvider && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="text-xl font-headline flex items-center text-green-700">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  {translate(clientDashboardTranslations, 'providerStatusActiveTitle')}
                </CardTitle>
                <CardDescription className="text-green-600">
                  {translate(clientDashboardTranslations, 'providerStatusActiveDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full sm:w-auto">
                  <Link href="/dashboard/provider" className="flex items-center">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    {translate(clientDashboardTranslations, 'goToProviderDashboard')}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Show provider registration process for non-providers */}
          {!userDisplayData?.isProvider && (
            <div className="space-y-4">
              {providerRequestFetchStatus === 'loading' && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="pt-6">
                    <div className="flex justify-center items-center py-4">
                      <Loader2 className="w-6 h-6 animate-spin text-primary" />
                      <p className="ml-2 text-muted-foreground">{translate(clientDashboardTranslations, 'checkingRequestStatus')}</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {providerRequestFetchStatus === 'error' && (
                <Alert variant="destructive">
                  <ShieldAlert className="h-5 w-5" />
                  <AlertTitle>{translate(clientDashboardTranslations, 'requestStatusError')}</AlertTitle>
                  <AlertDescription>{translate(clientDashboardTranslations, 'requestStatusErrorDesc')}</AlertDescription>
                </Alert>
              )}

              {providerRequestFetchStatus === 'success' && (
                <>
                  {providerRequestInfo?.Status === 'Pending' && (
                    <Card className="border-blue-200 bg-blue-50">
                      <CardHeader>
                        <CardTitle className="text-xl font-headline flex items-center text-blue-700">
                          <Hourglass className="w-5 h-5 mr-2" />
                          {translate(clientDashboardTranslations, 'requestStatusPendingTitle')}
                        </CardTitle>
                        <CardDescription className="text-blue-600">
                          {translate(clientDashboardTranslations, 'requestStatusPendingDescription')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Button asChild variant="outline" className="w-full sm:w-auto">
                          <Link href="/register-provider" className="flex items-center">
                            <FileText className="w-4 h-4 mr-2" />
                            {translate(clientDashboardTranslations, 'viewRequestDetailsButton')}
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  )}

                  {providerRequestInfo?.Status === 'Approved' && (
                    <Card className="border-green-200 bg-green-50">
                      <CardHeader>
                        <CardTitle className="text-xl font-headline flex items-center text-green-700">
                          <CheckCircle className="w-5 h-5 mr-2" />
                          {translate(clientDashboardTranslations, 'requestStatusApprovedTitle')}
                        </CardTitle>
                        <CardDescription className="text-green-600">
                          {translate(clientDashboardTranslations, 'requestStatusApprovedDescription')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Button asChild className="w-full sm:w-auto">
                          <Link href="/dashboard/provider" className="flex items-center">
                            <ExternalLink className="w-4 h-4 mr-2" />
                            {translate(clientDashboardTranslations, 'goToProviderDashboard')}
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  )}

                  {providerRequestInfo?.Status === 'Rejected' && (
                    <Card className="border-red-200 bg-red-50">
                      <CardHeader>
                        <CardTitle className="text-xl font-headline flex items-center text-red-700">
                          <ShieldAlert className="w-5 h-5 mr-2" />
                          {translate(clientDashboardTranslations, 'requestStatusRejectedTitle')}
                        </CardTitle>
                        <CardDescription className="text-red-600">
                          {translate(clientDashboardTranslations, 'requestStatusRejectedDescription').replace('{adminNotes}', providerRequestInfo.AdminNotes || translate(clientDashboardTranslations, 'noAdminNotes'))}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <Button asChild variant="outline" className="w-full sm:w-auto mr-2">
                          <Link href="/register-provider" className="flex items-center">
                            <FileText className="w-4 h-4 mr-2" />
                            {translate(clientDashboardTranslations, 'viewRequestDetailsButton')}
                          </Link>
                        </Button>
                        <Button asChild className="w-full sm:w-auto">
                          <Link href="/register-provider" className="flex items-center">
                            <PlusCircle className="w-4 h-4 mr-2" />
                            {translate(clientDashboardTranslations, 'reapplyAsProvider')}
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  )}

                  {!providerRequestInfo && (
                    <Card className="hover:shadow-lg transition-shadow border-primary/20">
                      <CardHeader>
                        <CardTitle className="text-xl font-headline flex items-center">
                          <Info className="w-5 h-5 mr-2 text-primary" />
                          {translate(clientDashboardTranslations, 'offerServicesCardTitle')}
                        </CardTitle>
                        <CardDescription>
                          {translate(clientDashboardTranslations, 'offerServicesCardDesc')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Button asChild className="w-full sm:w-auto">
                          <Link href="/register-provider" className="flex items-center">
                            <PlusCircle className="w-4 h-4 mr-2" />
                            {translate(clientDashboardTranslations, 'becomeProviderButton')}
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  )}
                </>
              )}
            </div>
          )}
        </>
      )}

      {userDisplayData?.isAdmin && (
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-headline flex items-center"><ShieldAlert className="w-6 h-6 mr-2 text-destructive" />{translate(clientDashboardTranslations, 'adminDashboardCardTitle')}</CardTitle>
            <CardDescription>{translate(clientDashboardTranslations, 'adminDashboardCardDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full sm:w-auto"><Link href="/admin" className="flex items-center">{translate(clientDashboardTranslations, 'goToAdminPanelButton')}<ExternalLink className="w-4 h-4 ml-2" /></Link></Button>
          </CardContent>
        </Card>
      )}

      {!userDisplayData?.isAdmin && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-headline">{translate(clientDashboardTranslations, 'clientPanelTitle')}</CardTitle>
              <CardDescription>{translate(clientDashboardTranslations, 'clientPanelDescription')}</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow"><CardHeader><CardTitle className="text-lg flex items-center"><Search className="w-5 h-5 mr-2 text-primary" /> {translate(clientDashboardTranslations, 'searchServicesCardTitle')}</CardTitle></CardHeader><CardContent><p className="text-xs text-muted-foreground mb-3">{translate(clientDashboardTranslations, 'searchServicesCardDesc')}</p><Button asChild className="w-full text-xs" size="sm"><Link href="/search">{translate(clientDashboardTranslations, 'goToSearchButton')}</Link></Button></CardContent></Card>
              <Card className="hover:shadow-lg transition-shadow"><CardHeader><CardTitle className="text-lg flex items-center"><Eye className="w-5 h-5 mr-2 text-primary" /> {translate(clientDashboardTranslations, 'viewBookingsCardTitle')}</CardTitle></CardHeader><CardContent><p className="text-xs text-muted-foreground mb-3">{translatedViewBookingsDesc}</p><Button asChild variant="outline" className="w-full text-xs" size="sm"><Link href="/dashboard/bookings">{translate(clientDashboardTranslations, 'myBookingsButton')}</Link></Button></CardContent></Card>
              <Card className="hover:shadow-lg transition-shadow"><CardHeader><CardTitle className="text-lg flex items-center"><PlusCircle className="w-5 h-5 mr-2 text-primary" /> {translate(clientDashboardTranslations, 'addReviewCardTitle')}</CardTitle></CardHeader><CardContent><p className="text-xs text-muted-foreground mb-3">{translatedAddReviewDesc}</p><Button asChild variant="outline" className="w-full text-xs" size="sm"><Link href="/dashboard/bookings">{translate(clientDashboardTranslations, 'writeReviewsButton')}</Link></Button></CardContent></Card>
            </CardContent>
          </Card>


        </>
      )}
    </div>
  );
}
