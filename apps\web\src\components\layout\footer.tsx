
"use client";
import Link from 'next/link';
import { Facebook, Instagram } from 'lucide-react';
import { Logo } from '@/components/logo';
import { useLanguage } from '@/contexts/language-context';

const footerTranslations = {
  tagline: { ro: "Platforma ta de încredere pentru servicii de îngrijire personală în Moldova.", ru: "Ваша надежная платформа для услуг личного ухода в Молдове.", en: "Your trusted platform for personal care services in Moldova." },
  servicesTitle: { ro: "Servicii", ru: "Услуги", en: "Services" },
  serviceNanny: { ro: "Bone", ru: "Няни", en: "Nannies" },
  serviceElderCare: { ro: "Îngri<PERSON>re bătrâni", ru: "Уход за пожилыми", en: "Elder Care" },
  serviceCleaning: { ro: "Servicii de curățenie", ru: "Услуги по уборке", en: "Cleaning Services" },
  serviceTutoring: { ro: "Medita<PERSON>ii", ru: "Репетиторство", en: "Tutoring" },
  serviceAll: { ro: "Toate serviciile", ru: "Все услуги", en: "All services" },
  companyTitle: { ro: "Compania", ru: "Компания", en: "Company" },
  aboutUs: { ro: "Despre noi", ru: "О нас", en: "About us" },
  careers: { ro: "Cariere", ru: "Карьера", en: "Careers" },
  contact: { ro: "Contact", ru: "Контакты", en: "Contact" },
  helpTitle: { ro: "Ajutor", ru: "Помощь", en: "Help" },
  helpCenter: { ro: "Centru de ajutor", ru: "Центр помощи", en: "Help center" },
  terms: { ro: "Termeni și condiții", ru: "Условия и положения", en: "Terms and conditions" },
  privacy: { ro: "Confidențialitate", ru: "Конфиденциальность", en: "Privacy" },
  cookies: { ro: "Cookie-uri", ru: "Файлы cookie", en: "Cookies" },
  gdpr: { ro: "GDPR", ru: "GDPR", en: "GDPR" },
  apiDocs: { ro: "Documentație API", ru: "Документация API", en: "API Docs" },
  copyright: { ro: `© ${new Date().getFullYear()} bonami. Toate drepturile rezervate.`, ru: `© ${new Date().getFullYear()} bonami. Все права защищены.`, en: `© ${new Date().getFullYear()} bonami. All rights reserved.` },
  termsShort: { ro: "Termeni", ru: "Условия", en: "Terms" },
  privacyShort: { ro: "Confidențialitate", ru: "Конфиденциальность", en: "Privacy" },
  cookiesShort: { ro: "Cookies", ru: "Cookie", en: "Cookies" },
  providerButton: { ro: "Devino prestator", ru: "Стать поставщиком", en: "Become a provider" },
  viewProvidersLink: { ro: "Vezi prestatori", ru: "Посмотреть поставщиков", en: "View providers" }
};

export function Footer() {
  const { translate } = useLanguage();

  return (
    <footer className="bg-gray-900 text-gray-300 pt-16 pb-8 px-6">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          <div>
            <Logo className="text-white mb-4" />
            <p className="text-gray-400 text-sm">
              {translate(footerTranslations, 'tagline')}
            </p>
            <div className="flex space-x-4 mt-6">
              <a href="#" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="w-6 h-6" />
                <span className="sr-only">Facebook</span>
              </a>
              <a href="#" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="w-6 h-6" />
                <span className="sr-only">Instagram</span>
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">{translate(footerTranslations, 'servicesTitle')}</h4>
            <ul className="space-y-2">
              <li><Link href="/search?service=nanny" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'serviceNanny')}</Link></li>
              <li><Link href="/search?service=elder-care" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'serviceElderCare')}</Link></li>
              <li><Link href="/search?service=cleaning" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'serviceCleaning')}</Link></li>
              <li><Link href="/search?service=tutoring" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'serviceTutoring')}</Link></li>
              <li><Link href="/search" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'serviceAll')}</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">{translate(footerTranslations, 'companyTitle')}</h4>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'aboutUs')}</Link></li>
              <li><Link href="/careers" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'careers')}</Link></li>
              <li><Link href="/contact" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'contact')}</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">{translate(footerTranslations, 'helpTitle')}</h4>
            <ul className="space-y-2">
              <li><Link href="/help-center" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'helpCenter')}</Link></li>
              <li><Link href="/terms" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'terms')}</Link></li>
              <li><Link href="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'privacy')}</Link></li>
              <li><Link href="/cookies" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'cookies')}</Link></li>
              <li><Link href="/gdpr" className="text-gray-400 hover:text-white transition-colors text-sm">{translate(footerTranslations, 'gdpr')}</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm mb-4 md:mb-0">{translate(footerTranslations, 'copyright')}</p>
          <div className="flex space-x-6">
            <Link href="/terms" className="text-gray-400 hover:text-white text-sm">{translate(footerTranslations, 'termsShort')}</Link>
            <Link href="/privacy" className="text-gray-400 hover:text-white text-sm">{translate(footerTranslations, 'privacyShort')}</Link>
            <Link href="/cookies" className="text-gray-400 hover:text-white text-sm">{translate(footerTranslations, 'cookiesShort')}</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
