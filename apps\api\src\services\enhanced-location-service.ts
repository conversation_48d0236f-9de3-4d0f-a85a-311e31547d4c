import { PrismaClient, Location, Address } from '@prisma/client';

const prisma = new PrismaClient();

export interface LocationHierarchy {
  country?: Location;
  region?: Location;
  city?: Location;
  sector?: Location;
}

export interface AddressWithLocations extends Address {
  CountryLocation?: Location | null;
  RegionLocation?: Location | null;
  CityLocation?: Location | null;
  SectorLocation?: Location | null;
}

/**
 * Enhanced Location Service for managing the new location architecture
 * Provides utilities for working with the Location-Address relationship
 */
export class EnhancedLocationService {
  
  /**
   * Get the full location hierarchy for a given location ID
   */
  static async getLocationHierarchy(locationId: number): Promise<LocationHierarchy> {
    const location = await prisma.location.findUnique({
      where: { Id: locationId, IsActive: true }
    });

    if (!location) {
      throw new Error(`Location with ID ${locationId} not found`);
    }

    const hierarchy: LocationHierarchy = {};

    // Build hierarchy by traversing up the parent chain
    let currentLocation = location;
    while (currentLocation) {
      switch (currentLocation.Type) {
        case 'Country':
          hierarchy.country = currentLocation;
          break;
        case 'Municipality':
          hierarchy.region = currentLocation;
          break;
        case 'City':
          hierarchy.city = currentLocation;
          break;
        case 'Sector':
        case 'Suburb':
          hierarchy.sector = currentLocation;
          break;
      }

      // Move to parent
      if (currentLocation.ParentId) {
        currentLocation = await prisma.location.findUnique({
          where: { Id: currentLocation.ParentId, IsActive: true }
        });
      } else {
        currentLocation = null;
      }
    }

    return hierarchy;
  }

  /**
   * Find location by name and type (for backward compatibility)
   */
  static async findLocationByName(name: string, type: string): Promise<Location | null> {
    return await prisma.location.findFirst({
      where: {
        Name: { contains: name, mode: 'insensitive' },
        Type: type as any,
        IsActive: true
      }
    });
  }

  /**
   * Convert old string-based address to new foreign key format
   */
  static async migrateAddressToForeignKeys(address: Address): Promise<Partial<Address>> {
    const updates: Partial<Address> = {};

    // Find country location
    if (address.Country) {
      const countryLocation = await this.findLocationByName(address.Country, 'Country');
      if (countryLocation) {
        updates.CountryId = countryLocation.Id;
      }
    }

    // Find region/municipality location
    if (address.Region) {
      const regionLocation = await this.findLocationByName(address.Region, 'Municipality');
      if (regionLocation) {
        updates.RegionId = regionLocation.Id;
      }
    }

    // Find city location
    if (address.City) {
      const cityLocation = await this.findLocationByName(address.City, 'City');
      if (cityLocation) {
        updates.CityId = cityLocation.Id;
      }
    }

    return updates;
  }

  /**
   * Get address with full location details
   */
  static async getAddressWithLocations(addressId: number): Promise<AddressWithLocations | null> {
    return await prisma.address.findUnique({
      where: { Id: addressId },
      include: {
        CountryLocation: true,
        RegionLocation: true,
        CityLocation: true,
        SectorLocation: true,
        User: true
      }
    });
  }

  /**
   * Create address with location foreign keys
   */
  static async createAddressWithLocations(data: {
    userId: number;
    label: string;
    street: string;
    locationId?: number; // Most specific location (sector/city)
    postalCode?: string;
    isDefault?: boolean;
  }): Promise<Address> {
    const addressData: any = {
      UserId: data.userId,
      Label: data.label,
      Street: data.street,
      PostalCode: data.postalCode,
      IsDefault: data.isDefault || false
    };

    // If locationId is provided, resolve the hierarchy
    if (data.locationId) {
      const hierarchy = await this.getLocationHierarchy(data.locationId);
      
      addressData.CountryId = hierarchy.country?.Id;
      addressData.RegionId = hierarchy.region?.Id;
      addressData.CityId = hierarchy.city?.Id;
      addressData.SectorId = hierarchy.sector?.Id;

      // Set backward compatibility fields
      addressData.Country = hierarchy.country?.Name;
      addressData.Region = hierarchy.region?.Name;
      addressData.City = hierarchy.city?.Name || hierarchy.sector?.Name;
    }

    return await prisma.address.create({
      data: addressData
    });
  }

  /**
   * Get formatted address string with location hierarchy
   */
  static async getFormattedAddress(addressId: number): Promise<string> {
    const address = await this.getAddressWithLocations(addressId);
    if (!address) return '';

    const parts: string[] = [];
    
    if (address.Street) parts.push(address.Street);
    if (address.SectorLocation) parts.push(address.SectorLocation.Name);
    if (address.CityLocation) parts.push(address.CityLocation.Name);
    if (address.RegionLocation) parts.push(address.RegionLocation.Name);
    if (address.CountryLocation) parts.push(address.CountryLocation.Name);

    return parts.join(', ');
  }

  /**
   * Batch migrate existing addresses to use foreign keys
   */
  static async batchMigrateAddresses(): Promise<{ updated: number; errors: number }> {
    const addresses = await prisma.address.findMany({
      where: {
        AND: [
          { CountryId: null },
          { RegionId: null },
          { CityId: null },
          { SectorId: null }
        ]
      }
    });

    let updated = 0;
    let errors = 0;

    for (const address of addresses) {
      try {
        const updates = await this.migrateAddressToForeignKeys(address);
        if (Object.keys(updates).length > 0) {
          await prisma.address.update({
            where: { Id: address.Id },
            data: updates
          });
          updated++;
        }
      } catch (error) {
        console.error(`Error migrating address ${address.Id}:`, error);
        errors++;
      }
    }

    return { updated, errors };
  }
}
