
"use client";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Zap, CreditCard } from 'lucide-react';
import { Card, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import type { ReactNode } from 'react';
import { useLanguage } from '@/contexts/language-context';

const advantagesSectionTranslations = {
  sectionTitle: { ro: "De ce să alegi bonami?", ru: "Почему стоит выбрать bonami?", en: "Why choose bonami?" },
  sectionDescription: { ro: "Oferim soluții complete pentru nevoile tale de îngrijire personală.", ru: "Мы предлагаем комплексные решения для ваших потребностей в личном уходе.", en: "We offer complete solutions for your personal care needs." },
  advantage1Title: { ro: "Siguranță și verificare", ru: "Безопасность и проверка", en: "Safety and verification" },
  advantage1Desc: { ro: "Toți prestatorii sunt verificați și evaluați pentru siguranța dumneavoastră.", ru: "Все поставщики проверяются и оцениваются для вашей безопасности.", en: "All providers are checked and evaluated for your safety." },
  advantage2Title: { ro: "Recenzii reale", ru: "Реальные отзывы", en: "Real reviews" },
  advantage2Desc: { ro: "Citește evaluările și opiniile altor clienți pentru a lua cea mai bună decizie.", ru: "Читайте оценки и мнения других клиентов, чтобы принять лучшее решение.", en: "Read ratings and opinions from other customers to make the best decision." },
  advantage3Title: { ro: "Rezervare rapidă", ru: "Быстрое бронирование", en: "Quick booking" },
  advantage3Desc: { ro: "Găsește și rezervă serviciile de care ai nevoie în câteva minute.", ru: "Найдите и забронируйте необходимые услуги за несколько минут.", en: "Find and book the services you need in minutes." },
  advantage4Title: { ro: "Plată online (în curând)", ru: "Онлайн-оплата (скоро)", en: "Online payment (soon)" },
  advantage4Desc: { ro: "Implementăm un sistem de plată sigur și convenabil direct pe platformă.", ru: "Мы внедряем безопасную и удобную систему онлайн-платежей прямо на платформе.", en: "We are implementing a safe and convenient online payment system directly on the platform." },
};

interface AdvantageItemProps {
  icon: ReactNode;
  title: string;
  description: string;
}

function AdvantageItem({ icon, title, description }: AdvantageItemProps) {
  return (
    <Card className="bg-background p-6 shadow-md hover:shadow-xl transition-shadow duration-300 rounded-lg flex flex-col">
      <CardHeader className="p-0 mb-4">
        <div className="flex flex-col items-center md:flex-row md:items-center gap-4">
          <div className="bg-accent/10 w-16 h-16 rounded-lg flex items-center justify-center text-accent shrink-0">
            {icon}
          </div>
          <CardTitle className="text-xl font-semibold font-headline text-center md:text-left">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="p-0 flex-grow">
        <p className="text-sm text-muted-foreground text-center md:text-left">{description}</p>
      </CardContent>
    </Card>
  );
}

export function AdvantagesSection() {
  const { translate } = useLanguage();
  const advantages = [
    { icon: <ShieldCheck className="w-8 h-8" />, titleKey: "advantage1Title", descriptionKey: "advantage1Desc" },
    { icon: <Star className="w-8 h-8" />, titleKey: "advantage2Title", descriptionKey: "advantage2Desc" },
    { icon: <Zap className="w-8 h-8" />, titleKey: "advantage3Title", descriptionKey: "advantage3Desc" },
    { icon: <CreditCard className="w-8 h-8" />, titleKey: "advantage4Title", descriptionKey: "advantage4Desc" },
  ];

  return (
    <section className="py-16 md:py-20 px-6 bg-card">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-4 font-headline">{translate(advantagesSectionTranslations, 'sectionTitle')}</h2>
        <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">
          {translate(advantagesSectionTranslations, 'sectionDescription')}
        </p>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {advantages.map((advantage) => (
            <AdvantageItem
              key={advantage.titleKey}
              icon={advantage.icon}
              title={translate(advantagesSectionTranslations, advantage.titleKey)}
              description={translate(advantagesSectionTranslations, advantage.descriptionKey)}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
