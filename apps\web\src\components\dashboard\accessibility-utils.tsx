"use client";

import { useEffect, useRef } from 'react';
import { useLanguage } from '@/contexts/language-context';

// Screen reader announcer utility
export function useScreenReaderAnnouncer() {
  const announcerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create a live region for screen reader announcements
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.id = 'screen-reader-announcer';
    document.body.appendChild(announcer);
    announcerRef.current = announcer;

    return () => {
      if (announcerRef.current && document.body.contains(announcerRef.current)) {
        document.body.removeChild(announcerRef.current);
      }
    };
  }, []);

  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announcerRef.current) {
      announcerRef.current.setAttribute('aria-live', priority);
      announcerRef.current.textContent = message;
      
      // Clear the message after a short delay to allow for re-announcements
      setTimeout(() => {
        if (announcerRef.current) {
          announcerRef.current.textContent = '';
        }
      }, 1000);
    }
  };

  return { announce };
}

// Focus management utility
export function useFocusManagement() {
  const focusableElementsSelector = [
    'a[href]',
    'button:not([disabled])',
    'textarea:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    '[tabindex]:not([tabindex="-1"])'
  ].join(', ');

  const trapFocus = (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(focusableElementsSelector);
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    
    // Focus the first element
    if (firstElement) {
      firstElement.focus();
    }

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  };

  const restoreFocus = (element: HTMLElement | null) => {
    if (element && typeof element.focus === 'function') {
      element.focus();
    }
  };

  return { trapFocus, restoreFocus, focusableElementsSelector };
}

// Skip link component for keyboard navigation
export function SkipLink({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <a
      href={href}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded-md focus:shadow-lg"
    >
      {children}
    </a>
  );
}

// Accessible heading component with proper hierarchy
export function AccessibleHeading({ 
  level, 
  children, 
  className,
  id 
}: { 
  level: 1 | 2 | 3 | 4 | 5 | 6; 
  children: React.ReactNode; 
  className?: string;
  id?: string;
}) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  return (
    <Tag className={className} id={id}>
      {children}
    </Tag>
  );
}

// Accessible button with proper ARIA attributes
export function AccessibleButton({
  children,
  onClick,
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
  ariaExpanded,
  ariaControls,
  className,
  type = 'button',
  ...props
}: {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  ariaExpanded?: boolean;
  ariaControls?: string;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  [key: string]: any;
}) {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-expanded={ariaExpanded}
      aria-controls={ariaControls}
      className={className}
      {...props}
    >
      {children}
    </button>
  );
}

// Accessible form field with proper labeling
export function AccessibleFormField({
  id,
  label,
  children,
  error,
  description,
  required = false,
  className
}: {
  id: string;
  label: string;
  children: React.ReactNode;
  error?: string;
  description?: string;
  required?: boolean;
  className?: string;
}) {
  const errorId = error ? `${id}-error` : undefined;
  const descriptionId = description ? `${id}-description` : undefined;
  const ariaDescribedBy = [errorId, descriptionId].filter(Boolean).join(' ') || undefined;

  return (
    <div className={className}>
      <label htmlFor={id} className="block text-sm font-medium mb-1">
        {label}
        {required && <span className="text-destructive ml-1" aria-label="required">*</span>}
      </label>
      
      {description && (
        <p id={descriptionId} className="text-sm text-muted-foreground mb-2">
          {description}
        </p>
      )}
      
      <div aria-describedby={ariaDescribedBy}>
        {children}
      </div>
      
      {error && (
        <p id={errorId} className="text-sm text-destructive mt-1" role="alert">
          {error}
        </p>
      )}
    </div>
  );
}

// Accessible status indicator
export function AccessibleStatus({
  status,
  children,
  className
}: {
  status: 'success' | 'error' | 'warning' | 'info';
  children: React.ReactNode;
  className?: string;
}) {
  const statusConfig = {
    success: { role: 'status', ariaLabel: 'Success' },
    error: { role: 'alert', ariaLabel: 'Error' },
    warning: { role: 'alert', ariaLabel: 'Warning' },
    info: { role: 'status', ariaLabel: 'Information' }
  };

  const config = statusConfig[status];

  return (
    <div
      role={config.role}
      aria-label={config.ariaLabel}
      className={className}
    >
      {children}
    </div>
  );
}

// Accessibility testing component (development only)
export function AccessibilityTester({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Check for common accessibility issues
      const checkAccessibility = () => {
        // Check for images without alt text
        const images = document.querySelectorAll('img:not([alt])');
        if (images.length > 0) {
          console.warn('Accessibility: Found images without alt text:', images);
        }

        // Check for buttons without accessible names
        const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
        const buttonsWithoutText = Array.from(buttons).filter(btn => !btn.textContent?.trim());
        if (buttonsWithoutText.length > 0) {
          console.warn('Accessibility: Found buttons without accessible names:', buttonsWithoutText);
        }

        // Check for form inputs without labels
        const inputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        const inputsWithoutLabels = Array.from(inputs).filter(input => {
          const id = input.getAttribute('id');
          return !id || !document.querySelector(`label[for="${id}"]`);
        });
        if (inputsWithoutLabels.length > 0) {
          console.warn('Accessibility: Found inputs without labels:', inputsWithoutLabels);
        }

        // Check for proper heading hierarchy
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        let previousLevel = 0;
        headings.forEach(heading => {
          const level = parseInt(heading.tagName.charAt(1));
          if (level > previousLevel + 1) {
            console.warn('Accessibility: Heading hierarchy skip detected:', heading);
          }
          previousLevel = level;
        });
      };

      // Run checks after a short delay to allow DOM to settle
      const timer = setTimeout(checkAccessibility, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  return <>{children}</>;
}

// High contrast mode detector
export function useHighContrastMode() {
  const { useEffect, useState } = require('react');
  const [isHighContrast, setIsHighContrast] = useState(false);

  useEffect(() => {
    const checkHighContrast = () => {
      // Check for Windows high contrast mode
      const isWindowsHighContrast = window.matchMedia('(-ms-high-contrast: active)').matches;
      
      // Check for forced colors (modern browsers)
      const isForcedColors = window.matchMedia('(forced-colors: active)').matches;
      
      setIsHighContrast(isWindowsHighContrast || isForcedColors);
    };

    checkHighContrast();
    
    // Listen for changes
    const mediaQuery = window.matchMedia('(forced-colors: active)');
    mediaQuery.addEventListener('change', checkHighContrast);
    
    return () => {
      mediaQuery.removeEventListener('change', checkHighContrast);
    };
  }, []);

  return isHighContrast;
}
