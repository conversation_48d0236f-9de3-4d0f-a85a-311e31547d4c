"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useUser } from '@/contexts/user-context';
import { PostRegistrationChoice } from '@/components/profile-setup/post-registration-choice';
import { ProfileSetupWizard } from '@/components/profile-setup/profile-setup-wizard';
import { Loader2 } from 'lucide-react';

type SetupMode = 'choice' | 'wizard';

function ProfileSetupContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  const { user, isLoading } = useUser();
  
  const [mode, setMode] = useState<SetupMode>('choice');
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Check if user should be redirected
  useEffect(() => {
    if (status === 'loading' || isLoading) return;

    // Redirect unauthenticated users to login
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    // Check if this is a fresh registration (from URL params)
    const fromRegistration = searchParams.get('from') === 'registration';
    
    // If not from registration and user already has some profile data, redirect to dashboard
    if (!fromRegistration && user) {
      const hasProfileData = user.phone || user.bio || (user.SpokenLanguages && user.SpokenLanguages.length > 1);
      if (hasProfileData) {
        setIsRedirecting(true);
        router.push('/dashboard');
        return;
      }
    }

    // If coming from registration or user has minimal profile, show the setup
    // Check URL params to see if we should go directly to wizard
    const skipChoice = searchParams.get('skip-choice') === 'true';
    if (skipChoice) {
      setMode('wizard');
    }
  }, [status, isLoading, user, searchParams, router]);

  const handleCompleteProfile = () => {
    setMode('wizard');
    // Update URL to reflect the change
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('skip-choice', 'true');
    window.history.replaceState({}, '', newUrl.toString());
  };

  const handleSkipForNow = () => {
    setIsRedirecting(true);
    router.push('/dashboard');
  };

  const handleWizardComplete = () => {
    setIsRedirecting(true);
    router.push('/dashboard?profile-completed=true');
  };

  const handleWizardExit = () => {
    setIsRedirecting(true);
    router.push('/dashboard');
  };

  // Show loading state
  if (status === 'loading' || isLoading || isRedirecting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Se încarcă...</p>
        </div>
      </div>
    );
  }

  // Show appropriate component based on mode
  if (mode === 'wizard') {
    return (
      <ProfileSetupWizard
        onComplete={handleWizardComplete}
        onExit={handleWizardExit}
      />
    );
  }

  return (
    <PostRegistrationChoice
      userName={user?.name || session?.user?.name || undefined}
      onCompleteProfile={handleCompleteProfile}
      onSkipForNow={handleSkipForNow}
    />
  );
}

export default function ProfileSetupPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Se încarcă...</p>
        </div>
      </div>
    }>
      <ProfileSetupContent />
    </Suspense>
  );
}
