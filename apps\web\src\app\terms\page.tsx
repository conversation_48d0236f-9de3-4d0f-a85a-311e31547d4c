
"use client";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { useLanguage } from '@/contexts/language-context';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { commonTranslations } from "@repo/translations"; // Import commonTranslations

export default function TermsPage() {
  const { translate } = useLanguage();

  const sections = [
    { titleKey: 'termsPage_introductionTitle', contentKey: 'termsPage_introductionContent' },
    { titleKey: 'termsPage_servicesTitle', contentKey: 'termsPage_servicesContent' },
    { titleKey: 'termsPage_userObligationsTitle', contentKey: 'termsPage_userObligationsContent' },
    { titleKey: 'termsPage_providerSpecificTermsTitle', contentKey: 'termsPage_providerSpecificTermsContent' },
    { titleKey: 'termsPage_disclaimerTitle', contentKey: 'termsPage_disclaimerContent' },
    { titleKey: 'termsPage_modificationsTitle', contentKey: 'termsPage_modificationsContent' },
    { titleKey: 'termsPage_contactTitle', contentKey: 'termsPage_contactContent' },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <Card className="max-w-3xl mx-auto mt-10 shadow-lg">
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center font-headline">
              {translate(commonTranslations, 'termsPage_pageTitle')}
            </CardTitle>
            <p className="text-sm text-muted-foreground text-center">{translate(commonTranslations, 'termsPage_lastUpdated')}</p>
          </CardHeader>
          <CardContent className="space-y-6 text-foreground/80">
            {sections.map(section => (
              <div key={section.titleKey}>
                <h2 className="text-xl font-semibold mb-2 font-headline text-foreground">
                  {translate(commonTranslations, section.titleKey as keyof typeof commonTranslations)}
                </h2>
                <p className="whitespace-pre-line">
                  {translate(commonTranslations, section.contentKey as keyof typeof commonTranslations)}
                </p>
              </div>
            ))}
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
