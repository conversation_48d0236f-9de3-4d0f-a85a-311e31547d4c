
"use client";

import { useState, useMemo, useEffect } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { format, parseISO, isSameDay, startOfMonth } from 'date-fns';
import { ro, ru, enUS as en } from 'date-fns/locale';
import { Loader2 } from 'lucide-react';
import type { Booking as PrismaBooking } from '@prisma/client';
import { useToast } from '@/hooks/use-toast';

interface BookingAvailabilityModalProps {
  isOpen: boolean;
  onClose: () => void;
  providerId: number;
  providerName: string;
  serviceId: number;
}

const dateFnsLocalesMap: Record<string, Locale> = {
  ro: ro, ru: ru, en: en,
};

interface EnrichedBookingForModal extends PrismaBooking {
  serviceName: string; 
}

export function BookingAvailabilityModal({ isOpen, onClose, providerId, providerName, serviceId }: BookingAvailabilityModalProps) {
  const { translate, currentLanguage } = useLanguage();
  const selectedLocale = dateFnsLocalesMap[currentLanguage.code] || ro;
  const { toast } = useToast();

  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [currentMonth, setCurrentMonth] = useState(startOfMonth(new Date()));
  const [providerBookings, setProviderBookings] = useState<EnrichedBookingForModal[]>([]);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);

  const [bookingTime, setBookingTime] = useState("");
  const [bookingNotes, setBookingNotes] = useState("");
  const [isRequestingBooking, setIsRequestingBooking] = useState(false);

  useEffect(() => {
    if (isOpen && providerId) {
      setSelectedDate(new Date());
      setCurrentMonth(startOfMonth(new Date()));
      setBookingTime("");
      setBookingNotes("");
      
      const fetchProviderBookings = async () => {
        setIsLoadingBookings(true);
        setBookingError(null);
        try {
          const response = await fetch(`/api/proxy/provider/appointments?providerId=${providerId}`);
          if (!response.ok) throw new Error("Failed to fetch bookings");
          const data = await response.json();
          setProviderBookings((data.appointments || []).map((b: any) => ({...b, eventStartDateTime: parseISO(b.date)}))); 
        } catch (err) {
          setBookingError(err instanceof Error ? err.message : "Error fetching bookings.");
          setProviderBookings([]);
        } finally {
          setIsLoadingBookings(false);
        }
      };
      fetchProviderBookings();
    }
  }, [isOpen, providerId]);

  const bookedDates = useMemo(() => {
    return providerBookings
      .filter(booking => booking.EventStartDateTime)
      .map(booking => booking.EventStartDateTime as Date);
  }, [providerBookings]);
  
  const handleBookingRequest = async () => {
    if (!selectedDate || !bookingTime || !serviceId || !providerId) {
        toast({ variant: "destructive", title: translate(commonTranslations, 'toastErrorTitle'), description: translate(commonTranslations, 'bookingFormIncompleteError') });
        return;
    }
    setIsRequestingBooking(true);

    const [hours, minutes] = bookingTime.split(':').map(Number);
    const eventDateTime = new Date(selectedDate);
    eventDateTime.setHours(hours, minutes);

    try {
        const response = await fetch('/api/proxy/bookings/create', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                providerId: providerId,
                serviceId: serviceId,
                eventDateTime: eventDateTime.toISOString(),
                clientNotes: bookingNotes,
            }),
        });
        const result = await response.json();
        if (!response.ok || !result.success) {
            throw new Error(result.message || translate(commonTranslations, 'bookingRequestErrorToast'));
        }
        toast({ title: translate(commonTranslations, 'toastSuccessTitle'), description: translate(commonTranslations, 'bookingRequestSuccessToast') });
        onClose();
    } catch (err) {
        const msg = err instanceof Error ? err.message : translate(commonTranslations, 'bookingRequestErrorToast');
        toast({ variant: "destructive", title: translate(commonTranslations, 'toastErrorTitle'), description: msg });
    } finally {
        setIsRequestingBooking(false);
    }
  };
  
  const modalTitle = translate(commonTranslations, 'bookingAndAvailabilityModalTitle').replace('{providerName}', providerName);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{modalTitle}</DialogTitle>
          <DialogDescription>
            {translate(commonTranslations, 'bookingAndAvailabilityModalDescription')}
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
          <div className="flex justify-center">
             <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              month={currentMonth}
              onMonthChange={setCurrentMonth}
              modifiers={{ booked: bookedDates }}
              modifiersClassNames={{ booked: 'day-booked' }}
              className="rounded-md border"
              locale={selectedLocale}
              weekStartsOn={1} 
              disabled={{ before: new Date() }}
            />
          </div>
          <div className="space-y-4">
            <h4 className="font-medium text-md text-center md:text-left">
              {translate(commonTranslations, 'selectBookingDetailsLabel')}
            </h4>
             <div className="grid gap-2">
              <Label htmlFor="bookingTime">{translate(commonTranslations, 'selectTimeLabel')}</Label>
              <Input id="bookingTime" type="time" value={bookingTime} onChange={(e) => setBookingTime(e.target.value)} />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="bookingNotes">{translate(commonTranslations, 'notesLabel')}</Label>
              <Textarea id="bookingNotes" placeholder={translate(commonTranslations, 'notesPlaceholder')} value={bookingNotes} onChange={(e) => setBookingNotes(e.target.value)} />
            </div>
            {isLoadingBookings ? (
                <div className="flex items-center text-sm text-muted-foreground">
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    <span>{translate(commonTranslations, 'loadingAppointments')}</span>
                </div>
            ) : bookingError ? (
                 <p className="text-sm text-destructive">{bookingError}</p>
            ): null}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>{translate(commonTranslations, 'cancelButton')}</Button>
          <Button onClick={handleBookingRequest} disabled={isRequestingBooking || !selectedDate || !bookingTime}>
            {isRequestingBooking && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {translate(commonTranslations, 'sendRequestButton')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
