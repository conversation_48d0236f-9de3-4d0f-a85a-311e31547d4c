"use client";

import React, { useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { X, Upload, File, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';

interface FileUploadProps {
  id: string;
  label: string;
  multiple?: boolean;
  accept?: string;
  maxSize?: number; // in bytes
  value?: File | File[] | null;
  onChange: (files: File | File[] | null) => void;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  error?: string;
}

const DEFAULT_ACCEPT = '.pdf,.jpg,.jpeg,.png';
const DEFAULT_MAX_SIZE = 5 * 1024 * 1024; // 5MB

export function FileUpload({
  id,
  label,
  multiple = false,
  accept = DEFAULT_ACCEPT,
  maxSize = DEFAULT_MAX_SIZE,
  value,
  onChange,
  className,
  disabled = false,
  required = false,
  error
}: FileUploadProps) {
  const { translate } = useLanguage();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const validateFile = useCallback((file: File): string | null => {
    // Check file type
    const acceptedTypes = accept.split(',').map(type => type.trim());
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const mimeTypeAccepted = acceptedTypes.some(type => 
      type.startsWith('.') ? type === fileExtension : file.type.includes(type)
    );
    
    if (!mimeTypeAccepted) {
      return translate(commonTranslations, 'fileInvalidType');
    }

    // Check file size
    if (file.size > maxSize) {
      return translate(commonTranslations, 'fileTooLarge');
    }

    return null;
  }, [accept, maxSize, translate]);

  const handleFileSelect = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    const validFiles: File[] = [];
    
    // Validate each file
    for (const file of fileArray) {
      const error = validateFile(file);
      if (error) {
        // You could show individual file errors here
        console.error(`File ${file.name}: ${error}`);
        continue;
      }
      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    // Simulate upload progress
    setUploading(true);
    setUploadProgress(0);
    
    // Simulate upload with progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          setUploading(false);
          return 100;
        }
        return prev + 10;
      });
    }, 100);

    // Update the form state
    if (multiple) {
      const currentFiles = Array.isArray(value) ? value : [];
      onChange([...currentFiles, ...validFiles]);
    } else {
      onChange(validFiles[0] || null);
    }
  }, [multiple, value, onChange, validateFile]);

  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    if (disabled) return;
    
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
  }, [disabled, handleFileSelect]);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  }, []);

  const handleRemoveFile = useCallback((fileToRemove: File) => {
    if (multiple && Array.isArray(value)) {
      const updatedFiles = value.filter(file => file !== fileToRemove);
      onChange(updatedFiles.length > 0 ? updatedFiles : null);
    } else {
      onChange(null);
    }
  }, [multiple, value, onChange]);

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileCount = (): number => {
    if (!value) return 0;
    return Array.isArray(value) ? value.length : 1;
  };

  const getFiles = (): File[] => {
    if (!value) return [];
    return Array.isArray(value) ? value : [value];
  };

  const hasFiles = getFileCount() > 0;

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id} className="text-sm font-medium">
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
      
      {/* Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          dragOver && !disabled ? "border-primary bg-primary/5" : "border-muted-foreground/25",
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary/50",
          error ? "border-destructive" : ""
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={!disabled ? handleButtonClick : undefined}
      >
        <Upload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground mb-2">
          {multiple 
            ? translate(commonTranslations, 'fileSelectMultipleButton')
            : translate(commonTranslations, 'fileSelectButton')
          }
        </p>
        <p className="text-xs text-muted-foreground">
          {translate(commonTranslations, 'fileUploadNote')}
        </p>
      </div>

      {/* Upload Progress */}
      {uploading && (
        <div className="space-y-2">
          <Progress value={uploadProgress} className="w-full" />
          <p className="text-xs text-muted-foreground text-center">
            {translate(commonTranslations, 'fileUploadSuccess')}... {uploadProgress}%
          </p>
        </div>
      )}

      {/* Selected Files Display */}
      {hasFiles && (
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">
            {getFileCount()} {getFileCount() === 1 
              ? translate(commonTranslations, 'fileSelected')
              : translate(commonTranslations, 'filesSelected')
            }
          </div>
          
          <div className="space-y-2">
            {getFiles().map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center justify-between p-3 bg-muted rounded-lg"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <File className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">{formatFileSize(file.size)}</p>
                  </div>
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                </div>
                
                {!disabled && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile(file);
                    }}
                    className="ml-2 h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="flex items-center space-x-2 text-sm text-destructive">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        id={id}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
}
