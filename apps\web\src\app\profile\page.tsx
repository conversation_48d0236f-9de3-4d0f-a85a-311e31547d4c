import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Edit3 } from "lucide-react";
import Link from "next/link";

// This would typically fetch current user data
const currentUser = {
  name: "Utilizator Test",
  email: "<EMAIL>",
  avatarUrl: "https://placehold.co/100x100.png?text=UT",
  memberSince: "Ianuarie 2024",
  bio: "Caut servicii de îngrijire pentru familia mea.",
};

export default function UserProfilePage() {
  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <Card className="max-w-2xl mx-auto">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="w-24 h-24">
                <AvatarImage src={currentUser.avatarUrl} alt={currentUser.name} data-ai-hint="person avatar" />
                <AvatarFallback>{currentUser.name.substring(0,2).toUpperCase()}</AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-3xl font-bold font-headline">{currentUser.name}</CardTitle>
            <CardDescription>{currentUser.email}</CardDescription>
            <Button variant="outline" size="sm" className="mx-auto mt-2">
              <Edit3 className="mr-2 h-4 w-4" /> Editează Profilul
            </Button>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-semibold text-lg mb-2">Despre Mine</h3>
              <p className="text-muted-foreground">{currentUser.bio || "Nicio descriere adăugată."}</p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-2">Detalii Cont</h3>
              <ul className="space-y-1 text-muted-foreground">
                <li>Membru din: {currentUser.memberSince}</li>
                {/* More details can be added here */}
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-2">Istoric Rezervări</h3>
              <p className="text-muted-foreground">Nicio rezervare efectuată încă. {" "}
                <Link href="/search" className="text-primary hover:underline">Caută servicii</Link>.
              </p>
              {/* Placeholder for booking history list */}
            </div>
             <div>
              <h3 className="font-semibold text-lg mb-2">Recenziile Mele</h3>
              <p className="text-muted-foreground">Nicio recenzie lăsată încă.</p>
              {/* Placeholder for reviews left by user */}
            </div>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
