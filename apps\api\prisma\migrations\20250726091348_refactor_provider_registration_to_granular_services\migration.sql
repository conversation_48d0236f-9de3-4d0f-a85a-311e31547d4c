/*
  Warnings:

  - You are about to drop the column `RequestedServices` on the `ProviderRegistrationRequest` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "PendingServiceStatus" AS ENUM ('PendingReview', 'Approved', 'Rejected', 'RequiresChanges');

-- AlterTable
ALTER TABLE "ProviderRegistrationRequest" DROP COLUMN "RequestedServices";

-- CreateTable
CREATE TABLE "PendingService" (
    "Id" SERIAL NOT NULL,
    "RequestId" TEXT NOT NULL,
    "CategoryId" INTEGER NOT NULL,
    "ServiceCategorySlug" "ServiceCategorySlug" NOT NULL,
    "ServiceName" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "ExperienceYears" INTEGER NOT NULL,
    "Status" "PendingServiceStatus" NOT NULL DEFAULT 'PendingReview',
    "AdminNotes" TEXT,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,
    "DocumentPaths" TEXT[],

    CONSTRAINT "PendingService_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "PendingNannyServiceDetails" (
    "Id" SERIAL NOT NULL,
    "PendingServiceId" INTEGER NOT NULL,
    "LocationValue" TEXT,
    "PricePerHour" TEXT,
    "PricePerDay" TEXT,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "PreferredAge_0_2" BOOLEAN DEFAULT false,
    "PreferredAge_3_6" BOOLEAN DEFAULT false,
    "PreferredAge_7_plus" BOOLEAN DEFAULT false,
    "AvailabilityFullTime" BOOLEAN DEFAULT false,
    "AvailabilityPartTime" BOOLEAN DEFAULT false,
    "AvailabilityOccasional" BOOLEAN DEFAULT false,
    "ServiceBabysitting" BOOLEAN DEFAULT false,
    "ServicePlaytime" BOOLEAN DEFAULT false,
    "ServiceMeals" BOOLEAN DEFAULT false,
    "ServiceBedtime" BOOLEAN DEFAULT false,
    "ServiceEducational" BOOLEAN DEFAULT false,
    "ServiceOutdoor" BOOLEAN DEFAULT false,
    "ServiceTransport" BOOLEAN DEFAULT false,
    "ServiceHousework" BOOLEAN DEFAULT false,
    "ExtraFirstAid" BOOLEAN DEFAULT false,
    "ExtraOwnTransport" BOOLEAN DEFAULT false,
    "ExtraCooking" BOOLEAN DEFAULT false,
    "ExtraLanguages" TEXT,
    "ExtraSpecialNeeds" BOOLEAN DEFAULT false,
    "ExtraOvernightCare" BOOLEAN DEFAULT false,

    CONSTRAINT "PendingNannyServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "PendingElderCareServiceDetails" (
    "Id" SERIAL NOT NULL,
    "PendingServiceId" INTEGER NOT NULL,
    "LocationValue" TEXT,
    "PricePerHour" TEXT,
    "PricePerDay" TEXT,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "AvailabilityFullTime" BOOLEAN DEFAULT false,
    "AvailabilityPartTime" BOOLEAN DEFAULT false,
    "AvailabilityOccasional" BOOLEAN DEFAULT false,
    "ServicePersonalCare" BOOLEAN DEFAULT false,
    "ServiceMedicalSupport" BOOLEAN DEFAULT false,
    "ServiceCompanionship" BOOLEAN DEFAULT false,
    "ServiceHousekeeping" BOOLEAN DEFAULT false,
    "ServiceMeals" BOOLEAN DEFAULT false,
    "ServiceTransport" BOOLEAN DEFAULT false,
    "ServiceShopping" BOOLEAN DEFAULT false,
    "ServiceMobility" BOOLEAN DEFAULT false,
    "ExtraFirstAid" BOOLEAN DEFAULT false,
    "ExtraMedicalTraining" BOOLEAN DEFAULT false,
    "ExtraOwnTransport" BOOLEAN DEFAULT false,
    "ExtraLanguages" TEXT,
    "ExtraSpecialNeeds" BOOLEAN DEFAULT false,
    "ExtraOvernightCare" BOOLEAN DEFAULT false,

    CONSTRAINT "PendingElderCareServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "PendingCleaningServiceDetails" (
    "Id" SERIAL NOT NULL,
    "PendingServiceId" INTEGER NOT NULL,
    "LocationValue" TEXT,
    "PricePerHour" TEXT,
    "PricePerDay" TEXT,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "AvailabilityFullTime" BOOLEAN DEFAULT false,
    "AvailabilityPartTime" BOOLEAN DEFAULT false,
    "AvailabilityOccasional" BOOLEAN DEFAULT false,
    "ServiceRegularCleaning" BOOLEAN DEFAULT false,
    "ServiceDeepCleaning" BOOLEAN DEFAULT false,
    "ServiceWindowCleaning" BOOLEAN DEFAULT false,
    "ServiceCarpetCleaning" BOOLEAN DEFAULT false,
    "ServiceLaundry" BOOLEAN DEFAULT false,
    "ServiceIroning" BOOLEAN DEFAULT false,
    "ServiceOrganizing" BOOLEAN DEFAULT false,
    "ServicePostConstruction" BOOLEAN DEFAULT false,
    "ExtraOwnSupplies" BOOLEAN DEFAULT false,
    "ExtraEcoFriendly" BOOLEAN DEFAULT false,
    "ExtraOwnTransport" BOOLEAN DEFAULT false,
    "ExtraInsured" BOOLEAN DEFAULT false,
    "ExtraWeekendAvailable" BOOLEAN DEFAULT false,
    "ExtraEmergencyService" BOOLEAN DEFAULT false,

    CONSTRAINT "PendingCleaningServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "PendingTutoringServiceDetails" (
    "Id" SERIAL NOT NULL,
    "PendingServiceId" INTEGER NOT NULL,
    "LocationValue" TEXT,
    "PricePerHour" TEXT,
    "PricePerDay" TEXT,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "ServiceAfterSchool" BOOLEAN DEFAULT false,
    "ServiceHomeworkHelp" BOOLEAN DEFAULT false,
    "ServiceIndividualLessons" BOOLEAN DEFAULT false,
    "Grades_1_4" BOOLEAN DEFAULT false,
    "Grades_5_8" BOOLEAN DEFAULT false,
    "Grades_9_12" BOOLEAN DEFAULT false,
    "SubjectRomanian" BOOLEAN DEFAULT false,
    "SubjectMath" BOOLEAN DEFAULT false,
    "SubjectEnglish" BOOLEAN DEFAULT false,
    "SubjectOther" TEXT,
    "FormatOnline" BOOLEAN DEFAULT false,
    "FormatOwnHome" BOOLEAN DEFAULT false,
    "FormatChildHome" BOOLEAN DEFAULT false,
    "ExtraGames" BOOLEAN DEFAULT false,
    "ExtraSnack" BOOLEAN DEFAULT false,
    "ExtraTransport" BOOLEAN DEFAULT false,
    "ExtraSupervisedHomework" BOOLEAN DEFAULT false,

    CONSTRAINT "PendingTutoringServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "PendingCookingServiceDetails" (
    "Id" SERIAL NOT NULL,
    "PendingServiceId" INTEGER NOT NULL,
    "LocationValue" TEXT,
    "PricePerHour" TEXT,
    "PricePerDay" TEXT,
    "PricePerMeal" TEXT,
    "MinPortions" TEXT,
    "PriceSubscriptionAmount" TEXT,
    "PriceSubscriptionUnit" TEXT,
    "PriceSubscriptionText" TEXT,
    "SubscriptionDetails" TEXT,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "ServiceMealPrep" BOOLEAN DEFAULT false,
    "ServiceCatering" BOOLEAN DEFAULT false,
    "ServiceSpecialDiet" BOOLEAN DEFAULT false,
    "ServiceBaking" BOOLEAN DEFAULT false,
    "ServiceGroceryShopping" BOOLEAN DEFAULT false,
    "ServiceKitchenCleanup" BOOLEAN DEFAULT false,
    "CuisineRomanian" BOOLEAN DEFAULT false,
    "CuisineItalian" BOOLEAN DEFAULT false,
    "CuisineFrench" BOOLEAN DEFAULT false,
    "CuisineAsian" BOOLEAN DEFAULT false,
    "CuisineVegetarian" BOOLEAN DEFAULT false,
    "CuisineVegan" BOOLEAN DEFAULT false,
    "CuisineOther" TEXT,
    "ExtraOwnIngredients" BOOLEAN DEFAULT false,
    "ExtraOwnTransport" BOOLEAN DEFAULT false,
    "ExtraWeekendAvailable" BOOLEAN DEFAULT false,

    CONSTRAINT "PendingCookingServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PendingNannyServiceDetails_PendingServiceId_key" ON "PendingNannyServiceDetails"("PendingServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "PendingElderCareServiceDetails_PendingServiceId_key" ON "PendingElderCareServiceDetails"("PendingServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "PendingCleaningServiceDetails_PendingServiceId_key" ON "PendingCleaningServiceDetails"("PendingServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "PendingTutoringServiceDetails_PendingServiceId_key" ON "PendingTutoringServiceDetails"("PendingServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "PendingCookingServiceDetails_PendingServiceId_key" ON "PendingCookingServiceDetails"("PendingServiceId");

-- AddForeignKey
ALTER TABLE "PendingService" ADD CONSTRAINT "PendingService_RequestId_fkey" FOREIGN KEY ("RequestId") REFERENCES "ProviderRegistrationRequest"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingService" ADD CONSTRAINT "PendingService_CategoryId_fkey" FOREIGN KEY ("CategoryId") REFERENCES "ServiceCategory"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingNannyServiceDetails" ADD CONSTRAINT "PendingNannyServiceDetails_PendingServiceId_fkey" FOREIGN KEY ("PendingServiceId") REFERENCES "PendingService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingElderCareServiceDetails" ADD CONSTRAINT "PendingElderCareServiceDetails_PendingServiceId_fkey" FOREIGN KEY ("PendingServiceId") REFERENCES "PendingService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingCleaningServiceDetails" ADD CONSTRAINT "PendingCleaningServiceDetails_PendingServiceId_fkey" FOREIGN KEY ("PendingServiceId") REFERENCES "PendingService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingTutoringServiceDetails" ADD CONSTRAINT "PendingTutoringServiceDetails_PendingServiceId_fkey" FOREIGN KEY ("PendingServiceId") REFERENCES "PendingService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingCookingServiceDetails" ADD CONSTRAINT "PendingCookingServiceDetails_PendingServiceId_fkey" FOREIGN KEY ("PendingServiceId") REFERENCES "PendingService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;
