import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

// Extindem tipul Request din Express pentru a putea adăuga datele utilizatorului
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    roles: string[];
    isAdmin: boolean;
  };
}

export const authenticate = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  // Nivel 1: Verificarea cheii API statice (obligatoriu)
  const apiKey = req.headers['x-api-key'];
  if (!apiKey || apiKey !== process.env.INTERNAL_API_KEY) {
    console.warn(`[API Auth] Invalid or missing API key. Access denied.`);
    return res.status(401).json({ message: 'Unauthorized: Invalid API Key' });
  }

  // Nivel 2: Verificarea token-ului JWT al utilizatorului (opțional)
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Format: Bearer TOKEN

  if (token) {
    try {
      // Verificăm token-ul folosind secretul din .env
      const decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET!);
      req.user = decoded as AuthenticatedRequest['user']; // Atasăm datele utilizatorului la cerere
      console.log(`[API Auth] JWT valid for user ID: ${req.user?.id}`);
    } catch (err) {
      // Token-ul este prezent, dar invalid (expirat, malformat etc.)
      // Nu blocăm cererea aici, dar nici nu atașăm datele utilizatorului.
      // Ruta finală va decide dacă un utilizator valid este necesar.
      console.warn(`[API Auth] Invalid JWT received. Proceeding as anonymous.`);
      req.user = undefined;
    }
  } else {
    // Nu există token JWT, cererea este anonimă (dar de încredere, datorită cheii API)
    req.user = undefined;
  }
  
  // Cererea a trecut de verificări, continuăm
  next();
};