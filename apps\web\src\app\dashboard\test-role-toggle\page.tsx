"use client";

import { RoleToggle, useRoleToggle } from '@/components/dashboard/role-toggle';
import { RoleIndicator } from '@/components/dashboard/role-indicator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useSession } from 'next-auth/react';

export default function TestRoleTogglePage() {
  const { data: session } = useSession();
  const { currentRole, switchRole } = useRoleToggle();
  const userFromSession = session?.user as any;
  const isProvider = userFromSession?.isProvider || false;

  if (!session) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Please log in to test the role toggle functionality.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Role Toggle Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">Current State</h3>
            <div className="space-y-2">
              <p><strong>Current Role:</strong> {currentRole}</p>
              <p><strong>Is Provider:</strong> {isProvider ? 'Yes' : 'No'}</p>
              <p><strong>User Name:</strong> {userFromSession?.name || 'Unknown'}</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Role Toggle Component</h3>
            <RoleToggle
              currentRole={currentRole}
              availableRoles={isProvider ? ['client', 'provider'] : ['client']}
              onRoleChange={switchRole}
            />
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Role Indicator Variants</h3>
            <div className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground mb-1">Default:</p>
                <RoleIndicator currentRole={currentRole} />
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">Compact:</p>
                <RoleIndicator currentRole={currentRole} variant="compact" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">Minimal:</p>
                <RoleIndicator currentRole={currentRole} variant="minimal" />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Manual Role Switch Test</h3>
            <div className="flex gap-2">
              <Button 
                onClick={() => switchRole('client')}
                variant={currentRole === 'client' ? 'default' : 'outline'}
              >
                Switch to Client
              </Button>
              {isProvider && (
                <Button 
                  onClick={() => switchRole('provider')}
                  variant={currentRole === 'provider' ? 'default' : 'outline'}
                >
                  Switch to Provider
                </Button>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">URL Information</h3>
            <div className="text-sm space-y-1">
              <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'Loading...'}</p>
              <p><strong>Expected Role from URL:</strong> {currentRole}</p>
            </div>
          </div>

          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Testing Instructions:</h4>
            <ol className="text-sm space-y-1 list-decimal list-inside">
              <li>Test keyboard navigation: Tab to role toggle, use arrow keys to switch</li>
              <li>Test screen reader: Role changes should be announced</li>
              <li>Test URL changes: Role switches should update the URL</li>
              <li>Test responsive behavior: Resize window to see mobile layout</li>
              <li>Test accessibility: Use screen reader to verify ARIA labels</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
