{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 9002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@repo/translations": "*", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "genkit": "^1.8.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "next": "^15.4.2", "next-auth": "^4.24.7", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "swagger-ui-react": "^5.27.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.1", "zod": "^3.24.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/swagger-ui-react": "^5.0.5", "autoprefixer": "^10.4.21", "postcss": "^8.4.49", "tailwindcss": "^3.4.17"}}