
"use client";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { useLanguage } from '@/contexts/language-context';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const careersPageTranslations = {
  pageTitle: { ro: "Cariere", ru: "Карьера", en: "Careers" },
  pageContent: { ro: "Informații despre cariere vor fi disponibile aici în curând.", ru: "Информация о карьере скоро будет доступна здесь.", en: "Careers information will be available here soon." },
};

export default function CareersPage() {
  const { translate } = useLanguage();

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <Card className="max-w-3xl mx-auto mt-10 shadow-lg">
            <CardHeader>
                <CardTitle className="text-3xl font-bold text-center font-headline">
                    {translate(careersPageTranslations, 'pageTitle')}
                </CardTitle>
            </CardHeader>
            <CardContent className="text-center py-8">
                <p className="text-lg text-muted-foreground">
                    {translate(careersPageTranslations, 'pageContent')}
                </p>
            </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
