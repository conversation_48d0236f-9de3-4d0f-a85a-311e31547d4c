// This file is obsolete. The API server now handles socket connections directly.
// Keeping a placeholder to prevent build errors if it's imported somewhere unexpectedly.
import { NextResponse } from 'next/server';

export async function GET(req: Request) {
  return new NextResponse(
    JSON.stringify({ 
        message: "This endpoint is deprecated. Socket.IO connections should be made directly to the API server." 
    }), 
    {
        status: 410, // Gone
        headers: { 'Content-Type': 'application/json' },
    }
  );
}
