import { Router } from 'express';
import prisma from '../lib/db';
import { getApiDocs } from '../lib/swagger';

const router = Router();


router.get('/service-categories', async (request, res) => {
  try {
    const categories = await prisma.serviceCategory.findMany({
      orderBy: { Id: 'asc' },
    });
    res.json(categories);
  } catch (error) {
    console.error('[API /public/service-categories GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch service categories' });
  }
});

// Route to serve the generated Swagger documentation
router.get('/swagger.json', async (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(await getApiDocs());
});

export default router;
