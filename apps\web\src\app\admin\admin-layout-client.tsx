"use client";

import Link from 'next/link';
import { Navbar } from '@/components/layout/navbar';
import { Footer } from '@/components/layout/footer';
import { Separator } from '@/components/ui/separator';
import {
  Shield,
  Users,
  ListChecks,
  LayoutDashboardIcon,
  UserCheck,
  BarChart3,
  Menu
} from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { useState } from 'react';
import type { ExtendedSession } from '@repo/auth';

interface AdminLayoutClientProps {
  children: React.ReactNode;
  session: ExtendedSession;
}

export function AdminLayoutClient({ children, session }: AdminLayoutClientProps) {
  const { translate } = useLanguage();
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const adminName = session?.user?.name || translate(commonTranslations, 'adminPanelUser');
  const adminEmail = session?.user?.email || "<EMAIL>";

  const adminNavLinks = [
    { href: '/admin', labelKey: 'adminDashboardNav', icon: LayoutDashboardIcon },
    { href: '/admin/analytics', labelKey: 'adminAnalyticsNav', icon: BarChart3 },
    { href: '/admin/users', labelKey: 'adminUsersNav', icon: Users },
    { href: '/admin/services', labelKey: 'adminServicesNav', icon: ListChecks },
    { href: '/admin/provider-requests', labelKey: 'adminProviderRequestsNav', icon: UserCheck },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <div className="flex flex-1">
        {/* Desktop Sidebar */}
        <div className="hidden md:block w-64 flex-shrink-0">
          <div className="sticky top-16 h-[calc(100vh-4rem)] bg-sidebar border-r border-border flex flex-col">
            {/* Sidebar Header */}
            <div className="border-b border-border p-4 flex-shrink-0">
              <div className="flex items-center space-x-3">
                <div className="bg-destructive text-destructive-foreground rounded-full w-10 h-10 flex items-center justify-center text-sm font-semibold">
                  <Shield className="w-5 h-5"/>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-semibold text-sidebar-foreground truncate">{adminName}</p>
                  <p className="text-xs text-sidebar-foreground/70 truncate">{adminEmail}</p>
                </div>
              </div>
            </div>

            {/* Sidebar Content */}
            <div className="flex-1 overflow-y-auto p-2">
              <nav className="space-y-1">
                {adminNavLinks.map((link) => {
                  const isActive = pathname === link.href;
                  const IconComponent = link.icon;

                  return (
                    <Link
                      key={link.href}
                      href={link.href}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md transition-colors",
                        isActive
                          ? "bg-sidebar-accent text-sidebar-accent-foreground font-semibold"
                          : "text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground"
                      )}
                    >
                      <IconComponent className="w-4 h-4" />
                      <span>{translate(commonTranslations, link.labelKey as keyof typeof commonTranslations)}</span>
                    </Link>
                  );
                })}
              </nav>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex flex-col flex-1">
          {/* Mobile Sidebar Trigger */}
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 md:hidden">
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-64 p-0">
                <SheetHeader className="p-4 border-b">
                  <SheetTitle className="text-left">
                    <div className="flex items-center space-x-3">
                      <div className="bg-destructive text-destructive-foreground rounded-full w-10 h-10 flex items-center justify-center text-sm font-semibold">
                        <Shield className="w-5 h-5"/>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-semibold text-foreground truncate">{adminName}</p>
                        <p className="text-xs text-muted-foreground truncate">{adminEmail}</p>
                      </div>
                    </div>
                  </SheetTitle>
                </SheetHeader>
                <div className="p-2">
                  <nav className="space-y-1">
                    {adminNavLinks.map((link) => {
                      const isActive = pathname === link.href;
                      const IconComponent = link.icon;

                      return (
                        <Link
                          key={link.href}
                          href={link.href}
                          onClick={() => setMobileMenuOpen(false)}
                          className={cn(
                            "flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md transition-colors",
                            isActive
                              ? "bg-primary/10 text-primary font-semibold"
                              : "text-foreground hover:bg-muted hover:text-foreground"
                          )}
                        >
                          <IconComponent className="w-4 h-4" />
                          <span>{translate(commonTranslations, link.labelKey as keyof typeof commonTranslations)}</span>
                        </Link>
                      );
                    })}
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
            <Separator orientation="vertical" className="mr-2 h-4" />
            <h1 className="font-semibold">{translate(commonTranslations, 'adminPanel')}</h1>
          </header>

          {/* Page Content with Independent Scrolling */}
          <main className="flex-1 overflow-y-auto bg-background">
            <div className="p-6">
              {children}
            </div>
          </main>
        </div>
      </div>

      <Footer />
    </div>
  );
}
