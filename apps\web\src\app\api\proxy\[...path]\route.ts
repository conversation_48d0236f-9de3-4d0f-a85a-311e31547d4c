import { type NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import jwt from 'jsonwebtoken';
import apiFetch from '@/lib/api-client';

async function handler(req: NextRequest) {
  const path = req.nextUrl.pathname.replace('/api/proxy', '');
  const search = req.nextUrl.search; // Get the query string, e.g., "?clientId=2"
  const endpointWithQuery = `${path}${search}`; // Combine them

  // 1. Decodează token-ul de sesiune NextAuth (JWE) pentru a obține datele utilizatorului
  const sessionToken = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  
  let internalApiJwt: string | null = null;
  
  // 2. Dacă utilizatorul este logat, creează un NOU token, simplu (JWT), pentru API-ul intern
  if (sessionToken) {
    const payload = {
      id: sessionToken.id,
      roles: sessionToken.roles,
      isAdmin: sessionToken.isAdmin,
      // Poți adăuga aici și alte date din sesiune de care API-ul ar putea avea nevoie
    };

    // Semnează noul token cu același secret
    internalApiJwt = jwt.sign(
      payload,
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '5m' } // Un token cu viață scurtă, suficient pentru o călătorie a cererii
    );
  }

  try {
    // 3. Pasează noul JWT (sau null dacă utilizatorul nu e logat) către helper-ul apiFetch
    const apiResponse = await apiFetch(endpointWithQuery, { // Use the full path with query
      method: req.method,
      body: req.method !== 'GET' ? await req.text() : undefined,
      jwt: internalApiJwt, 
    });

    const data = await apiResponse.json();
    return NextResponse.json(data, { status: apiResponse.status });

  } catch (error) {
    console.error(`[API Proxy] Error for path ${endpointWithQuery}:`, error);
    return NextResponse.json({ message: 'API Proxy Error' }, { status: 500 });
  }
}

export { handler as GET, handler as POST, handler as PUT, handler as DELETE, handler as PATCH };
