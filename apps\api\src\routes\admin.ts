
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { UserRole, ServiceStatus, ProviderRegistrationRequestStatus, BookingStatus, Prisma, NotificationType, ServiceCategorySlug } from '@prisma/client';
import type { AuthenticatedRequest } from '../middleware/auth';
import { sendProviderRequestApprovedEmailToUser, sendProviderRequestRejectedEmailToUser } from '../services/email-service';
// Define the interface locally since the web import is not available in API
interface ServiceRequestDetailForAdmin {
  categoryId: number;
  serviceCategorySlug: ServiceCategorySlug;
  experienceYears: number | string;
  description: string;
  availabilityWeekdays?: boolean;
  availabilityWeekends?: boolean;
  availabilityEvenings?: boolean;
  LocationValue?: string;
  [key: string]: any;
}


const router = Router();

// Middleware to check for Admin role for all routes in this file
router.use((req: AuthenticatedRequest, res: Response, next) => {
  if (!req.user?.isAdmin) {
    return res.status(403).json({ message: 'Forbidden: Access is restricted to administrators.' });
  }
  next();
});

router.get('/stats', async (req, res) => {
  try {
    const totalUsers = await prisma.user.count();
    const totalProviders = await prisma.user.count({ where: { UserRoles: { some: { Role: { Name: UserRole.Provider } } } } });
    const totalActiveServices = await prisma.advertisedService.count({ where: { Status: ServiceStatus.Activ } });
    const totalServices = await prisma.advertisedService.count();
    const pendingProviderRequests = await prisma.providerRegistrationRequest.count({ where: { Status: ProviderRegistrationRequestStatus.Pending } });

    res.json({ totalUsers, totalProviders, totalActiveServices, totalServices, pendingProviderRequests });
  } catch (error) {
    console.error('[API /admin/stats GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch admin stats' });
  }
});

// Analytics endpoints for dashboard

// Overview analytics endpoint
router.get('/analytics/overview', async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    let dateFilter: Date;

    switch (period) {
      case '7d':
        dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        dateFilter = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        dateFilter = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get current totals
    const totalUsers = await prisma.user.count();
    const totalProviders = await prisma.user.count({
      where: { UserRoles: { some: { Role: { Name: UserRole.Provider } } } }
    });
    const activeServices = await prisma.advertisedService.count({
      where: { Status: ServiceStatus.Activ }
    });
    const totalBookings = await prisma.booking.count();

    // Get previous period data for growth calculation
    const previousPeriodFilter = new Date(dateFilter.getTime() - (Date.now() - dateFilter.getTime()));

    const previousUsers = await prisma.user.count({
      where: { CreatedAt: { gte: previousPeriodFilter, lt: dateFilter } }
    });
    const currentUsers = await prisma.user.count({
      where: { CreatedAt: { gte: dateFilter } }
    });

    const previousProviders = await prisma.user.count({
      where: {
        CreatedAt: { gte: previousPeriodFilter, lt: dateFilter },
        UserRoles: { some: { Role: { Name: UserRole.Provider } } }
      }
    });
    const currentProviders = await prisma.user.count({
      where: {
        CreatedAt: { gte: dateFilter },
        UserRoles: { some: { Role: { Name: UserRole.Provider } } }
      }
    });

    const previousServices = await prisma.advertisedService.count({
      where: { CreatedAt: { gte: previousPeriodFilter, lt: dateFilter } }
    });
    const currentServices = await prisma.advertisedService.count({
      where: { CreatedAt: { gte: dateFilter } }
    });

    const previousBookings = await prisma.booking.count({
      where: { CreatedAt: { gte: previousPeriodFilter, lt: dateFilter } }
    });
    const currentBookings = await prisma.booking.count({
      where: { CreatedAt: { gte: dateFilter } }
    });

    // Calculate growth percentages
    const userGrowth = previousUsers > 0 ? ((currentUsers - previousUsers) / previousUsers) * 100 : 0;
    const providerGrowth = previousProviders > 0 ? ((currentProviders - previousProviders) / previousProviders) * 100 : 0;
    const serviceGrowth = previousServices > 0 ? ((currentServices - previousServices) / previousServices) * 100 : 0;
    const bookingGrowth = previousBookings > 0 ? ((currentBookings - previousBookings) / previousBookings) * 100 : 0;

    res.json({
      totalUsers,
      totalProviders,
      activeServices,
      totalBookings,
      userGrowth: Math.round(userGrowth * 10) / 10,
      providerGrowth: Math.round(providerGrowth * 10) / 10,
      serviceGrowth: Math.round(serviceGrowth * 10) / 10,
      bookingGrowth: Math.round(bookingGrowth * 10) / 10
    });
  } catch (error) {
    console.error('[API /admin/analytics/overview GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch overview analytics' });
  }
});

router.get('/analytics/user-registrations', async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    let dateFilter: Date;

    switch (period) {
      case '7d':
        dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        dateFilter = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        dateFilter = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    const registrations = await prisma.user.findMany({
      where: {
        CreatedAt: { gte: dateFilter }
      },
      select: {
        CreatedAt: true,
        UserRoles: {
          include: {
            Role: true
          }
        }
      },
      orderBy: { CreatedAt: 'asc' }
    });

    // Group by date and role
    const dailyStats: { [key: string]: { date: string; clients: number; providers: number; total: number } } = {};

    registrations.forEach(user => {
      const date = user.CreatedAt.toISOString().split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { date, clients: 0, providers: 0, total: 0 };
      }

      const isProvider = user.UserRoles.some(ur => ur.Role.Name === UserRole.Provider);
      if (isProvider) {
        dailyStats[date].providers++;
      } else {
        dailyStats[date].clients++;
      }
      dailyStats[date].total++;
    });

    res.json(Object.values(dailyStats));
  } catch (error) {
    console.error('[API /admin/analytics/user-registrations GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch user registration analytics' });
  }
});

router.get('/analytics/active-users', async (req, res) => {
  try {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Note: This assumes you have a LastLoginAt field or similar tracking
    // For now, we'll use CreatedAt as a proxy for activity
    const todayActive = await prisma.user.count({
      where: { CreatedAt: { gte: today } }
    });

    const weekActive = await prisma.user.count({
      where: { CreatedAt: { gte: thisWeek } }
    });

    const monthActive = await prisma.user.count({
      where: { CreatedAt: { gte: thisMonth } }
    });

    res.json({
      today: todayActive,
      thisWeek: weekActive,
      thisMonth: monthActive
    });
  } catch (error) {
    console.error('[API /admin/analytics/active-users GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch active users analytics' });
  }
});

router.get('/analytics/service-categories', async (req, res) => {
  try {
    const categoryStats = await prisma.advertisedService.groupBy({
      by: ['ServiceCategorySlug'],
      _count: {
        Id: true
      },
      orderBy: {
        _count: {
          Id: 'desc'
        }
      }
    });

    const categories = await prisma.serviceCategory.findMany();
    const categoryMap = categories.reduce((acc, cat) => {
      acc[cat.Slug] = cat.NameKey;
      return acc;
    }, {} as { [key: string]: string });

    const result = categoryStats.map(stat => ({
      category: categoryMap[stat.ServiceCategorySlug] || stat.ServiceCategorySlug,
      slug: stat.ServiceCategorySlug,
      count: stat._count.Id
    }));

    res.json(result);
  } catch (error) {
    console.error('[API /admin/analytics/service-categories GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch service category analytics' });
  }
});

router.get('/analytics/provider-requests', async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    let dateFilter: Date;

    switch (period) {
      case '7d':
        dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        dateFilter = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        dateFilter = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    const requests = await prisma.providerRegistrationRequest.findMany({
      where: {
        RequestDate: { gte: dateFilter }
      },
      select: {
        RequestDate: true,
        Status: true
      },
      orderBy: { RequestDate: 'asc' }
    });

    // Group by date and status
    const dailyStats: { [key: string]: { date: string; pending: number; approved: number; rejected: number; total: number } } = {};

    requests.forEach(request => {
      const date = request.RequestDate.toISOString().split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { date, pending: 0, approved: 0, rejected: 0, total: 0 };
      }

      switch (request.Status) {
        case ProviderRegistrationRequestStatus.Pending:
          dailyStats[date].pending++;
          break;
        case ProviderRegistrationRequestStatus.Approved:
          dailyStats[date].approved++;
          break;
        case ProviderRegistrationRequestStatus.Rejected:
          dailyStats[date].rejected++;
          break;
      }
      dailyStats[date].total++;
    });

    res.json(Object.values(dailyStats));
  } catch (error) {
    console.error('[API /admin/analytics/provider-requests GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch provider request analytics' });
  }
});

router.get('/analytics/bookings', async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    let dateFilter: Date;

    switch (period) {
      case '7d':
        dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        dateFilter = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        dateFilter = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    const bookings = await prisma.booking.findMany({
      where: {
        CreatedAt: { gte: dateFilter }
      },
      select: {
        CreatedAt: true,
        Status: true,
        AdvertisedService: {
          select: {
            ServiceCategorySlug: true
          }
        }
      },
      orderBy: { CreatedAt: 'asc' }
    });

    // Group by date
    const dailyStats: { [key: string]: { date: string; pending: number; confirmed: number; completed: number; cancelled: number; total: number } } = {};

    bookings.forEach(booking => {
      const date = booking.CreatedAt.toISOString().split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { date, pending: 0, confirmed: 0, completed: 0, cancelled: 0, total: 0 };
      }

      switch (booking.Status) {
        case 'Pending':
          dailyStats[date].pending++;
          break;
        case 'Confirmed':
          dailyStats[date].confirmed++;
          break;
        case 'Completed':
          dailyStats[date].completed++;
          break;
        case 'Cancelled':
          dailyStats[date].cancelled++;
          break;
      }
      dailyStats[date].total++;
    });

    res.json(Object.values(dailyStats));
  } catch (error) {
    console.error('[API /admin/analytics/bookings GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch booking analytics' });
  }
});

router.get('/analytics/top-providers', async (req, res) => {
  try {
    const topProviders = await prisma.user.findMany({
      where: {
        UserRoles: {
          some: {
            Role: {
              Name: UserRole.Provider
            }
          }
        }
      },
      select: {
        Id: true,
        FullName: true,
        Email: true,
        _count: {
          select: {
            BookingsAsProvider: true,
            AdvertisedServices: {
              where: {
                Status: ServiceStatus.Activ
              }
            }
          }
        }
      },
      orderBy: {
        BookingsAsProvider: {
          _count: 'desc'
        }
      },
      take: 10
    });

    const result = topProviders.map(provider => ({
      id: provider.Id,
      name: provider.FullName || 'N/A',
      email: provider.Email,
      bookingsCount: provider._count.BookingsAsProvider,
      activeServicesCount: provider._count.AdvertisedServices
    }));

    res.json(result);
  } catch (error) {
    console.error('[API /admin/analytics/top-providers GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch top providers analytics' });
  }
});

// User engagement analytics endpoint
// Provider performance analytics endpoint
router.get('/analytics/provider-performance', async (req, res) => {
  try {
    // Get provider approval rate
    const totalProviderRequests = await prisma.providerRegistrationRequest.count();
    const approvedRequests = await prisma.providerRegistrationRequest.count({
      where: { Status: ProviderRegistrationRequestStatus.Approved }
    });
    const approvalRate = totalProviderRequests > 0 ? (approvedRequests / totalProviderRequests) * 100 : 0;

    // Get average provider rating
    const reviews = await prisma.review.findMany({
      select: { Rating: true }
    });
    const averageRating = reviews.length > 0
      ? reviews.reduce((sum, review) => sum + review.Rating, 0) / reviews.length
      : 0;

    // Get average response time (using booking creation to confirmation as proxy)
    const confirmedBookings = await prisma.booking.findMany({
      where: { Status: BookingStatus.Confirmed },
      select: { CreatedAt: true, UpdatedAt: true }
    });

    let averageResponseTime = 0;
    if (confirmedBookings.length > 0) {
      const totalResponseTime = confirmedBookings.reduce((sum, booking) => {
        return sum + (booking.UpdatedAt.getTime() - booking.CreatedAt.getTime());
      }, 0);
      averageResponseTime = totalResponseTime / confirmedBookings.length / (1000 * 60 * 60); // Convert to hours
    }

    res.json({
      approvalRate: Math.round(approvalRate * 10) / 10,
      averageRating: Math.round(averageRating * 10) / 10,
      averageResponseTime: Math.round(averageResponseTime * 10) / 10
    });
  } catch (error) {
    console.error('[API /admin/analytics/provider-performance GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch provider performance analytics' });
  }
});

router.get('/analytics/user-engagement', async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    let dateFilter: Date;

    switch (period) {
      case '7d':
        dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        dateFilter = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        dateFilter = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get engagement metrics
    const totalMessages = await prisma.chatMessage.count({
      where: { CreatedAt: { gte: dateFilter } }
    });

    const totalBookings = await prisma.booking.count({
      where: { CreatedAt: { gte: dateFilter } }
    });

    const totalReviews = await prisma.review.count({
      where: { CreatedAt: { gte: dateFilter } }
    });

    const totalServices = await prisma.advertisedService.count({
      where: { CreatedAt: { gte: dateFilter } }
    });

    // Get active users (users who have created bookings, messages, or services)
    const activeUserIds = new Set();

    const usersWithBookings = await prisma.booking.findMany({
      where: { CreatedAt: { gte: dateFilter } },
      select: { ClientId: true, ProviderId: true }
    });

    usersWithBookings.forEach(booking => {
      activeUserIds.add(booking.ClientId);
      activeUserIds.add(booking.ProviderId);
    });

    const usersWithMessages = await prisma.chatMessage.findMany({
      where: { CreatedAt: { gte: dateFilter } },
      select: { SenderId: true }
    });

    usersWithMessages.forEach(message => {
      activeUserIds.add(message.SenderId);
    });

    const usersWithServices = await prisma.advertisedService.findMany({
      where: { CreatedAt: { gte: dateFilter } },
      select: { ProviderId: true }
    });

    usersWithServices.forEach(service => {
      activeUserIds.add(service.ProviderId);
    });

    res.json({
      totalMessages,
      totalBookings,
      totalReviews,
      totalServices,
      activeUsers: activeUserIds.size
    });
  } catch (error) {
    console.error('[API /admin/analytics/user-engagement GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch user engagement analytics' });
  }
});

router.get('/analytics/recent-activity', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;

    // Get recent user registrations
    const recentUsers = await prisma.user.findMany({
      select: {
        Id: true,
        FullName: true,
        Email: true,
        CreatedAt: true,
        UserRoles: {
          include: {
            Role: true
          }
        }
      },
      orderBy: { CreatedAt: 'desc' },
      take: Math.floor(limit / 4)
    });

    // Get recent bookings
    const recentBookings = await prisma.booking.findMany({
      select: {
        Id: true,
        CreatedAt: true,
        Status: true,
        Client: {
          select: {
            FullName: true
          }
        },
        AdvertisedService: {
          select: {
            ServiceName: true
          }
        }
      },
      orderBy: { CreatedAt: 'desc' },
      take: Math.floor(limit / 4)
    });

    // Get recent services
    const recentServices = await prisma.advertisedService.findMany({
      select: {
        Id: true,
        ServiceName: true,
        CreatedAt: true,
        Status: true,
        Provider: {
          select: {
            FullName: true
          }
        }
      },
      orderBy: { CreatedAt: 'desc' },
      take: Math.floor(limit / 4)
    });

    // Get recent provider requests
    const recentProviderRequests = await prisma.providerRegistrationRequest.findMany({
      select: {
        Id: true,
        RequestDate: true,
        Status: true,
        User: {
          select: {
            FullName: true,
            Email: true
          }
        }
      },
      orderBy: { RequestDate: 'desc' },
      take: Math.floor(limit / 4)
    });

    // Combine and format all activities
    const activities = [
      ...recentUsers.map(user => ({
        id: `user-${user.Id}`,
        type: 'user_registration',
        title: 'New User Registration',
        description: `${user.FullName || user.Email} registered as ${user.UserRoles.map(ur => ur.Role.Name).join(', ')}`,
        timestamp: user.CreatedAt,
        metadata: { userId: user.Id, email: user.Email }
      })),
      ...recentBookings.map(booking => ({
        id: `booking-${booking.Id}`,
        type: 'booking_created',
        title: 'New Booking',
        description: `${booking.Client.FullName} booked ${booking.AdvertisedService.ServiceName}`,
        timestamp: booking.CreatedAt,
        metadata: { bookingId: booking.Id, status: booking.Status }
      })),
      ...recentServices.map(service => ({
        id: `service-${service.Id}`,
        type: 'service_created',
        title: 'New Service',
        description: `${service.Provider.FullName} created service: ${service.ServiceName}`,
        timestamp: service.CreatedAt,
        metadata: { serviceId: service.Id, status: service.Status }
      })),
      ...recentProviderRequests.map(request => ({
        id: `provider-request-${request.Id}`,
        type: 'provider_request',
        title: 'Provider Request',
        description: `${request.User.FullName || request.User.Email} submitted provider request`,
        timestamp: request.RequestDate,
        metadata: { requestId: request.Id, status: request.Status }
      }))
    ];

    // Sort by timestamp and limit
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    res.json(activities.slice(0, limit));
  } catch (error) {
    console.error('[API /admin/analytics/recent-activity GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch recent activity' });
  }
});

router.get('/users', async (req, res) => {
  try {
    const { page = '1', limit = '10', role, search } = req.query;
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    const where: Prisma.UserWhereInput = {};
    if (role && typeof role === 'string' && ['Admin', 'Provider', 'Client'].includes(role)) {
      where.UserRoles = { some: { Role: { Name: role as UserRole } } };
    }
    if (search && typeof search === 'string') {
      where.OR = [
        { FullName: { contains: search, mode: 'insensitive' } },
        { Email: { contains: search, mode: 'insensitive' } },
      ];
    }

    const totalUsers = await prisma.user.count({ where });
    const users = await prisma.user.findMany({
      where,
      include: {
        UserRoles: {
          include: {
            Role: true
          }
        }
      },
      orderBy: { CreatedAt: 'desc' },
      skip,
      take: limitNum,
    });

    const formattedUsers = users.map(user => ({
      id: user.Id,
      fullName: user.FullName,
      email: user.Email,
      avatarUrl: user.AvatarUrl,
      roles: user.UserRoles.map(userRole => userRole.Role.Name),
      createdAt: user.CreatedAt.toISOString(),
    }));
    
    res.json({ 
        users: formattedUsers,
        totalPages: Math.ceil(totalUsers / limitNum),
        totalUsers,
        currentPage: pageNum,
    });
  } catch (error) {
    console.error('[API /admin/users GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch users' });
  }
});

// GET all services for admin view with filtering and pagination
router.get('/services', async (req, res) => {
    try {
        const { page = '1', limit = '10', status, category, search } = req.query;
        const pageNum = parseInt(page as string, 10);
        const limitNum = parseInt(limit as string, 10);
        const skip = (pageNum - 1) * limitNum;

        const where: Prisma.AdvertisedServiceWhereInput = {};

        if (status && typeof status === 'string' && ['Activ', 'Inactiv', 'PendingReview', 'Rejected'].includes(status)) {
            where.Status = status as ServiceStatus;
        }
        if (category && typeof category === 'string' && Object.values(ServiceCategorySlug).includes(category as ServiceCategorySlug)) {
            where.ServiceCategorySlug = category as ServiceCategorySlug;
        }
        if (search && typeof search === 'string') {
            where.OR = [
                { ServiceName: { contains: search, mode: 'insensitive' } },
                { Provider: { FullName: { contains: search, mode: 'insensitive' } } },
            ];
        }

        const totalServices = await prisma.advertisedService.count({ where });
        const services = await prisma.advertisedService.findMany({
            where,
            include: {
                Provider: { select: { FullName: true } },
                Category: { select: { NameKey: true, Slug: true } },
            },
            orderBy: { CreatedAt: 'desc' },
            skip,
            take: limitNum,
        });

        res.json({ 
            services,
            totalPages: Math.ceil(totalServices / limitNum),
            currentPage: pageNum,
            totalServices,
        });
    } catch (error) {
        console.error('[API /admin/services GET] Error:', error);
        res.status(500).json({ message: 'Failed to fetch services' });
    }
});


// POST to update a service's status (approve/reject)
router.post('/services/update-status', async (req, res) => {
    const { serviceId, status } = req.body;
    if (!serviceId || !status || !['Activ', 'Rejected'].includes(status)) {
        return res.status(400).json({ message: 'Service ID and a valid status (Activ, Rejected) are required.' });
    }

    try {
        const updatedService = await prisma.advertisedService.update({
            where: { Id: serviceId },
            data: { Status: status },
        });

        // Notify the provider about the status change
        await prisma.notification.create({
            data: {
                UserId: updatedService.ProviderId,
                Message: `Statusul serviciului tău "${updatedService.ServiceName}" a fost actualizat la: ${status}.`,
                Link: '/dashboard/provider/services',
                Type: 'ServiceStatusChanged',
            },
        });

        res.json({ success: true, service: updatedService });
    } catch (error) {
        console.error('[API /admin/services/update-status POST] Error:', error);
        res.status(500).json({ message: 'Failed to update service status' });
    }
});


// GET provider registration requests with filters and pagination
router.get('/provider-requests', async (req, res) => {
    try {
        const {
            status = 'Pending',
            page = '1',
            limit = '10',
            search = ''
        } = req.query;

        const pageNum = parseInt(page as string);
        const limitNum = parseInt(limit as string);
        const offset = (pageNum - 1) * limitNum;

        // Build where clause
        const whereClause: any = {};

        // Status filter
        if (status === 'All') {
            // No status filter - show all requests
        } else if (['Pending', 'Approved', 'Rejected'].includes(status as string)) {
            whereClause.Status = status as ProviderRegistrationRequestStatus;
        } else {
            whereClause.Status = ProviderRegistrationRequestStatus.Pending;
        }

        // Search filter
        if (search) {
            whereClause.OR = [
                { UserName: { contains: search as string, mode: 'insensitive' } },
                { UserEmail: { contains: search as string, mode: 'insensitive' } },
                { AdminNotes: { contains: search as string, mode: 'insensitive' } }
            ];
        }

        // Get total count for pagination
        const totalCount = await prisma.providerRegistrationRequest.count({
            where: whereClause
        });

        // Get requests with pagination
        const requests = await prisma.providerRegistrationRequest.findMany({
            where: whereClause,
            include: {
                User: {
                    select: {
                        Id: true,
                        FullName: true,
                        Email: true
                    }
                },
                PendingServices: {
                    include: {
                        Category: true,
                        NannyServiceDetails: true,
                        ElderCareServiceDetails: true,
                        CleaningServiceDetails: true,
                        TutoringServiceDetails: true,
                        CookingServiceDetails: true,
                    }
                }
            },
            orderBy: {
                RequestDate: 'desc',
            },
            skip: offset,
            take: limitNum,
        });

        const totalPages = Math.ceil(totalCount / limitNum);

        res.json({
            requests,
            pagination: {
                currentPage: pageNum,
                totalPages,
                totalCount,
                limit: limitNum,
                hasNext: pageNum < totalPages,
                hasPrev: pageNum > 1
            }
        });
    } catch (error) {
        console.error('[API /admin/provider-requests GET] Error:', error);
        res.status(500).json({ message: 'Failed to fetch provider requests' });
    }
});

// GET individual provider registration request by ID
router.get('/provider-requests/:requestId', async (req, res) => {
    try {
        const { requestId } = req.params;

        if (!requestId) {
            return res.status(400).json({ message: 'Request ID is required' });
        }

        const request = await prisma.providerRegistrationRequest.findUnique({
            where: { Id: requestId },
            include: {
                User: {
                    select: {
                        Id: true,
                        FullName: true,
                        Email: true,
                        Phone: true,
                        Bio: true,
                        SpokenLanguages: true,
                        EmailVerified: true,
                        CreatedAt: true,
                        UpdatedAt: true,
                        AvatarUrl: true,
                        Addresses: {
                            select: {
                                Id: true,
                                Label: true,
                                Street: true,
                                City: true,
                                Region: true,
                                PostalCode: true,
                                Country: true,
                                IsDefault: true
                            }
                        }
                    }
                },
                PendingServices: {
                    include: {
                        Category: true,
                        NannyServiceDetails: true,
                        ElderCareServiceDetails: true,
                        CleaningServiceDetails: true,
                        TutoringServiceDetails: true,
                        CookingServiceDetails: true,
                    },
                    orderBy: {
                        CreatedAt: 'asc'
                    }
                }
            }
        });

        if (!request) {
            return res.status(404).json({ message: 'Provider request not found' });
        }

        // Ensure all services have their corresponding service details
        if (request.PendingServices) {
            for (const service of request.PendingServices) {
                // Create empty service details if they don't exist
                if (service.ServiceCategorySlug === 'Cooking' && !service.CookingServiceDetails) {
                    service.CookingServiceDetails = {
                        Id: '',
                        PendingServiceId: service.Id,
                        LocationValue: null,
                        PricePerHour: null,
                        PricePerDay: null,
                        PricePerMeal: null,
                        MinPortions: null,
                        PriceSubscriptionAmount: null,
                        PriceSubscriptionUnit: null,
                        PriceSubscriptionText: null,
                        SubscriptionDetails: null,
                        AvailabilityWeekdays: false,
                        AvailabilityWeekends: false,
                        AvailabilityEvenings: false,
                        ServiceMealPrep: false,
                        ServiceCatering: false,
                        ServiceSpecialDiet: false,
                        ServiceBaking: false,
                        ServiceGroceryShopping: false,
                        ServiceKitchenCleanup: false,
                        CuisineRomanian: false,
                        CuisineItalian: false,
                        CuisineFrench: false,
                        CuisineAsian: false,
                        CuisineVegetarian: false,
                        CuisineVegan: false,
                        CuisineOther: null,
                        ExtraOwnIngredients: false,
                        ExtraOwnTransport: false,
                        ExtraWeekendAvailable: false
                    };
                }

                if (service.ServiceCategorySlug === 'ElderCare' && !service.ElderCareServiceDetails) {
                    service.ElderCareServiceDetails = {
                        Id: '',
                        PendingServiceId: service.Id,
                        LocationValue: null,
                        PricePerHour: null,
                        PricePerDay: null,
                        ExperienceYears: null,
                        AvailabilityWeekdays: false,
                        AvailabilityWeekends: false,
                        AvailabilityEvenings: false,
                        AvailabilityFullTime: false,
                        AvailabilityPartTime: false,
                        AvailabilityOccasional: false,
                        ServicePersonalCare: false,
                        ServiceMedicalSupport: false,
                        ServiceCompanionship: false,
                        ServiceHousekeeping: false,
                        ServiceMeals: false,
                        ServiceTransport: false,
                        ServiceShopping: false,
                        ServiceMobility: false,
                        ExtraFirstAid: false,
                        ExtraMedicalTraining: false,
                        ExtraOwnTransport: false,
                        ExtraLanguages: null,
                        ExtraSpecialNeeds: false,
                        ExtraOvernightCare: false
                    };
                }

                if (service.ServiceCategorySlug === 'Tutoring' && !service.TutoringServiceDetails) {
                    service.TutoringServiceDetails = {
                        Id: '',
                        PendingServiceId: service.Id,
                        LocationValue: null,
                        PricePerHour: null,
                        PricePerDay: null,
                        AvailabilityWeekdays: false,
                        AvailabilityWeekends: false,
                        AvailabilityEvenings: false,
                        ServiceAfterSchool: false,
                        ServiceHomeworkHelp: false,
                        ServiceIndividualLessons: false,
                        Grades_1_4: false,
                        Grades_5_8: false,
                        Grades_9_12: false,
                        SubjectRomanian: false,
                        SubjectMath: false,
                        SubjectEnglish: false,
                        SubjectOther: null,
                        FormatOnline: false,
                        FormatOwnHome: false,
                        FormatChildHome: false,
                        ExtraGames: false,
                        ExtraSnack: false,
                        ExtraTransport: false,
                        ExtraSupervisedHomework: false
                    };
                }

                if (service.ServiceCategorySlug === 'Cleaning' && !service.CleaningServiceDetails) {
                    service.CleaningServiceDetails = {
                        Id: '',
                        PendingServiceId: service.Id,
                        LocationValue: null,
                        PricePerHour: null,
                        PricePerDay: null,
                        AvailabilityWeekdays: false,
                        AvailabilityWeekends: false,
                        AvailabilityEvenings: false,
                        AvailabilityFullTime: false,
                        AvailabilityPartTime: false,
                        AvailabilityOccasional: false,
                        ServiceRegularCleaning: false,
                        ServiceDeepCleaning: false,
                        ServiceWindowCleaning: false,
                        ServiceCarpetCleaning: false,
                        ServiceLaundry: false,
                        ServiceIroning: false,
                        ServiceOrganizing: false,
                        ServicePostConstruction: false,
                        ExtraOwnSupplies: false,
                        ExtraEcoFriendly: false,
                        ExtraOwnTransport: false,
                        ExtraInsured: false,
                        ExtraWeekendAvailable: false,
                        ExtraEmergencyService: false
                    };
                }

                if (service.ServiceCategorySlug === 'Nanny' && !service.NannyServiceDetails) {
                    service.NannyServiceDetails = {
                        Id: '',
                        PendingServiceId: service.Id,
                        LocationValue: null,
                        PricePerHour: null,
                        PricePerDay: null,
                        ExperienceYears: null,
                        AvailabilityWeekdays: false,
                        AvailabilityWeekends: false,
                        AvailabilityEvenings: false,
                        PreferredAge_0_2: false,
                        PreferredAge_3_6: false,
                        PreferredAge_7_plus: false,
                        AvailabilityFullTime: false,
                        AvailabilityPartTime: false,
                        AvailabilityOccasional: false,
                        ServiceBabysitting: false,
                        ServicePlaytime: false,
                        ServiceMeals: false,
                        ServiceBedtime: false,
                        ServiceEducational: false,
                        ServiceOutdoor: false,
                        ServiceTransport: false,
                        ServiceHousework: false,
                        ExtraFirstAid: false,
                        ExtraOwnTransport: false,
                        ExtraCooking: false,
                        ExtraLanguages: null,
                        ExtraSpecialNeeds: false,
                        ExtraOvernightCare: false
                    };
                }
            }
        }

        res.json({ request });
    } catch (error) {
        console.error('[API /admin/provider-requests/:requestId GET] Error:', error);
        res.status(500).json({ message: 'Failed to fetch provider request details' });
    }
});

// POST to update a provider request status (approve/reject)
router.post('/provider-requests/update', async (req: AuthenticatedRequest, res) => {
    const { requestId, action, adminNotes } = req.body; // action: 'approve' | 'reject'

    if (!requestId || !action || !['approve', 'reject'].includes(action)) {
        return res.status(400).json({ message: 'Request ID and a valid action (approve, reject) are required.' });
    }
    if (action === 'reject' && (!adminNotes || typeof adminNotes !== 'string' || !adminNotes.trim())) {
        return res.status(400).json({ message: 'Rejection reason (adminNotes) is required when rejecting.' });
    }
    
    try {
        const request = await prisma.providerRegistrationRequest.findUnique({
            where: { Id: requestId },
        });

        if (!request) {
            return res.status(404).json({ message: 'Provider request not found.' });
        }
        if (request.Status !== ProviderRegistrationRequestStatus.Pending) {
            return res.status(409).json({ message: `Request is already in '${request.Status}' status.` });
        }

        const user = await prisma.user.findUnique({
            where: { Id: request.UserId },
        });

        if (!user) {
            await prisma.providerRegistrationRequest.update({
                where: { Id: requestId },
                data: { Status: ProviderRegistrationRequestStatus.Rejected, AdminNotes: 'User account associated with this request no longer exists.' },
            });
            return res.status(404).json({ message: 'User associated with the request not found.' });
        }
        
        const adminsToNotify = await prisma.user.findMany({ where: { UserRoles: { some: { Role: { Name: UserRole.Admin } } } } });

        if (action === 'approve') {
            // Check if request is already approved
            if (request.Status === ProviderRegistrationRequestStatus.Approved) {
                return res.status(400).json({ message: 'Request is already approved' });
            }

            const providerRole = await prisma.role.findUnique({ where: { Name: UserRole.Provider } });
            if (!providerRole) {
                console.error("Critical: 'Provider' role not found in database.");
                return res.status(500).json({ message: 'Server configuration error: Provider role is missing.' });
            }

            // Get pending services for this request
            const pendingServices = await prisma.pendingService.findMany({
                where: { RequestId: requestId },
                include: {
                    Category: true,
                    NannyServiceDetails: true,
                    ElderCareServiceDetails: true,
                    CleaningServiceDetails: true,
                    TutoringServiceDetails: true,
                    CookingServiceDetails: true,
                }
            });

            console.log(`[Admin] Found ${pendingServices.length} pending services for request ${requestId}`);
            pendingServices.forEach(service => {
                console.log(`[Admin] Service: ${service.ServiceName} - Status: ${service.Status} - Category: ${service.ServiceCategorySlug}`);
            });

            if (pendingServices.length === 0) {
                console.error(`[Admin] ERROR: No pending services found for request ${requestId}`);
                return res.status(400).json({ message: 'No pending services found for this request.' });
            }

            await prisma.$transaction(async (tx) => {
                // 1. Grant Provider Role (only if not already assigned)
                const existingRole = await tx.userRoleJunction.findFirst({
                    where: {
                        UserId: user.Id,
                        RoleId: providerRole.Id
                    }
                });

                if (!existingRole) {
                    await tx.userRoleJunction.create({
                        data: {
                            UserId: user.Id,
                            RoleId: providerRole.Id
                        }
                    });
                    console.log(`[Admin] Granted Provider role to user ${user.Id}`);
                } else {
                    console.log(`[Admin] User ${user.Id} already has Provider role, skipping role assignment`);
                }

                // 2. Auto-approve all pending services when approving the overall request
                console.log(`[Admin] Auto-approving all pending services for request ${requestId}`);

                // First, check how many services are pending
                const pendingCount = await tx.pendingService.count({
                    where: {
                        RequestId: requestId,
                        Status: 'PendingReview'
                    }
                });
                console.log(`[Admin] Found ${pendingCount} services with PendingReview status to auto-approve`);

                const approvedServicesCount = await tx.pendingService.updateMany({
                    where: {
                        RequestId: requestId,
                        Status: 'PendingReview' // Only approve services that are still pending
                    },
                    data: {
                        Status: 'Approved',
                        AdminNotes: 'Auto-approved with overall request approval',
                        UpdatedAt: new Date()
                    }
                });
                console.log(`[Admin] Auto-approved ${approvedServicesCount.count} pending services (expected: ${pendingCount})`);

                // 3. Mark request as Approved
                console.log(`[Admin] Updating request ${requestId} status to Approved`);
                const updatedRequest = await tx.providerRegistrationRequest.update({
                    where: { Id: requestId },
                    data: { Status: ProviderRegistrationRequestStatus.Approved, AdminNotes: adminNotes || 'Approved by admin.' },
                });
                console.log(`[Admin] Request ${requestId} status updated to: ${updatedRequest.Status}`);

                // 4. Create a notification for the user
                await tx.notification.create({
                    data: {
                        UserId: user.Id,
                        Message: `Felicitări! Cererea ta de a deveni prestator a fost aprobată.`,
                        Link: '/dashboard/provider',
                        Type: "ProviderRequestApproved",
                    },
                });

                // 5. Get updated pending services after auto-approval
                const updatedPendingServices = await tx.pendingService.findMany({
                    where: { RequestId: requestId },
                    include: {
                        Category: true,
                        NannyServiceDetails: true,
                        ElderCareServiceDetails: true,
                        CleaningServiceDetails: true,
                        TutoringServiceDetails: true,
                        CookingServiceDetails: true,
                    }
                });

                // 6. Create the services for the new provider from approved pending services
                console.log(`[Admin] Processing ${updatedPendingServices.length} updated pending services`);
                let createdServicesCount = 0;

                for (const pendingService of updatedPendingServices) {
                    console.log(`[Admin] Processing service: ${pendingService.ServiceName} - Status: ${pendingService.Status} - Category: ${pendingService.ServiceCategorySlug}`);

                    if (pendingService.Status !== 'Approved') {
                        // Only create services for approved pending services
                        console.log(`[Admin] Skipping service ${pendingService.ServiceName} with status: ${pendingService.Status}`);
                        continue;
                    }

                    console.log(`[Admin] Creating live service for: ${pendingService.ServiceName} (Status: ${pendingService.Status})`);

                    try {

                        const newService = await tx.advertisedService.create({
                            data: {
                                ProviderId: user.Id,
                                CategoryId: pendingService.CategoryId,
                                ServiceCategorySlug: pendingService.ServiceCategorySlug,
                                ServiceName: pendingService.ServiceName,
                                Description: pendingService.Description,
                                Status: 'Activ',
                            }
                        });

                        console.log(`[Admin] Created AdvertisedService with ID: ${newService.Id} for provider ${user.Id}`);
                        createdServicesCount++;

                        const connectData = { AdvertisedService: { connect: { Id: newService.Id }}};

                    switch (pendingService.ServiceCategorySlug) {
                        case 'Nanny':
                            if (pendingService.NannyServiceDetails) {
                                await tx.nannyServiceDetails.create({ data: {
                                    ...connectData,
                                    ExperienceYears: pendingService.ExperienceYears,
                                    LocationId: pendingService.NannyServiceDetails.LocationId,
                                    PricePerHour: pendingService.NannyServiceDetails.PricePerHour ? parseFloat(pendingService.NannyServiceDetails.PricePerHour) : null,
                                    PricePerDay: pendingService.NannyServiceDetails.PricePerDay ? parseFloat(pendingService.NannyServiceDetails.PricePerDay) : null,
                                    AvailabilityWeekdays: pendingService.NannyServiceDetails.AvailabilityWeekdays,
                                    AvailabilityWeekends: pendingService.NannyServiceDetails.AvailabilityWeekends,
                                    AvailabilityEvenings: pendingService.NannyServiceDetails.AvailabilityEvenings,
                                    PreferredAge_0_2: pendingService.NannyServiceDetails.PreferredAge_0_2,
                                    PreferredAge_3_6: pendingService.NannyServiceDetails.PreferredAge_3_6,
                                    PreferredAge_7_plus: pendingService.NannyServiceDetails.PreferredAge_7_plus,
                                }});
                            }
                            break;
                        case 'ElderCare':
                            if (pendingService.ElderCareServiceDetails) {
                                await tx.elderCareServiceDetails.create({ data: {
                                    ...connectData,
                                    ExperienceYears: pendingService.ExperienceYears,
                                    LocationId: pendingService.ElderCareServiceDetails.LocationId,
                                    PricePerHour: pendingService.ElderCareServiceDetails.PricePerHour ? parseFloat(pendingService.ElderCareServiceDetails.PricePerHour) : null,
                                    PricePerDay: pendingService.ElderCareServiceDetails.PricePerDay ? parseFloat(pendingService.ElderCareServiceDetails.PricePerDay) : null,
                                    AvailabilityWeekdays: pendingService.ElderCareServiceDetails.AvailabilityWeekdays,
                                    AvailabilityWeekends: pendingService.ElderCareServiceDetails.AvailabilityWeekends,
                                    AvailabilityEvenings: pendingService.ElderCareServiceDetails.AvailabilityEvenings,
                                }});
                            }
                            break;
                        case 'Cleaning':
                            if (pendingService.CleaningServiceDetails) {
                                await tx.cleaningServiceDetails.create({ data: {
                                    ...connectData,
                                    ExperienceYears: pendingService.ExperienceYears,
                                    LocationId: pendingService.CleaningServiceDetails.LocationId,
                                    PricePerHour: pendingService.CleaningServiceDetails.PricePerHour ? parseFloat(pendingService.CleaningServiceDetails.PricePerHour) : null,
                                    PricePerDay: pendingService.CleaningServiceDetails.PricePerDay ? parseFloat(pendingService.CleaningServiceDetails.PricePerDay) : null,
                                    AvailabilityWeekdays: pendingService.CleaningServiceDetails.AvailabilityWeekdays,
                                    AvailabilityWeekends: pendingService.CleaningServiceDetails.AvailabilityWeekends,
                                    AvailabilityEvenings: pendingService.CleaningServiceDetails.AvailabilityEvenings,
                                }});
                            }
                            break;
                        case 'Tutoring':
                            if (pendingService.TutoringServiceDetails) {
                                await tx.tutoringServiceDetails.create({ data: {
                                    ...connectData,
                                    ExperienceYears: pendingService.ExperienceYears,
                                    LocationId: pendingService.TutoringServiceDetails.LocationId,
                                    PricePerHour: pendingService.TutoringServiceDetails.PricePerHour ? parseFloat(pendingService.TutoringServiceDetails.PricePerHour) : null,
                                    PricePerDay: pendingService.TutoringServiceDetails.PricePerDay ? parseFloat(pendingService.TutoringServiceDetails.PricePerDay) : null,
                                    AvailabilityWeekdays: pendingService.TutoringServiceDetails.AvailabilityWeekdays,
                                    AvailabilityWeekends: pendingService.TutoringServiceDetails.AvailabilityWeekends,
                                    AvailabilityEvenings: pendingService.TutoringServiceDetails.AvailabilityEvenings,
                                    Grades_1_4: pendingService.TutoringServiceDetails.Grades_1_4,
                                    Grades_5_8: pendingService.TutoringServiceDetails.Grades_5_8,
                                    Grades_9_12: pendingService.TutoringServiceDetails.Grades_9_12,
                                }});
                            }
                            break;
                        case 'Cooking':
                            if (pendingService.CookingServiceDetails) {
                                await tx.cookingServiceDetails.create({ data: {
                                    ...connectData,
                                    ExperienceYears: pendingService.ExperienceYears,
                                    LocationId: pendingService.CookingServiceDetails.LocationId,
                                    PricePerHour: pendingService.CookingServiceDetails.PricePerHour ? parseFloat(pendingService.CookingServiceDetails.PricePerHour) : null,
                                    PricePerDay: pendingService.CookingServiceDetails.PricePerDay ? parseFloat(pendingService.CookingServiceDetails.PricePerDay) : null,
                                    AvailabilityWeekdays: pendingService.CookingServiceDetails.AvailabilityWeekdays,
                                    AvailabilityWeekends: pendingService.CookingServiceDetails.AvailabilityWeekends,
                                    AvailabilityEvenings: pendingService.CookingServiceDetails.AvailabilityEvenings,
                                }});
                            }
                            break;
                        default: console.warn(`Unknown service slug for detail creation: ${pendingService.ServiceCategorySlug}`);
                    }

                    console.log(`[Admin] Successfully created live service and details for: ${pendingService.ServiceName}`);

                    } catch (error) {
                        console.error(`[Admin] ERROR creating service ${pendingService.ServiceName}:`, error);
                        throw error; // Re-throw to rollback transaction
                    }
                }

                console.log(`[Admin] Service creation completed. Created ${createdServicesCount} live services from ${updatedPendingServices.length} pending services`);
            });

            await sendProviderRequestApprovedEmailToUser(user.Email, user.FullName || 'Utilizator');

            res.json({
                success: true,
                message: 'Request approved successfully. User is now a provider and services are active.',
                userBecameProvider: true, // Flag to indicate session should be refreshed
                userId: user.Id
            });

        } else { // action === 'reject'
            await prisma.$transaction(async (tx) => {
                await tx.providerRegistrationRequest.update({
                    where: { Id: requestId },
                    data: { Status: ProviderRegistrationRequestStatus.Rejected, AdminNotes: adminNotes },
                });

                await tx.notification.create({
                    data: {
                        UserId: user.Id,
                        Message: `Cererea ta de prestator a fost respinsă. Motiv: ${adminNotes}`,
                        Link: '/register-provider',
                        Type: "ProviderRequestRejected",
                    },
                });
            });

            await sendProviderRequestRejectedEmailToUser(user.Email, user.FullName || 'Utilizator', adminNotes);
            
            res.json({ success: true, message: 'Request rejected successfully.' });
        }

    } catch (error) {
        console.error('[API /admin/provider-requests/update POST] Error:', error);
        if (error instanceof Prisma.PrismaClientValidationError) {
          console.error('Prisma Validation Error details:', error.message);
        }
        res.status(500).json({ message: 'Failed to update provider request status.' });
    }
});


export default router;
