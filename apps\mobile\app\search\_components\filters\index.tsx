
"use client";

import { useState, useMemo, useCallback } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from '@/components/ui/button-mobile';
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

// Import specific filter components
import { NannyFilters } from './nanny-filters';
import { ElderCareFilters } from './elder-care-filters';
import { CleaningFilters } from './cleaning-filters';
import { TutoringFilters } from './tutoring-filters';
import { CookingFilters } from './cooking-filters';
import { ScrollView } from 'react-native-gesture-handler';

const searchFiltersTranslations = {
  title: { ro: "Filtrează Rezultatele", ru: "Фильтровать результаты", en: "Filter Results" },
  locationLabel: { ro: "Locație", ru: "Местоположение", en: "Location" },
  priceRangeLabel: { ro: "Interval Preț (MDL)", ru: "Ценовой диапазон (MDL)", en: "Price Range (MDL)" },
  minRatingLabel: { ro: "Rating Minim", ru: "Минимальный рейтинг", en: "Minimum Rating" },
  minRatingPlaceholder: { ro: "Orice rating", ru: "Любой рейтинг", en: "Any rating" },
  ratingOption: { ro: "Peste {stars} stele", ru: "Более {stars} звезд", en: "Over {stars} stars" },
  availabilityLabel: { ro: "Disponibilitate", ru: "Доступность", en: "Availability" },
  applyButton: { ro: "Aplică Filtre", ru: "Применить фильтры", en: "Apply Filters" },
};

interface SearchFiltersProps {
  serviceType: string;
}

export function SearchFilters({ serviceType }: SearchFiltersProps) {
  const { translate } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Initialize state from URL params
  const [locationValue, setLocationValue] = useState(searchParams.get('location') || "all");
  const [minRating, setMinRating] = useState(Number(searchParams.get('minRating') || 0));
  
  const handleApplyFilters = useCallback(() => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('page', '1'); // Reset to first page on filter change

    // Location
    if (locationValue !== 'all') newParams.set('location', locationValue);
    else newParams.delete('location');

    // Rating
    if (minRating > 0) newParams.set('minRating', String(minRating));
    else newParams.delete('minRating');

    router.push(`${pathname}?${newParams.toString()}`);
  }, [locationValue, minRating, router, pathname, searchParams]);
  
  const renderSpecificFilters = () => {
    switch (serviceType) {
      case 'Nanny': return <NannyFilters />;
      case 'ElderCare': return <ElderCareFilters />;
      case 'Cleaning': return <CleaningFilters />;
      case 'Tutoring': return <TutoringFilters />;
      case 'Cooking': return <CookingFilters />;
      default: return null;
    }
  };

  return (
    <ScrollView style={styles.container}>
        <Text style={styles.title}>{translate(searchFiltersTranslations, 'title')}</Text>
        
        {/* Placeholder for Location Filter */}
        <View style={styles.filterGroup}>
            <Text style={styles.label}>{translate(searchFiltersTranslations, 'locationLabel')}</Text>
            <Text>Location filter will be here</Text>
        </View>

        {/* Placeholder for Rating Filter */}
        <View style={styles.filterGroup}>
            <Text style={styles.label}>{translate(searchFiltersTranslations, 'minRatingLabel')}</Text>
            <Text>Rating filter will be here</Text>
        </View>

        {renderSpecificFilters()}
        
        <Button onPress={handleApplyFilters} title={translate(searchFiltersTranslations, 'applyButton')} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
    container: {
        padding: 16,
        backgroundColor: 'white',
        borderRadius: 8,
        margin: 8,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    filterGroup: {
        marginBottom: 16,
    },
    label: {
        fontSize: 16,
        marginBottom: 8,
    }
});
