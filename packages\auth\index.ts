import NextAuth, { type NextAuthOptions, type User as NextAuthUser, type Session as NextAuthSession, type Profile as NextAuthProfile, type Account as NextAuthAccount } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import FacebookProvider from "next-auth/providers/facebook";
import CredentialsProvider from "next-auth/providers/credentials";
import { UserRole } from "@prisma/client";
import bcrypt from 'bcryptjs';

// Define interfaces for extended User and Session types
export interface ExtendedNextAuthUser extends NextAuthUser {
  id: string; // NextAuth uses string ID here
  roles?: UserRole[];
  isProvider?: boolean;
  isAdmin?: boolean;
  MustChangePassword?: boolean;
  SpokenLanguages?: string[];
  provider?: string;
  EmailVerified?: Date | null; // Use PascalCase to match Prisma schema
  Image?: string | null; // PascalCase as per Prisma schema
  bio?: string | null;
  phone?: string | null;
  createdAt?: string;
}

export interface ExtendedSession extends NextAuthSession {
  user: ExtendedNextAuthUser;
}

// Helper function to make API calls
const callAuthApi = async (endpoint: string, data?: any) => {
  const apiUrl = process.env.API_URL || "http://localhost:9001/api";
  const response = await fetch(`${apiUrl}/auth${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || `API call to ${endpoint} failed`);
  }
  return response.json();
};

export const authOptions: NextAuthOptions = {
  // adapter: PrismaAdapter(prisma), // Remove Prisma Adapter
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID as string,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET as string,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          console.log('[NextAuth Credentials] Email or password missing');
          return null;
        }
        console.log('[NextAuth Credentials] Attempting to authorize user:', credentials.email);
        
        try {
          const user = await callAuthApi('/user-by-email', { email: credentials.email });

          // Since the API returns the user with password, we can do the comparison here
          if (user && user.Password && bcrypt.compareSync(credentials.password, user.Password)) {
            console.log('[NextAuth Credentials] User authenticated successfully:', user.Email);
             // Map the API user object to the ExtendedNextAuthUser format
            return {
              id: String(user.Id), 
              email: user.Email,
              name: user.FullName,
              image: user.Image || user.AvatarUrl,
              roles: user.Roles.map((role: { Name: UserRole }) => role.Name), // Assuming Roles is an array of objects with a Name property
              isProvider: user.Roles.some((role: { Name: UserRole }) => role.Name === UserRole.Provider),
              isAdmin: user.Roles.some((role: { Name: UserRole }) => role.Name === UserRole.Admin),
              MustChangePassword: user.MustChangePassword,
              EmailVerified: user.EmailVerified, 
              SpokenLanguages: user.SpokenLanguages,
              Image: user.Image, 
            };
          }
        } catch (error) {
           console.error('[NextAuth Credentials] Error during authorization:', error);
        }

        console.log('[NextAuth Credentials] Authentication failed for:', credentials.email);
        return null;
      }
    })
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // This block only runs on initial sign-in to populate the token.
      if (user) {
        const internalUser = user as ExtendedNextAuthUser;
        token.id = internalUser.id;
        token.roles = internalUser.roles;
        token.isProvider = internalUser.isProvider;
        token.isAdmin = internalUser.isAdmin;
        token.MustChangePassword = internalUser.MustChangePassword;
        token.SpokenLanguages = internalUser.SpokenLanguages;
        token.EmailVerified = internalUser.EmailVerified;
        token.picture = internalUser.image;
        token.provider = account?.provider;
      }

      // Mark token as invalid if user doesn't exist, but don't return null
      // The session callback will handle the actual invalidation
      if (token.id) {
        try {
          const dbUser = await callAuthApi('/user-by-id', { id: token.id });
          if (!dbUser) {
            console.log(`[NextAuth JWT] User with ID ${token.id} no longer exists. Marking token as invalid.`);
            token.userExists = false; // Mark as invalid
          } else {
            token.userExists = true; // Mark as valid
          }
        } catch (error) {
          console.error(`[NextAuth JWT] Error validating user ${token.id}:`, error);
          token.userExists = false; // Mark as invalid on error
        }
      }

      return token;
    },
    
    async session({ session, token }): Promise<ExtendedSession> {
      const extendedSession = session as ExtendedSession;

      // If token is null or invalid, throw error to invalidate session
      if (!token || !token.id) {
        console.log(`[NextAuth Session] Invalid or missing token. Invalidating session.`);
        throw new Error('Invalid session token');
      }

      // Check if JWT callback marked the user as non-existent
      if (token.userExists === false) {
        console.log(`[NextAuth Session] User with ID ${token.id} no longer exists. Invalidating session.`);
        throw new Error('User no longer exists');
      }

      // If session.user doesn't exist, create a minimal structure to avoid null errors
      if (!extendedSession.user) {
        extendedSession.user = {} as ExtendedNextAuthUser;
      }

      // Fetch fresh user data for the session
      try {
        const dbUser = await callAuthApi('/user-by-id', { id: token.id });
        if (!dbUser) {
          console.log(`[NextAuth Session] User with ID ${token.id} no longer exists in database. Invalidating session.`);
          throw new Error('User no longer exists in database');
        }

        // Update session with fresh user data from database
        extendedSession.user.id = String(dbUser.Id);
        extendedSession.user.roles = dbUser.UserRoles?.map((ur: any) => ur.Role.Name) || [];
        extendedSession.user.isProvider = dbUser.UserRoles?.some((ur: any) => ur.Role.Name === UserRole.Provider) || false;
        extendedSession.user.isAdmin = dbUser.UserRoles?.some((ur: any) => ur.Role.Name === UserRole.Admin) || false;
        extendedSession.user.provider = token.provider as string | undefined;
        extendedSession.user.MustChangePassword = dbUser.MustChangePassword;
        extendedSession.user.SpokenLanguages = dbUser.SpokenLanguages;
        extendedSession.user.EmailVerified = dbUser.EmailVerified;
        extendedSession.user.name = dbUser.FullName;
        extendedSession.user.email = dbUser.Email;
        extendedSession.user.image = dbUser.Image || dbUser.AvatarUrl;
      } catch (error) {
        console.error(`[NextAuth Session] Error validating user ${token.id}:`, error);
        throw new Error('Error validating user session');
      }

      return extendedSession;
    },

    async signIn({ user, account, profile }) { 
      if (account?.provider === "google" || account?.provider === "facebook") {
        if (!user.email) {
          console.error("OAuth sign-in denied: Email not provided by provider.", profile);
          return false;
        }

        try {
          let dbUser = await callAuthApi('/user-by-email', { email: user.email });

          const oauthImageFromProfile = (profile as any)?.image || (profile as any)?.picture?.data?.url || (profile as any)?.picture;

          if (dbUser) { 
            const accountExists = dbUser.Accounts.some(
              (acc: any) => acc.Provider === account.provider && acc.ProviderAccountId === account.providerAccountId
            );
            if (!accountExists) {
               await callAuthApi('/create-account', {
                  UserId: dbUser.Id,
                  Type: account.type as string, 
                  Provider: account.provider,
                  ProviderAccountId: account.providerAccountId,
                  RefreshToken: account.refresh_token,
                  AccessToken: account.access_token,
                  ExpiresAt: account.expires_at,
                  TokenType: account.token_type,
                  Scope: account.scope,
                  IdToken: account.id_token,
                  SessionState: account.session_state as string | undefined,
                });
              console.log(`OAuth account ${account.provider} linked for existing user ${dbUser.Email}`);
            }
            
            if (oauthImageFromProfile && (dbUser.AvatarUrl !== oauthImageFromProfile || dbUser.Image !== oauthImageFromProfile)) {
              await callAuthApi('/update-user', {
                id: dbUser.Id,
                data: { AvatarUrl: oauthImageFromProfile, Image: oauthImageFromProfile }
              });
               // Update dbUser object to reflect the change immediately
              dbUser.AvatarUrl = oauthImageFromProfile;
              dbUser.Image = oauthImageFromProfile;
            }
          } else { 
            const clientRole = await callAuthApi('/role/client');
            if (!clientRole) {
              console.error("Critical: 'Client' role not found via API. Cannot assign role to new OAuth user.");
              return false; 
            }
            
            const newUserData = { 
              Email: user.email,
              FullName: user.name,
              AvatarUrl: oauthImageFromProfile, 
              Image: oauthImageFromProfile,       
              EmailVerified: new Date(), 
              MustChangePassword: false,
              SpokenLanguages: [], 
              Roles: {
                connect: [{ Id: clientRole.Id }], 
              },
              Accounts: { 
                create: [
                  { 
                    Type: account.type as string,
                    Provider: account.provider,
                    ProviderAccountId: account.providerAccountId,
                    RefreshToken: account.refresh_token,
                    AccessToken: account.access_token,
                    ExpiresAt: account.expires_at,
                    TokenType: account.token_type,
                    Scope: account.scope,
                    IdToken: account.id_token,
                    SessionState: account.session_state as string | undefined,
                  },
                ],
              },
            };
            dbUser = await callAuthApi('/create-user', newUserData);
            console.log(`New OAuth user created: ${dbUser.Email} via ${account.provider}`);
          }
          
          if (dbUser) {
              const extendedUser = user as ExtendedNextAuthUser; 
              extendedUser.id = String(dbUser.Id);
              extendedUser.roles = dbUser.Roles.map((r: { Name: UserRole }) => r.Name);
              extendedUser.isProvider = dbUser.Roles.some((r: { Name: UserRole }) => r.Name === UserRole.Provider);
              extendedUser.isAdmin = dbUser.Roles.some((r: { Name: UserRole }) => r.Name === UserRole.Admin);
              extendedUser.MustChangePassword = dbUser.MustChangePassword;
              extendedUser.SpokenLanguages = dbUser.SpokenLanguages;
              extendedUser.EmailVerified = dbUser.EmailVerified; 
              extendedUser.provider = account.provider;
              extendedUser.image = dbUser.Image || dbUser.AvatarUrl;
          }
        } catch (error) {
             console.error('[NextAuth signIn] Error during OAuth sign-in:', error);
             return false; // Prevent sign-in on API error
        }
      } else if (account?.provider === 'credentials') {
        if (!(user as ExtendedNextAuthUser).provider) { 
            (user as ExtendedNextAuthUser).provider = 'credentials';
        }
         // For credentials provider, the user object is populated in the authorize callback
         // We just need to ensure the id is a string
        if (user.id) {
             user.id = String(user.id);
        }
      }
      return true; 
    },
  },
  pages: {
    signIn: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
};
