/*
  Warnings:

  - You are about to drop the column `emailVerified` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `image` on the `User` table. All the data in the column will be lost.
  - The primary key for the `sessions` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `expires` on the `sessions` table. All the data in the column will be lost.
  - You are about to drop the column `id` on the `sessions` table. All the data in the column will be lost.
  - You are about to drop the column `sessionToken` on the `sessions` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `sessions` table. All the data in the column will be lost.
  - You are about to drop the `accounts` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[SessionToken]` on the table `sessions` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `Expires` to the `sessions` table without a default value. This is not possible if the table is not empty.
  - The required column `Id` was added to the `sessions` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `SessionToken` to the `sessions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `UserId` to the `sessions` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "accounts" DROP CONSTRAINT "accounts_userId_fkey";

-- DropForeignKey
ALTER TABLE "sessions" DROP CONSTRAINT "sessions_userId_fkey";

-- DropIndex
DROP INDEX "sessions_sessionToken_key";

-- AlterTable
ALTER TABLE "User" DROP COLUMN "emailVerified",
DROP COLUMN "image",
ADD COLUMN     "EmailVerified" TIMESTAMP(3),
ADD COLUMN     "Image" TEXT,
ADD COLUMN     "SpokenLanguages" TEXT[] DEFAULT ARRAY[]::TEXT[];

-- AlterTable
ALTER TABLE "sessions" DROP CONSTRAINT "sessions_pkey",
DROP COLUMN "expires",
DROP COLUMN "id",
DROP COLUMN "sessionToken",
DROP COLUMN "userId",
ADD COLUMN     "Expires" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "Id" TEXT NOT NULL,
ADD COLUMN     "SessionToken" TEXT NOT NULL,
ADD COLUMN     "UserId" INTEGER NOT NULL,
ADD CONSTRAINT "sessions_pkey" PRIMARY KEY ("Id");

-- DropTable
DROP TABLE "accounts";

-- CreateTable
CREATE TABLE "Accounts" (
    "Id" TEXT NOT NULL,
    "UserId" INTEGER NOT NULL,
    "Type" TEXT NOT NULL,
    "Provider" TEXT NOT NULL,
    "ProviderAccountId" TEXT NOT NULL,
    "Refresh_token" TEXT,
    "Access_token" TEXT,
    "Expires_at" INTEGER,
    "Token_type" TEXT,
    "Scope" TEXT,
    "IdToken" TEXT,
    "Session_state" TEXT,

    CONSTRAINT "Accounts_pkey" PRIMARY KEY ("Id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Accounts_Provider_ProviderAccountId_key" ON "Accounts"("Provider", "ProviderAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_SessionToken_key" ON "sessions"("SessionToken");

-- AddForeignKey
ALTER TABLE "Accounts" ADD CONSTRAINT "Accounts_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;
