
import { Router, type Response } from 'express';
import prisma from '../lib/db';
// Location logic now uses database queries instead of hardcoded arrays
import { commonTranslations } from '@repo/translations';
import type { AdvertisedService as PrismaAdvertisedService, User as PrismaUser, ServiceCategory as PrismaServiceCategory, Review as PrismaReview, NannyServiceDetails, ElderCareServiceDetails, CleaningServiceDetails, TutoringServiceDetails, CookingServiceDetails, Prisma } from '@prisma/client';
import { ServiceCategorySlug, ServiceStatus, UserRole } from '@prisma/client';
import { type AuthenticatedRequest } from '../middleware/auth';
import type { CaregiverSearchResult } from '@repo/types';

const ITEMS_PER_PAGE = 6;

function translateApi(translations: Record<string, any>, key: string, lang: string = 'ro', fallback?: string): string {
  const translationSection = translations[key];
  if (typeof translationSection === 'object' && translationSection !== null) {
    return translationSection[lang] || translationSection['ro'] || fallback || key;
  }
  if (typeof translations[key] === 'string') {
      return translations[key] as string;
  }
  return fallback || key;
}

async function getLocationDisplayName(locationId?: number | null, lang: string = 'ro'): Promise<string> {
  if (!locationId) return translateApi(commonTranslations, 'availabilityNotSpecified', lang);

  try {
    const location = await prisma.location.findUnique({
      where: { Id: locationId, IsActive: true },
    });

    if (location) {
      // Use the stored Name field which already contains proper hierarchy
      // or translate using the translation key if needed
      if (lang !== 'ro') {
        return translateApi(commonTranslations, location.TranslationKey as keyof typeof commonTranslations, lang);
      }
      return location.Name;
    }
  } catch (error) {
    console.error('Error fetching location:', error);
  }

  return locationSlug;
}

function formatPriceRate(
    details: NannyServiceDetails | ElderCareServiceDetails | CleaningServiceDetails | TutoringServiceDetails | CookingServiceDetails | null,
    lang: string = 'ro'
): string {
    if (!details) return translateApi(commonTranslations, 'priceContactFor', lang);
    
    const parts: string[] = [];
    if (details.PricePerHour) parts.push(`${details.PricePerHour.toFixed(2)} MDL/oră`);
    if (details.PricePerDay) parts.push(`${details.PricePerDay.toFixed(2)} MDL/zi`);
    
    if ('PricePerMeal' in details && (details as CookingServiceDetails).PricePerMeal) {
        const cookingDetails = details as CookingServiceDetails;
        if (cookingDetails.PricePerMeal) { 
            let mealPricePart = `${cookingDetails.PricePerMeal.toFixed(2)} MDL/masă`;
            if (cookingDetails.MealDetails) {
                mealPricePart += ` (${cookingDetails.MealDetails})`;
            }
            parts.push(mealPricePart);
        }
    }

    if (details.PriceSubscriptionAmount && details.PriceSubscriptionUnit) {
        parts.push(`${details.PriceSubscriptionAmount.toFixed(2)} MDL/${details.PriceSubscriptionUnit}`);
    } else if (details.PriceSubscriptionText) {
        parts.push(details.PriceSubscriptionText);
    }
    
    if (details.SubscriptionDetails && (details.PriceSubscriptionAmount || details.PriceSubscriptionText)) {
      const lastPartIndex = parts.length -1;
      if (lastPartIndex >= 0) {
          parts[lastPartIndex] += ` (${details.SubscriptionDetails})`;
      } else { 
          parts.push(details.SubscriptionDetails);
      }
    }

    if (parts.length === 0) return translateApi(commonTranslations, 'priceContactFor', lang);
    return parts.join(' / ');
}

// Helper function to build service-specific where clauses
function buildServiceSpecificWhereClause(serviceType: string, filters: Record<string, any>): any {
  const serviceSpecificWhere: any = {};

  // Extract boolean filters (those with value 'true')
  const booleanFilters = Object.entries(filters)
    .filter(([key, value]) => value === 'true')
    .reduce((acc, [key, _]) => ({ ...acc, [key]: true }), {});

  if (Object.keys(booleanFilters).length === 0) {
    return null; // No service-specific filters applied
  }

  // Map service types to their detail table names
  const serviceDetailTables = {
    'Nanny': 'nannyServiceDetails',
    'ElderCare': 'elderCareServiceDetails',
    'Cleaning': 'cleaningServiceDetails',
    'Tutoring': 'tutoringServiceDetails',
    'Cooking': 'cookingServiceDetails'
  };

  const detailTableName = serviceDetailTables[serviceType as keyof typeof serviceDetailTables];
  if (detailTableName) {
    serviceSpecificWhere[detailTableName] = booleanFilters;
  }

  return serviceSpecificWhere;
}

const router = Router();

// Search for services
router.get('/', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      page: pageStr,
      limit: limitStr,
      lang = 'ro',
      serviceType,
      location,
      minPrice,
      maxPrice,
      minRating,
      sort,
      excludeProviderId,
      weekdays,
      weekends,
      evenings,
      // Service-specific filters will be extracted dynamically
      ...serviceSpecificFilters
    } = req.query;

    const page = parseInt(pageStr as string || '1', 10);
    const limit = parseInt(limitStr as string || String(ITEMS_PER_PAGE), 10);
    const skip = (page - 1) * limit;

    let whereClause: Prisma.AdvertisedServiceWhereInput = { Status: ServiceStatus.Activ };

    if (excludeProviderId) {
        const providerIdToExclude = parseInt(excludeProviderId as string, 10);
        if (!isNaN(providerIdToExclude)) {
            whereClause.ProviderId = { not: providerIdToExclude };
        }
    }

    if (serviceType && serviceType !== 'all') {
        whereClause.ServiceCategorySlug = serviceType as ServiceCategorySlug;

        // Add service-specific filters
        const serviceSpecificWhere = buildServiceSpecificWhereClause(serviceType as string, serviceSpecificFilters);
        if (serviceSpecificWhere) {
            whereClause = { ...whereClause, ...serviceSpecificWhere };
        }
    }
    
    const totalServicesCount = await prisma.advertisedService.count({ where: whereClause });
    const servicesFromDb = await prisma.advertisedService.findMany({
      where: whereClause,
      include: {
        Provider: { select: { Id: true, FullName: true, AvatarUrl: true, Bio: true, ReviewsAsProvider: { select: { Rating: true } } } },
        Category: { select: { NameKey: true } },
        NannyServiceDetails: true, ElderCareServiceDetails: true, CleaningServiceDetails: true, TutoringServiceDetails: true, CookingServiceDetails: true, 
      },
      orderBy: { CreatedAt: 'desc' }, 
      skip: skip,
      take: limit,
    });
    
    const caregivers: CaregiverSearchResult[] = await Promise.all(servicesFromDb.map(async service => {
        const reviews = service.Provider.ReviewsAsProvider;
        const rating = reviews.length > 0
            ? reviews.reduce((acc, review) => acc + review.Rating, 0) / reviews.length
            : 0;

        const getServiceDetails = (s: typeof service) => {
            switch (s.ServiceCategorySlug) {
                case 'Nanny': return s.NannyServiceDetails;
                case 'ElderCare': return s.ElderCareServiceDetails;
                case 'Cleaning': return s.CleaningServiceDetails;
                case 'Tutoring': return s.TutoringServiceDetails;
                case 'Cooking': return s.CookingServiceDetails;
                default: return null;
            }
        };

        const serviceDetails = getServiceDetails(service);
        const locationDisplayName = await getLocationDisplayName(serviceDetails?.LocationId, lang as string);

        return {
            id: service.Provider.Id,
            name: service.Provider.FullName || "Prestator Necunoscut",
            imageUrl: service.Provider.AvatarUrl,
            serviceType: service.Category ? translateApi(commonTranslations, service.Category.NameKey, lang as string) : "Nespecificat",
            location: locationDisplayName,
            rating: rating,
            reviewsCount: reviews.length,
            description: service.Description,
            priceRate: formatPriceRate(serviceDetails, lang as string),
            serviceIdForLink: service.Id,
        };
    }));

    return res.json({ caregivers, totalPages: Math.ceil(totalServicesCount / limit), currentPage: page, totalItems: totalServicesCount });
  } catch (error) {
    console.error(`[API /services GET] Error:`, error);
    return res.status(500).json({ message: 'API Error: Failed to fetch services' });
  }
});

// Get provider profile by ID
router.get('/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const providerUserIdNum = parseInt(req.params.id, 10);
    if (isNaN(providerUserIdNum)) {
        return res.status(400).json({ message: 'Invalid caregiver ID format' });
    }

    const providerUser = await prisma.user.findUnique({
        where: { Id: providerUserIdNum, Roles: { some: { Name: UserRole.Provider }}},
        include: {
            Roles: true, 
            AdvertisedServices: { where: { Status: ServiceStatus.Activ }, include: { 
                Category: { select: { NameKey: true, Slug: true, Id: true } }, 
                NannyServiceDetails: true, ElderCareServiceDetails: true, CleaningServiceDetails: true, TutoringServiceDetails: true, CookingServiceDetails: true,
            }},
            ReviewsAsProvider: { select: { Rating: true }}
        }
    });

    if (providerUser) {
        const providerInfo = {
            id: providerUser.Id,
            fullName: providerUser.FullName,
            email: providerUser.Email,
            avatarUrl: providerUser.AvatarUrl,
            bio: providerUser.Bio,
            phone: providerUser.Phone,
        };

        const servicesWithDetails = providerUser.AdvertisedServices.map(service => ({
            id: service.Id,
            providerId: service.ProviderId,
            categoryId: service.CategoryId,
            serviceCategorySlug: service.ServiceCategorySlug,
            serviceName: service.ServiceName,
            description: service.Description,
            status: service.Status,
            category: service.Category ? { 
                nameKey: service.Category.NameKey,
                slug: service.Category.Slug,
            } : null,
            nannyServiceDetails: service.NannyServiceDetails,
            elderCareServiceDetails: service.ElderCareServiceDetails,
            cleaningServiceDetails: service.CleaningServiceDetails,
            tutoringServiceDetails: service.TutoringServiceDetails,
            cookingServiceDetails: service.CookingServiceDetails,
        }));

        const reviews = providerUser.ReviewsAsProvider;
        const averageRating = reviews.length > 0 ? reviews.reduce((acc, review) => acc + review.Rating, 0) / reviews.length : 0;
        
        const responseData = {
            providerInfo,
            services: servicesWithDetails,
            averageRating,
            reviewsCount: reviews.length,
        };

        return res.json(responseData);
    } else {
      return res.status(404).json({ message: 'Provider not found or user is not a provider' });
    }
  } catch (error) {
    console.error(`[API /services/:id GET] Error:`, error);
    return res.status(500).json({ message: 'Server error fetching profile data' });
  }
});

export default router;
