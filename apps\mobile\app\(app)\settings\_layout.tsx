
import React from 'react';
import { Stack } from 'expo-router';
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';

export default function SettingsLayout() {
  const { translate } = useLanguage();

  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: translate(commonTranslations, 'profileSettings'),
        }}
      />
      <Stack.Screen
        name="addresses"
        options={{
          title: translate(commonTranslations, 'myAddresses'),
        }}
      />
    </Stack>
  );
}
