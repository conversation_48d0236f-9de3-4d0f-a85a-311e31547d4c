/**
 * File upload utilities for handling document uploads in the provider registration form
 */

export interface UploadedFile {
  fileName: string;
  originalName: string;
  size: number;
  mimeType: string;
  uploadedAt: Date;
}

export interface FileUploadResult {
  success: boolean;
  fileName?: string;
  error?: string;
}

export interface MultipleFileUploadResult {
  success: boolean;
  fileNames?: string[];
  errors?: string[];
}

/**
 * Validates a file before upload
 */
export function validateFile(file: File): string | null {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
  
  // Check file size
  if (file.size > maxSize) {
    return 'File is too large. Maximum size is 5MB.';
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return 'Invalid file type. Only PDF, JPG, and PNG files are allowed.';
  }
  
  return null;
}

/**
 * Uploads a single file to the server
 */
export async function uploadSingleFile(file: File, category: string = 'document'): Promise<FileUploadResult> {
  try {
    // Validate file first
    const validationError = validateFile(file);
    if (validationError) {
      return { success: false, error: validationError };
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);

    const response = await fetch('/api/upload/document', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return { 
        success: false, 
        error: errorData.message || `Upload failed with status ${response.status}` 
      };
    }

    const result = await response.json();
    return {
      success: true,
      fileName: result.fileName
    };
  } catch (error) {
    console.error('File upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
}

/**
 * Uploads multiple files to the server
 */
export async function uploadMultipleFiles(files: File[], category: string = 'document'): Promise<MultipleFileUploadResult> {
  try {
    const results = await Promise.all(
      files.map(file => uploadSingleFile(file, category))
    );

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    if (failed.length > 0) {
      return {
        success: false,
        errors: failed.map(f => f.error || 'Unknown error')
      };
    }

    return {
      success: true,
      fileNames: successful.map(r => r.fileName!).filter(Boolean)
    };
  } catch (error) {
    console.error('Multiple file upload error:', error);
    return {
      success: false,
      errors: [error instanceof Error ? error.message : 'Upload failed']
    };
  }
}

/**
 * Converts File objects to file names for form submission
 * This is a temporary solution until we implement actual file upload
 */
export function convertFilesToFileNames(files: File | File[] | null): string | string[] | null {
  if (!files) return null;
  
  if (Array.isArray(files)) {
    return files.map(file => file.name);
  }
  
  return files.name;
}

/**
 * Simulates file upload for development/testing
 * In production, this should be replaced with actual upload logic
 */
export async function simulateFileUpload(file: File): Promise<FileUploadResult> {
  // Validate file
  const validationError = validateFile(file);
  if (validationError) {
    return { success: false, error: validationError };
  }

  // Simulate upload delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Generate a simulated filename
  const timestamp = Date.now();
  const extension = file.name.split('.').pop();
  const simulatedFileName = `uploaded_${timestamp}.${extension}`;

  return {
    success: true,
    fileName: simulatedFileName
  };
}

/**
 * Simulates multiple file upload for development/testing
 */
export async function simulateMultipleFileUpload(files: File[]): Promise<MultipleFileUploadResult> {
  const results = await Promise.all(
    files.map(file => simulateFileUpload(file))
  );

  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  if (failed.length > 0) {
    return {
      success: false,
      errors: failed.map(f => f.error || 'Unknown error')
    };
  }

  return {
    success: true,
    fileNames: successful.map(r => r.fileName!).filter(Boolean)
  };
}

/**
 * Gets file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * Formats file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Checks if a file type is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * Checks if a file type is a PDF
 */
export function isPDFFile(file: File): boolean {
  return file.type === 'application/pdf';
}

/**
 * Creates a preview URL for image files
 */
export function createImagePreview(file: File): string | null {
  if (!isImageFile(file)) return null;
  return URL.createObjectURL(file);
}

/**
 * Cleans up preview URLs to prevent memory leaks
 */
export function cleanupPreviewURL(url: string): void {
  URL.revokeObjectURL(url);
}
