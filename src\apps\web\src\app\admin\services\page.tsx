
"use client";
import { useEffect, useState, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, Loader2, AlertTriangle, CheckCircle, XCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { useDebounce } from 'use-debounce';
import type { AdvertisedService, User, ServiceCategory, ServiceStatus, ServiceCategorySlug } from '@prisma/client';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { useToast } from '@/hooks/use-toast';

interface AdminServiceListItem extends AdvertisedService {
  Provider: Pick<User, 'FullName'>;
  Category: Pick<ServiceCategory, 'NameKey' | 'Slug'>;
}

interface ApiResponse {
  services: AdminServiceListItem[];
  totalPages: number;
  currentPage: number;
  totalServices: number;
}

const serviceStatusOptions: { value: 'all' | ServiceStatus, labelKey: keyof typeof commonTranslations }[] = [
    { value: 'all', labelKey: 'allStatuses' },
    { value: 'Activ', labelKey: 'statusActiv' },
    { value: 'Inactiv', labelKey: 'statusInactiv' },
    { value: 'PendingReview', labelKey: 'statusPendingReview' },
    { value: 'Rejected', labelKey: 'statusRejected' },
];

const pageTranslations = {
    allStatuses: { ro: "Toate statusurile", ru: "Все статусы", en: "All statuses" },
    allCategories: { ro: "Toate categoriile", ru: "Все категории", en: "All categories" },
    searchPlaceholder: { ro: "Caută după nume serviciu/prestator...", ru: "Поиск по названию услуги/поставщика...", en: "Search by service/provider name..." }
}

export default function AdminServicesPage() {
  const { translate } = useLanguage();
  const { toast } = useToast();
  const [data, setData] = useState<ApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 500);

  const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([]);

  useEffect(() => {
    async function fetchCategories() {
        try {
            const response = await fetch('/api/proxy/service-categories');
            if (!response.ok) throw new Error("Failed to fetch categories");
            const data = await response.json();
            setServiceCategories(data || []);
        } catch (err) {
            toast({ variant: "destructive", title: "Eroare", description: "Nu s-au putut încărca categoriile de servicii." });
        }
    }
    fetchCategories();
  }, [toast]);

  const fetchServices = useCallback(async (page: number, status: string, category: string, search: string) => {
      setIsLoading(true);
      setError(null);
      try {
        const params = new URLSearchParams({
            page: String(page),
            limit: '10',
        });
        if (status && status !== 'all') params.append('status', status);
        if (category && category !== 'all') params.append('category', category);
        if (search) params.append('search', search);

        const response = await fetch(`/api/proxy/admin/services?${params.toString()}`);
        const responseData = await response.json();
        if (!response.ok) {
          throw new Error(responseData.message || translate(commonTranslations, 'adminErrorFetchingServices'));
        }
        setData(responseData);
      } catch (err) {
        setError(err instanceof Error ? err.message : translate(commonTranslations, 'adminErrorUnknown'));
      } finally {
        setIsLoading(false);
      }
    }, [translate]);

  useEffect(() => {
    fetchServices(currentPage, statusFilter, categoryFilter, debouncedSearchTerm);
  }, [currentPage, statusFilter, categoryFilter, debouncedSearchTerm, fetchServices]);

  const handleUpdateStatus = async (serviceId: number, status: 'Activ' | 'Rejected') => {
    setIsUpdating(serviceId);
    try {
        const response = await fetch(`/api/proxy/admin/services/update-status`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ serviceId, status }),
        });
        const data = await response.json();
        if (!response.ok) throw new Error(data.message);
        
        toast({ title: "Succes", description: `Serviciul a fost ${status === 'Activ' ? 'aprobat' : 'respins'}.` });
        fetchServices(currentPage, statusFilter, categoryFilter, debouncedSearchTerm);
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "A apărut o eroare.";
        toast({ variant: "destructive", title: "Eroare", description: errorMessage });
    } finally {
        setIsUpdating(null);
    }
  };

  const statusTranslations: Record<ServiceStatus, keyof typeof commonTranslations> = {
    Activ: 'statusActiv',
    Inactiv: 'statusInactiv',
    PendingReview: 'statusPendingReview',
    Rejected: 'statusRejected',
  };

  const getTranslatedStatus = (statusKey: ServiceStatus) => {
    const key = statusTranslations[statusKey];
    return key ? translate(commonTranslations, key) : statusKey;
  };

  const getStatusBadgeClass = (status: string) => {
      switch(status) {
        case 'Activ': return 'bg-green-100 text-green-700';
        case 'PendingReview': return 'bg-yellow-100 text-yellow-700';
        case 'Rejected': return 'bg-red-100 text-red-700';
        case 'Inactiv': return 'bg-gray-100 text-gray-700';
        default: return 'bg-gray-100 text-gray-700';
    }
  }
  
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= (data?.totalPages || 1)) {
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-headline">{translate(commonTranslations, 'adminServiceManagementTitle')}</CardTitle>
          <CardDescription>{translate(commonTranslations, 'adminServiceManagementDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
           <div className="flex flex-col sm:flex-row gap-4">
            <Input
              placeholder={translate(pageTranslations, 'searchPlaceholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filtrează după status" />
              </SelectTrigger>
              <SelectContent>
                {serviceStatusOptions.map(opt => (
                    <SelectItem key={opt.value} value={opt.value}>
                        {opt.value === 'all'
                            ? translate(pageTranslations, opt.labelKey as keyof typeof pageTranslations)
                            : translate(commonTranslations, opt.labelKey)
                        }
                    </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filtrează după categorie" />
              </SelectTrigger>
              <SelectContent>
                 <SelectItem value="all">{translate(pageTranslations, 'allCategories')}</SelectItem>
                {serviceCategories.map(cat => (
                    <SelectItem key={cat.Slug} value={cat.Slug}>{translate(commonTranslations, cat.NameKey as keyof typeof commonTranslations)}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
        </div>
      )}

      {error && (
         <Card>
          <CardContent className="py-10 text-center text-destructive">
            <AlertTriangle className="mx-auto h-8 w-8 mb-2" />
            <p>{translate(commonTranslations, 'adminErrorLoadingData')}: {error}</p>
          </CardContent>
        </Card>
      )}

      {!isLoading && !error && data && (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">{translate(commonTranslations, 'adminTableServiceId')}</TableHead>
                  <TableHead>{translate(commonTranslations, 'adminTableProviderName')}</TableHead>
                  <TableHead>{translate(commonTranslations, 'adminTableCategory')}</TableHead>
                  <TableHead>{translate(commonTranslations, 'adminTableStatus')}</TableHead>
                  <TableHead className="hidden md:table-cell">{translate(commonTranslations, 'adminRequestsTableRequestDate')}</TableHead>
                  <TableHead className="text-right">{translate(commonTranslations, 'adminTableActions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.services.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      {translate(commonTranslations, 'adminNoServicesFound')}
                    </TableCell>
                  </TableRow>
                )}
                {data.services.map((service) => (
                  <TableRow key={service.Id}>
                    <TableCell className="font-mono text-xs">{service.Id}</TableCell>
                    <TableCell className="font-medium">{service.Provider.FullName}</TableCell>
                    <TableCell>
                      {translate(commonTranslations, service.Category.NameKey as keyof typeof commonTranslations) || service.Category.NameKey}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getStatusBadgeClass(service.Status)}>
                        {getTranslatedStatus(service.Status)}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-xs text-muted-foreground">
                        {new Date(service.CreatedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right space-x-2">
                      {isUpdating === service.Id ? (
                        <Loader2 className="h-4 w-4 animate-spin inline-block" />
                      ) : (
                        <>
                          {service.Status === 'PendingReview' && (
                            <>
                              <Button variant="outline" size="icon" title="Aprobă" onClick={() => handleUpdateStatus(service.Id, 'Activ')} className="text-green-600 border-green-500 hover:bg-green-50 hover:text-green-700">
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="icon" title="Respinge" onClick={() => handleUpdateStatus(service.Id, 'Rejected')} className="text-red-600 border-red-500 hover:bg-red-50 hover:text-red-700">
                                <XCircle className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                           <Button variant="ghost" size="icon" title={translate(commonTranslations, 'adminViewDetailsButton')}>
                            <Eye className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
           {data.totalPages > 1 && (
            <div className="p-4 border-t">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} aria-disabled={currentPage === 1} className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''} />
                  </PaginationItem>
                   {[...Array(data.totalPages).keys()].map(i => (
                    <PaginationItem key={i}>
                       <PaginationLink onClick={() => handlePageChange(i + 1)} isActive={currentPage === i + 1}>
                         {i + 1}
                       </PaginationLink>
                     </PaginationItem>
                   ))}
                  <PaginationItem>
                    <PaginationNext onClick={() => handlePageChange(currentPage + 1)} aria-disabled={currentPage === data.totalPages} className={currentPage === data.totalPages ? 'pointer-events-none opacity-50' : ''}/>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </Card>
      )}
    </div>
  );
}
