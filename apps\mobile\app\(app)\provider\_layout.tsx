
import React from 'react';
import { Stack } from 'expo-router';
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';

export default function ProviderLayout() {
  const { translate } = useLanguage();

  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: translate(commonTranslations, 'providerDashboardNav'),
        }}
      />
      <Stack.Screen
        name="services"
        options={{
          title: translate(commonTranslations, 'myServices'),
        }}
      />
      <Stack.Screen
        name="calendar"
        options={{
          title: translate(commonTranslations, 'providerCalendar'),
        }}
      />
    </Stack>
  );
}
