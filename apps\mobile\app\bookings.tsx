
import React from 'react';
import { SafeAreaView, View, Text, StyleSheet } from 'react-native';

const BookingsScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Re<PERSON><PERSON><PERSON><PERSON><PERSON></Text>
        <Text style={styles.subtitle}>Aici va fi lista de rezervări.</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F2F5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  }
});

export default BookingsScreen;
