"use client";

import { useFeatureFlag } from '@/lib/feature-flags';

// Analytics event types for role toggle functionality
export interface RoleToggleAnalyticsEvents {
  role_switch: {
    from_role: 'client' | 'provider';
    to_role: 'client' | 'provider';
    method: 'header_toggle' | 'mobile_sheet' | 'keyboard' | 'url_direct';
    timestamp: number;
    user_id?: string;
    session_id?: string;
    page_path: string;
    switch_duration_ms?: number;
  };

  navigation_click: {
    current_role: 'client' | 'provider';
    destination: string;
    label: string;
    source: 'sidebar_navigation' | 'mobile_drawer' | 'breadcrumb';
    timestamp: number;
    user_id?: string;
    session_id?: string;
  };

  role_toggle_error: {
    error_type: 'navigation_failed' | 'permission_denied' | 'network_error';
    error_message: string;
    current_role: 'client' | 'provider';
    target_role: 'client' | 'provider';
    timestamp: number;
    user_id?: string;
    session_id?: string;
  };

  onboarding_interaction: {
    action: 'started' | 'step_completed' | 'skipped' | 'completed';
    step?: number;
    total_steps?: number;
    user_type: 'new_provider' | 'existing_user';
    timestamp: number;
    user_id?: string;
  };

  dashboard_page_view: {
    role: 'client' | 'provider';
    page_path: string;
    referrer?: string;
    load_time_ms?: number;
    timestamp: number;
    user_id?: string;
    session_id?: string;
  };

  user_engagement: {
    event_type: 'session_start' | 'session_end' | 'role_usage_time';
    role?: 'client' | 'provider';
    duration_ms?: number;
    actions_count?: number;
    timestamp: number;
    user_id?: string;
    session_id?: string;
  };
}

// Analytics service class
export class RoleToggleAnalytics {
  private isEnabled: boolean = false;
  private sessionId: string;
  private userId?: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeAnalytics();
  }

  private initializeAnalytics() {
    // Check if analytics is enabled via feature flags
    if (typeof window !== 'undefined') {
      this.isEnabled = true; // Will be controlled by feature flags
      this.userId = this.getUserId();
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getUserId(): string | undefined {
    try {
      // Get user ID from session storage or auth context
      const session = localStorage.getItem('bonami-session');
      if (session) {
        const parsed = JSON.parse(session);
        return parsed.user?.id;
      }
    } catch (error) {
      console.debug('Could not get user ID for analytics:', error);
    }
    return undefined;
  }

  // Track role switching events
  trackRoleSwitch(data: Omit<RoleToggleAnalyticsEvents['role_switch'], 'timestamp' | 'user_id' | 'session_id'>) {
    if (!this.isEnabled) return;

    const event: RoleToggleAnalyticsEvents['role_switch'] = {
      ...data,
      timestamp: Date.now(),
      user_id: this.userId,
      session_id: this.sessionId,
    };

    this.sendEvent('role_switch', event);
  }

  // Track navigation clicks
  trackNavigationClick(data: Omit<RoleToggleAnalyticsEvents['navigation_click'], 'timestamp' | 'user_id' | 'session_id'>) {
    if (!this.isEnabled) return;

    const event: RoleToggleAnalyticsEvents['navigation_click'] = {
      ...data,
      timestamp: Date.now(),
      user_id: this.userId,
      session_id: this.sessionId,
    };

    this.sendEvent('navigation_click', event);
  }

  // Track errors
  trackError(data: Omit<RoleToggleAnalyticsEvents['role_toggle_error'], 'timestamp' | 'user_id' | 'session_id'>) {
    if (!this.isEnabled) return;

    const event: RoleToggleAnalyticsEvents['role_toggle_error'] = {
      ...data,
      timestamp: Date.now(),
      user_id: this.userId,
      session_id: this.sessionId,
    };

    this.sendEvent('role_toggle_error', event);
  }

  // Track onboarding interactions
  trackOnboarding(data: Omit<RoleToggleAnalyticsEvents['onboarding_interaction'], 'timestamp' | 'user_id'>) {
    if (!this.isEnabled) return;

    const event: RoleToggleAnalyticsEvents['onboarding_interaction'] = {
      ...data,
      timestamp: Date.now(),
      user_id: this.userId,
    };

    this.sendEvent('onboarding_interaction', event);
  }

  // Track page views
  trackPageView(data: Omit<RoleToggleAnalyticsEvents['dashboard_page_view'], 'timestamp' | 'user_id' | 'session_id'>) {
    if (!this.isEnabled) return;

    const event: RoleToggleAnalyticsEvents['dashboard_page_view'] = {
      ...data,
      timestamp: Date.now(),
      user_id: this.userId,
      session_id: this.sessionId,
    };

    this.sendEvent('dashboard_page_view', event);
  }

  // Track user engagement
  trackEngagement(data: Omit<RoleToggleAnalyticsEvents['user_engagement'], 'timestamp' | 'user_id' | 'session_id'>) {
    if (!this.isEnabled) return;

    const event: RoleToggleAnalyticsEvents['user_engagement'] = {
      ...data,
      timestamp: Date.now(),
      user_id: this.userId,
      session_id: this.sessionId,
    };

    this.sendEvent('user_engagement', event);
  }

  // Send event to analytics service
  private async sendEvent<T extends keyof RoleToggleAnalyticsEvents>(
    eventType: T,
    eventData: RoleToggleAnalyticsEvents[T]
  ) {
    try {
      // Send to multiple analytics providers
      await Promise.allSettled([
        this.sendToCustomAnalytics(eventType, eventData),
        this.sendToGoogleAnalytics(eventType, eventData),
        this.sendToMixpanel(eventType, eventData),
      ]);
    } catch (error) {
      console.error('Analytics error:', error);
    }
  }

  // Send to custom analytics endpoint
  private async sendToCustomAnalytics(eventType: string, eventData: any) {
    try {
      await fetch('/api/analytics/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event_type: eventType,
          event_data: eventData,
          source: 'role_toggle_system',
        }),
      });
    } catch (error) {
      console.debug('Custom analytics not available:', error);
    }
  }

  // Send to Google Analytics
  private sendToGoogleAnalytics(eventType: string, eventData: any) {
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', eventType, {
        custom_parameter_1: eventData.from_role || eventData.current_role,
        custom_parameter_2: eventData.to_role || eventData.destination,
        value: 1,
      });
    }
  }

  // Send to Mixpanel
  private sendToMixpanel(eventType: string, eventData: any) {
    if (typeof window !== 'undefined' && (window as any).mixpanel) {
      (window as any).mixpanel.track(eventType, eventData);
    }
  }

  // Get analytics summary for dashboard
  async getAnalyticsSummary(timeRange: '24h' | '7d' | '30d' = '7d') {
    try {
      const response = await fetch(`/api/analytics/role-toggle-summary?range=${timeRange}`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Error fetching analytics summary:', error);
    }
    return null;
  }
}

// Global analytics instance
export const roleToggleAnalytics = new RoleToggleAnalytics();

// Expose to window for global access
if (typeof window !== 'undefined') {
  (window as any).roleToggleAnalytics = roleToggleAnalytics;
}

// React hook for analytics
export function useRoleToggleAnalytics() {
  const isEnabled = useFeatureFlag('roleToggleAnalytics');
  
  return {
    trackRoleSwitch: isEnabled ? roleToggleAnalytics.trackRoleSwitch.bind(roleToggleAnalytics) : () => {},
    trackNavigationClick: isEnabled ? roleToggleAnalytics.trackNavigationClick.bind(roleToggleAnalytics) : () => {},
    trackError: isEnabled ? roleToggleAnalytics.trackError.bind(roleToggleAnalytics) : () => {},
    trackOnboarding: isEnabled ? roleToggleAnalytics.trackOnboarding.bind(roleToggleAnalytics) : () => {},
    trackPageView: isEnabled ? roleToggleAnalytics.trackPageView.bind(roleToggleAnalytics) : () => {},
    trackEngagement: isEnabled ? roleToggleAnalytics.trackEngagement.bind(roleToggleAnalytics) : () => {},
    getAnalyticsSummary: roleToggleAnalytics.getAnalyticsSummary.bind(roleToggleAnalytics),
    isEnabled,
  };
}
