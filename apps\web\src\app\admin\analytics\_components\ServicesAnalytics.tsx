"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Briefcase, BarChart3, TrendingUp, Calendar, Eye, Star } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { ServiceCategoryChart } from './ServiceCategoryChart';
import { BookingsChart } from './BookingsChart';

const servicesTranslations = {
  servicesAnalytics: { ro: "Analiză servicii", ru: "Аналитика услуг", en: "Services Analytics" },
  serviceCategories: { ro: "Categorii servicii", ru: "Категории услуг", en: "Service Categories" },
  bookingsTrends: { ro: "Tendințe rezervări", ru: "Тенденции бронирований", en: "Bookings Trends" },
  totalServices: { ro: "Total servicii", ru: "Всего услуг", en: "Total Services" },
  activeServices: { ro: "Servicii active", ru: "Активные услуги", en: "Active Services" },
  totalBookings: { ro: "Total rezervări", ru: "Всего бронирований", en: "Total Bookings" },
  averageRating: { ro: "Rating mediu", ru: "Средний рейтинг", en: "Average Rating" },
  popularServices: { ro: "Servicii populare", ru: "Популярные услуги", en: "Popular Services" },
  servicePerformance: { ro: "Performanța serviciilor", ru: "Производительность услуг", en: "Service Performance" },
  bookingRate: { ro: "Rata de rezervare", ru: "Уровень бронирования", en: "Booking Rate" },
  conversionRate: { ro: "Rata de conversie", ru: "Коэффициент конверсии", en: "Conversion Rate" },
  comingSoon: { ro: "În curând...", ru: "Скоро...", en: "Coming soon..." },
};

interface ServicesAnalyticsProps {
  timePeriod: string;
  refreshTrigger: Date;
}

interface ServiceStats {
  totalServices: number;
  activeServices: number;
  totalBookings: number;
  averageRating: number;
}

export function ServicesAnalytics({ timePeriod, refreshTrigger }: ServicesAnalyticsProps) {
  const { translate } = useLanguage();
  const [stats, setStats] = useState<ServiceStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const [statsRes, bookingsRes, performanceRes] = await Promise.all([
          fetch('/api/proxy/admin/stats'),
          fetch(`/api/proxy/admin/analytics/bookings?period=${timePeriod}`),
          fetch('/api/proxy/admin/analytics/provider-performance')
        ]);

        if (!statsRes.ok || !bookingsRes.ok || !performanceRes.ok) {
          throw new Error('Failed to fetch service analytics');
        }

        const statsData = await statsRes.json();
        const bookingsData = await bookingsRes.json();
        const performanceData = await performanceRes.json();

        // Calculate total bookings from bookings data
        const totalBookings = bookingsData.reduce((sum: number, day: any) => sum + day.total, 0);

        setStats({
          totalServices: statsData.totalServices,
          activeServices: statsData.totalActiveServices,
          totalBookings,
          averageRating: performanceData.averageRating
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error fetching service analytics:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger, timePeriod]);

  if (error) {
    return (
      <div className="text-center text-red-600 py-8">
        <p>Error loading service analytics: {error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-2">
        <Briefcase className="w-6 h-6 text-primary" />
        <h2 className="text-2xl font-bold">
          {translate(servicesTranslations, 'servicesAnalytics')}
        </h2>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {translate(servicesTranslations, 'totalServices')}
            </CardTitle>
            <Briefcase className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16 mb-2" />
            ) : (
              <div className="text-2xl font-bold">{stats?.totalServices || 0}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Total services created
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {translate(servicesTranslations, 'activeServices')}
            </CardTitle>
            <Eye className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16 mb-2" />
            ) : (
              <div className="text-2xl font-bold">{stats?.activeServices || 0}</div>
            )}
            <p className="text-xs text-muted-foreground">
              {stats?.totalServices ? Math.round((stats.activeServices / stats.totalServices) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {translate(servicesTranslations, 'totalBookings')}
            </CardTitle>
            <Calendar className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-20 mb-2" />
            ) : (
              <div className="text-2xl font-bold">{stats?.totalBookings || 0}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Total bookings made
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {translate(servicesTranslations, 'averageRating')}
            </CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16 mb-2" />
            ) : (
              <div className="text-2xl font-bold">{stats?.averageRating || 0}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Average service rating
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Service Categories and Bookings Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ServiceCategoryChart refreshTrigger={refreshTrigger} />
        <BookingsChart period={timePeriod as '7d' | '30d' | '90d' | '1y'} refreshTrigger={refreshTrigger} />
      </div>

      {/* Service Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              {translate(servicesTranslations, 'bookingRate')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Childcare</span>
                <span className="text-sm text-muted-foreground">78%</span>
              </div>
              <div className="bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '78%' }}></div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Eldercare</span>
                <span className="text-sm text-muted-foreground">65%</span>
              </div>
              <div className="bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '65%' }}></div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Housekeeping</span>
                <span className="text-sm text-muted-foreground">52%</span>
              </div>
              <div className="bg-gray-200 rounded-full h-2">
                <div className="bg-purple-600 h-2 rounded-full" style={{ width: '52%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              {translate(servicesTranslations, 'conversionRate')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">24.3%</div>
              <p className="text-sm text-muted-foreground mb-4">
                Views to bookings conversion
              </p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-semibold">5,523</div>
                  <div className="text-muted-foreground">Total Views</div>
                </div>
                <div>
                  <div className="font-semibold">1,342</div>
                  <div className="text-muted-foreground">Bookings</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Popular Services */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5" />
            {translate(servicesTranslations, 'popularServices')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(servicesTranslations, 'comingSoon')}</p>
            <p className="text-sm mt-2">Most popular services and trending categories</p>
          </div>
        </CardContent>
      </Card>

      {/* Service Performance Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            {translate(servicesTranslations, 'servicePerformance')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(servicesTranslations, 'comingSoon')}</p>
            <p className="text-sm mt-2">Detailed service performance analytics and insights</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
