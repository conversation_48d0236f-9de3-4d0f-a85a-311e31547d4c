# =============================================================================
# BONAMI MONOREPO .GITIGNORE
# =============================================================================
# This file contains ignore patterns for a TypeScript monorepo with:
# - Next.js web application
# - Node.js/Express API with Prisma
# - React Native mobile application
# - Multiple workspace packages
# =============================================================================

# =============================================================================
# DEPENDENCIES & PACKAGE MANAGERS
# =============================================================================
# Node.js dependencies (applies to all workspaces)
**/node_modules/
node_modules/

# Package manager files
.pnp
.pnp.js
.yarn/install-state.gz
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz

# npm
npm-debug.log*
.npm

# Yarn
yarn-debug.log*
yarn-error.log*

# pnpm
pnpm-debug.log*
.pnpm-store/

# =============================================================================
# BUILD OUTPUTS & ARTIFACTS
# =============================================================================
# General build directories
**/build/
**/dist/
**/out/
**/coverage/

# Next.js specific
**/.next/
.next/
.next/cache/
/export/

# TypeScript build info
*.tsbuildinfo
.tsbuildinfo

# =============================================================================
# ENVIRONMENT & CONFIGURATION
# =============================================================================
# Environment variables (all directories)
**/.env
**/.env.local
**/.env.development.local
**/.env.test.local
**/.env.production.local
**/.env.*.local

# Keep example environment files
!**/.env.example
!**/.env.sample
!**/.env.template

# =============================================================================
# DATABASE & PRISMA
# =============================================================================
# Prisma generated files
**/prisma/generated/
**/.prisma/
**/node_modules/.prisma/

# Database files (SQLite)
**/*.db
**/*.db-journal
**/*.sqlite
**/*.sqlite3

# Database dumps
**/*.sql.gz
**/*.dump

# =============================================================================
# LOGS & TEMPORARY FILES
# =============================================================================
# Log files
**/*.log
**/logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
**/pids/
**/*.pid
**/*.seed
**/*.pid.lock

# Temporary directories
**/tmp/
**/temp/
**/.tmp/
**/.temp/

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# CERTIFICATES & SECURITY
# =============================================================================
# SSL certificates
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# =============================================================================
# DEPLOYMENT & HOSTING
# =============================================================================
# Vercel
.vercel
.vercel.json

# Netlify
.netlify/

# Firebase
.firebase/
firebase-debug.log*
firestore-debug.log*

# =============================================================================
# MOBILE DEVELOPMENT (React Native / Expo)
# =============================================================================
# Expo
.expo/
.expo-shared/
expo-env.d.ts

# React Native
# Metro bundler cache
.metro-health-check*

# Android
**/android/app/build/
**/android/build/
**/android/.gradle/
**/android/local.properties
**/android/app/release/

# iOS
**/ios/build/
**/ios/Pods/
**/ios/*.xcworkspace/xcuserdata/
**/ios/*.xcodeproj/xcuserdata/
**/ios/DerivedData/

# =============================================================================
# TESTING & COVERAGE
# =============================================================================
# Test coverage
**/coverage/
**/.nyc_output/
**/lcov.info

# Jest
**/.jest/

# =============================================================================
# MISCELLANEOUS
# =============================================================================
# Archives
*.zip
*.tar.gz
*.rar

# Backup files
*.bak
*.backup
*.old

# Lock files (keep package-lock.json but ignore others)
# yarn.lock  # Uncomment if using npm instead of yarn
# pnpm-lock.yaml  # Uncomment if using npm/yarn instead of pnpm

# User uploads (if applicable)
**/uploads/
**/public/uploads/

# Cache directories
**/.cache/
**/cache/