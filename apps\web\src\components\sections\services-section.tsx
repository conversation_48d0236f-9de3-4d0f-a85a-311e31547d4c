
"use client";
import { Baby, <PERSON>Handshake, Spark<PERSON>, GraduationCap, CookingPot } from 'lucide-react'; // Am actualizat iconițele importate
import { ServiceCard } from '@/components/ui/service-card';
import { useLanguage } from '@/contexts/language-context';

const servicesSectionTranslations = {
  title: { ro: "Categorii de Servicii", ru: "Категории услуг", en: "Service Categories" },
  description: { ro: "Găsește ajutor de încredere explorând principalele categorii de servicii de pe platforma noastră.", ru: "Найдите надежную помощь, изучив основные категории услуг на нашей платформе.", en: "Find trusted help by exploring the main service categories on our platform." },
  nannyTitle: { ro: "Bone", ru: "Няни", en: "Nannies" },
  nannyDesc: { ro: "Îngrijire profesională pentru copilul tău oferită de persoane calificate și cu experiență.", ru: "Профессиональный уход за вашим ребенком от квалифицированных и опытных людей.", en: "Professional care for your child provided by qualified and experienced individuals." },
  elderlyTitle: { ro: "Îngrijire bătrâni", ru: "Уход за пожилыми", en: "Elderly care" },
  elderlyDesc: { ro: "Asistență specializată pentru membrii vârstnici ai familiei, acasă sau în instituții.", ru: "Специализированная помощь пожилым членам семьи на дому или в учреждениях.", en: "Specialized assistance for elderly family members, at home or in institutions." },
  cleaningTitle: { ro: "Curățenie", ru: "Уборка", en: "Cleaning" },
  cleaningDesc: { ro: "Servicii complete de menaj pentru o casă impecabilă fără efort din partea ta.", ru: "Полный комплекс услуг по уборке для безупречного дома без ваших усилий.", en: "Complete housekeeping services for a spotless home without any effort on your part." },
  tutoringTitle: { ro: "Meditații", ru: "Репетиторство", en: "Tutoring" },
  tutoringDesc: { ro: "Suport educațional personalizat pentru diverse materii și niveluri de studiu.", ru: "Индивидуальная образовательная поддержка по различным предметам и уровням обучения.", en: "Personalized educational support for various subjects and study levels." },
  cookingTitle: { ro: "Gătit acasă", ru: "Приготовление пищи", en: "Home Cooking" },
  cookingDesc: { ro: "Bucătari talentați care prepară mese delicioase și sănătoase la tine acasă sau cu livrare.", ru: "Талантливые повара, которые готовят вкусные и здоровые блюда у вас дома или с доставкой.", en: "Talented cooks who prepare delicious and healthy meals in your home or for delivery." },
  detailsLink: { ro: "Detalii", ru: "Подробнее", en: "Details" },
  viewProvidersLink: { ro: "Vezi furnizori", ru: "Посмотреть поставщиков", en: "View providers" }
};

export function ServicesSection() {
  const { translate } = useLanguage();

  const services = [
    {
      icon: <Baby className="w-10 h-10" />,
      titleKey: "nannyTitle",
      descriptionKey: "nannyDesc",
      link: "/search?service=Nanny",
    },
    {
      icon: <HeartHandshake className="w-10 h-10" />,
      titleKey: "elderlyTitle",
      descriptionKey: "elderlyDesc",
      link: "/search?service=ElderCare",
    },
    {
      icon: <Sparkles className="w-10 h-10" />,
      titleKey: "cleaningTitle",
      descriptionKey: "cleaningDesc",
      link: "/search?service=Cleaning",
    },
    {
      icon: <GraduationCap className="w-10 h-10" />,
      titleKey: "tutoringTitle",
      descriptionKey: "tutoringDesc",
      link: "/search?service=Tutoring",
    },
    {
      icon: <CookingPot className="w-10 h-10" />,
      titleKey: "cookingTitle",
      descriptionKey: "cookingDesc",
      link: "/search?service=Cooking",
    },
  ];

  return (
    <section className="py-16 md:py-20 px-6 bg-background">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-4 font-headline">{translate(servicesSectionTranslations, 'title')}</h2>
        <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">
          {translate(servicesSectionTranslations, 'description')}
        </p>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8">
          {services.map((service) => (
            <ServiceCard
              key={service.titleKey}
              icon={service.icon}
              title={translate(servicesSectionTranslations, service.titleKey)}
              description={translate(servicesSectionTranslations, service.descriptionKey)}
              link={service.link}
              linkText={translate(servicesSectionTranslations, 'viewProvidersLink' as keyof typeof servicesSectionTranslations)}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
