"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import type { CookingServiceDetails } from "@prisma/client";
import { useLanguage } from "@/contexts/language-context";
import { commonTranslations } from "@repo/translations";
import { BooleanDetailItem } from "../DetailItem";

interface CookingDetailsProps {
    details: CookingServiceDetails | null;
}

export function CookingDetailsView({ details }: CookingDetailsProps) {
    const { translate } = useLanguage();
    if (!details) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">
                    {translate(commonTranslations, 'cookingTitle')}
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'cookingCuisineTypeTraditional')} value={details.CuisineTypeTraditional} />
                    <BooleanDetailItem label={translate(commonTranslations, 'cookingCuisineTypeVegetarian')} value={details.CuisineTypeVegetarian} />
                </div>
            </CardContent>
        </Card>
    );
}
