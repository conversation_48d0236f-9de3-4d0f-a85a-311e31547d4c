import swaggerJsdoc from 'swagger-jsdoc';

export const getApiDocs = async () => {
  const options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'mesami Platform API',
        version: '1.0.0',
        description: 'API documentation for the bonami caregiver platform.',
      },
      components: {
        securitySchemes: {
          InternalApiKey: {
            type: 'apiKey',
            in: 'header',
            name: 'x-api-key',
            description: 'Static API key for internal service-to-service communication.'
          },
          BearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            description: 'User JWT token for authenticated endpoints.',
          },
        },
      },
      security: [
        {
          InternalApiKey: [],
        },
        {
          BearerAuth: [],
        },
      ],
      tags: [
        {
            name: "Public",
            description: "Endpoints accessible without user authentication."
        },
        {
            name: "Auth",
            description: "User authentication and registration."
        },
        {
            name: "Admin",
            description: "Endpoints related to administrative functions."
        },
      ]
    },
    // Path to the API docs
    apis: ['./src/routes/*.ts'], 
  };
  
  return swaggerJsdoc(options);
};
