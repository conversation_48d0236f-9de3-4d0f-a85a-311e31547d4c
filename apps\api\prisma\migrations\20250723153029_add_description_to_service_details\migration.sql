/*
  Warnings:

  - The values [General] on the enum `NotificationType` will be removed. If these variants are still used in the database, this will fail.
  - The values [Approved,Rejected] on the enum `ProviderRegistrationRequestStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `CleaningServiceDetailsId` on the `AdvertisedService` table. All the data in the column will be lost.
  - You are about to drop the column `CookingServiceDetailsId` on the `AdvertisedService` table. All the data in the column will be lost.
  - You are about to drop the column `ElderCareServiceDetailsId` on the `AdvertisedService` table. All the data in the column will be lost.
  - You are about to drop the column `NannyServiceDetailsId` on the `AdvertisedService` table. All the data in the column will be lost.
  - You are about to drop the column `TutoringServiceDetailsId` on the `AdvertisedService` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityEvenings` on the `CleaningServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekdays` on the `CleaningServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekends` on the `CleaningServiceDetails` table. All the data in the column will be lost.
  - The `DocDiplomeFileNames` column on the `CleaningServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `DocRecomandariFileNames` column on the `CleaningServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `AvailabilityEvenings` on the `CookingServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekdays` on the `CookingServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekends` on the `CookingServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `OwnProducts` on the `CookingServiceDetails` table. All the data in the column will be lost.
  - The `DocDiplomeFileNames` column on the `CookingServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `DocRecomandariFileNames` column on the `CookingServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `AvailabilityEvenings` on the `ElderCareServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekdays` on the `ElderCareServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekends` on the `ElderCareServiceDetails` table. All the data in the column will be lost.
  - The `DocDiplomeFileNames` column on the `ElderCareServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `DocRecomandariFileNames` column on the `ElderCareServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `AvailabilityEvenings` on the `NannyServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekdays` on the `NannyServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekends` on the `NannyServiceDetails` table. All the data in the column will be lost.
  - The `DocDiplomeFileNames` column on the `NannyServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `DocRecomandariFileNames` column on the `NannyServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `UpdatedAt` on the `ProviderRegistrationRequest` table. All the data in the column will be lost.
  - You are about to drop the column `ProviderRespondedAt` on the `Review` table. All the data in the column will be lost.
  - You are about to drop the column `ProviderResponse` on the `Review` table. All the data in the column will be lost.
  - You are about to drop the column `ReviewDate` on the `Review` table. All the data in the column will be lost.
  - You are about to drop the column `ReviewerId` on the `Review` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityEvenings` on the `TutoringServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekdays` on the `TutoringServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `AvailabilityWeekends` on the `TutoringServiceDetails` table. All the data in the column will be lost.
  - The `DocDiplomeFileNames` column on the `TutoringServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `DocRecomandariFileNames` column on the `TutoringServiceDetails` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `Accounts` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Addresses` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_UserRoles` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `sessions` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[AiExpectedValue]` on the table `ServiceCategory` will be added. If there are existing duplicate values, this will fail.
  - Made the column `AdvertisedServiceId` on table `CleaningServiceDetails` required. This step will fail if there are existing NULL values in that column.
  - Made the column `AdvertisedServiceId` on table `CookingServiceDetails` required. This step will fail if there are existing NULL values in that column.
  - Made the column `AdvertisedServiceId` on table `ElderCareServiceDetails` required. This step will fail if there are existing NULL values in that column.
  - Made the column `AdvertisedServiceId` on table `NannyServiceDetails` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `ClientId` to the `Review` table without a default value. This is not possible if the table is not empty.
  - Made the column `AdvertisedServiceId` on table `TutoringServiceDetails` required. This step will fail if there are existing NULL values in that column.
  - Made the column `Email` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "NotificationType_new" AS ENUM ('NewBookingRequest', 'BookingStatusChanged', 'NewMessage', 'NewReview', 'ProviderRequestApproved', 'ProviderRequestRejected', 'ServiceStatusChanged', 'NewProviderRequest', 'ServiceForReview');
ALTER TABLE "Notification" ALTER COLUMN "Type" DROP DEFAULT;
ALTER TABLE "Notification" ALTER COLUMN "Type" TYPE "NotificationType_new" USING ("Type"::text::"NotificationType_new");
ALTER TYPE "NotificationType" RENAME TO "NotificationType_old";
ALTER TYPE "NotificationType_new" RENAME TO "NotificationType";
DROP TYPE "NotificationType_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "ProviderRegistrationRequestStatus_new" AS ENUM ('Pending');
ALTER TABLE "ProviderRegistrationRequest" ALTER COLUMN "Status" DROP DEFAULT;
ALTER TABLE "ProviderRegistrationRequest" ALTER COLUMN "Status" TYPE "ProviderRegistrationRequestStatus_new" USING ("Status"::text::"ProviderRegistrationRequestStatus_new");
ALTER TYPE "ProviderRegistrationRequestStatus" RENAME TO "ProviderRegistrationRequestStatus_old";
ALTER TYPE "ProviderRegistrationRequestStatus_new" RENAME TO "ProviderRegistrationRequestStatus";
DROP TYPE "ProviderRegistrationRequestStatus_old";
ALTER TABLE "ProviderRegistrationRequest" ALTER COLUMN "Status" SET DEFAULT 'Pending';
COMMIT;

-- DropForeignKey
ALTER TABLE "Accounts" DROP CONSTRAINT "Accounts_UserId_fkey";

-- DropForeignKey
ALTER TABLE "Addresses" DROP CONSTRAINT "Addresses_UserId_fkey";

-- DropForeignKey
ALTER TABLE "AdvertisedService" DROP CONSTRAINT "AdvertisedService_ProviderId_fkey";

-- DropForeignKey
ALTER TABLE "ChatRoom" DROP CONSTRAINT "ChatRoom_AdvertisedServiceId_fkey";

-- DropForeignKey
ALTER TABLE "Notification" DROP CONSTRAINT "Notification_UserId_fkey";

-- DropForeignKey
ALTER TABLE "ProviderRegistrationRequest" DROP CONSTRAINT "ProviderRegistrationRequest_UserId_fkey";

-- DropForeignKey
ALTER TABLE "Review" DROP CONSTRAINT "Review_BookingId_fkey";

-- DropForeignKey
ALTER TABLE "Review" DROP CONSTRAINT "Review_ReviewerId_fkey";

-- DropForeignKey
ALTER TABLE "_UserRoles" DROP CONSTRAINT "_UserRoles_A_fkey";

-- DropForeignKey
ALTER TABLE "_UserRoles" DROP CONSTRAINT "_UserRoles_B_fkey";

-- DropForeignKey
ALTER TABLE "sessions" DROP CONSTRAINT "sessions_UserId_fkey";

-- DropIndex
DROP INDEX "AdvertisedService_CleaningServiceDetailsId_key";

-- DropIndex
DROP INDEX "AdvertisedService_CookingServiceDetailsId_key";

-- DropIndex
DROP INDEX "AdvertisedService_ElderCareServiceDetailsId_key";

-- DropIndex
DROP INDEX "AdvertisedService_NannyServiceDetailsId_key";

-- DropIndex
DROP INDEX "AdvertisedService_TutoringServiceDetailsId_key";

-- DropIndex
DROP INDEX "ChatRoom_ClientId_ProviderId_AdvertisedServiceId_key";

-- AlterTable
ALTER TABLE "AdvertisedService" DROP COLUMN "CleaningServiceDetailsId",
DROP COLUMN "CookingServiceDetailsId",
DROP COLUMN "ElderCareServiceDetailsId",
DROP COLUMN "NannyServiceDetailsId",
DROP COLUMN "TutoringServiceDetailsId";

-- AlterTable
ALTER TABLE "Booking" ALTER COLUMN "Status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "CleaningServiceDetails" DROP COLUMN "AvailabilityEvenings",
DROP COLUMN "AvailabilityWeekdays",
DROP COLUMN "AvailabilityWeekends",
ADD COLUMN     "Description" TEXT,
ADD COLUMN     "availabilityEvenings" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekdays" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekends" BOOLEAN DEFAULT false,
ALTER COLUMN "AdvertisedServiceId" SET NOT NULL,
DROP COLUMN "DocDiplomeFileNames",
ADD COLUMN     "DocDiplomeFileNames" TEXT[],
DROP COLUMN "DocRecomandariFileNames",
ADD COLUMN     "DocRecomandariFileNames" TEXT[];

-- AlterTable
ALTER TABLE "CookingServiceDetails" DROP COLUMN "AvailabilityEvenings",
DROP COLUMN "AvailabilityWeekdays",
DROP COLUMN "AvailabilityWeekends",
DROP COLUMN "OwnProducts",
ADD COLUMN     "Description" TEXT,
ADD COLUMN     "availabilityEvenings" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekdays" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekends" BOOLEAN DEFAULT false,
ADD COLUMN     "cookingOwnProducts" BOOLEAN DEFAULT false,
ALTER COLUMN "AdvertisedServiceId" SET NOT NULL,
DROP COLUMN "DocDiplomeFileNames",
ADD COLUMN     "DocDiplomeFileNames" TEXT[],
DROP COLUMN "DocRecomandariFileNames",
ADD COLUMN     "DocRecomandariFileNames" TEXT[];

-- AlterTable
ALTER TABLE "ElderCareServiceDetails" DROP COLUMN "AvailabilityEvenings",
DROP COLUMN "AvailabilityWeekdays",
DROP COLUMN "AvailabilityWeekends",
ADD COLUMN     "Description" TEXT,
ADD COLUMN     "availabilityEvenings" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekdays" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekends" BOOLEAN DEFAULT false,
ALTER COLUMN "AdvertisedServiceId" SET NOT NULL,
DROP COLUMN "DocDiplomeFileNames",
ADD COLUMN     "DocDiplomeFileNames" TEXT[],
DROP COLUMN "DocRecomandariFileNames",
ADD COLUMN     "DocRecomandariFileNames" TEXT[];

-- AlterTable
ALTER TABLE "NannyServiceDetails" DROP COLUMN "AvailabilityEvenings",
DROP COLUMN "AvailabilityWeekdays",
DROP COLUMN "AvailabilityWeekends",
ADD COLUMN     "Description" TEXT,
ADD COLUMN     "availabilityEvenings" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekdays" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekends" BOOLEAN DEFAULT false,
ALTER COLUMN "AdvertisedServiceId" SET NOT NULL,
DROP COLUMN "DocDiplomeFileNames",
ADD COLUMN     "DocDiplomeFileNames" TEXT[],
DROP COLUMN "DocRecomandariFileNames",
ADD COLUMN     "DocRecomandariFileNames" TEXT[];

-- AlterTable
ALTER TABLE "Notification" ALTER COLUMN "Type" DROP DEFAULT;

-- AlterTable
ALTER TABLE "ProviderRegistrationRequest" DROP COLUMN "UpdatedAt";

-- AlterTable
ALTER TABLE "Review" DROP COLUMN "ProviderRespondedAt",
DROP COLUMN "ProviderResponse",
DROP COLUMN "ReviewDate",
DROP COLUMN "ReviewerId",
ADD COLUMN     "ClientId" INTEGER NOT NULL,
ADD COLUMN     "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "ServiceCategory" ALTER COLUMN "DefaultImageHint" DROP NOT NULL;

-- AlterTable
ALTER TABLE "TutoringServiceDetails" DROP COLUMN "AvailabilityEvenings",
DROP COLUMN "AvailabilityWeekdays",
DROP COLUMN "AvailabilityWeekends",
ADD COLUMN     "Description" TEXT,
ADD COLUMN     "availabilityEvenings" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekdays" BOOLEAN DEFAULT false,
ADD COLUMN     "availabilityWeekends" BOOLEAN DEFAULT false,
ALTER COLUMN "AdvertisedServiceId" SET NOT NULL,
DROP COLUMN "DocDiplomeFileNames",
ADD COLUMN     "DocDiplomeFileNames" TEXT[],
DROP COLUMN "DocRecomandariFileNames",
ADD COLUMN     "DocRecomandariFileNames" TEXT[];

-- AlterTable
ALTER TABLE "User" ALTER COLUMN "Email" SET NOT NULL,
ALTER COLUMN "SpokenLanguages" SET DEFAULT ARRAY['ro']::TEXT[];

-- DropTable
DROP TABLE "Accounts";

-- DropTable
DROP TABLE "Addresses";

-- DropTable
DROP TABLE "_UserRoles";

-- DropTable
DROP TABLE "sessions";

-- CreateTable
CREATE TABLE "Account" (
    "Id" SERIAL NOT NULL,
    "UserId" INTEGER NOT NULL,
    "Type" TEXT NOT NULL,
    "Provider" TEXT NOT NULL,
    "ProviderAccountId" TEXT NOT NULL,
    "RefreshToken" TEXT,
    "AccessToken" TEXT,
    "ExpiresAt" INTEGER,
    "TokenType" TEXT,
    "Scope" TEXT,
    "IdToken" TEXT,
    "SessionState" TEXT,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Address" (
    "Id" SERIAL NOT NULL,
    "UserId" INTEGER NOT NULL,
    "Label" TEXT NOT NULL,
    "Street" TEXT NOT NULL,
    "City" TEXT NOT NULL,
    "Region" TEXT,
    "PostalCode" TEXT,
    "Country" TEXT NOT NULL DEFAULT 'Moldova',
    "IsDefault" BOOLEAN NOT NULL DEFAULT false,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Address_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "items_table_placeholder" (
    "id" SERIAL NOT NULL,
    "name" TEXT,
    "value" TEXT,

    CONSTRAINT "items_table_placeholder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_RoleToUser" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "Account_Provider_ProviderAccountId_key" ON "Account"("Provider", "ProviderAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "_RoleToUser_AB_unique" ON "_RoleToUser"("A", "B");

-- CreateIndex
CREATE INDEX "_RoleToUser_B_index" ON "_RoleToUser"("B");

-- CreateIndex
CREATE UNIQUE INDEX "ServiceCategory_AiExpectedValue_key" ON "ServiceCategory"("AiExpectedValue");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AdvertisedService" ADD CONSTRAINT "AdvertisedService_ProviderId_fkey" FOREIGN KEY ("ProviderId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Review" ADD CONSTRAINT "Review_BookingId_fkey" FOREIGN KEY ("BookingId") REFERENCES "Booking"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Review" ADD CONSTRAINT "Review_ClientId_fkey" FOREIGN KEY ("ClientId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoom" ADD CONSTRAINT "ChatRoom_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProviderRegistrationRequest" ADD CONSTRAINT "ProviderRegistrationRequest_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_RoleToUser" ADD CONSTRAINT "_RoleToUser_A_fkey" FOREIGN KEY ("A") REFERENCES "Role"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_RoleToUser" ADD CONSTRAINT "_RoleToUser_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;
