"use client";

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ArrowLeft, ArrowRight, Check, X } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { useToast } from '@/hooks/use-toast';
import { useUser } from '@/contexts/user-context';
import type { ProfileSetupData, ProfileSetupProgress } from '@/types/profile-setup';

// Import step components
import { ProfilePhotoStep } from './steps/profile-photo-step';
import { PhoneNumberStep } from './steps/phone-number-step';
import { BioStep } from './steps/bio-step';
import { LanguagesStep } from './steps/languages-step';

interface ProfileSetupWizardProps {
  onComplete: () => void;
  onExit: () => void;
}

const wizardTranslations = {
  en: {
    profileSetupTitle: "Complete Your Profile",
    profileSetupDescription: "Help others get to know you better",
    stepOf: "Step {current} of {total}",
    previousButton: "Previous",
    nextButton: "Next",
    skipButton: "Skip",
    finishButton: "Finish",
    exitButton: "Exit",
    savingProgress: "Saving...",
    profileCompleted: "Profile completed successfully!",
    errorSaving: "Error saving profile information",
    confirmExit: "Are you sure you want to exit? Your progress will be saved.",
    exitConfirm: "Yes, Exit",
    exitCancel: "Continue Setup"
  },
  ro: {
    profileSetupTitle: "Completează-ți Profilul",
    profileSetupDescription: "Ajută-i pe alții să te cunoască mai bine",
    stepOf: "Pasul {current} din {total}",
    previousButton: "Anterior",
    nextButton: "Următorul",
    skipButton: "Sari",
    finishButton: "Finalizează",
    exitButton: "Ieși",
    savingProgress: "Se salvează...",
    profileCompleted: "Profilul a fost completat cu succes!",
    errorSaving: "Eroare la salvarea informațiilor profilului",
    confirmExit: "Ești sigur că vrei să ieși? Progresul tău va fi salvat.",
    exitConfirm: "Da, Ieși",
    exitCancel: "Continuă Configurarea"
  },
  ru: {
    profileSetupTitle: "Заполните Ваш Профиль",
    profileSetupDescription: "Помогите другим узнать вас лучше",
    stepOf: "Шаг {current} из {total}",
    previousButton: "Назад",
    nextButton: "Далее",
    skipButton: "Пропустить",
    finishButton: "Завершить",
    exitButton: "Выйти",
    savingProgress: "Сохранение...",
    profileCompleted: "Профиль успешно заполнен!",
    errorSaving: "Ошибка сохранения информации профиля",
    confirmExit: "Вы уверены, что хотите выйти? Ваш прогресс будет сохранен.",
    exitConfirm: "Да, Выйти",
    exitCancel: "Продолжить Настройку"
  }
};

const TOTAL_STEPS = 4;

export function ProfileSetupWizard({ onComplete, onExit }: ProfileSetupWizardProps) {
  const router = useRouter();
  const { translate } = useLanguage();
  const { toast } = useToast();
  const { revalidateUser } = useUser();

  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState<ProfileSetupData>({
    profilePhoto: null,
    profilePhotoUrl: null,
    phone: '',
    bio: '',
    spokenLanguages: ['ro'], // Default to Romanian
  });

  const progress = (currentStep / TOTAL_STEPS) * 100;

  const updateProfileData = useCallback((updates: Partial<ProfileSetupData>) => {
    setProfileData(prev => ({ ...prev, ...updates }));
  }, []);

  const saveProgress = useCallback(async (data: Partial<ProfileSetupData>) => {
    try {
      setIsLoading(true);

      // Save profile data (excluding photo which is handled separately)
      const { profilePhoto, profilePhotoUrl, ...profileInfo } = data;
      
      if (Object.keys(profileInfo).length > 0) {
        const response = await fetch('/api/user/profile/update', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(profileInfo),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to save profile');
        }
      }

      // Handle photo upload separately if there's a new photo
      if (profilePhoto instanceof File) {
        const formData = new FormData();
        formData.append('photo', profilePhoto);

        const photoResponse = await fetch('/api/user/profile/photo', {
          method: 'POST',
          body: formData,
        });

        if (!photoResponse.ok) {
          const errorData = await photoResponse.json();
          throw new Error(errorData.message || 'Failed to upload photo');
        }

        const photoResult = await photoResponse.json();
        updateProfileData({ profilePhotoUrl: photoResult.avatarUrl });
      }

      // Revalidate user data to get updated profile
      await revalidateUser();

    } catch (error) {
      console.error('Error saving profile progress:', error);
      toast({
        title: "Eroare!",
        description: translate(wizardTranslations, 'errorSaving'),
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [updateProfileData, revalidateUser, toast, translate]);

  const handleNext = async () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(prev => prev + 1);
    } else {
      // Final step - save all data and complete
      try {
        await saveProgress(profileData);
        toast({
          title: "Succes!",
          description: translate(wizardTranslations, 'profileCompleted'),
        });
        onComplete();
      } catch (error) {
        // Error already handled in saveProgress
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSkipStep = async () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(prev => prev + 1);
    } else {
      // Skip final step means complete with current data
      try {
        await saveProgress(profileData);
        onComplete();
      } catch (error) {
        // Error already handled in saveProgress
      }
    }
  };

  const handleExit = async () => {
    // Save current progress before exiting
    try {
      await saveProgress(profileData);
    } catch (error) {
      // Continue with exit even if save fails
    }
    onExit();
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <ProfilePhotoStep
            value={profileData.profilePhoto}
            currentPhotoUrl={profileData.profilePhotoUrl}
            onChange={(photo) => updateProfileData({ profilePhoto: photo })}
            onPhotoUrlChange={(url) => updateProfileData({ profilePhotoUrl: url })}
          />
        );
      case 2:
        return (
          <PhoneNumberStep
            value={profileData.phone || ''}
            onChange={(phone) => updateProfileData({ phone })}
          />
        );
      case 3:
        return (
          <BioStep
            value={profileData.bio || ''}
            onChange={(bio) => updateProfileData({ bio })}
          />
        );
      case 4:
        return (
          <LanguagesStep
            value={profileData.spokenLanguages || ['ro']}
            onChange={(languages) => updateProfileData({ spokenLanguages: languages })}
          />
        );
      default:
        return null;
    }
  };

  const isStepOptional = currentStep === 1 || currentStep === 3; // Photo and Bio are optional
  const canProceed = currentStep === 1 || currentStep === 3 || 
                    (currentStep === 2 && profileData.phone && profileData.phone.length >= 8) ||
                    (currentStep === 4 && profileData.spokenLanguages && profileData.spokenLanguages.length > 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl mx-auto">
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-6">
            <div className="flex items-center justify-between mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleExit}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4 mr-1" />
                {translate(wizardTranslations, 'exitButton')}
              </Button>
              <div className="text-sm text-muted-foreground">
                {translate(wizardTranslations, 'stepOf')
                  .replace('{current}', currentStep.toString())
                  .replace('{total}', TOTAL_STEPS.toString())}
              </div>
            </div>
            
            <Progress value={progress} className="mb-4" />
            
            <CardTitle className="text-2xl font-bold font-headline">
              {translate(wizardTranslations, 'profileSetupTitle')}
            </CardTitle>
            <CardDescription className="text-base">
              {translate(wizardTranslations, 'profileSetupDescription')}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Current Step Content */}
            <div className="min-h-[300px]">
              {renderCurrentStep()}
            </div>

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between pt-6 border-t">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1 || isLoading}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {translate(wizardTranslations, 'previousButton')}
              </Button>

              <div className="flex items-center gap-2">
                {isStepOptional && (
                  <Button
                    variant="ghost"
                    onClick={handleSkipStep}
                    disabled={isLoading}
                  >
                    {translate(wizardTranslations, 'skipButton')}
                  </Button>
                )}
                
                <Button
                  onClick={handleNext}
                  disabled={!canProceed || isLoading}
                >
                  {isLoading ? (
                    translate(wizardTranslations, 'savingProgress')
                  ) : currentStep === TOTAL_STEPS ? (
                    <>
                      <Check className="w-4 h-4 mr-2" />
                      {translate(wizardTranslations, 'finishButton')}
                    </>
                  ) : (
                    <>
                      {translate(wizardTranslations, 'nextButton')}
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
