
"use client";

import { useEffect, type ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, usePathname } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { Navbar } from '@/components/layout/navbar';
import { Footer } from '@/components/layout/footer';
import type { ExtendedNextAuthUser } from '@repo/auth';
import { useUser } from '@/contexts/user-context';

const LOGIN_PATH = '/login';
const CHANGE_PASSWORD_PATH = '/change-password-initial';
const REGISTER_PROVIDER_PATH = '/register-provider';

const LoadingScreen = () => (
  <div className="flex flex-col min-h-screen">
    <Navbar />
    <main className="flex-grow flex flex-col items-center justify-center">
      <Loader2 className="h-12 w-12 animate-spin text-primary" />
    </main>
    <Footer />
  </div>
);

export function AuthGuard({ children }: { children: ReactNode }) {
  const { status: sessionStatus } = useSession();
  const { user, isLoading: isUserLoading } = useUser();
  const router = useRouter();
  const currentPath = usePathname();
  
  useEffect(() => {
    const isLoading = sessionStatus === 'loading' || isUserLoading;

    console.log('[AuthGuard] Effect triggered:', {
      sessionStatus,
      isUserLoading,
      hasUser: !!user,
      userIsAdmin: user?.isAdmin,
      currentPath,
      isLoading
    });

    if (isLoading) {
      return;
    }

    if (sessionStatus === 'unauthenticated') {
      console.log('[AuthGuard] Unauthenticated - redirecting to login');
      const loginRedirectUrl = new URL(LOGIN_PATH, window.location.origin);
      loginRedirectUrl.searchParams.set('callbackUrl', currentPath);
      router.replace(loginRedirectUrl.toString());
      return;
    }

    if (user) {
      // Rule 1: Handle forced password change
      if (user.MustChangePassword && currentPath !== CHANGE_PASSWORD_PATH) {
        console.log('[AuthGuard] User must change password - redirecting');
        router.replace(CHANGE_PASSWORD_PATH);
        return;
      }
      if (!user.MustChangePassword && currentPath === CHANGE_PASSWORD_PATH) {
        console.log('[AuthGuard] Password change complete - redirecting to dashboard');
        router.replace(user.isAdmin ? '/admin' : '/dashboard');
        return;
      }

      // Rule 2: Handle role-based access - STRICT SEPARATION
      if (currentPath.startsWith('/admin') && !user.isAdmin) {
        console.log('[AuthGuard] Non-admin user attempting admin access - redirecting to dashboard');
        router.replace('/dashboard');
        return;
      }
      if (currentPath.startsWith('/dashboard') && user.isAdmin) {
        console.log('[AuthGuard] CRITICAL: Admin user attempting dashboard access - redirecting to admin panel');
        router.replace('/admin');
        return;
      }
      if (currentPath === REGISTER_PROVIDER_PATH && (user.isProvider || user.isAdmin)) {
        console.log('[AuthGuard] Already registered user accessing registration - redirecting');
        router.replace(user.isAdmin ? '/admin' : '/dashboard');
        return;
      }
    } else if (sessionStatus === 'authenticated' && !isUserLoading) {
      // This case means session exists, but user data couldn't be fetched.
      // Redirect to login to force a re-authentication.
      console.log('[AuthGuard] Session exists but no user data - redirecting to login');
      router.replace(LOGIN_PATH);
    }

  }, [sessionStatus, user, isUserLoading, currentPath, router]);
  
  if (sessionStatus === 'loading' || isUserLoading) {
    return <LoadingScreen />;
  }
  
  if (user) {
    return <>{children}</>;
  }

  return <LoadingScreen />;
}
