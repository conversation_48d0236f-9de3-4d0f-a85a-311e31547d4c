
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { BookingStatus, ServiceStatus } from '@prisma/client';
import { type AuthenticatedRequest } from '../middleware/auth';

const router = Router();

router.get('/client-stats', async (req: AuthenticatedRequest, res: Response) => {
  const clientIdStr = req.query.clientId as string | undefined;

  if (!clientIdStr) {
    return res.status(400).json({ message: 'Client ID is required' });
  }
  const clientId = parseInt(clientIdStr, 10);
  if (isNaN(clientId)) {
    return res.status(400).json({ message: 'Invalid Client ID format' });
  }

  try {
    const recentBookingsCount = await prisma.booking.count({
      where: {
        ClientId: clientId,
      },
    });

    const pendingReviewsCount = await prisma.booking.count({
        where: {
            ClientId: clientId,
            Status: BookingStatus.Completed,
            Review: null,
        }
    });

    return res.json({
      recentBookingsCount,
      pendingReviewsCount,
    });

  } catch (error) {
    console.error(`[API /dashboard/client-stats] Error fetching stats for client ${clientId}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch client stats';
    return res.status(500).json({ message: errorMessage });
  }
});

router.get('/provider-stats', async (req: AuthenticatedRequest, res: Response) => {
  const providerIdStr = req.query.providerId as string | undefined;

  if (!providerIdStr) {
    return res.status(400).json({ message: 'Provider ID is required' });
  }
  const providerId = parseInt(providerIdStr, 10);
  if (isNaN(providerId)) {
    return res.status(400).json({ message: 'Invalid Provider ID format' });
  }

  try {
    const activeServicesCount = await prisma.advertisedService.count({ 
      where: {
        ProviderId: providerId,
        Status: ServiceStatus.Activ, 
      },
    });

    const pendingRequestsCount = await prisma.booking.count({
        where: {
            ProviderId: providerId,
            Status: BookingStatus.Pending, 
        }
    });

    return res.json({
      activeServicesCount,
      pendingRequestsCount, 
    });

  } catch (error) {
    console.error(`[API /dashboard/provider-stats] Error fetching stats for provider ${providerId}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch provider stats';
    return res.status(500).json({ message: errorMessage });
  }
});    

export default router;
