import { Router, Response } from 'express';
import prisma from '../lib/db';
import { AuthenticatedRequest } from '../middleware/auth';
import { BookingStatus, NotificationType } from '@prisma/client';

const router = Router();

// Create a review for a completed booking
router.post('/create', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const clientId = parseInt(req.user.id, 10);
    const { bookingId, rating, comment } = req.body;

    if (!bookingId || !rating || rating < 1 || rating > 5) {
      return res.status(400).json({ 
        success: false, 
        message: 'Lipsesc datele necesare sau rating-ul este invalid (1-5).' 
      });
    }

    // Verify booking exists and is completed
    const booking = await prisma.booking.findFirst({
      where: {
        Id: bookingId,
        ClientId: clientId,
        Status: BookingStatus.Completed,
      },
      include: {
        Provider: { select: { FullName: true } },
        AdvertisedService: { select: { ServiceName: true } }
      }
    });

    if (!booking) {
      return res.status(404).json({ 
        success: false, 
        message: 'Rezervarea nu a fost găsită sau nu este finalizată.' 
      });
    }

    // Check if review already exists
    const existingReview = await prisma.review.findUnique({
      where: { BookingId: bookingId }
    });

    if (existingReview) {
      return res.status(409).json({ 
        success: false, 
        message: 'Ai deja o recenzie pentru această rezervare.' 
      });
    }

    // Create review
    const review = await prisma.review.create({
      data: {
        BookingId: bookingId,
        ClientId: clientId,
        ProviderId: booking.ProviderId,
        Rating: rating,
        Comment: comment || null,
      },
      include: {
        Client: { select: { FullName: true } },
        Booking: {
          include: {
            AdvertisedService: { select: { ServiceName: true } }
          }
        }
      }
    });

    // Notify provider of new review
    await prisma.notification.create({
      data: {
        UserId: booking.ProviderId,
        Message: `Ai primit o recenzie nouă de la ${booking.Client.FullName} pentru serviciul "${booking.AdvertisedService.ServiceName}".`,
        Link: `/dashboard/provider/reviews`,
        Type: NotificationType.NewReview,
      }
    });

    return res.json({ success: true, review });

  } catch (error) {
    console.error('[API /reviews/create POST] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la crearea recenziei.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Get reviews for a provider
router.get('/provider/:providerId', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const providerId = parseInt(req.params.providerId, 10);

    if (isNaN(providerId)) {
      return res.status(400).json({ success: false, message: 'ID furnizor invalid.' });
    }

    const reviews = await prisma.review.findMany({
      where: { ProviderId: providerId },
      include: {
        Client: { select: { FullName: true, AvatarUrl: true } },
        Booking: {
          include: {
            AdvertisedService: { select: { ServiceName: true } }
          }
        }
      },
      orderBy: { CreatedAt: 'desc' }
    });

    // Calculate average rating
    const averageRating = reviews.length > 0 
      ? reviews.reduce((sum, review) => sum + review.Rating, 0) / reviews.length
      : 0;

    return res.json({ 
      success: true, 
      reviews: reviews.map(review => ({
        id: review.Id,
        rating: review.Rating,
        comment: review.Comment,
        createdAt: review.CreatedAt.toISOString(),
        client: {
          fullName: review.Client.FullName,
          avatarUrl: review.Client.AvatarUrl,
        },
        service: {
          serviceName: review.Booking.AdvertisedService.ServiceName,
        }
      })),
      averageRating: Math.round(averageRating * 10) / 10,
      totalReviews: reviews.length
    });

  } catch (error) {
    console.error('[API /reviews/provider/:id GET] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la preluarea recenziilor.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Get reviews written by a client
router.get('/client/:clientId', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const clientId = parseInt(req.params.clientId, 10);
    const requestingUserId = parseInt(req.user.id, 10);

    // Ensure the requesting user is the client
    if (clientId !== requestingUserId) {
      return res.status(403).json({ success: false, message: 'Acces interzis.' });
    }

    const reviews = await prisma.review.findMany({
      where: { ClientId: clientId },
      include: {
        Provider: { select: { FullName: true } },
        Booking: {
          include: {
            AdvertisedService: { select: { ServiceName: true } }
          }
        }
      },
      orderBy: { CreatedAt: 'desc' }
    });

    return res.json({ 
      success: true, 
      reviews: reviews.map(review => ({
        id: review.Id,
        rating: review.Rating,
        comment: review.Comment,
        createdAt: review.CreatedAt.toISOString(),
        provider: {
          fullName: review.Provider.FullName,
        },
        service: {
          serviceName: review.Booking.AdvertisedService.ServiceName,
        }
      }))
    });

  } catch (error) {
    console.error('[API /reviews/client/:id GET] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la preluarea recenziilor.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Get bookings eligible for review (completed but not reviewed)
router.get('/eligible-bookings/:clientId', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat.' });
    }
    const clientId = parseInt(req.params.clientId, 10);
    const requestingUserId = parseInt(req.user.id, 10);

    // Ensure the requesting user is the client
    if (clientId !== requestingUserId) {
      return res.status(403).json({ success: false, message: 'Acces interzis.' });
    }

    const eligibleBookings = await prisma.booking.findMany({
      where: {
        ClientId: clientId,
        Status: BookingStatus.Completed,
        Review: null, // No review exists yet
      },
      include: {
        Provider: { select: { FullName: true, AvatarUrl: true } },
        AdvertisedService: { select: { ServiceName: true } }
      },
      orderBy: { UpdatedAt: 'desc' }
    });

    return res.json({ 
      success: true, 
      bookings: eligibleBookings.map(booking => ({
        id: booking.Id,
        eventStartDateTime: booking.EventStartDateTime?.toISOString(),
        provider: {
          id: booking.ProviderId,
          fullName: booking.Provider.FullName,
          avatarUrl: booking.Provider.AvatarUrl,
        },
        service: {
          id: booking.AdvertisedServiceId,
          serviceName: booking.AdvertisedService.ServiceName,
        }
      }))
    });

  } catch (error) {
    console.error('[API /reviews/eligible-bookings/:id GET] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la preluarea rezervărilor eligibile.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

// Update provider average rating (called after new review)
router.post('/update-provider-rating/:providerId', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const providerId = parseInt(req.params.providerId, 10);

    if (isNaN(providerId)) {
      return res.status(400).json({ success: false, message: 'ID furnizor invalid.' });
    }

    // Calculate new average rating
    const reviews = await prisma.review.findMany({
      where: { ProviderId: providerId },
      select: { Rating: true }
    });

    const averageRating = reviews.length > 0 
      ? reviews.reduce((sum, review) => sum + review.Rating, 0) / reviews.length
      : 0;

    // Update provider's average rating (assuming there's a field for this)
    // This would require adding an AverageRating field to the User model
    // For now, we'll just return the calculated rating
    
    return res.json({ 
      success: true, 
      averageRating: Math.round(averageRating * 10) / 10,
      totalReviews: reviews.length
    });

  } catch (error) {
    console.error('[API /reviews/update-provider-rating/:id POST] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la actualizarea rating-ului.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

export default router;
