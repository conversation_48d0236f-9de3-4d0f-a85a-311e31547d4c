// Types for the post-registration profile setup flow

export interface ProfileSetupStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isOptional: boolean;
}

export interface ProfileSetupData {
  profilePhoto: File | null;
  profilePhotoUrl: string | null;
  phone: string;
  bio: string;
  spokenLanguages: string[];
}

export interface ProfileSetupFormData {
  profilePhoto?: File | null;
  phone: string;
  bio: string;
  spokenLanguages: string[];
}

export interface ProfileSetupProgress {
  currentStep: number;
  totalSteps: number;
  completedSteps: string[];
  data: ProfileSetupData;
}

// Language options for the multi-select
export interface LanguageOption {
  code: string;
  label: string;
  nativeLabel: string;
}

// Available languages for selection
export const AVAILABLE_LANGUAGES: LanguageOption[] = [
  { code: 'ro', label: 'Romanian', nativeLabel: 'Română' },
  { code: 'ru', label: 'Russian', nativeLabel: 'Русский' },
  { code: 'uk', label: 'Ukrainian', nativeLabel: 'Українська' },
  { code: 'gag', label: 'Gagauz', nativeLabel: 'Gagauz' },
  { code: 'bg', label: 'Bulgarian', nativeLabel: 'Български' },
  { code: 'en', label: 'English', nativeLabel: 'English' },
  { code: 'it', label: 'Italian', nativeLabel: 'Italiano' },
  { code: 'es', label: 'Spanish', nativeLabel: 'Español' },
  { code: 'fr', label: 'French', nativeLabel: 'Français' },
  { code: 'de', label: 'German', nativeLabel: 'Deutsch' },
  { code: 'pt', label: 'Portuguese', nativeLabel: 'Português' },
  { code: 'tr', label: 'Turkish', nativeLabel: 'Türkçe' },
  { code: 'ar', label: 'Arabic', nativeLabel: 'العربية' },
  { code: 'pl', label: 'Polish', nativeLabel: 'Polski' },
];

// Profile setup steps configuration
export const PROFILE_SETUP_STEPS: ProfileSetupStep[] = [
  {
    id: 'photo',
    title: 'Adaugă o fotografie de profil',
    description: 'Ajută-i pe alții să te recunoască cu o fotografie de profil.',
    isCompleted: false,
    isOptional: true,
  },
  {
    id: 'phone',
    title: 'Adaugă numărul de telefon',
    description: 'Necesar pentru rezervări și comunicare cu furnizorii de servicii.',
    isCompleted: false,
    isOptional: false,
  },
  {
    id: 'bio',
    title: 'Scrie o scurtă descriere',
    description: 'Spune-le altora ceva despre tine și ce servicii cauți.',
    isCompleted: false,
    isOptional: true,
  },
  {
    id: 'languages',
    title: 'Selectează limbile vorbite',
    description: 'Ajută furnizorii să știe în ce limbă pot comunica cu tine.',
    isCompleted: false,
    isOptional: false,
  },
];

// Validation rules
export const PROFILE_SETUP_VALIDATION = {
  phone: {
    minLength: 8,
    maxLength: 15,
    pattern: /^[\+]?[0-9\s\-\(\)]+$/,
    errorMessage: 'Numărul de telefon nu este valid. Folosește doar cifre, spații, +, -, (, ).',
  },
  bio: {
    maxLength: 500,
    errorMessage: 'Descrierea nu poate depăși 500 de caractere.',
  },
  profilePhoto: {
    maxSizeBytes: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    errorMessage: 'Fotografia trebuie să fie în format JPG, PNG sau WebP și să nu depășească 5MB.',
  },
  spokenLanguages: {
    minCount: 1,
    errorMessage: 'Selectează cel puțin o limbă.',
  },
} as const;

// API response types
export interface ProfileUpdateResponse {
  success: boolean;
  message?: string;
  user?: {
    id: string;
    avatarUrl?: string | null;
    phone?: string | null;
    bio?: string | null;
    spokenLanguages?: string[];
  };
}

export interface ProfilePhotoUploadResponse {
  success: boolean;
  message?: string;
  avatarUrl?: string | null;
}

// Profile completion status
export interface ProfileCompletionStatus {
  isComplete: boolean;
  completedFields: string[];
  missingFields: string[];
  completionPercentage: number;
}
