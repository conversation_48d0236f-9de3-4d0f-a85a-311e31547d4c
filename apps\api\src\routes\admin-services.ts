import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { type AuthenticatedRequest } from '../middleware/auth';
import { PendingServiceStatus, UserRole } from '@prisma/client';

const router = Router();

/**
 * Update individual pending service status
 * POST /admin/provider-services/update
 */
router.post('/update', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { Id: parseInt(req.user.id, 10) },
      include: {
        UserRoles: {
          include: {
            Role: true
          }
        }
      }
    });

    if (!user || !user.UserRoles.some(ur => ur.Role.Name === UserRole.Admin)) {
      return res.status(403).json({ message: 'Access denied. Admin role required.' });
    }

    const { serviceId, status, adminNotes } = req.body;

    if (!serviceId || !status) {
      return res.status(400).json({ message: 'Service ID and status are required' });
    }

    // Validate status
    const validStatuses: PendingServiceStatus[] = ['PendingReview', 'Approved', 'Rejected', 'RequiresChanges'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status value' });
    }

    // Find the pending service
    const pendingService = await prisma.pendingService.findUnique({
      where: { Id: parseInt(serviceId, 10) },
      include: {
        Request: true,
        Category: true
      }
    });

    if (!pendingService) {
      return res.status(404).json({ message: 'Pending service not found' });
    }

    // Use transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Update the service status
      const updatedService = await tx.pendingService.update({
        where: { Id: parseInt(serviceId, 10) },
        data: {
          Status: status,
          AdminNotes: adminNotes || null,
          UpdatedAt: new Date()
        },
        include: {
          Request: true,
          Category: true,
          NannyServiceDetails: true,
          ElderCareServiceDetails: true,
          CleaningServiceDetails: true,
          TutoringServiceDetails: true,
          CookingServiceDetails: true,
        }
      });

      console.log(`[Admin Individual Service] ${user.FullName || user.Email} updated service ${pendingService.ServiceName} (ID: ${serviceId}) to status: ${status}`);

      let createdAdvertisedService = null;
      let providerRoleGranted = false;

      // If service is approved, create live AdvertisedService and grant provider role
      if (status === 'Approved') {
        console.log(`[Admin Individual Service] Creating live service for approved service: ${pendingService.ServiceName}`);

        // Check if user already has provider role
        const providerRole = await tx.role.findUnique({ where: { Name: 'Provider' } });
        if (!providerRole) {
          throw new Error('Provider role not found in database');
        }

        const existingProviderRole = await tx.userRoleJunction.findFirst({
          where: {
            UserId: pendingService.Request.UserId,
            RoleId: providerRole.Id
          }
        });

        // Grant provider role if not already assigned
        if (!existingProviderRole) {
          await tx.userRoleJunction.create({
            data: {
              UserId: pendingService.Request.UserId,
              RoleId: providerRole.Id
            }
          });
          console.log(`[Admin Individual Service] Granted Provider role to user ${pendingService.Request.UserId}`);
          providerRoleGranted = true;
        } else {
          console.log(`[Admin Individual Service] User ${pendingService.Request.UserId} already has Provider role`);
        }

        // Create live AdvertisedService
        createdAdvertisedService = await tx.advertisedService.create({
          data: {
            ProviderId: pendingService.Request.UserId,
            CategoryId: pendingService.CategoryId,
            ServiceCategorySlug: pendingService.ServiceCategorySlug,
            ServiceName: pendingService.ServiceName,
            Description: pendingService.Description,
            Status: 'Activ',
          }
        });

        console.log(`[Admin Individual Service] Created AdvertisedService with ID: ${createdAdvertisedService.Id} for provider ${pendingService.Request.UserId}`);

        // Create service-specific details
        const connectData = { AdvertisedService: { connect: { Id: createdAdvertisedService.Id }}};

        switch (pendingService.ServiceCategorySlug) {
          case 'Nanny':
            if (updatedService.NannyServiceDetails) {
              await tx.nannyServiceDetails.create({ data: {
                ...connectData,
                ExperienceYears: pendingService.ExperienceYears,
                LocationId: updatedService.NannyServiceDetails.LocationId,
                PricePerHour: updatedService.NannyServiceDetails.PricePerHour ? parseFloat(updatedService.NannyServiceDetails.PricePerHour) : null,
                PricePerDay: updatedService.NannyServiceDetails.PricePerDay ? parseFloat(updatedService.NannyServiceDetails.PricePerDay) : null,
                AvailabilityWeekdays: updatedService.NannyServiceDetails.AvailabilityWeekdays,
                AvailabilityWeekends: updatedService.NannyServiceDetails.AvailabilityWeekends,
                AvailabilityEvenings: updatedService.NannyServiceDetails.AvailabilityEvenings,
                PreferredAge_0_2: updatedService.NannyServiceDetails.PreferredAge_0_2,
                PreferredAge_3_6: updatedService.NannyServiceDetails.PreferredAge_3_6,
                PreferredAge_7_plus: updatedService.NannyServiceDetails.PreferredAge_7_plus,
              }});
            }
            break;
          case 'ElderCare':
            if (updatedService.ElderCareServiceDetails) {
              await tx.elderCareServiceDetails.create({ data: {
                ...connectData,
                ExperienceYears: pendingService.ExperienceYears,
                LocationId: updatedService.ElderCareServiceDetails.LocationId,
                PricePerHour: updatedService.ElderCareServiceDetails.PricePerHour ? parseFloat(updatedService.ElderCareServiceDetails.PricePerHour) : null,
                PricePerDay: updatedService.ElderCareServiceDetails.PricePerDay ? parseFloat(updatedService.ElderCareServiceDetails.PricePerDay) : null,
                AvailabilityWeekdays: updatedService.ElderCareServiceDetails.AvailabilityWeekdays,
                AvailabilityWeekends: updatedService.ElderCareServiceDetails.AvailabilityWeekends,
                AvailabilityEvenings: updatedService.ElderCareServiceDetails.AvailabilityEvenings,
              }});
            }
            break;
          case 'Cleaning':
            if (updatedService.CleaningServiceDetails) {
              await tx.cleaningServiceDetails.create({ data: {
                ...connectData,
                ExperienceYears: pendingService.ExperienceYears,
                LocationId: updatedService.CleaningServiceDetails.LocationId,
                PricePerHour: updatedService.CleaningServiceDetails.PricePerHour ? parseFloat(updatedService.CleaningServiceDetails.PricePerHour) : null,
                PricePerDay: updatedService.CleaningServiceDetails.PricePerDay ? parseFloat(updatedService.CleaningServiceDetails.PricePerDay) : null,
                AvailabilityWeekdays: updatedService.CleaningServiceDetails.AvailabilityWeekdays,
                AvailabilityWeekends: updatedService.CleaningServiceDetails.AvailabilityWeekends,
                AvailabilityEvenings: updatedService.CleaningServiceDetails.AvailabilityEvenings,
              }});
            }
            break;
          case 'Tutoring':
            if (updatedService.TutoringServiceDetails) {
              await tx.tutoringServiceDetails.create({ data: {
                ...connectData,
                ExperienceYears: pendingService.ExperienceYears,
                LocationId: updatedService.TutoringServiceDetails.LocationId,
                PricePerHour: updatedService.TutoringServiceDetails.PricePerHour ? parseFloat(updatedService.TutoringServiceDetails.PricePerHour) : null,
                PricePerDay: updatedService.TutoringServiceDetails.PricePerDay ? parseFloat(updatedService.TutoringServiceDetails.PricePerDay) : null,
                AvailabilityWeekdays: updatedService.TutoringServiceDetails.AvailabilityWeekdays,
                AvailabilityWeekends: updatedService.TutoringServiceDetails.AvailabilityWeekends,
                AvailabilityEvenings: updatedService.TutoringServiceDetails.AvailabilityEvenings,
                Grades_1_4: updatedService.TutoringServiceDetails.Grades_1_4,
                Grades_5_8: updatedService.TutoringServiceDetails.Grades_5_8,
                Grades_9_12: updatedService.TutoringServiceDetails.Grades_9_12,
              }});
            }
            break;
          case 'Cooking':
            if (updatedService.CookingServiceDetails) {
              await tx.cookingServiceDetails.create({ data: {
                ...connectData,
                ExperienceYears: pendingService.ExperienceYears,
                LocationId: updatedService.CookingServiceDetails.LocationId,
                PricePerHour: updatedService.CookingServiceDetails.PricePerHour ? parseFloat(updatedService.CookingServiceDetails.PricePerHour) : null,
                PricePerDay: updatedService.CookingServiceDetails.PricePerDay ? parseFloat(updatedService.CookingServiceDetails.PricePerDay) : null,
                AvailabilityWeekdays: updatedService.CookingServiceDetails.AvailabilityWeekdays,
                AvailabilityWeekends: updatedService.CookingServiceDetails.AvailabilityWeekends,
                AvailabilityEvenings: updatedService.CookingServiceDetails.AvailabilityEvenings,
              }});
            }
            break;
          default:
            console.warn(`[Admin Individual Service] Unknown service slug for detail creation: ${pendingService.ServiceCategorySlug}`);
        }

        console.log(`[Admin Individual Service] Successfully created live service and details for: ${pendingService.ServiceName}`);

        // Create notification for user
        await tx.notification.create({
          data: {
            UserId: pendingService.Request.UserId,
            Message: `Serviciul tău "${pendingService.ServiceName}" a fost aprobat și este acum activ!`,
            Link: '/dashboard/provider/services',
            Type: "ServiceApproved",
          },
        });
      }

      return {
        updatedService,
        createdAdvertisedService,
        providerRoleGranted
      };
    });

    return res.status(200).json({
      success: true,
      message: `Service status updated to ${status}`,
      service: result.updatedService,
      liveServiceCreated: !!result.createdAdvertisedService,
      providerRoleGranted: result.providerRoleGranted,
      liveServiceId: result.createdAdvertisedService?.Id
    });

  } catch (error) {
    console.error('[API /admin/provider-services/update] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update service status';
    return res.status(500).json({ message: errorMessage });
  }
});

/**
 * Get pending services with filtering
 * GET /admin/provider-services
 */
router.get('/', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { Id: parseInt(req.user.id, 10) },
      include: {
        UserRoles: {
          include: {
            Role: true
          }
        }
      }
    });

    if (!user || !user.UserRoles.some(ur => ur.Role.Name === UserRole.Admin)) {
      return res.status(403).json({ message: 'Access denied. Admin role required.' });
    }

    const { status, category, page = '1', limit = '10' } = req.query;

    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {};
    
    if (status && status !== 'all') {
      where.Status = status;
    }
    
    if (category && category !== 'all') {
      where.ServiceCategorySlug = category;
    }

    // Get services with pagination
    const [services, totalCount] = await Promise.all([
      prisma.pendingService.findMany({
        where,
        include: {
          Request: {
            include: {
              User: true
            }
          },
          Category: true,
          NannyServiceDetails: true,
          ElderCareServiceDetails: true,
          CleaningServiceDetails: true,
          TutoringServiceDetails: true,
          CookingServiceDetails: true,
        },
        orderBy: { CreatedAt: 'desc' },
        skip,
        take: limitNum
      }),
      prisma.pendingService.count({ where })
    ]);

    const totalPages = Math.ceil(totalCount / limitNum);

    return res.status(200).json({
      services,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        limit: limitNum,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1
      }
    });

  } catch (error) {
    console.error('[API /admin/provider-services GET] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch services';
    return res.status(500).json({ message: errorMessage });
  }
});

/**
 * Bulk update service statuses
 * POST /admin/provider-services/bulk-update
 */
router.post('/bulk-update', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { Id: parseInt(req.user.id, 10) },
      include: {
        UserRoles: {
          include: {
            Role: true
          }
        }
      }
    });

    if (!user || !user.UserRoles.some(ur => ur.Role.Name === UserRole.Admin)) {
      return res.status(403).json({ message: 'Access denied. Admin role required.' });
    }

    const { serviceIds, status, adminNotes } = req.body;

    if (!Array.isArray(serviceIds) || serviceIds.length === 0 || !status) {
      return res.status(400).json({ message: 'Service IDs array and status are required' });
    }

    // Validate status
    const validStatuses: PendingServiceStatus[] = ['PendingReview', 'Approved', 'Rejected', 'RequiresChanges'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status value' });
    }

    // Use transaction for bulk operations
    const result = await prisma.$transaction(async (tx) => {
      // First, get the services to be updated with their details
      const servicesToUpdate = await tx.pendingService.findMany({
        where: {
          Id: {
            in: serviceIds.map((id: any) => parseInt(id, 10))
          }
        },
        include: {
          Request: true,
          Category: true,
          NannyServiceDetails: true,
          ElderCareServiceDetails: true,
          CleaningServiceDetails: true,
          TutoringServiceDetails: true,
          CookingServiceDetails: true,
        }
      });

      // Update multiple services
      const updatedServices = await tx.pendingService.updateMany({
        where: {
          Id: {
            in: serviceIds.map((id: any) => parseInt(id, 10))
          }
        },
        data: {
          Status: status,
          AdminNotes: adminNotes || null,
          UpdatedAt: new Date()
        }
      });

      let createdServicesCount = 0;
      const userIdsToGrantRole = new Set<number>();

      // If status is Approved, create live services
      if (status === 'Approved') {
        console.log(`[Admin Bulk Service] Creating live services for ${servicesToUpdate.length} approved services`);

        const providerRole = await tx.role.findUnique({ where: { Name: 'Provider' } });
        if (!providerRole) {
          throw new Error('Provider role not found in database');
        }

        for (const service of servicesToUpdate) {
          try {
            // Track users who need provider role
            userIdsToGrantRole.add(service.Request.UserId);

            // Create live AdvertisedService
            const createdAdvertisedService = await tx.advertisedService.create({
              data: {
                ProviderId: service.Request.UserId,
                CategoryId: service.CategoryId,
                ServiceCategorySlug: service.ServiceCategorySlug,
                ServiceName: service.ServiceName,
                Description: service.Description,
                Status: 'Activ',
              }
            });

            console.log(`[Admin Bulk Service] Created AdvertisedService with ID: ${createdAdvertisedService.Id} for service: ${service.ServiceName}`);
            createdServicesCount++;

            // Create service-specific details (simplified for bulk operation)
            const connectData = { AdvertisedService: { connect: { Id: createdAdvertisedService.Id }}};

            switch (service.ServiceCategorySlug) {
              case 'Nanny':
                if (service.NannyServiceDetails) {
                  await tx.nannyServiceDetails.create({ data: {
                    ...connectData,
                    ExperienceYears: service.ExperienceYears,
                    LocationId: service.NannyServiceDetails.LocationId,
                    PricePerHour: service.NannyServiceDetails.PricePerHour ? parseFloat(service.NannyServiceDetails.PricePerHour) : null,
                    PricePerDay: service.NannyServiceDetails.PricePerDay ? parseFloat(service.NannyServiceDetails.PricePerDay) : null,
                    AvailabilityWeekdays: service.NannyServiceDetails.AvailabilityWeekdays,
                    AvailabilityWeekends: service.NannyServiceDetails.AvailabilityWeekends,
                    AvailabilityEvenings: service.NannyServiceDetails.AvailabilityEvenings,
                    PreferredAge_0_2: service.NannyServiceDetails.PreferredAge_0_2,
                    PreferredAge_3_6: service.NannyServiceDetails.PreferredAge_3_6,
                    PreferredAge_7_plus: service.NannyServiceDetails.PreferredAge_7_plus,
                  }});
                }
                break;
              case 'ElderCare':
                if (service.ElderCareServiceDetails) {
                  await tx.elderCareServiceDetails.create({ data: {
                    ...connectData,
                    ExperienceYears: service.ExperienceYears,
                    LocationId: service.ElderCareServiceDetails.LocationId,
                    PricePerHour: service.ElderCareServiceDetails.PricePerHour ? parseFloat(service.ElderCareServiceDetails.PricePerHour) : null,
                    PricePerDay: service.ElderCareServiceDetails.PricePerDay ? parseFloat(service.ElderCareServiceDetails.PricePerDay) : null,
                    AvailabilityWeekdays: service.ElderCareServiceDetails.AvailabilityWeekdays,
                    AvailabilityWeekends: service.ElderCareServiceDetails.AvailabilityWeekends,
                    AvailabilityEvenings: service.ElderCareServiceDetails.AvailabilityEvenings,
                  }});
                }
                break;
              case 'Cleaning':
                if (service.CleaningServiceDetails) {
                  await tx.cleaningServiceDetails.create({ data: {
                    ...connectData,
                    ExperienceYears: service.ExperienceYears,
                    LocationId: service.CleaningServiceDetails.LocationId,
                    PricePerHour: service.CleaningServiceDetails.PricePerHour ? parseFloat(service.CleaningServiceDetails.PricePerHour) : null,
                    PricePerDay: service.CleaningServiceDetails.PricePerDay ? parseFloat(service.CleaningServiceDetails.PricePerDay) : null,
                    AvailabilityWeekdays: service.CleaningServiceDetails.AvailabilityWeekdays,
                    AvailabilityWeekends: service.CleaningServiceDetails.AvailabilityWeekends,
                    AvailabilityEvenings: service.CleaningServiceDetails.AvailabilityEvenings,
                  }});
                }
                break;
              case 'Tutoring':
                if (service.TutoringServiceDetails) {
                  await tx.tutoringServiceDetails.create({ data: {
                    ...connectData,
                    ExperienceYears: service.ExperienceYears,
                    LocationId: service.TutoringServiceDetails.LocationId,
                    PricePerHour: service.TutoringServiceDetails.PricePerHour ? parseFloat(service.TutoringServiceDetails.PricePerHour) : null,
                    PricePerDay: service.TutoringServiceDetails.PricePerDay ? parseFloat(service.TutoringServiceDetails.PricePerDay) : null,
                    AvailabilityWeekdays: service.TutoringServiceDetails.AvailabilityWeekdays,
                    AvailabilityWeekends: service.TutoringServiceDetails.AvailabilityWeekends,
                    AvailabilityEvenings: service.TutoringServiceDetails.AvailabilityEvenings,
                    Grades_1_4: service.TutoringServiceDetails.Grades_1_4,
                    Grades_5_8: service.TutoringServiceDetails.Grades_5_8,
                    Grades_9_12: service.TutoringServiceDetails.Grades_9_12,
                  }});
                }
                break;
              case 'Cooking':
                if (service.CookingServiceDetails) {
                  await tx.cookingServiceDetails.create({ data: {
                    ...connectData,
                    ExperienceYears: service.ExperienceYears,
                    LocationId: service.CookingServiceDetails.LocationId,
                    PricePerHour: service.CookingServiceDetails.PricePerHour ? parseFloat(service.CookingServiceDetails.PricePerHour) : null,
                    PricePerDay: service.CookingServiceDetails.PricePerDay ? parseFloat(service.CookingServiceDetails.PricePerDay) : null,
                    AvailabilityWeekdays: service.CookingServiceDetails.AvailabilityWeekdays,
                    AvailabilityWeekends: service.CookingServiceDetails.AvailabilityWeekends,
                    AvailabilityEvenings: service.CookingServiceDetails.AvailabilityEvenings,
                  }});
                }
                break;
              default:
                console.warn(`[Admin Bulk Service] Unknown service slug for detail creation: ${service.ServiceCategorySlug}`);
            }

          } catch (error) {
            console.error(`[Admin Bulk Service] ERROR creating service ${service.ServiceName}:`, error);
            throw error; // Re-throw to rollback transaction
          }
        }

        // Grant provider role to all affected users
        for (const userId of userIdsToGrantRole) {
          const existingRole = await tx.userRoleJunction.findFirst({
            where: {
              UserId: userId,
              RoleId: providerRole.Id
            }
          });

          if (!existingRole) {
            await tx.userRoleJunction.create({
              data: {
                UserId: userId,
                RoleId: providerRole.Id
              }
            });
            console.log(`[Admin Bulk Service] Granted Provider role to user ${userId}`);
          }
        }

        console.log(`[Admin Bulk Service] Successfully created ${createdServicesCount} live services from bulk approval`);
      }

      return {
        updatedCount: updatedServices.count,
        createdServicesCount,
        usersGrantedRole: userIdsToGrantRole.size
      };
    });

    console.log(`[Admin Bulk Service] ${user.FullName || user.Email} bulk updated ${result.updatedCount} services to status: ${status}`);

    return res.status(200).json({
      success: true,
      message: `${result.updatedCount} services updated to ${status}`,
      updatedCount: result.updatedCount,
      liveServicesCreated: result.createdServicesCount,
      usersGrantedProviderRole: result.usersGrantedRole
    });

  } catch (error) {
    console.error('[API /admin/provider-services/bulk-update] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to bulk update services';
    return res.status(500).json({ message: errorMessage });
  }
});

export default router;
