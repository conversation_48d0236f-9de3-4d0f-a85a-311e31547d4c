
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { type AuthenticatedRequest } from '../middleware/auth';
import { ChatMessageStatus, NotificationType } from '@prisma/client';

const router = Router();

router.get('/rooms', async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user?.id) {
    return res.status(401).json({ success: false, message: 'Neautorizat' });
  }
  const currentUserId = parseInt(req.user.id, 10);
  if (isNaN(currentUserId)) {
      return res.status(400).json({ success: false, message: 'ID utilizator invalid' });
  }

  try {
    const chatRoomsFromDb = await prisma.chatRoom.findMany({
      where: {
        OR: [{ ClientId: currentUserId }, { ProviderId: currentUserId }],
      },
      include: {
        Client: { select: { Id: true, FullName: true, AvatarUrl: true } },
        Provider: { select: { Id: true, FullName: true, AvatarUrl: true } },
        AdvertisedService: { select: { ServiceName: true } },
        Messages: {
          orderBy: { CreatedAt: 'desc' },
          take: 1,
        },
      },
      orderBy: {
        UpdatedAt: 'desc'
      }
    });

    const formattedRooms = chatRoomsFromDb.map(room => {
      const otherUser = room.ClientId === currentUserId ? room.Provider : room.Client;
      const lastMessage = room.Messages[0];
      return {
        id: room.Id,
        otherUserName: otherUser.FullName,
        otherUserAvatar: otherUser.AvatarUrl,
        lastMessageContent: lastMessage?.Content,
        lastMessageAt: lastMessage?.CreatedAt.toISOString(),
        serviceName: room.AdvertisedService.ServiceName,
        // unreadCount needs a more complex query, skipping for now
      };
    });

    return res.json({ success: true, rooms: formattedRooms });
  } catch (error) {
    console.error('[API /chat/rooms GET] Eroare:', error);
    return res.status(500).json({ success: false, message: 'Eroare la preluarea conversațiilor.' });
  }
});

router.get('/:roomId/messages', async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user?.id) {
    return res.status(401).json({ success: false, message: 'Neautorizat' });
  }
  const currentUserId = parseInt(req.user.id, 10);
  if (isNaN(currentUserId)) {
      return res.status(400).json({ success: false, message: 'ID utilizator invalid' });
  }

  const { roomId } = req.params;
  if (!roomId) {
    return res.status(400).json({ success: false, message: 'ID cameră lipsă.' });
  }

  try {
    const room = await prisma.chatRoom.findFirst({
      where: {
        Id: roomId,
        OR: [{ ClientId: currentUserId }, { ProviderId: currentUserId }],
      },
      include: {
        Client: { select: { Id: true, FullName: true, AvatarUrl: true } },
        Provider: { select: { Id: true, FullName: true, AvatarUrl: true } },
        AdvertisedService: { select: { ServiceName: true } },
        Messages: {
          orderBy: { CreatedAt: 'asc' },
        },
      },
    });

    if (!room) {
      return res.status(404).json({ success: false, message: 'Camera nu a fost găsită sau nu ai acces.' });
    }

    const otherUser = room.ClientId === currentUserId ? room.Provider : room.Client;
    
    // Transform messages to camelCase for API response consistency
    const formattedMessages = room.Messages.map(msg => ({
        id: msg.Id,
        chatRoomId: msg.ChatRoomId,
        senderId: msg.SenderId,
        recipientId: msg.RecipientId,
        content: msg.Content,
        createdAt: msg.CreatedAt.toISOString(),
        status: msg.Status
    }));

    return res.json({
      success: true,
      messages: formattedMessages,
      partner: {
        id: otherUser.Id,
        name: otherUser.FullName,
        avatar: otherUser.AvatarUrl
      },
      service: {
        name: room.AdvertisedService.ServiceName
      }
    });

  } catch (error) {
    console.error(`[API /chat/${roomId}/messages GET] Eroare:`, error);
    return res.status(500).json({ success: false, message: 'Eroare la preluarea mesajelor.' });
  }
});

router.post('/initiate', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ success: false, message: 'Neautorizat: sesiune invalidă.' });
    }
    
    const clientId = parseInt(req.user.id, 10);
    if (isNaN(clientId)) {
         return res.status(400).json({ success: false, message: 'ID utilizator invalid în sesiune.' });
    }

    const { providerId, serviceId, initialMessageContent } = req.body;

    if (!providerId || !serviceId || !initialMessageContent || !initialMessageContent.trim()) {
      return res.status(400).json({ success: false, message: 'Lipsesc datele necesare: providerId, serviceId și initialMessageContent sunt obligatorii.' });
    }
    
    if (clientId === providerId) {
      return res.status(400).json({ success: false, message: 'Nu poți iniția o conversație cu tine însuți.' });
    }

    const serviceExists = await prisma.advertisedService.findUnique({
        where: { Id: serviceId, ProviderId: providerId }
    });
    if (!serviceExists) {
        return res.status(404).json({ success: false, message: 'Serviciul specificat sau furnizorul nu există sau nu corespund.' });
    }

    let chatRoom = await prisma.chatRoom.findFirst({
      where: {
          ClientId: clientId,
          ProviderId: providerId,
          AdvertisedServiceId: serviceId,
      },
    });

    if (!chatRoom) {
      chatRoom = await prisma.chatRoom.create({
        data: {
          ClientId: clientId,
          ProviderId: providerId,
          AdvertisedServiceId: serviceId,
        },
      });
    }

    const newMessage = await prisma.chatMessage.create({
      data: {
        ChatRoomId: chatRoom.Id,
        SenderId: clientId,
        RecipientId: providerId, 
        Content: initialMessageContent,
        Status: ChatMessageStatus.Sent, 
      },
    });

    const clientUser = await prisma.user.findUnique({ where: {Id: clientId}, select: {FullName: true}});
    const clientName = clientUser?.FullName || "Un client";
    
    await prisma.notification.create({
        data: {
            UserId: providerId,
            Message: `Mesaj nou de la ${clientName} referitor la serviciul "${serviceExists.ServiceName}".`,
            Link: `/dashboard/chat/${chatRoom.Id}`,
            Type: NotificationType.NewMessage 
        }
    });

    return res.json({ 
        success: true, 
        chatRoomId: chatRoom.Id, 
        message: { 
            id: newMessage.Id,
            chatRoomId: newMessage.ChatRoomId,
            senderId: newMessage.SenderId,
            recipientId: newMessage.RecipientId,
            content: newMessage.Content,
            createdAt: newMessage.CreatedAt.toISOString(),
            status: newMessage.Status,
        } 
    });

  } catch (error) {
    console.error('[API /chat/initiate POST] Eroare:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la inițierea conversației.';
    return res.status(500).json({ success: false, message: errorMessage });
  }
});

export default router;
