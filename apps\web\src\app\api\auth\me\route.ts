
'use server';

import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@repo/auth";
import type { ExtendedSession, ExtendedNextAuthUser } from "@repo/auth";
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      console.log(`[API /me] No active NextAuth session found.`);
      return NextResponse.json({ authenticated: false, user: null, message: 'Not authenticated' }, { status: 401 });
    }

    // The user object from the session is already populated by the JWT callback in NextAuth options.
    // It contains all the necessary, up-to-date information.
    // No direct database call is needed here.
    const userFromSession = session.user;

    console.log(`[API /me] User ${userFromSession.email} (ID: ${userFromSession.id}) fetched successfully from session token.`);

    return NextResponse.json({
      authenticated: true,
      user: {
        id: userFromSession.id,
        fullName: userFromSession.name,
        email: userFromSession.email,
        avatarUrl: userFromSession.image, // NextAuth maps token.picture to session.user.image
        image: userFromSession.image,
        bio: userFromSession.bio,
        phone: userFromSession.phone,
        roles: userFromSession.roles,
        isProvider: userFromSession.isProvider,
        isAdmin: userFromSession.isAdmin,
        MustChangePassword: userFromSession.MustChangePassword,
        SpokenLanguages: userFromSession.SpokenLanguages,
        provider: userFromSession.provider,
        createdAt: userFromSession.createdAt,
        EmailVerified: userFromSession.EmailVerified,
      },
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error in /api/auth/me';
    console.error('[API /me] Error fetching current user from session:', errorMessage, error);
    
    return NextResponse.json({ authenticated: false, user: null, message: `Error fetching user from session: ${errorMessage}` }, { status: 500 });
  }
}
