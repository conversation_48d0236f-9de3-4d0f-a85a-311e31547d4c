"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  RefreshCw,
  Upload,
  Eye,
  MessageSquare,
  Star,
  BarChart3
} from "lucide-react";
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import Link from 'next/link';

// Field Display Component for comprehensive service details
interface FieldDisplayProps {
  label: string;
  value: any;
  type: 'text' | 'boolean' | 'number' | 'array' | 'decimal';
  unit?: string;
}

const FieldDisplay: React.FC<FieldDisplayProps> = ({ label, value, type, unit }) => {
  const { translate } = useLanguage();

  const renderValue = () => {
    switch (type) {
      case 'boolean':
        return (
          <div className="flex items-center gap-2">
            <Switch checked={Boolean(value)} disabled />
            <span className="text-sm">
              {value ? translate(commonTranslations, 'adminFieldYes') : translate(commonTranslations, 'adminFieldNo')}
            </span>
          </div>
        );
      case 'array':
        if (!value || (Array.isArray(value) && value.length === 0)) {
          return <span className="text-muted-foreground italic">{translate(commonTranslations, 'adminFieldNoneSpecified')}</span>;
        }
        return (
          <div className="flex flex-wrap gap-1">
            {value.map((item: string, index: number) => (
              <Badge key={index} variant="outline" className="text-xs">
                {item}
              </Badge>
            ))}
          </div>
        );
      case 'number':
      case 'decimal':
        if (value === null || value === undefined) {
          return <span className="text-muted-foreground italic">{translate(commonTranslations, 'adminFieldNotProvided')}</span>;
        }
        return <span>{value}{unit ? ` ${unit}` : ''}</span>;
      case 'text':
      default:
        if (!value || value === '') {
          return <span className="text-muted-foreground italic">{translate(commonTranslations, 'adminFieldNotProvided')}</span>;
        }
        return <span>{value}</span>;
    }
  };

  return (
    <div className="space-y-1">
      <p className="text-xs font-medium text-muted-foreground">{label}</p>
      <div className="text-sm">{renderValue()}</div>
    </div>
  );
};

// Comprehensive Service Details Renderer
interface ComprehensiveServiceDetailsProps {
  service: PendingService;
}

const ComprehensiveServiceDetails: React.FC<ComprehensiveServiceDetailsProps> = ({ service }) => {
  const { translate } = useLanguage();

  const getServiceDetails = () => {
    return service.NannyServiceDetails ||
           service.ElderCareServiceDetails ||
           service.CleaningServiceDetails ||
           service.TutoringServiceDetails ||
           service.CookingServiceDetails;
  };

  const getServiceType = (): 'nanny' | 'eldercare' | 'cleaning' | 'tutoring' | 'cooking' | null => {
    if (service.NannyServiceDetails) return 'nanny';
    if (service.ElderCareServiceDetails) return 'eldercare';
    if (service.CleaningServiceDetails) return 'cleaning';
    if (service.TutoringServiceDetails) return 'tutoring';
    if (service.CookingServiceDetails) return 'cooking';
    return null;
  };

  const details = getServiceDetails();
  const serviceType = getServiceType();

  if (!details || !serviceType) {
    return (
      <div className="p-4 bg-orange-50 border border-orange-200 rounded-md">
        <p className="text-orange-700 text-sm">{translate(commonTranslations, 'adminNoServiceDetailsAvailable')}</p>
      </div>
    );
  }

  const renderCommonFields = () => (
    <div className="space-y-4">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'adminBasicInformation')}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <FieldDisplay label={translate(commonTranslations, 'locationLabel')} value={details.LocationValue} type="text" />
        <FieldDisplay label={translate(commonTranslations, 'pricePerHourLabel')} value={details.PricePerHour} type="decimal" unit="MDL" />
        <FieldDisplay label={translate(commonTranslations, 'pricePerDayLabel')} value={details.PricePerDay} type="decimal" unit="MDL" />
      </div>

      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2 mt-6">{translate(commonTranslations, 'generalAvailabilityLabel')}</h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FieldDisplay label={translate(commonTranslations, 'availabilityWeekdays')} value={details.AvailabilityWeekdays} type="boolean" />
        <FieldDisplay label={translate(commonTranslations, 'availabilityWeekends')} value={details.AvailabilityWeekends} type="boolean" />
        <FieldDisplay label={translate(commonTranslations, 'availabilityEvenings')} value={details.AvailabilityEvenings} type="boolean" />
      </div>
    </div>
  );

  const renderServiceSpecificFields = () => {
    switch (serviceType) {
      case 'nanny':
        return renderNannyFields();
      case 'eldercare':
        return renderElderCareFields();
      case 'cleaning':
        return renderCleaningFields();
      case 'tutoring':
        return renderTutoringFields();
      case 'cooking':
        return renderCookingFields();
      default:
        return null;
    }
  };

  const renderNannyFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'childcareTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcarePreferredAgeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAge0_2')} value={details.PreferredAge_0_2} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAge3_6')} value={details.PreferredAge_3_6} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAge7_plus')} value={details.PreferredAge_7_plus} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcareAvailabilityTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityFullTime')} value={details.AvailabilityFullTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityPartTime')} value={details.AvailabilityPartTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOccasional')} value={details.AvailabilityOccasional} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminBabysitting')} value={details.ServiceBabysitting} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminPlaytime')} value={details.ServicePlaytime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMeals')} value={details.ServiceMeals} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminBedtime')} value={details.ServiceBedtime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEducational')} value={details.ServiceEducational} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOutdoorActivities')} value={details.ServiceOutdoor} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminTransport')} value={details.ServiceTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHousework')} value={details.ServiceHousework} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalQualifications')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareFirstAidLabel')} value={details.ExtraFirstAid} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransport')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCooking')} value={details.ExtraCooking} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSpecialNeeds')} value={details.ExtraSpecialNeeds} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOvernightCare')} value={details.ExtraOvernightCare} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminAdditionalLanguages')} value={details.ExtraLanguages} type="text" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderElderCareFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'elderCareTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcareAvailabilityTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityFullTime')} value={details.AvailabilityFullTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityPartTime')} value={details.AvailabilityPartTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOccasional')} value={details.AvailabilityOccasional} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminPersonalCare')} value={details.ServicePersonalCare} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMedicalSupport')} value={details.ServiceMedicalSupport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCompanionship')} value={details.ServiceCompanionship} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHousekeeping')} value={details.ServiceHousekeeping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMeals')} value={details.ServiceMeals} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminTransport')} value={details.ServiceTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminShopping')} value={details.ServiceShopping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMobility')} value={details.ServiceMobility} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalQualifications')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminFirstAid')} value={details.ExtraFirstAid} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMedicalTraining')} value={details.ExtraMedicalTraining} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransport')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSpecialNeeds')} value={details.ExtraSpecialNeeds} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOvernightCare')} value={details.ExtraOvernightCare} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminAdditionalLanguages')} value={details.ExtraLanguages} type="text" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderCleaningFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'cleaningTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcareAvailabilityTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityFullTime')} value={details.AvailabilityFullTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityPartTime')} value={details.AvailabilityPartTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOccasional')} value={details.AvailabilityOccasional} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminRegularCleaning')} value={details.ServiceRegularCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminDeepCleaning')} value={details.ServiceDeepCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWindowCleaning')} value={details.ServiceWindowCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCarpetCleaning')} value={details.ServiceCarpetCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminLaundry')} value={details.ServiceLaundry} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminIroning')} value={details.ServiceIroning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOrganizing')} value={details.ServiceOrganizing} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminPostConstruction')} value={details.ServicePostConstruction} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalFeatures')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminOwnSupplies')} value={details.ExtraOwnSupplies} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEcoFriendly')} value={details.ExtraEcoFriendly} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransport')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminInsured')} value={details.ExtraInsured} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWeekendAvailable')} value={details.ExtraWeekendAvailable} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEmergencyService')} value={details.ExtraEmergencyService} type="boolean" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderTutoringFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'tutoringTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminAfterSchool')} value={details.ServiceAfterSchool} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHomeworkHelp')} value={details.ServiceHomeworkHelp} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminIndividualLessons')} value={details.ServiceIndividualLessons} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminGradeLevels')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminGrades1_4')} value={details.Grades_1_4} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminGrades5_8')} value={details.Grades_5_8} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminGrades9_12')} value={details.Grades_9_12} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminSubjects')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminRomanian')} value={details.SubjectRomanian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMath')} value={details.SubjectMath} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEnglish')} value={details.SubjectEnglish} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOtherSubjects')} value={details.SubjectOther} type="text" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminLessonFormats')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminOnline')} value={details.FormatOnline} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnHome')} value={details.FormatOwnHome} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminChildHome')} value={details.FormatChildHome} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalServices')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminGames')} value={details.ExtraGames} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSnack')} value={details.ExtraSnack} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminTransport')} value={details.ExtraTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSupervisedHomework')} value={details.ExtraSupervisedHomework} type="boolean" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderCookingFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'cookingTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminMealPrep')} value={details.ServiceMealPrep} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCatering')} value={details.ServiceCatering} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSpecialDiet')} value={details.ServiceSpecialDiet} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminBaking')} value={details.ServiceBaking} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminGroceryShopping')} value={details.ServiceGroceryShopping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminKitchenCleanup')} value={details.ServiceKitchenCleanup} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminCuisineTypes')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminRomanian')} value={details.CuisineRomanian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminItalian')} value={details.CuisineItalian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminFrench')} value={details.CuisineFrench} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminAsian')} value={details.CuisineAsian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminVegetarian')} value={details.CuisineVegetarian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminVegan')} value={details.CuisineVegan} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOtherCuisine')} value={details.CuisineOther} type="text" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminMealDetails')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminPricePerMeal')} value={details.PricePerMeal} type="decimal" unit="MDL" />
            <FieldDisplay label={translate(commonTranslations, 'adminMinPortions')} value={details.MinPortions} type="number" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalServices')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminOwnIngredients')} value={details.ExtraOwnIngredients} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransport')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWeekendAvailable')} value={details.ExtraWeekendAvailable} type="boolean" />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {renderCommonFields()}
      {renderServiceSpecificFields()}
    </div>
  );
};

export interface PendingService {
  Id: number;
  ServiceName: string;
  Description: string;
  ServiceCategorySlug: string;
  ExperienceYears: number;
  Status: 'PendingReview' | 'Approved' | 'Rejected' | 'RequiresChanges';
  AdminNotes?: string | null;
  DocumentPaths: string[];
  CreatedAt: string;
  UpdatedAt: string;
  Category: {
    Id: number;
    NameKey: string;
    Slug: string;
  };
  // Service-specific details
  NannyServiceDetails?: any;
  ElderCareServiceDetails?: any;
  CleaningServiceDetails?: any;
  TutoringServiceDetails?: any;
  CookingServiceDetails?: any;
}

export interface ProviderRegistrationWithServices {
  Id: string;
  UserName: string;
  UserEmail: string;
  Status: 'Pending' | 'Approved' | 'Rejected';
  RequestDate: string;
  AdminNotes?: string | null;
  PendingServices: PendingService[];
}

interface ProviderStatusDashboardProps {
  request: ProviderRegistrationWithServices;
  onRefresh?: () => void;
  onResubmitService?: (serviceId: number) => void;
}

const getStatusConfig = (translate: any) => ({
  PendingReview: {
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: Clock,
    label: translate(commonTranslations, 'provStatusUnderReview'),
    description: translate(commonTranslations, 'provStatusDescUnderReview')
  },
  Approved: {
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
    label: translate(commonTranslations, 'provStatusApproved'),
    description: translate(commonTranslations, 'provStatusDescApproved')
  },
  Rejected: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: XCircle,
    label: translate(commonTranslations, 'provStatusRejected'),
    description: translate(commonTranslations, 'provStatusDescRejected')
  },
  RequiresChanges: {
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: AlertTriangle,
    label: translate(commonTranslations, 'provStatusNeedsChanges'),
    description: translate(commonTranslations, 'provStatusDescNeedsChanges')
  }
});

export function ProviderStatusDashboard({ request, onRefresh, onResubmitService }: ProviderStatusDashboardProps) {
  const { translate } = useLanguage();
  const [activeServiceTab, setActiveServiceTab] = useState<number>(0);

  // Helper function to get experience years from service details
  const getServiceExperienceYears = (service: PendingService): number => {
    // ExperienceYears is on the main PendingService object, not in service details
    return service.ExperienceYears || 0;
  };

  // Helper function to calculate completion percentage
  const calculateCompletionPercentage = (service: PendingService): number => {
    const details = service.NannyServiceDetails ||
                   service.ElderCareServiceDetails ||
                   service.CleaningServiceDetails ||
                   service.TutoringServiceDetails ||
                   service.CookingServiceDetails;

    if (!details) {
      return 0;
    }

    // Count total fields and filled fields
    let totalFields = 0;
    let filledFields = 0;

    // Helper to check if a value is considered "filled"
    const isFilled = (value: any): boolean => {
      if (value === null || value === undefined) return false;
      if (typeof value === 'string' && value.trim() === '') return false;
      if (typeof value === 'boolean') return value; // Only count true booleans as filled
      if (typeof value === 'number') return value > 0;
      return true;
    };

    // Count fields based on service type
    const serviceType = service.ServiceCategorySlug;

    // Common fields for all services (from service details, not main service)
    const commonFields = [
      'LocationValue', 'PricePerHour', 'PricePerDay',
      'AvailabilityWeekdays', 'AvailabilityWeekends', 'AvailabilityEvenings'
    ];

    // Count main service fields
    totalFields++;
    if (isFilled(service.ExperienceYears)) filledFields++;

    // Count common service detail fields
    commonFields.forEach(field => {
      totalFields++;
      const fieldValue = details[field];
      const filled = isFilled(fieldValue);
      if (filled) filledFields++;
    });

    // Service-specific fields
    switch (serviceType) {
      case 'Nanny':
        const nannyFields = [
          // Age preferences
          'PreferredAge_0_2', 'PreferredAge_3_6', 'PreferredAge_7_plus',
          // Availability types
          'AvailabilityFullTime', 'AvailabilityPartTime', 'AvailabilityOccasional',
          // Services offered
          'ServiceBabysitting', 'ServicePlaytime', 'ServiceMeals', 'ServiceBedtime',
          'ServiceEducational', 'ServiceOutdoor', 'ServiceTransport', 'ServiceHousework',
          'ServiceHomeworkHelp',
          // Extra qualifications
          'ExtraFirstAid', 'ExtraOwnTransport', 'ExtraCooking', 'ExtraLanguages',
          'ExtraSpecialNeeds', 'ExtraOvernightCare'
        ];
        nannyFields.forEach(field => {
          totalFields++;
          const fieldValue = details[field];
          const filled = isFilled(fieldValue);
          if (filled) filledFields++;
        });
        break;

      case 'ElderCare':
        const elderCareFields = [
          // Availability types
          'AvailabilityFullTime', 'AvailabilityPartTime', 'AvailabilityOccasional',
          // Services offered
          'ServicePersonalCare', 'ServiceMedicalSupport', 'ServiceCompanionship',
          'ServiceHousekeeping', 'ServiceMeals', 'ServiceTransport', 'ServiceShopping',
          'ServiceMobility',
          // Extra qualifications
          'ExtraFirstAid', 'ExtraMedicalTraining', 'ExtraOwnTransport',
          'ExtraLanguages', 'ExtraSpecialNeeds', 'ExtraOvernightCare'
        ];
        elderCareFields.forEach(field => {
          totalFields++;
          const fieldValue = details[field];
          const filled = isFilled(fieldValue);
          if (filled) filledFields++;
        });
        break;

      case 'Cleaning':
        const cleaningFields = [
          // Availability types
          'AvailabilityFullTime', 'AvailabilityPartTime', 'AvailabilityOccasional',
          // Services offered
          'ServiceRegularCleaning', 'ServiceDeepCleaning', 'ServiceWindowCleaning',
          'ServiceCarpetCleaning', 'ServiceLaundry', 'ServiceIroning', 'ServiceOrganizing',
          'ServicePostConstruction',
          // Extra features
          'ExtraOwnSupplies', 'ExtraEcoFriendly', 'ExtraOwnTransport', 'ExtraInsured',
          'ExtraWeekendAvailable', 'ExtraEmergencyService'
        ];
        cleaningFields.forEach(field => {
          totalFields++;
          const fieldValue = details[field];
          const filled = isFilled(fieldValue);
          if (filled) filledFields++;
        });
        break;

      case 'Tutoring':
        const tutoringFields = [
          // Services offered
          'ServiceAfterSchool', 'ServiceHomeworkHelp', 'ServiceIndividualLessons',
          // Grade levels
          'Grades_1_4', 'Grades_5_8', 'Grades_9_12',
          // Subjects
          'SubjectRomanian', 'SubjectMath', 'SubjectEnglish', 'SubjectOther',
          // Lesson formats
          'FormatOnline', 'FormatOwnHome', 'FormatChildHome',
          // Additional services
          'ExtraGames', 'ExtraSnack', 'ExtraTransport', 'ExtraSupervisedHomework'
        ];
        tutoringFields.forEach(field => {
          totalFields++;
          const fieldValue = details[field];
          const filled = isFilled(fieldValue);
          if (filled) filledFields++;
        });
        break;

      case 'Cooking':
        const cookingFields = [
          // Pricing fields specific to cooking
          'PricePerMeal', 'MinPortions', 'PriceSubscriptionAmount', 'PriceSubscriptionUnit',
          'PriceSubscriptionText', 'SubscriptionDetails',
          // Services offered
          'ServiceMealPrep', 'ServiceCatering', 'ServiceSpecialDiet', 'ServiceBaking',
          'ServiceGroceryShopping', 'ServiceKitchenCleanup',
          // Cuisine types
          'CuisineRomanian', 'CuisineItalian', 'CuisineFrench', 'CuisineAsian',
          'CuisineVegetarian', 'CuisineVegan', 'CuisineOther',
          // Additional services
          'ExtraOwnIngredients', 'ExtraOwnTransport', 'ExtraWeekendAvailable'
        ];
        cookingFields.forEach(field => {
          totalFields++;
          const fieldValue = details[field];
          const filled = isFilled(fieldValue);
          if (filled) filledFields++;
        });
        break;
    }

    const percentage = totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0;
    return percentage;
  };

  // Helper function to get completion badge color
  const getCompletionBadgeColor = (percentage: number): string => {
    if (percentage >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (percentage >= 50) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  // Helper function to get service-specific experience badge color
  const getExperienceBadgeColor = (serviceSlug: string): string => {
    switch (serviceSlug) {
      case 'Nanny': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'ElderCare': return 'bg-teal-100 text-teal-800 border-teal-200';
      case 'Cleaning': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Tutoring': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Cooking': return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getOverallProgress = () => {
    const totalServices = request.PendingServices.length;
    const approvedServices = request.PendingServices.filter(s => s.Status === 'Approved').length;
    const rejectedServices = request.PendingServices.filter(s => s.Status === 'Rejected').length;
    const pendingServices = request.PendingServices.filter(s => s.Status === 'PendingReview').length;
    const changesNeeded = request.PendingServices.filter(s => s.Status === 'RequiresChanges').length;

    return {
      total: totalServices,
      approved: approvedServices,
      rejected: rejectedServices,
      pending: pendingServices,
      changesNeeded,
      progressPercentage: totalServices > 0 ? (approvedServices / totalServices) * 100 : 0
    };
  };

  const progress = getOverallProgress();

  const renderServiceCard = (service: PendingService) => {
    const statusConfig = getStatusConfig(translate);
    const statusInfo = statusConfig[service.Status];
    const StatusIcon = statusInfo.icon;
    const experienceYears = getServiceExperienceYears(service);
    const completionPercentage = calculateCompletionPercentage(service);

    return (
      <Card key={service.Id} className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <CardTitle className="text-lg">{service.ServiceName}</CardTitle>
              <div className="flex items-center gap-2 flex-wrap">
                <Badge variant="outline" className="text-xs">
                  {service.Category.NameKey}
                </Badge>
                <Badge className={`text-xs ${statusInfo.color}`}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {statusInfo.label}
                </Badge>

                {/* Experience Badge */}
                {experienceYears >= 0 && (
                  <Badge className={`text-xs ${getExperienceBadgeColor(service.ServiceCategorySlug)}`}>
                    <Star className="w-3 h-3 mr-1" />
                    {experienceYears} {translate(commonTranslations, 'yearsSuffix')}
                  </Badge>
                )}

                {/* Completion Percentage Badge */}
                <Badge className={`text-xs ${getCompletionBadgeColor(completionPercentage)}`}>
                  <BarChart3 className="w-3 h-3 mr-1" />
                  {completionPercentage}% {translate(commonTranslations, 'adminComplete')}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">{statusInfo.description}</p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="text-sm">
            <p className="text-muted-foreground">{service.Description}</p>
          </div>

          {service.DocumentPaths.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">{translate(commonTranslations, 'provStatusUploadedDocs')}</h4>
              <div className="flex flex-wrap gap-2">
                {service.DocumentPaths.map((_, index) => (
                  <Button key={index} variant="outline" size="sm" className="text-xs">
                    <FileText className="w-3 h-3 mr-1" />
                    {translate(commonTranslations, 'provStatusDocumentNumber').replace('{number}', (index + 1).toString())}
                    <Eye className="w-3 h-3 ml-1" />
                  </Button>
                ))}
              </div>
            </div>
          )}

          <div className="space-y-3 pt-3 border-t">
            <div className="text-xs text-muted-foreground">
              <p><span className="font-medium">{translate(commonTranslations, 'provStatusSubmitted')}</span> {new Date(service.CreatedAt).toLocaleDateString()}</p>
              <p><span className="font-medium">{translate(commonTranslations, 'provStatusLastUpdated')}</span> {new Date(service.UpdatedAt).toLocaleDateString()}</p>
            </div>

            {/* Comprehensive Service Details */}
            <div className="mt-4">
              <h5 className="font-medium text-sm mb-3">{translate(commonTranslations, 'adminServiceDetails')}</h5>
              <ComprehensiveServiceDetails service={service} />
            </div>
          </div>

          {service.AdminNotes && (
            <div className="space-y-2 pt-3 border-t">
              <div className="flex items-center gap-2">
                <MessageSquare className="w-4 h-4 text-muted-foreground" />
                <Label className="text-sm font-medium">{translate(commonTranslations, 'provStatusAdminFeedback')}</Label>
              </div>
              <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
                {service.AdminNotes}
              </div>
            </div>
          )}

          {(service.Status === 'Rejected' || service.Status === 'RequiresChanges') && (
            <div className="flex gap-2 pt-3 border-t">
              <Button
                onClick={() => onResubmitService?.(service.Id)}
                size="sm"
                className="flex-1"
              >
                <Upload className="w-4 h-4 mr-1" />
                Resubmit Service
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Overall Status Card */}
      <Card className="shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold">{translate(commonTranslations, 'provStatusRegistrationTitle')}</CardTitle>
              <p className="text-muted-foreground mt-1">
                {translate(commonTranslations, 'provStatusTrackProgress')}
              </p>
            </div>
            {onRefresh && (
              <Button variant="outline" size="sm" onClick={onRefresh}>
                <RefreshCw className="w-4 h-4 mr-1" />
                Refresh
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{translate(commonTranslations, 'provStatusOverallProgress')}</span>
              <span>{translate(commonTranslations, 'provStatusServicesApproved')
                .replace('{approved}', progress.approved.toString())
                .replace('{total}', progress.total.toString())}</span>
            </div>
            <Progress value={progress.progressPercentage} className="h-2" />
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{progress.approved}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'provStatusApproved')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{progress.pending}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'provStatusUnderReview')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{progress.changesNeeded}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'provStatusNeedsChanges')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{progress.rejected}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'provStatusRejected')}</div>
            </div>
          </div>

          {request.AdminNotes && (
            <div className="space-y-2 pt-4 border-t">
              <Label className="text-sm font-medium">{translate(commonTranslations, 'provStatusOverallNotes')}</Label>
              <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
                {request.AdminNotes}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Individual Service Cards */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">{translate(commonTranslations, 'adminYourServiceApplications')}</h3>

        {request.PendingServices.length > 1 ? (
          // Tabbed interface for multiple services
          <Tabs value={activeServiceTab.toString()} onValueChange={(value) => setActiveServiceTab(parseInt(value))}>
            <TabsList className={`grid w-full gap-2 ${
              request.PendingServices.length === 2 ? 'grid-cols-2' :
              request.PendingServices.length === 3 ? 'grid-cols-3' :
              request.PendingServices.length === 4 ? 'grid-cols-4' :
              'grid-cols-5'
            }`}>
              {request.PendingServices.map((service, index) => (
                <TabsTrigger
                  key={service.Id}
                  value={index.toString()}
                  className="text-xs"
                >
                  {translate(commonTranslations, service.Category.NameKey as keyof typeof commonTranslations)}
                </TabsTrigger>
              ))}
            </TabsList>

            {request.PendingServices.map((service, index) => (
              <TabsContent key={service.Id} value={index.toString()}>
                {renderServiceCard(service)}
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          // Single service - no tabs needed
          request.PendingServices.map(renderServiceCard)
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4 justify-center pt-6">
        <Button asChild variant="outline">
          <Link href="/dashboard">{translate(commonTranslations, 'provStatusGoToDashboard')}</Link>
        </Button>
        {progress.approved > 0 && (
          <Button asChild>
            <Link href="/dashboard/provider">{translate(commonTranslations, 'provStatusViewProviderPanel')}</Link>
          </Button>
        )}
      </div>
    </div>
  );
}
