
"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { UserRole } from '@prisma/client';
import { Edit2, ShieldAlert } from "lucide-react";
import { getAvatarUrl } from '@/lib/avatar-utils';

interface UserWithRoles {
  id: number;
  fullName: string | null;
  email: string;
  avatarUrl?: string | null;
  roles: UserRole[];
  createdAt: string;
}

interface UserDetailsDialogProps {
  user: UserWithRoles | null;
  isOpen: boolean;
  onClose: () => void;
}

const getInitials = (name: string | null | undefined): string => {
  if (!name || typeof name !== 'string') return 'U';
  const words = name.split(' ').filter(Boolean);
  if (words.length > 1) {
    return (words[0][0] + words[words.length - 1][0]).toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
};

const getRoleBadgeVariant = (role: UserRole) => {
  if (role === UserRole.Admin) return 'destructive';
  if (role === UserRole.Provider) return 'secondary';
  return 'outline';
};

export function UserDetailsDialog({ user, isOpen, onClose }: UserDetailsDialogProps) {
  const { translate } = useLanguage();

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-4 mb-4">
             <Avatar className="h-16 w-16">
                <AvatarImage src={getAvatarUrl(user.avatarUrl)} alt={user.fullName || "Avatar"} />
                <AvatarFallback>{getInitials(user.fullName)}</AvatarFallback>
            </Avatar>
            <div>
              <DialogTitle className="text-2xl">{user.fullName}</DialogTitle>
              <DialogDescription>{user.email}</DialogDescription>
            </div>
          </div>
        </DialogHeader>
        
        <div className="space-y-4 py-2">
            <div className="text-sm">
                <span className="font-semibold text-muted-foreground">ID Utilizator:</span>
                <span className="ml-2 font-mono bg-muted px-2 py-1 rounded text-foreground">{user.id}</span>
            </div>
            <div className="text-sm">
                <span className="font-semibold text-muted-foreground">Înregistrat la:</span>
                <span className="ml-2">{new Date(user.createdAt).toLocaleDateString('ro-RO', { year: 'numeric', month: 'long', day: 'numeric'})}</span>
            </div>
             <div className="flex flex-wrap items-center gap-2">
                <span className="font-semibold text-muted-foreground">Roluri:</span>
                {user.roles.map(role => (
                    <Badge key={role} variant={getRoleBadgeVariant(role)} className="capitalize">
                      {translate(commonTranslations, `role${role}` as keyof typeof commonTranslations) || role}
                    </Badge>
                ))}
            </div>
        </div>

        <DialogFooter className="mt-4 flex-col sm:flex-row gap-2">
            <Button variant="outline" disabled><Edit2 className="w-4 h-4 mr-2"/> Editează Roluri</Button>
            <Button variant="destructive" disabled><ShieldAlert className="w-4 h-4 mr-2"/> Suspendă</Button>
            <DialogClose asChild>
                <Button variant="secondary">Închide</Button>
            </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

