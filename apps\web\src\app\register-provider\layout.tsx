
import { AuthGuard } from "@/components/auth/auth-guard";
import { Footer } from "@/components/layout/footer";
import { Navbar } from "@/components/layout/navbar";

export default function RegisterProviderLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard>
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow flex items-center justify-center py-8 md:py-12 px-4 sm:px-6 lg:px-8 bg-background">
          {children}
        </main>
        <Footer />
      </div>
    </AuthGuard>
  );
}
