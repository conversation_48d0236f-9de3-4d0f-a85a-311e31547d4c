
"use client";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { useLanguage } from '@/contexts/language-context';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Link from "next/link";
import { Button } from "@/components/ui/button";

const helpCenterPageTranslations = {
  pageTitle: { ro: "Centru de Ajutor", ru: "Центр помощи", en: "Help Center" },
  pageDescription: { 
    ro: "Găsește răspunsuri la întrebările tale și ghiduri pentru a utiliza platforma bonami.", 
    ru: "Найдите ответы на ваши вопросы и руководства по использованию платформы bonami.", 
    en: "Find answers to your questions and guides to use the bonami platform." 
  },

  generalFaqTitle: { ro: "Înt<PERSON><PERSON><PERSON><PERSON>e Generale", ru: "Общие часто задаваемые вопросы", en: "General Frequently Asked Questions" },
  qGeneral1: { ro: "Ce este bonami?", ru: "Что такое bonami?", en: "What is bonami?" },
  aGeneral1: { ro: "bonami este o platformă care conectează familiile cu profesioniști de încredere pentru diverse servicii de îngrijire personală. Detalii complete vor fi disponibile în curând.", ru: "bonami — это платформа, которая связывает семьи с надежными профессионалами для различных услуг личного ухода. Полная информация скоро будет доступна.", en: "bonami is a platform that connects families with trusted professionals for various personal care services. Full details will be available soon." },
  qGeneral2: { ro: "Este gratuit să folosesc bonami?", ru: "Бесплатно ли пользоваться bonami?", en: "Is it free to use bonami?" },
  aGeneral2: { ro: "Crearea unui cont și căutarea serviciilor este gratuită pentru clienți. Furnizorii pot avea anumite condiții. Detalii complete vor fi disponibile în curând.", ru: "Создание учетной записи и поиск услуг бесплатны для клиентов. У поставщиков могут быть определенные условия. Полная информация скоро будет доступна.", en: "Creating an account and searching for services is free for clients. Providers may have certain conditions. Full details will be available soon." },

  forClientsTitle: { ro: "Pentru Clienți", ru: "Для клиентов", en: "For Clients" },
  qClient1: { ro: "Cum creez un cont?", ru: "Как создать учетную запись?", en: "How do I create an account?" },
  aClient1: { ro: "Click pe butonul 'Înregistrare' din meniu și urmează pașii. Detalii complete vor fi disponibile în curând.", ru: "Нажмите кнопку 'Регистрация' в меню и следуйте инструкциям. Полная информация скоро будет доступна.", en: "Click the 'Register' button in the menu and follow the steps. Full details will be available soon." },
  qClient2: { ro: "Cum găsesc un furnizor?", ru: "Как найти поставщика услуг?", en: "How do I find a provider?" },
  aClient2: { ro: "Utilizează pagina 'Servicii' sau funcția de căutare de pe prima pagină. Poți filtra după tipul de serviciu, locație, etc. Detalii complete vor fi disponibile în curând.", ru: "Используйте страницу 'Услуги' или функцию поиска на главной странице. Вы можете фильтровать по типу услуги, местоположению и т. д. Полная информация скоро будет доступна.", en: "Use the 'Services' page or the search function on the homepage. You can filter by service type, location, etc. Full details will be available soon." },
  qClient3: { ro: "Cum funcționează plata?", ru: "Как работает оплата?", en: "How does payment work?" },
  aClient3: { ro: "Plățile se fac de obicei direct către furnizor, conform acordului. Platforma poate oferi opțiuni de plată sigură în viitor. Detalii complete vor fi disponibile în curând.", ru: "Платежи обычно производятся напрямую поставщику услуг в соответствии с договоренностью. Платформа может предложить безопасные варианты оплаты в будущем. Полная информация скоро будет доступна.", en: "Payments are usually made directly to the provider as agreed. The platform may offer secure payment options in the future. Full details will be available soon." },

  forProvidersTitle: { ro: "Pentru Furnizori", ru: "Для поставщиков", en: "For Providers" },
  qProvider1: { ro: "Cum mă înregistrez ca furnizor?", ru: "Как зарегистрироваться в качестве поставщика?", en: "How do I register as a provider?" },
  aProvider1: { ro: "Accesează pagina 'Devino Furnizor' și completează formularul. Vom analiza cererea ta. Detalii complete vor fi disponibile în curând.", ru: "Перейдите на страницу 'Стать поставщиком' и заполните форму. Мы рассмотрим вашу заявку. Полная информация скоро будет доступна.", en: "Go to the 'Become a Provider' page and fill out the form. We will review your application. Full details will be available soon." },
  qProvider2: { ro: "Cum îmi gestionez profilul și serviciile?", ru: "Как управлять своим профилем и услугами?", en: "How do I manage my profile and services?" },
  aProvider2: { ro: "După autentificare, vei avea acces la un panou de control unde poți actualiza informațiile și serviciile oferite. Detalii complete vor fi disponibile în curând.", ru: "После входа в систему у вас будет доступ к панели управления, где вы сможете обновлять информацию и предлагаемые услуги. Полная информация скоро будет доступна.", en: "After logging in, you will have access to a control panel where you can update your information and offered services. Full details will be available soon." },

  contactSupportTitle: { ro: "Nu ai găsit răspunsul?", ru: "Не нашли ответ?", en: "Didn't find the answer?" },
  contactSupportDescription: { 
    ro: "Echipa noastră de suport este gata să te ajute. Contactează-ne pentru asistență suplimentară.", 
    ru: "Наша служба поддержки готова вам помочь. Свяжитесь с нами для получения дополнительной помощи.", 
    en: "Our support team is ready to help you. Contact us for further assistance." 
  },
  contactButton: { ro: "Contactează Suportul", ru: "Связаться с поддержкой", en: "Contact Support" },
};

export default function HelpCenterPage() {
  const { translate } = useLanguage();

  const faqSections = [
    {
      titleKey: 'generalFaqTitle',
      items: [
        { qKey: 'qGeneral1', aKey: 'aGeneral1' },
        { qKey: 'qGeneral2', aKey: 'aGeneral2' },
      ]
    },
    {
      titleKey: 'forClientsTitle',
      items: [
        { qKey: 'qClient1', aKey: 'aClient1' },
        { qKey: 'qClient2', aKey: 'aClient2' },
        { qKey: 'qClient3', aKey: 'aClient3' },
      ]
    },
    {
      titleKey: 'forProvidersTitle',
      items: [
        { qKey: 'qProvider1', aKey: 'aProvider1' },
        { qKey: 'qProvider2', aKey: 'aProvider2' },
      ]
    }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <div className="max-w-3xl mx-auto">
            <CardHeader className="text-center px-0 pt-0 pb-8">
                <CardTitle className="text-3xl md:text-4xl font-bold font-headline">
                    {translate(helpCenterPageTranslations, 'pageTitle')}
                </CardTitle>
                <CardDescription className="text-lg text-muted-foreground">
                    {translate(helpCenterPageTranslations, 'pageDescription')}
                </CardDescription>
            </CardHeader>

            {faqSections.map((section, sectionIndex) => (
              <Card key={sectionIndex} className="mb-8 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-xl font-headline text-primary">
                    {translate(helpCenterPageTranslations, section.titleKey)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {section.items.map((faq, index) => (
                      <AccordionItem value={`item-${sectionIndex}-${index}`} key={index}>
                        <AccordionTrigger className="text-left hover:no-underline">
                          {translate(helpCenterPageTranslations, faq.qKey)}
                        </AccordionTrigger>
                        <AccordionContent className="text-muted-foreground">
                          {translate(helpCenterPageTranslations, faq.aKey)}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            ))}
            
            <Card className="text-center shadow-lg">
                <CardHeader>
                    <CardTitle className="text-xl font-headline">
                        {translate(helpCenterPageTranslations, 'contactSupportTitle')}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground mb-6">
                        {translate(helpCenterPageTranslations, 'contactSupportDescription')}
                    </p>
                    <Button asChild>
                        <Link href="/contact">
                            {translate(helpCenterPageTranslations, 'contactButton')}
                        </Link>
                    </Button>
                </CardContent>
            </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}
