
"use client";
import { useEffect, useState, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, Loader2, Users } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { UserRole } from '@prisma/client';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useDebounce } from 'use-debounce';
import { UserDetailsDialog } from './_components/user-details-dialog';

interface UserWithRoles {
  id: number;
  fullName: string | null;
  email: string;
  avatarUrl?: string | null;
  roles: UserRole[];
  createdAt: string;
}

interface ApiResponse {
  users: UserWithRoles[];
  totalPages: number;
  currentPage: number;
  totalUsers: number;
}

export default function AdminUsersPage() {
  const { translate } = useLanguage();
  const [data, setData] = useState<ApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [currentPage, setCurrentPage] = useState(1);
  const [roleFilter, setRoleFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 500);

  const [selectedUser, setSelectedUser] = useState<UserWithRoles | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  const fetchUsers = useCallback(async (page: number, role: string, search: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: String(page),
        limit: '10',
      });
      if (role && role !== 'all') {
        params.append('role', role);
      }
      if (search) {
        params.append('search', search);
      }

      const response = await fetch(`/api/proxy/admin/users?${params.toString()}`);
      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(responseData.message || translate(commonTranslations, 'adminErrorFetchingUsers'));
      }
      setData(responseData);
    } catch (err) {
      setError(err instanceof Error ? err.message : translate(commonTranslations, 'adminErrorUnknown'));
    } finally {
      setIsLoading(false);
    }
  }, [translate]);

  useEffect(() => {
    fetchUsers(currentPage, roleFilter, debouncedSearchTerm);
  }, [currentPage, roleFilter, debouncedSearchTerm, fetchUsers]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= (data?.totalPages || 1)) {
      setCurrentPage(newPage);
    }
  };

  const handleRoleChange = (newRole: string) => {
    setRoleFilter(newRole);
    setCurrentPage(1); // Reset to first page on filter change
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset to first page on search
  };

  const getRoleBadgeVariant = (role: UserRole) => {
    if (role === UserRole.Admin) return 'destructive';
    if (role === UserRole.Provider) return 'secondary';
    return 'outline';
  };

  const handleViewDetails = (user: UserWithRoles) => {
    setSelectedUser(user);
    setIsDetailsDialogOpen(true);
  };

  return (
    <>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-headline flex items-center">
              <Users className="w-6 h-6 mr-2 text-primary" />
              {translate(commonTranslations, 'adminUserManagementTitle')}
            </CardTitle>
            <CardDescription>{translate(commonTranslations, 'adminUserManagementDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <Input
                placeholder={translate(commonTranslations, 'searchUsersPlaceholder')}
                value={searchTerm}
                onChange={handleSearchChange}
                className="max-w-sm"
              />
              <Select value={roleFilter} onValueChange={handleRoleChange}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder={translate(commonTranslations, 'filterByRole')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{translate(commonTranslations, 'allRoles')}</SelectItem>
                  <SelectItem value="Admin">{translate(commonTranslations, 'roleAdmin')}</SelectItem>
                  <SelectItem value="Provider">{translate(commonTranslations, 'roleProvider')}</SelectItem>
                  <SelectItem value="Client">{translate(commonTranslations, 'roleClient')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {isLoading && (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
            <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
          </div>
        )}

        {error && (
          <Card>
            <CardContent className="py-10 text-center text-destructive">
              <p>{translate(commonTranslations, 'adminErrorLoadingData')}: {error}</p>
            </CardContent>
          </Card>
        )}

        {!isLoading && !error && data && (
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{translate(commonTranslations, 'adminTableUserId')}</TableHead>
                    <TableHead>{translate(commonTranslations, 'adminTableFullName')}</TableHead>
                    <TableHead>{translate(commonTranslations, 'adminTableEmail')}</TableHead>
                    <TableHead>{translate(commonTranslations, 'adminTableRoles')}</TableHead>
                    <TableHead className="text-right">{translate(commonTranslations, 'adminTableActions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        {translate(commonTranslations, 'adminNoUsersFound')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    data.users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>{user.id}</TableCell>
                        <TableCell className="font-medium">{user.fullName}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {user.roles.map(role => (
                              <Badge key={role} variant={getRoleBadgeVariant(role)} className="capitalize">
                                {translate(commonTranslations, `role${role}` as keyof typeof commonTranslations) || role}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="icon" title={translate(commonTranslations, 'adminViewDetailsButton')} onClick={() => handleViewDetails(user)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            {/* Pagination with total count */}
            <div className="flex items-center justify-between p-4 border-t">
              <div className="text-sm text-muted-foreground">
                {data.totalUsers > 0 ? (
                  <>
                    {translate(commonTranslations, 'showingResults')
                      .replace('{start}', String(((currentPage - 1) * 10) + 1))
                      .replace('{end}', String(Math.min(currentPage * 10, data.totalUsers)))
                      .replace('{total}', String(data.totalUsers))
                    } {translate(commonTranslations, 'usersText')}
                  </>
                ) : (
                  translate(commonTranslations, 'adminNoUsersFound')
                )}
              </div>
              {data.totalPages > 1 && (
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} aria-disabled={currentPage === 1} className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''} />
                    </PaginationItem>
                    {[...Array(data.totalPages).keys()].map(i => (
                      <PaginationItem key={i}>
                         <PaginationLink onClick={() => handlePageChange(i + 1)} isActive={currentPage === i + 1}>
                           {i + 1}
                         </PaginationLink>
                       </PaginationItem>
                     ))}
                    <PaginationItem>
                      <PaginationNext onClick={() => handlePageChange(currentPage + 1)} aria-disabled={currentPage === data.totalPages} className={currentPage === data.totalPages ? 'pointer-events-none opacity-50' : ''}/>
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              )}
            </div>
          </Card>
        )}
      </div>
      
      {selectedUser && (
        <UserDetailsDialog
          user={selectedUser}
          isOpen={isDetailsDialogOpen}
          onClose={() => setIsDetailsDialogOpen(false)}
        />
      )}
    </>
  );
}

