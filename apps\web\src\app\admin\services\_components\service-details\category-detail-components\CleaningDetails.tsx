"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import type { CleaningServiceDetails } from "@prisma/client";
import { useLanguage } from "@/contexts/language-context";
import { commonTranslations } from "@repo/translations";
import { BooleanDetailItem } from "../DetailItem";

interface CleaningDetailsProps {
    details: CleaningServiceDetails | null;
}

export function CleaningDetailsView({ details }: CleaningDetailsProps) {
    const { translate } = useLanguage();
    if (!details) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">
                    {translate(commonTranslations, 'cleaningTitle')}
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'cleaningPropertyTypeApartments')} value={details.PropertyTypeApartments} />
                    <BooleanDetailItem label={translate(commonTranslations, 'cleaningPropertyTypeHouses')} value={details.PropertyTypeHouses} />
                    <BooleanDetailItem label={translate(commonTranslations, 'cleaningPropertyTypeOffices')} value={details.PropertyTypeOffices} />
                </div>
            </CardContent>
        </Card>
    );
}
