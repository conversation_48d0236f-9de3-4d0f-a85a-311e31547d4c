"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import type { ElderCareServiceDetails } from "@prisma/client";
import { useLanguage } from "@/contexts/language-context";
import { commonTranslations } from "@repo/translations";
import { BooleanDetailItem } from "../DetailItem";

interface ElderCareDetailsProps {
    details: ElderCareServiceDetails | null;
}

export function ElderCareDetailsView({ details }: ElderCareDetailsProps) {
    const { translate } = useLanguage();
    if (!details) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">
                    {translate(commonTranslations, 'elderCareTitle')}
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'elderCareTypeMobil')} value={details.TypeMobil} />
                    <BooleanDetailItem label={translate(commonTranslations, 'elderCareTypePartialImobilizat')} value={details.TypePartialImobilizat} />
                    <BooleanDetailItem label={translate(commonTranslations, 'elderCareTypeCompletImobilizat')} value={details.TypeCompletImobilizat} />
                </div>
            </CardContent>
        </Card>
    );
}
