"use client";

// Simple test component to verify all filter components render without errors
import { NannyFilters } from './nanny-filters';
import { ElderCareFilters } from './eldercare-filters';
import { CleaningFilters } from './cleaning-filters';
import { TutoringFilters } from './tutoring-filters';
import { CookingFilters } from './cooking-filters';

export function TestFilters() {
  return (
    <div className="space-y-8 p-4">
      <div>
        <h2 className="text-xl font-bold mb-4">Nanny Filters</h2>
        <NannyFilters />
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-4">Elder Care Filters</h2>
        <ElderCareFilters />
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-4">Cleaning Filters</h2>
        <CleaningFilters />
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-4">Tutoring Filters</h2>
        <TutoringFilters />
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-4">Cooking Filters</h2>
        <CookingFilters />
      </div>
    </div>
  );
}
