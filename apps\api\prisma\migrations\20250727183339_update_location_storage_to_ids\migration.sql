/*
  Warnings:

  - You are about to drop the column `LocationValue` on the `CleaningServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `CookingServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `ElderCareServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `NannyServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `PendingCleaningServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `PendingCookingServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `PendingElderCareServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `PendingNannyServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `PendingTutoringServiceDetails` table. All the data in the column will be lost.
  - You are about to drop the column `LocationValue` on the `TutoringServiceDetails` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "CleaningServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "CookingServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "ElderCareServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "NannyServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "PendingCleaningServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "PendingCookingServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "PendingElderCareServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "PendingNannyServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "PendingTutoringServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AlterTable
ALTER TABLE "TutoringServiceDetails" DROP COLUMN "LocationValue",
ADD COLUMN     "LocationId" INTEGER;

-- AddForeignKey
ALTER TABLE "PendingNannyServiceDetails" ADD CONSTRAINT "PendingNannyServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingElderCareServiceDetails" ADD CONSTRAINT "PendingElderCareServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingCleaningServiceDetails" ADD CONSTRAINT "PendingCleaningServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingTutoringServiceDetails" ADD CONSTRAINT "PendingTutoringServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PendingCookingServiceDetails" ADD CONSTRAINT "PendingCookingServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NannyServiceDetails" ADD CONSTRAINT "NannyServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ElderCareServiceDetails" ADD CONSTRAINT "ElderCareServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CleaningServiceDetails" ADD CONSTRAINT "CleaningServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CookingServiceDetails" ADD CONSTRAINT "CookingServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TutoringServiceDetails" ADD CONSTRAINT "TutoringServiceDetails_LocationId_fkey" FOREIGN KEY ("LocationId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;
