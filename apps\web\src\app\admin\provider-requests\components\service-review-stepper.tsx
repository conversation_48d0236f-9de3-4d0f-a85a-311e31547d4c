"use client";

import React, { useState } from 'react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  ChevronLeft, 
  ChevronRight, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  List,
  Eye
} from "lucide-react";
import { GranularServiceCard } from './granular-service-card';

interface PendingService {
  Id: number;
  ServiceName: string;
  Description: string;
  ServiceCategorySlug: string;
  ExperienceYears: number;
  Status: 'PendingReview' | 'Approved' | 'Rejected' | 'RequiresChanges';
  AdminNotes?: string | null;
  DocumentPaths: string[];
  CreatedAt: string;
  UpdatedAt: string;
  Category: {
    Id: number;
    NameKey: string;
    Slug: string;
  };
  NannyServiceDetails?: any;
  ElderCareServiceDetails?: any;
  CleaningServiceDetails?: any;
  TutoringServiceDetails?: any;
  CookingServiceDetails?: any;
}

interface ServiceReviewStepperProps {
  services: PendingService[];
  onStatusUpdate: (serviceId: number, status: string, adminNotes?: string) => Promise<void>;
  updatingServiceId?: number | null;
}

const statusConfig = {
  PendingReview: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  Approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  Rejected: { color: 'bg-red-100 text-red-800', icon: XCircle },
  RequiresChanges: { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle }
};

export function ServiceReviewStepper({ services, onStatusUpdate, updatingServiceId }: ServiceReviewStepperProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [viewMode, setViewMode] = useState<'stepper' | 'overview'>('stepper');
  const { translate } = useLanguage();

  // Helper function to get service category display name
  const getServiceCategoryName = (service: PendingService) => {
    const categorySlug = service.ServiceCategorySlug;
    switch (categorySlug) {
      case 'Nanny':
        return translate(commonTranslations, 'categoryNanny');
      case 'ElderCare':
        return translate(commonTranslations, 'categoryElderCare');
      case 'Cleaning':
        return translate(commonTranslations, 'categoryCleaning');
      case 'Tutoring':
        return translate(commonTranslations, 'categoryTutoring');
      case 'Cooking':
        return translate(commonTranslations, 'categoryCooking');
      default:
        return service.Category.NameKey || categorySlug;
    }
  };

  const currentService = services[currentStep];
  const totalServices = services.length;
  const completedServices = services.filter(s => s.Status !== 'PendingReview').length;
  const progress = totalServices > 0 ? (completedServices / totalServices) * 100 : 0;

  const goToNext = () => {
    if (currentStep < totalServices - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToService = (index: number) => {
    setCurrentStep(index);
  };

  if (totalServices === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No services to review</p>
      </div>
    );
  }

  if (viewMode === 'overview') {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">{translate(commonTranslations, 'adminAllServicesOverview')}</h3>
          <Button variant="outline" onClick={() => setViewMode('stepper')}>
            <Eye className="h-4 w-4 mr-2" />
            {translate(commonTranslations, 'adminReviewMode')}
          </Button>
        </div>
        <div className="space-y-4">
          {services.map((service, index) => (
            <div key={service.Id} className="relative">
              <div className="absolute top-4 left-4 z-10">
                <Badge variant="outline" className="bg-white">
                  {index + 1} {translate(commonTranslations, 'adminOf')} {totalServices}
                </Badge>
              </div>
              <GranularServiceCard
                service={service}
                onStatusUpdate={onStatusUpdate}
                isUpdating={updatingServiceId === service.Id}
              />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stepper Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <CardTitle className="flex items-center gap-2">
              {translate(commonTranslations, 'adminServiceReviewProgress')}
            </CardTitle>
            <Button variant="outline" onClick={() => setViewMode('overview')} className="w-full sm:w-auto">
              <List className="h-4 w-4 mr-2" />
              {translate(commonTranslations, 'adminViewAll')}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span>{translate(commonTranslations, 'adminProgress')}: {completedServices} {translate(commonTranslations, 'adminOf')} {totalServices} {translate(commonTranslations, 'adminServicesReviewed')}</span>
            <span>{Math.round(progress)}% {translate(commonTranslations, 'adminComplete')}</span>
          </div>
          <Progress value={progress} className="h-2" />
          
          {/* Service Navigation */}
          <div className="flex items-center gap-2 flex-wrap">
            {services.map((service, index) => {
              const StatusIcon = statusConfig[service.Status].icon;
              const categoryName = getServiceCategoryName(service);
              return (
                <Button
                  key={service.Id}
                  variant={index === currentStep ? "default" : "outline"}
                  size="sm"
                  onClick={() => goToService(index)}
                  className={`${statusConfig[service.Status].color} ${index === currentStep ? 'ring-2 ring-blue-500' : ''} text-xs`}
                  title={`${categoryName} - ${service.ServiceName}`}
                >
                  <StatusIcon className="h-3 w-3 mr-1" />
                  <span className="truncate max-w-[80px]">{categoryName}</span>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Current Service */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h3 className="text-lg font-semibold">
            {translate(commonTranslations, 'adminServiceOf')} {currentStep + 1} {translate(commonTranslations, 'adminOf')} {totalServices}: {currentService.ServiceName}
          </h3>
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPrevious}
              disabled={currentStep === 0}
              className="flex-1 sm:flex-none"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              {translate(commonTranslations, 'adminPrevious')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNext}
              disabled={currentStep === totalServices - 1}
              className="flex-1 sm:flex-none"
            >
              {translate(commonTranslations, 'adminNext')}
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>

        <GranularServiceCard
          service={currentService}
          onStatusUpdate={onStatusUpdate}
          isUpdating={updatingServiceId === currentService.Id}
        />
      </div>
    </div>
  );
}
