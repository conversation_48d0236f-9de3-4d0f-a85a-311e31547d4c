
import React from 'react';
import { SafeAreaView, View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Link, useRouter } from 'expo-router';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs-mobile';
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';

const profileScreenTranslations = {
  clientSectionTitle: { ro: "Client", ru: "Клиент", en: "Client" },
  providerSectionTitle: { ro: "Prestator", ru: "Поставщик", en: "Provider" },
};

const ProfileScreen = () => {
  const router = useRouter();
  const { translate } = useLanguage();
  
  // For now, we assume the user is logged in for the new view.
  // This will be replaced with actual session logic later.
  const isLoggedIn = true;
  const isProvider = true; // Assume user is also a provider for demonstration
  const userName = "Utilizator Test";

  const handleLogout = () => {
    // Mock logout logic
    Alert.alert("Deconectare", "Ai fost deconectat cu succes.", [
      { text: "OK", onPress: () => router.push('/') }
    ]);
  };

  const clientMenuItems = [
    { href: "/settings", icon: "settings", iconFamily: "MaterialIcons", labelKey: "profileSettings" },
    { href: "/settings/addresses", icon: "location-on", iconFamily: "MaterialIcons", labelKey: "myAddresses" },
    { href: "/bookings", icon: "event", iconFamily: "MaterialIcons", labelKey: "myBookingsClient" },
    { href: "/messages", icon: "message", iconFamily: "MaterialIcons", labelKey: "myMessages" },
  ];

  const providerMenuItems = [
    { href: "/provider", icon: "work", iconFamily: "MaterialIcons", labelKey: "providerDashboardNav" },
    { href: "/provider/services", icon: "checklist", iconFamily: "MaterialIcons", labelKey: "myServices" },
    { href: "/provider/calendar", icon: "schedule", iconFamily: "MaterialIcons", labelKey: "providerCalendar" },
  ];

  const renderIcon = (item: typeof clientMenuItems[0]) => {
    const iconProps = { color: "#4B5563", size: 22 };
    if (item.iconFamily === "MaterialIcons") {
      return <MaterialIcons name={item.icon as any} {...iconProps} />;
    }
    return <MaterialIcons name="help" {...iconProps} />;
  };

  const renderMenu = (items: typeof clientMenuItems) => (
    <View style={styles.menuContainer}>
      {items.map((item) => (
        <Link key={item.href} href={item.href as any} asChild>
          <TouchableOpacity style={styles.menuItem}>
            <View style={styles.menuItemContent}>
              {renderIcon(item)}
              <Text style={styles.menuItemText}>{translate(commonTranslations, item.labelKey as keyof typeof commonTranslations)}</Text>
            </View>
            <MaterialIcons name="chevron-right" color="#9CA3AF" size={20} />
          </TouchableOpacity>
        </Link>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {isLoggedIn ? (
          <>
            <View style={styles.profileHeader}>
                <View style={styles.avatar}>
                    <MaterialIcons name="person" color="#FFFFFF" size={40} />
                </View>
                <Text style={styles.userName}>{userName}</Text>
                <Text style={styles.userEmail}><EMAIL></Text>
            </View>
            
            {isProvider ? (
              <Tabs defaultValue="client">
                <TabsList>
                  <TabsTrigger value="client" label={translate(profileScreenTranslations, 'clientSectionTitle')} />
                  <TabsTrigger value="provider" label={translate(profileScreenTranslations, 'providerSectionTitle')} />
                </TabsList>
                <TabsContent value="client">
                  {renderMenu(clientMenuItems)}
                </TabsContent>
                <TabsContent value="provider">
                  {renderMenu(providerMenuItems)}
                </TabsContent>
              </Tabs>
            ) : (
              renderMenu(clientMenuItems)
            )}

            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <MaterialIcons name="logout" color="#EF4444" size={22} />
              <Text style={styles.logoutButtonText}>Deconectare</Text>
            </TouchableOpacity>
          </>
        ) : (
          <View style={styles.loggedOutContainer}>
            <Text style={styles.title}>Nu ești autentificat</Text>
            <Text style={styles.subtitle}>Creează un cont sau autentifică-te pentru a-ți vedea profilul.</Text>
            <View style={styles.buttonContainer}>
                <Link href="/login" asChild>
                    <TouchableOpacity style={styles.buttonPrimary}>
                        <Text style={styles.buttonTextPrimary}>Autentificare</Text>
                    </TouchableOpacity>
                </Link>
                <Link href="/register" asChild>
                     <TouchableOpacity style={styles.buttonSecondary}>
                        <Text style={styles.buttonTextSecondary}>Înregistrare</Text>
                    </TouchableOpacity>
                </Link>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F3F4F6',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  profileHeader: {
    alignItems: 'center',
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#3F51B5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  userEmail: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  menuContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    marginLeft: 16,
    color: '#374151',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  logoutButtonText: {
    fontSize: 16,
    color: '#EF4444',
    marginLeft: 8,
    fontWeight: '600',
  },
  // Logged out styles
  loggedOutContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
  },
  buttonContainer: {
      flexDirection: 'row',
      gap: 16,
  },
  buttonPrimary: {
    backgroundColor: '#3F51B5',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonTextPrimary: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonSecondary: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  buttonTextSecondary: {
    color: '#1F2937',
    fontSize: 16,
    fontWeight: '600',
  }
});

export default ProfileScreen;
