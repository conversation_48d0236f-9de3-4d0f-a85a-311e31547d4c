"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Too<PERSON><PERSON>, Legend } from 'recharts';
import { Loader2, <PERSON><PERSON><PERSON> as PieChartIcon } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const chartTranslations = {
  serviceCategories: { ro: "Categorii servicii", ru: "Категории услуг", en: "Service Categories" },
  categoryDistribution: { ro: "Distribuția categoriilor", ru: "Распределение категорий", en: "Category Distribution" },
  services: { ro: "servicii", ru: "услуг", en: "services" },
  loadingChart: { ro: "Se încarcă graficul...", ru: "Загрузка графика...", en: "Loading chart..." },
  errorLoadingChart: { ro: "<PERSON><PERSON><PERSON> la încărcarea graficului", ru: "Ошибка загрузки графика", en: "Error loading chart" },
  noDataAvailable: { ro: "Nu sunt date disponibile", ru: "Нет доступных данных", en: "No data available" },
  totalServices: { ro: "Total servicii", ru: "Всего услуг", en: "Total services" },
};

interface ServiceCategoryData {
  category: string;
  slug: string;
  count: number;
}

interface ServiceCategoryChartProps {
  refreshTrigger: Date;
}

// Colors for the pie chart
const COLORS = [
  '#3b82f6', // blue
  '#10b981', // emerald
  '#f59e0b', // amber
  '#ef4444', // red
  '#8b5cf6', // violet
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
];

export function ServiceCategoryChart({ refreshTrigger }: ServiceCategoryChartProps) {
  const { translate } = useLanguage();
  const [data, setData] = useState<ServiceCategoryData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch('/api/proxy/admin/analytics/service-categories');
        if (!response.ok) {
          throw new Error('Failed to fetch service category data');
        }
        
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        console.error('Error fetching service category data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.category}</p>
          <p className="text-sm text-muted-foreground">
            {data.count} {translate(chartTranslations, 'services')}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // Don't show labels for slices smaller than 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        className="text-xs font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChartIcon className="w-5 h-5" />
            {translate(chartTranslations, 'serviceCategories')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-5 h-5 animate-spin" />
              {translate(chartTranslations, 'loadingChart')}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChartIcon className="w-5 h-5" />
            {translate(chartTranslations, 'serviceCategories')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'errorLoadingChart')}</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChartIcon className="w-5 h-5" />
            {translate(chartTranslations, 'serviceCategories')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'noDataAvailable')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalServices = data.reduce((sum, item) => sum + item.count, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PieChartIcon className="w-5 h-5" />
          {translate(chartTranslations, 'serviceCategories')}
        </CardTitle>
        <div className="text-sm text-muted-foreground">
          {translate(chartTranslations, 'totalServices')}: {totalServices}
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={CustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="bottom" 
              height={36}
              formatter={(value, entry: any) => (
                <span style={{ color: entry.color }}>
                  {entry.payload.category} ({entry.payload.count})
                </span>
              )}
            />
          </PieChart>
        </ResponsiveContainer>
        
        {/* Category breakdown */}
        <div className="mt-4 space-y-2">
          {data.map((item, index) => (
            <div key={item.slug} className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                />
                <span>{item.category}</span>
              </div>
              <span className="font-medium">{item.count}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
