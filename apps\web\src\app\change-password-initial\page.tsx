
"use client";

import { useState, type FormEvent, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Loader2, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from '@/contexts/language-context';
import { useRouter } from "next/navigation";
import { AuthGuard } from "@/components/auth/auth-guard";
import { useUser } from "@/contexts/user-context";

const changePasswordInitialPageTranslations = {
  pageTitle: { ro: "<PERSON><PERSON><PERSON><PERSON> Parola Inițială", ru: "Сменить начальный пароль", en: "Change Initial Password" },
  description: { ro: "Pentru securitatea contului tău, te rugăm să îți setezi o nouă parolă.", ru: "Для безопасности вашего аккаунта, пожалуйста, установите новый пароль.", en: "For your account security, please set a new password." },
  emailInfo: { ro: "Schimbi parola pentru contul: {email}", ru: "Вы меняете пароль для аккаунта: {email}", en: "You are changing the password for account: {email}" },
  newPasswordLabel: { ro: "Parolă Nouă", ru: "Новый пароль", en: "New Password" },
  newPasswordPlaceholder: { ro: "Minim 8 caractere, o majusculă, o minusculă, o cifră, un caracter special", ru: "Мин. 8 симв., 1 загл., 1 строч., 1 цифра, 1 спецсимвол", en: "Min 8 chars, 1 uppercase, 1 lowercase, 1 digit, 1 special" },
  confirmNewPasswordLabel: { ro: "Confirmă Parola Nouă", ru: "Подтвердите новый пароль", en: "Confirm New Password" },
  submitButton: { ro: "Setează Parola Nouă", ru: "Установить новый пароль", en: "Set New Password" },
  loadingButton: { ro: "Se setează parola...", ru: "Установка пароля...", en: "Setting password..." },
  errorAlertTitle: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  passwordMismatch: { ro: "Parolele nu se potrivesc.", ru: "Пароли не совпадают.", en: "Passwords do not match." },
  passwordTooWeak: { ro: "Parola nu este suficient de complexă. Asigură-te că are minim 8 caractere, o literă mare, o literă mică, o cifră și un caracter special.", ru: "Пароль недостаточно сложен. Убедитесь, что он содержит не менее 8 символов, одну заглавную букву, одну строчную букву, одну цифру и один специальный символ.", en: "Password is not complex enough. Ensure it has at least 8 characters, one uppercase letter, one lowercase letter, one digit, and one special character." },
  toastSuccessTitle: { ro: "Succes!", ru: "Успех!", en: "Success!" },
  toastPasswordChanged: { ro: "Parola a fost schimbată cu succes! Vei fi redirecționat.", ru: "Пароль успешно изменен! Вы будете перенаправлены.", en: "Password changed successfully! You will be redirected." },
  toastErrorTitle: { ro: "Eroare la Schimbarea Parolei", ru: "Ошибка смены пароля", en: "Password Change Error" },
  errorNoUserId: { ro: "ID utilizator lipsă. Nu se poate schimba parola.", ru: "Отсутствует ID пользователя. Невозможно сменить пароль.", en: "User ID missing. Cannot change password." },
  errorNoEmail: { ro: "Email utilizator lipsă.", ru: "Отсутствует email пользователя.", en: "User email missing." },
  strengthLabel: { ro: "Complexitate parolă:", ru: "Сложность пароля:", en: "Password strength:" },
  strengthTooShort: { ro: "Prea scurtă", ru: "Слишком короткий", en: "Too short" },
  strengthVeryWeak: { ro: "Foarte Slabă", ru: "Очень слабый", en: "Very Weak" },
  strengthWeak: { ro: "Slabă", ru: "Слабый", en: "Weak" },
  strengthMedium: { ro: "Medie", ru: "Средний", en: "Medium" },
  strengthStrong: { ro: "Puternică", ru: "Сильный", en: "Strong" },
  strengthVeryStrong: { ro: "Foarte Puternică", ru: "Очень сильный", en: "Very Strong" }
};

const PasswordStrengthMeter = ({ score }: { score: number }) => {
  const strengthColors = [
    'bg-gray-300 dark:bg-gray-600', 
    'bg-red-500',  
    'bg-orange-500',
    'bg-yellow-500',
    'bg-lime-500',  
    'bg-green-500'  
  ];
  const segments = Array(5).fill(0);
  return (
    <div className="flex h-2 rounded-full overflow-hidden mt-1" aria-label="Password strength indicator">
      {segments.map((_, index) => (
        <div
          key={index}
          className={`flex-1 transition-colors duration-300 ${
            score > index ? strengthColors[Math.min(score, 5)] : strengthColors[0]
          }`}
          role="presentation"
        />
      ))}
    </div>
  );
};

export default function ChangePasswordInitialPage() {
  const { translate } = useLanguage();
  const router = useRouter();
  const { toast } = useToast();
  const { user, isLoading: isUserLoading, revalidateUser } = useUser();

  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [passwordStrengthScore, setPasswordStrengthScore] = useState(0);
  const [passwordStrengthText, setPasswordStrengthText] = useState('');

  const userEmail = user?.email;

  const calculatePasswordStrength = (currentPassword: string) => {
    let score = 0;
    if (!currentPassword) {
      setPasswordStrengthScore(0);
      setPasswordStrengthText('');
      return;
    }
    if (currentPassword.length >= 8) score++; else {
      setPasswordStrengthScore(0); 
      setPasswordStrengthText(translate(changePasswordInitialPageTranslations, 'strengthTooShort'));
      return;
    }
    if (/[a-z]/.test(currentPassword)) score++;
    if (/[A-Z]/.test(currentPassword)) score++;
    if (/\d/.test(currentPassword)) score++;
    if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(currentPassword)) score++;
    setPasswordStrengthScore(score);
    switch (score) {
      case 0: case 1: setPasswordStrengthText(translate(changePasswordInitialPageTranslations, 'strengthVeryWeak')); break;
      case 2: setPasswordStrengthText(translate(changePasswordInitialPageTranslations, 'strengthWeak')); break;
      case 3: setPasswordStrengthText(translate(changePasswordInitialPageTranslations, 'strengthMedium')); break;
      case 4: setPasswordStrengthText(translate(changePasswordInitialPageTranslations, 'strengthStrong')); break;
      case 5: setPasswordStrengthText(translate(changePasswordInitialPageTranslations, 'strengthVeryStrong')); break;
      default: setPasswordStrengthText('');
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const currentNewPassword = e.target.value;
    setNewPassword(currentNewPassword);
    calculatePasswordStrength(currentNewPassword);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);

    if (newPassword !== confirmPassword) {
      setError(translate(changePasswordInitialPageTranslations, 'passwordMismatch'));
      return;
    }
    if (passwordStrengthScore < 3) {
        setError(translate(changePasswordInitialPageTranslations, 'passwordTooWeak'));
        return;
    }
    if (!user?.id) {
      setError(translate(changePasswordInitialPageTranslations, 'errorNoUserId'));
      return;
    }
    
    setIsSubmitting(true);

    try {
      // This front-end API route will proxy the request to the back-end
      const response = await fetch('/api/auth/change-password-initial', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id, newPassword }),
      });
      const responseData = await response.json();

      if (!response.ok || !responseData.success) {
        throw new Error(responseData.message || "A apărut o eroare la schimbarea parolei.");
      }
      
      toast({
        title: translate(changePasswordInitialPageTranslations, 'toastSuccessTitle'),
        description: translate(changePasswordInitialPageTranslations, 'toastPasswordChanged'),
      });
      
      // Re-fetch user data to update the context and session
      revalidateUser();
      
      const targetPath = user.isAdmin ? '/admin' : '/dashboard';
      router.push(targetPath);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Eroare necunoscută.";
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: translate(changePasswordInitialPageTranslations, 'toastErrorTitle'),
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const getStrengthTextColor = () => {
    switch (passwordStrengthScore) {
      case 1: return 'text-red-600 dark:text-red-400';
      case 2: return 'text-orange-600 dark:text-orange-400';
      case 3: return 'text-yellow-600 dark:text-yellow-400';
      case 4: return 'text-lime-600 dark:text-lime-400';
      case 5: return 'text-green-600 dark:text-green-400';
      default: return 'text-muted-foreground';
    }
  };

  if (isUserLoading || !user) {
    return (
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <main className="flex-grow flex items-center justify-center">
            {error ? <p className="text-destructive">{error}</p> : <Loader2 className="h-8 w-8 animate-spin" />}
          </main>
          <Footer />
        </div>
      );
  }

  return (
    <AuthGuard>
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-background">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold font-headline">{translate(changePasswordInitialPageTranslations, 'pageTitle')}</CardTitle>
              <CardDescription>{translate(changePasswordInitialPageTranslations, 'description')}</CardDescription>
              {userEmail && <p className="text-sm text-muted-foreground pt-2">{translate(changePasswordInitialPageTranslations, 'emailInfo').replace('{email}', userEmail)}</p>}
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">{translate(changePasswordInitialPageTranslations, 'newPasswordLabel')}</Label>
                  <Input 
                    id="newPassword" 
                    type="password" 
                    required 
                    value={newPassword}
                    onChange={handlePasswordChange}
                    placeholder={translate(changePasswordInitialPageTranslations, 'newPasswordPlaceholder')}
                  />
                  {newPassword && (
                    <div className="mt-1">
                      <PasswordStrengthMeter score={passwordStrengthScore} />
                      {passwordStrengthText && (
                        <p className={`text-xs mt-1 ${getStrengthTextColor()}`}>
                          {passwordStrengthText}
                        </p>
                      )}
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">{translate(changePasswordInitialPageTranslations, 'confirmNewPasswordLabel')}</Label>
                  <Input 
                    id="confirmPassword" 
                    type="password" 
                    required 
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                </div>
                {error && (
                  <Alert variant="destructive" className="mt-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>{translate(changePasswordInitialPageTranslations, 'errorAlertTitle')}</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  {isSubmitting ? translate(changePasswordInitialPageTranslations, 'loadingButton') : translate(changePasswordInitialPageTranslations, 'submitButton')}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </main>
        <Footer />
      </div>
    </AuthGuard>
  );
}
