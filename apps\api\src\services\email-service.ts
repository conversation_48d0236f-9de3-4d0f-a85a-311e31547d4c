// apps/api/src/services/email-service.ts

// ACESTA ESTE UN SERVICIU SIMULAT PENTRU TRIMITEREA DE EMAILURI
// ÎNTR-O APLICAȚIE REALĂ, AICI S-AR INTEGRA O BIBLIOTECĂ PRECUM NODEMAILER
// ȘI UN SERVICIU SMTP (EX: SENDGRID, MAILGUN, ETC.)

interface EmailDetails {
  to: string;
  subject: string;
  text: string;
  html?: string;
}

async function sendEmail(details: EmailDetails): Promise<void> {
  console.log("--- SIMULARE TRIMITERE EMAIL (API) ---");
  console.log(`Către: ${details.to}`);
  console.log(`Subiect: ${details.subject}`);
  console.log(`Text: ${details.text}`);
  if (details.html) {
    console.log(`HTML: ${details.html.substring(0, 100)}...`);
  }
  console.log("--- SFÂRȘIT SIMULARE ---");
}

export async function sendNewProviderRequestEmailToAdmin(
  userName: string,
  userEmail: string,
  requestId: string
): Promise<void> {
  const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
  const webAppUrl = process.env.WEB_APP_URL || "http://localhost:9002";
  const subject = `Cerere Nouă de Înregistrare Furnizor: ${userName}`;
  const text = `Utilizatorul ${userName} (${userEmail}) a trimis o nouă cerere de înregistrare ca furnizor.\n\nID Cerere: ${requestId}\n\nTe rugăm să o analizezi în panoul de administrare: ${webAppUrl}/admin/provider-requests`;
  await sendEmail({ to: adminEmail, subject, text });
}

export async function sendProviderRequestApprovedEmailToUser(
  userEmail: string,
  userName: string
): Promise<void> {
  const webAppUrl = process.env.WEB_APP_URL || "http://localhost:9002";
  const subject = "Cererea ta de înregistrare ca furnizor a fost Aprobată!";
  const text = `Felicitări, ${userName}!\n\nCererea ta de a deveni furnizor pe platforma bonami a fost aprobată.\n\nAcum poți adăuga și gestiona serviciile tale din panoul de control: ${webAppUrl}/dashboard/provider\n\nEchipa bonami`;
  await sendEmail({ to: userEmail, subject, text });
}

export async function sendProviderRequestRejectedEmailToUser(
  userEmail: string,
  userName: string,
  adminNotes?: string | null
): Promise<void> {
  const webAppUrl = process.env.WEB_APP_URL || "http://localhost:9002";
  const subject = "Actualizare privind cererea ta de înregistrare ca furnizor";
  const reasonText = adminNotes ? `Motivul respingerii: ${adminNotes}` : "Nu a fost specificat un motiv detaliat.";
  const text = `Bună, ${userName},\n\nAm analizat cererea ta de a deveni furnizor pe platforma bonami.\n\nStatus cerere: Respinsă.\n${reasonText}\n\nDacă ai întrebări sau dorești să trimiți o nouă cerere după ce ai adresat feedback-ul (dacă este cazul), te rugăm să accesezi platforma.\n\nEchipa bonami`;
  await sendEmail({ to: userEmail, subject, text });
}
