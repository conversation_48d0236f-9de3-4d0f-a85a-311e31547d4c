

import { Router, type Response } from 'express';
import prisma from './lib/db';
import { UserRole, ServiceStatus, ProviderRegistrationRequestStatus, Prisma, NotificationType, ServiceCategorySlug } from '@prisma/client';
import type { AuthenticatedRequest } from '../middleware/auth';
import { sendProviderRequestApprovedEmailToUser, sendProviderRequestRejectedEmailToUser } from '../services/email-service';
import type { ServiceRequestDetailForAdmin } from 'apps/web/src/app/admin/provider-requests/page';
import { revalidateTag } from 'next/cache';


const router = Router();

// Middleware to check for Admin role for all routes in this file
router.use((req: AuthenticatedRequest, res: Response, next) => {
  if (!req.user?.isAdmin) {
    return res.status(403).json({ message: 'Forbidden: Access is restricted to administrators.' });
  }
  next();
});

router.get('/stats', async (req, res) => {
  try {
    const totalUsers = await prisma.user.count();
    const totalProviders = await prisma.user.count({ where: { Roles: { some: { Name: UserRole.Provider } } } });
    const totalActiveServices = await prisma.advertisedService.count({ where: { Status: ServiceStatus.Activ } });
    const totalServices = await prisma.advertisedService.count();
    const pendingProviderRequests = await prisma.providerRegistrationRequest.count({ where: { Status: ProviderRegistrationRequestStatus.Pending } });
    
    res.json({ totalUsers, totalProviders, totalActiveServices, totalServices, pendingProviderRequests });
  } catch (error) {
    console.error('[API /admin/stats GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch admin stats' });
  }
});

router.get('/users', async (req, res) => {
  try {
    const { page = '1', limit = '10', role, search } = req.query;
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    const where: Prisma.UserWhereInput = {};
    if (role && typeof role === 'string' && ['Admin', 'Provider', 'Client'].includes(role)) {
      where.Roles = { some: { Name: role as UserRole } };
    }
    if (search && typeof search === 'string') {
      where.OR = [
        { FullName: { contains: search, mode: 'insensitive' } },
        { Email: { contains: search, mode: 'insensitive' } },
      ];
    }

    const totalUsers = await prisma.user.count({ where });
    const users = await prisma.user.findMany({
      where,
      include: { Roles: true },
      orderBy: { CreatedAt: 'desc' },
      skip,
      take: limitNum,
    });

    const formattedUsers = users.map(user => ({
      id: user.Id,
      fullName: user.FullName,
      email: user.Email,
      avatarUrl: user.AvatarUrl,
      roles: user.Roles.map(role => role.Name),
      createdAt: user.CreatedAt.toISOString(),
    }));
    
    res.json({ 
        users: formattedUsers,
        totalPages: Math.ceil(totalUsers / limitNum),
        totalUsers,
        currentPage: pageNum,
    });
  } catch (error) {
    console.error('[API /admin/users GET] Error:', error);
    res.status(500).json({ message: 'Failed to fetch users' });
  }
});

// GET all services for admin view with filtering and pagination
router.get('/services', async (req, res) => {
    try {
        const { page = '1', limit = '10', status, category, search } = req.query;
        const pageNum = parseInt(page as string, 10);
        const limitNum = parseInt(limit as string, 10);
        const skip = (pageNum - 1) * limitNum;

        const where: Prisma.AdvertisedServiceWhereInput = {};

        if (status && typeof status === 'string' && ['Activ', 'Inactiv', 'PendingReview', 'Rejected'].includes(status)) {
            where.Status = status as ServiceStatus;
        }
        if (category && typeof category === 'string' && Object.values(ServiceCategorySlug).includes(category as ServiceCategorySlug)) {
            where.ServiceCategorySlug = category as ServiceCategorySlug;
        }
        if (search && typeof search === 'string') {
            where.OR = [
                { ServiceName: { contains: search, mode: 'insensitive' } },
                { Provider: { FullName: { contains: search, mode: 'insensitive' } } },
            ];
        }

        const totalServices = await prisma.advertisedService.count({ where });
        const services = await prisma.advertisedService.findMany({
            where,
            include: {
                Provider: { select: { FullName: true } },
                Category: { select: { NameKey: true, Slug: true } },
            },
            orderBy: { CreatedAt: 'desc' },
            skip,
            take: limitNum,
        });

        res.json({ 
            services,
            totalPages: Math.ceil(totalServices / limitNum),
            currentPage: pageNum,
            totalServices,
        });
    } catch (error) {
        console.error('[API /admin/services GET] Error:', error);
        res.status(500).json({ message: 'Failed to fetch services' });
    }
});


// POST to update a service's status (approve/reject)
router.post('/services/update-status', async (req, res) => {
    const { serviceId, status } = req.body;
    if (!serviceId || !status || !['Activ', 'Rejected'].includes(status)) {
        return res.status(400).json({ message: 'Service ID and a valid status (Activ, Rejected) are required.' });
    }

    try {
        const updatedService = await prisma.advertisedService.update({
            where: { Id: serviceId },
            data: { Status: status },
        });

        // Notify the provider about the status change
        await prisma.notification.create({
            data: {
                UserId: updatedService.ProviderId,
                Message: `Statusul serviciului tău "${updatedService.ServiceName}" a fost actualizat la: ${status}.`,
                Link: '/dashboard/provider/services',
                Type: 'ServiceStatusChanged',
            },
        });

        res.json({ success: true, service: updatedService });
    } catch (error) {
        console.error('[API /admin/services/update-status POST] Error:', error);
        res.status(500).json({ message: 'Failed to update service status' });
    }
});


// GET all provider registration requests
router.get('/provider-requests', async (req, res) => {
    try {
        const requests = await prisma.providerRegistrationRequest.findMany({
            where: {
                Status: ProviderRegistrationRequestStatus.Pending,
            },
            include: {
                User: {
                    select: {
                        Id: true,
                        FullName: true,
                        Email: true
                    }
                }
            },
            orderBy: {
                RequestDate: 'desc',
            },
        });
        res.json({ requests });
    } catch (error) {
        console.error('[API /admin/provider-requests GET] Error:', error);
        res.status(500).json({ message: 'Failed to fetch provider requests' });
    }
});

// POST to update a provider request status (approve/reject)
router.post('/provider-requests/update', async (req: AuthenticatedRequest, res) => {
    const { requestId, action, adminNotes } = req.body; // action: 'approve' | 'reject'

    if (!requestId || !action || !['approve', 'reject'].includes(action)) {
        return res.status(400).json({ message: 'Request ID and a valid action (approve, reject) are required.' });
    }
    if (action === 'reject' && (!adminNotes || typeof adminNotes !== 'string' || !adminNotes.trim())) {
        return res.status(400).json({ message: 'Rejection reason (adminNotes) is required when rejecting.' });
    }
    
    try {
        const request = await prisma.providerRegistrationRequest.findUnique({
            where: { Id: requestId },
        });

        if (!request) {
            return res.status(404).json({ message: 'Provider request not found.' });
        }
        if (request.Status !== ProviderRegistrationRequestStatus.Pending) {
            return res.status(409).json({ message: `Request is already in '${request.Status}' status.` });
        }

        const user = await prisma.user.findUnique({
            where: { Id: request.UserId },
        });

        if (!user) {
            await prisma.providerRegistrationRequest.update({
                where: { Id: requestId },
                data: { Status: ProviderRegistrationRequestStatus.Rejected, AdminNotes: 'User account associated with this request no longer exists.' },
            });
            return res.status(404).json({ message: 'User associated with the request not found.' });
        }
        
        const adminsToNotify = await prisma.user.findMany({ where: { Roles: { some: { Name: UserRole.Admin } } } });

        if (action === 'approve') {
            const providerRole = await prisma.role.findUnique({ where: { Name: UserRole.Provider } });
            if (!providerRole) {
                console.error("Critical: 'Provider' role not found in database.");
                return res.status(500).json({ message: 'Server configuration error: Provider role is missing.' });
            }

            const requestedServices = request.RequestedServices as unknown as ServiceRequestDetailForAdmin[];

            await prisma.$transaction(async (tx) => {
                // 1. Grant Provider Role
                await tx.user.update({
                    where: { Id: user.Id },
                    data: { Roles: { connect: { Id: providerRole.Id } } },
                });

                // 2. Mark request as Approved
                await tx.providerRegistrationRequest.update({
                    where: { Id: requestId },
                    data: { Status: ProviderRegistrationRequestStatus.Approved, AdminNotes: 'Approved by admin.' },
                });
                
                // 3. Create a notification for the user
                await tx.notification.create({
                    data: {
                        UserId: user.Id,
                        Message: `Felicitări! Cererea ta de a deveni prestator a fost aprobată.`,
                        Link: '/dashboard/provider',
                        Type: "ProviderRequestApproved",
                    },
                });

                // 4. Create the services for the new provider
                for (const serviceDetail of requestedServices) {
                    const category = await tx.serviceCategory.findUnique({ where: { Id: serviceDetail.categoryId } });
                    if (!category) {
                        console.warn(`Category with ID ${serviceDetail.categoryId} not found. Skipping service creation.`);
                        continue;
                    }
                    
                    const newService = await tx.advertisedService.create({
                        data: {
                            ProviderId: user.Id,
                            CategoryId: category.Id,
                            ServiceCategorySlug: category.Slug,
                            ServiceName: `Serviciu de ${category.NameKey}`, 
                            Description: serviceDetail.description,
                            Status: 'Activ', 
                        }
                    });
                    
                    const connectData = { AdvertisedService: { connect: { Id: newService.Id }}};
                    
                    switch (category.Slug) {
                        case 'Nanny': 
                            await tx.nannyServiceDetails.create({ data: {
                                ...connectData,
                                ExperienceYears: parseInt(String(serviceDetail.experienceYears), 10) || 0,
                                AvailabilityWeekdays: serviceDetail.availabilityWeekdays,
                                AvailabilityWeekends: serviceDetail.availabilityWeekends,
                                AvailabilityEvenings: serviceDetail.availabilityEvenings,
                                PreferredAge_0_2: serviceDetail.PreferredAge_0_2,
                                PreferredAge_3_6: serviceDetail.PreferredAge_3_6,
                                PreferredAge_7_plus: serviceDetail.PreferredAge_7_plus,
                            }}); 
                            break;
                        case 'ElderCare': 
                             await tx.elderCareServiceDetails.create({ data: {
                                ...connectData, 
                                ExperienceYears: parseInt(String(serviceDetail.experienceYears), 10) || 0,
                                AvailabilityWeekdays: serviceDetail.availabilityWeekdays,
                                AvailabilityWeekends: serviceDetail.availabilityWeekends,
                                AvailabilityEvenings: serviceDetail.availabilityEvenings,
                                TypeMobil: serviceDetail.TypeMobil,
                                TypePartialImobilizat: serviceDetail.TypePartialImobilizat,
                                TypeCompletImobilizat: serviceDetail.TypeCompletImobilizat,
                            }}); 
                            break;
                        case 'Cleaning': 
                             await tx.cleaningServiceDetails.create({ data: {
                                ...connectData, 
                                ExperienceYears: parseInt(String(serviceDetail.experienceYears), 10) || 0,
                                AvailabilityWeekdays: serviceDetail.availabilityWeekdays,
                                AvailabilityWeekends: serviceDetail.availabilityWeekends,
                                AvailabilityEvenings: serviceDetail.availabilityEvenings,
                                PropertyTypeApartments: serviceDetail.PropertyTypeApartments,
                                PropertyTypeHouses: serviceDetail.PropertyTypeHouses,
                                PropertyTypeOffices: serviceDetail.PropertyTypeOffices,
                            }}); 
                            break;
                        case 'Tutoring': 
                             await tx.tutoringServiceDetails.create({ data: {
                                ...connectData, 
                                ExperienceYears: parseInt(String(serviceDetail.experienceYears), 10) || 0,
                                AvailabilityWeekdays: serviceDetail.availabilityWeekdays,
                                AvailabilityWeekends: serviceDetail.availabilityWeekends,
                                AvailabilityEvenings: serviceDetail.availabilityEvenings,
                                Grades_1_4: serviceDetail.Grades_1_4,
                                Grades_5_8: serviceDetail.Grades_5_8,
                                Grades_9_12: serviceDetail.Grades_9_12,
                            }}); 
                            break;
                        case 'Cooking': 
                             await tx.cookingServiceDetails.create({ data: {
                                ...connectData, 
                                ExperienceYears: parseInt(String(serviceDetail.experienceYears), 10) || 0,
                                AvailabilityWeekdays: serviceDetail.availabilityWeekdays,
                                AvailabilityWeekends: serviceDetail.availabilityWeekends,
                                AvailabilityEvenings: serviceDetail.availabilityEvenings,
                                CuisineTypeTraditional: serviceDetail.CuisineTypeTraditional,
                                CuisineTypeVegetarian: serviceDetail.CuisineTypeVegetarian,
                            }}); 
                            break;
                        default: console.warn(`Unknown service slug for detail creation: ${category.Slug}`);
                    }
                }
            });

            await sendProviderRequestApprovedEmailToUser(user.Email, user.FullName || 'Utilizator');
            revalidateTag(`user-session:${user.Id}`);

            res.json({ success: true, message: 'Request approved successfully. User is now a provider and services are active.' });

        } else { // action === 'reject'
            await prisma.$transaction(async (tx) => {
                await tx.providerRegistrationRequest.update({
                    where: { Id: requestId },
                    data: { Status: ProviderRegistrationRequestStatus.Rejected, AdminNotes: adminNotes },
                });

                await tx.notification.create({
                    data: {
                        UserId: user.Id,
                        Message: `Cererea ta de prestator a fost respinsă. Motiv: ${adminNotes}`,
                        Link: '/register-provider',
                        Type: "ProviderRequestRejected",
                    },
                });
            });

            await sendProviderRequestRejectedEmailToUser(user.Email, user.FullName || 'Utilizator', adminNotes);
            
            res.json({ success: true, message: 'Request rejected successfully.' });
        }

    } catch (error) {
        console.error('[API /admin/provider-requests/update POST] Error:', error);
        if (error instanceof Prisma.PrismaClientValidationError) {
          console.error('Prisma Validation Error details:', error.message);
        }
        res.status(500).json({ message: 'Failed to update provider request status.' });
    }
});


export default router;
