
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { type AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// GET all addresses for the logged-in user
router.get('/', async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user?.id) {
    return res.status(401).json({ success: false, message: 'Neautorizat.' });
  }
  const userId = parseInt(req.user.id, 10);

  try {
    const addresses = await prisma.address.findMany({
      where: { UserId: userId },
      orderBy: { CreatedAt: 'desc' },
    });
    res.json({ success: true, addresses });
  } catch (error) {
    console.error('[API /address GET] Eroare:', error);
    res.status(500).json({ success: false, message: 'Eroare la preluarea adreselor.' });
  }
});

// POST a new address for the logged-in user
router.post('/', async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user?.id) {
    return res.status(401).json({ success: false, message: 'Neautorizat.' });
  }
  const userId = parseInt(req.user.id, 10);

  const { label, street, city, region, postalCode, countryId, regionId, cityId, sectorId } = req.body;

  if (!label || !street) {
    return res.status(400).json({ success: false, message: 'Eticheta și strada sunt obligatorii.' });
  }

  try {
    const newAddress = await prisma.address.create({
      data: {
        UserId: userId,
        Label: label,
        Street: street,

        // Enhanced location architecture
        CountryId: countryId || null,
        RegionId: regionId || null,
        CityId: cityId || null,
        SectorId: sectorId || null,

        // Backward compatibility fields
        City: city || null,
        Region: region || null,
        Country: 'Moldova',

        PostalCode: postalCode || null,
        IsDefault: false,
      },
    });
    res.status(201).json({ success: true, address: newAddress });
  } catch (error) {
    console.error('[API /address POST] Eroare:', error);
    res.status(500).json({ success: false, message: 'Eroare la crearea adresei.' });
  }
});

// PUT (update) an existing address
router.put('/:id', async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user?.id) {
    return res.status(401).json({ success: false, message: 'Neautorizat.' });
  }
  const userId = parseInt(req.user.id, 10);
  const addressId = parseInt(req.params.id, 10);

  if (isNaN(addressId)) {
    return res.status(400).json({ success: false, message: 'ID adresă invalid.' });
  }

  const { label, street, city, region, postalCode, countryId, regionId, cityId, sectorId } = req.body;
  if (!label || !street) {
    return res.status(400).json({ success: false, message: 'Eticheta și strada sunt obligatorii.' });
  }

  try {
    const addressToUpdate = await prisma.address.findUnique({
      where: { Id: addressId },
    });

    if (!addressToUpdate || addressToUpdate.UserId !== userId) {
      return res.status(404).json({ success: false, message: 'Adresa nu a fost găsită sau nu aveți permisiunea de a o modifica.' });
    }

    const updatedAddress = await prisma.address.update({
      where: { Id: addressId },
      data: {
        Label: label,
        Street: street,

        // Enhanced location architecture
        CountryId: countryId || null,
        RegionId: regionId || null,
        CityId: cityId || null,
        SectorId: sectorId || null,

        // Backward compatibility fields
        City: city || null,
        Region: region || null,

        PostalCode: postalCode || null,
      },
    });

    res.json({ success: true, address: updatedAddress });
  } catch (error) {
    console.error(`[API /address/${addressId} PUT] Eroare:`, error);
    res.status(500).json({ success: false, message: 'Eroare la actualizarea adresei.' });
  }
});

// DELETE an address
router.delete('/:id', async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user?.id) {
    return res.status(401).json({ success: false, message: 'Neautorizat.' });
  }
  const userId = parseInt(req.user.id, 10);
  const addressId = parseInt(req.params.id, 10);

  if (isNaN(addressId)) {
    return res.status(400).json({ success: false, message: 'ID adresă invalid.' });
  }

  try {
    const addressToDelete = await prisma.address.findUnique({
      where: { Id: addressId },
    });

    if (!addressToDelete || addressToDelete.UserId !== userId) {
      return res.status(404).json({ success: false, message: 'Adresa nu a fost găsită sau nu aveți permisiunea de a o șterge.' });
    }

    await prisma.address.delete({
      where: { Id: addressId },
    });

    res.json({ success: true, message: 'Adresa a fost ștearsă cu succes.' });
  } catch (error) {
    console.error(`[API /address/${addressId} DELETE] Eroare:`, error);
    res.status(500).json({ success: false, message: 'Eroare la ștergerea adresei.' });
  }
});

export default router;
