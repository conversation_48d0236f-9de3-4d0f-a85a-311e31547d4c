
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-body), sans-serif;
}

@layer base {
  :root {
    --background: 220 20% 95%; /* #F0F2F5 */
    --foreground: 215 28% 17%; /* #1F2937 */

    --muted: 220 10% 85%;
    --muted-foreground: 220 10% 45%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 28% 17%;

    --card: 0 0% 100%;
    --card-foreground: 215 28% 17%;

    --border: 220 10% 88%;
    --input: 220 10% 88%;

    --primary: 231 48% 48%; /* #3F51B5 */
    --primary-foreground: 0 0% 100%;

    --secondary: 231 48% 38%; /* Darker shade for gradients */
    --secondary-foreground: 0 0% 100%;

    --accent: 260 47% 63%; /* #9575CD */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;

    --ring: 231 48% 48%;

    --radius: 0.5rem;

    --chart-1: 231 48% 48%;
    --chart-2: 260 47% 63%;
    --chart-3: 220 70% 50%;
    --chart-4: 160 60% 45%;
    --chart-5: 30 80% 55%;

    --sidebar-background: 220 20% 97%; /* Slightly lighter for sidebar if needed */
    --sidebar-foreground: 215 28% 17%;
    --sidebar-primary: 231 48% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 260 47% 63%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 10% 88%;
    --sidebar-ring: 231 48% 48%;
  }

  .dark {
    --background: 215 28% 12%;
    --foreground: 220 20% 95%;

    --muted: 215 20% 25%;
    --muted-foreground: 220 10% 65%;

    --popover: 215 28% 10%;
    --popover-foreground: 220 20% 95%;

    --card: 215 28% 10%;
    --card-foreground: 220 20% 95%;

    --border: 215 20% 28%;
    --input: 215 20% 28%;

    --primary: 231 48% 52%;
    --primary-foreground: 0 0% 10%;

    --secondary: 231 48% 42%;
    --secondary-foreground: 0 0% 10%;

    --accent: 260 47% 68%;
    --accent-foreground: 0 0% 10%;

    --destructive: 0 62% 40%;
    --destructive-foreground: 0 0% 100%;

    --ring: 231 48% 52%;
    
    --chart-1: 231 48% 52%;
    --chart-2: 260 47% 68%;

    --sidebar-background: 215 28% 10%;
    --sidebar-foreground: 220 20% 95%;
    --sidebar-primary: 231 48% 52%;
    --sidebar-primary-foreground: 0 0% 10%;
    --sidebar-accent: 260 47% 68%;
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 215 20% 28%;
    --sidebar-ring: 231 48% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.hero-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
}

/* Styling for booked days in the provider's main calendar (src/app/dashboard/provider/calendar/page.tsx) */
/* This class is used by 'modifiersClassNames={{ booked: 'day-with-event' }}' */
.day-with-event > button {
  @apply bg-primary text-primary-foreground font-semibold !important; /* Use primary color for events */
  /* Important is used to ensure override over default ShadCN styles if necessary */
}

/* Styling for selected day that also has an event */
.rdp-day_selected.day-with-event > button {
  @apply bg-primary text-primary-foreground opacity-85 !important; /* Slightly different opacity for selected event */
}

/* Styling for 'today' that also has an event */
.rdp-day_today.day-with-event > button {
  @apply bg-primary text-primary-foreground ring-2 ring-accent !important; /* Event on today has primary bg and accent ring */
}


/* Styling for booked days in the availability modal (src/components/profile/availability-modal.tsx) */
/* This class is used by 'modifiersClassNames={{ booked: 'day-booked' }}' */
.day-booked {
  @apply bg-destructive/20 text-destructive-foreground rounded-full font-semibold relative;
}
.day-booked::after {
  content: '';
  @apply absolute bottom-1 left-1/2 -translate-x-1/2 w-1 h-1 bg-destructive rounded-full;
}
.rdp-day_selected.day-booked > button,
.rdp-day_selected:focus.day-booked > button,
.rdp-day_selected:hover.day-booked > button {
  @apply bg-destructive/40 text-destructive-foreground; /* Darker red for selected booked day */
}

/* Ensure default react-day-picker selected styles don't conflict too much if .day-with-event is also selected */
.rdp-day_selected > button:not(.day-with-event > button) {
  /* Default selected style from shadcn if not also a day-with-event */
   @apply bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground;
}