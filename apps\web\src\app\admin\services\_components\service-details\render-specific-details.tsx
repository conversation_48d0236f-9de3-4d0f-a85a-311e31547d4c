"use client";

import type { ServiceCategorySlug } from "@prisma/client";
import { NannyDetailsView } from "./category-detail-components/NannyDetails";
import { ElderCareDetailsView } from "./category-detail-components/ElderCareDetails";
import { CleaningDetailsView } from "./category-detail-components/CleaningDetails";
import { TutoringDetailsView } from "./category-detail-components/TutoringDetails";
import { CookingDetailsView } from "./category-detail-components/CookingDetails";

// A more complete type for the service object needed by this component
interface ServiceWithAllDetails {
    ServiceCategorySlug: ServiceCategorySlug | null;
    NannyServiceDetails: import("@prisma/client").NannyServiceDetails | null;
    ElderCareServiceDetails: import("@prisma/client").ElderCareServiceDetails | null;
    CleaningServiceDetails: import("@prisma/client").CleaningServiceDetails | null;
    TutoringServiceDetails: import("@prisma/client").TutoringServiceDetails | null;
    CookingServiceDetails: import("@prisma/client").CookingServiceDetails | null;
}

interface RenderSpecificDetailsProps {
    service: ServiceWithAllDetails;
}

export function RenderSpecificDetails({ service }: RenderSpecificDetailsProps) {
    switch (service.ServiceCategorySlug) {
        case 'Nanny':
            return <NannyDetailsView details={service.NannyServiceDetails} />;
        case 'ElderCare':
            return <ElderCareDetailsView details={service.ElderCareServiceDetails} />;
        case 'Cleaning':
            return <CleaningDetailsView details={service.CleaningServiceDetails} />;
        case 'Tutoring':
            return <TutoringDetailsView details={service.TutoringServiceDetails} />;
        case 'Cooking':
            return <CookingDetailsView details={service.CookingServiceDetails} />;
        default:
            return null; 
    }
}
