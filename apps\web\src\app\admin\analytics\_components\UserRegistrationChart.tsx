"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Loader2, Users, TrendingUp } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const chartTranslations = {
  userRegistrations: { ro: "Înregistrări utilizatori", ru: "Регистрации пользователей", en: "User Registrations" },
  dailyRegistrations: { ro: "Înregistrări zilnice", ru: "Ежедневные регистрации", en: "Daily Registrations" },
  clients: { ro: "Clienți", ru: "Клиенты", en: "Clients" },
  providers: { ro: "Prestatori", ru: "Поставщики", en: "Providers" },
  total: { ro: "Total", ru: "Всего", en: "Total" },
  date: { ro: "Data", ru: "Дата", en: "Date" },
  loadingChart: { ro: "Se încarcă graficul...", ru: "Загрузка графика...", en: "Loading chart..." },
  errorLoadingChart: { ro: "Eroare la încărcarea graficului", ru: "Ошибка загрузки графика", en: "Error loading chart" },
  noDataAvailable: { ro: "Nu sunt date disponibile", ru: "Нет доступных данных", en: "No data available" },
};

interface UserRegistrationData {
  date: string;
  clients: number;
  providers: number;
  total: number;
}

interface UserRegistrationChartProps {
  period: '7d' | '30d' | '90d' | '1y';
  refreshTrigger: Date;
}

export function UserRegistrationChart({ period, refreshTrigger }: UserRegistrationChartProps) {
  const { translate } = useLanguage();
  const [data, setData] = useState<UserRegistrationData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/proxy/admin/analytics/user-registrations?period=${period}`);
        if (!response.ok) {
          throw new Error('Failed to fetch user registration data');
        }
        
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        console.error('Error fetching user registration data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [period, refreshTrigger]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ro-RO', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{`${translate(chartTranslations, 'date')}: ${formatDate(label)}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {translate(chartTranslations, 'userRegistrations')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-5 h-5 animate-spin" />
              {translate(chartTranslations, 'loadingChart')}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {translate(chartTranslations, 'userRegistrations')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'errorLoadingChart')}</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {translate(chartTranslations, 'userRegistrations')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'noDataAvailable')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate totals for summary
  const totalClients = data.reduce((sum, item) => sum + item.clients, 0);
  const totalProviders = data.reduce((sum, item) => sum + item.providers, 0);
  const totalUsers = data.reduce((sum, item) => sum + item.total, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          {translate(chartTranslations, 'userRegistrations')}
        </CardTitle>
        <div className="flex gap-4 text-sm text-muted-foreground">
          <span>{translate(chartTranslations, 'clients')}: {totalClients}</span>
          <span>{translate(chartTranslations, 'providers')}: {totalProviders}</span>
          <span>{translate(chartTranslations, 'total')}: {totalUsers}</span>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              tickFormatter={formatDate}
              className="text-xs"
            />
            <YAxis className="text-xs" />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="clients" 
              stroke="#3b82f6" 
              strokeWidth={2}
              name={translate(chartTranslations, 'clients')}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="providers" 
              stroke="#10b981" 
              strokeWidth={2}
              name={translate(chartTranslations, 'providers')}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="total" 
              stroke="#8b5cf6" 
              strokeWidth={2}
              name={translate(chartTranslations, 'total')}
              dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
