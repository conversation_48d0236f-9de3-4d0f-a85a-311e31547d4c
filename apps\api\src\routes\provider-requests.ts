
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { type AuthenticatedRequest } from '../middleware/auth';
import { ProviderRegistrationRequestStatus, UserRole, NotificationType, ServiceCategorySlug } from '@prisma/client';
import { ProviderRegistrationService, ServiceSubmissionData } from '../services/provider-registration-service';

// Updated interface for granular service submission
interface RequestServiceDetailPayload {
  categoryId: number;
  serviceCategorySlug: ServiceCategorySlug;
  serviceName?: string;
  experienceYears: string | number;
  description: string;
  availabilityWeekdays?: boolean;
  availabilityWeekends?: boolean;
  availabilityEvenings?: boolean;
  LocationId?: number;

  // File uploads (File objects from frontend)
  DocBuletinFile?: File | null;
  DocDiplomeFiles?: File[] | null;
  DocRecomandariFiles?: File[] | null;

  // Service-specific fields (will be distributed to appropriate detail models)
  [key: string]: any;
}

const router = Router();

router.get('/my-status', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ request: null, message: 'Not authenticated' });
    }

    const userId = parseInt(req.user.id, 10);
    if (isNaN(userId)) {
      return res.status(400).json({ request: null, message: 'Invalid user ID in session.' });
    }
    
    const providerRequestFromDb = await prisma.providerRegistrationRequest.findUnique({
      where: { UserId: userId },
      include: {
        PendingServices: {
          include: {
            Category: true,
            NannyServiceDetails: true,
            ElderCareServiceDetails: true,
            CleaningServiceDetails: true,
            TutoringServiceDetails: true,
            CookingServiceDetails: true,
          }
        }
      }
    });

    if (!providerRequestFromDb) {
      return res.json({ request: null, message: 'No provider registration request found for this user.' });
    }

    // Ensure all services have their corresponding service details (same logic as admin endpoint)
    if (providerRequestFromDb.PendingServices) {
      for (const service of providerRequestFromDb.PendingServices) {
        // Create empty service details if they don't exist
        if (service.ServiceCategorySlug === 'Cooking' && !service.CookingServiceDetails) {
          service.CookingServiceDetails = {
            Id: '',
            PendingServiceId: service.Id,
            LocationId: null,
            PricePerHour: null,
            PricePerDay: null,
            ExperienceYears: null,
            AvailabilityWeekdays: false,
            AvailabilityWeekends: false,
            AvailabilityEvenings: false,
            ServiceMealPrep: false,
            ServiceCatering: false,
            ServiceSpecialDiet: false,
            ServiceBaking: false,
            ServiceGroceryShopping: false,
            ServiceKitchenCleanup: false,
            CuisineRomanian: false,
            CuisineItalian: false,
            CuisineFrench: false,
            CuisineAsian: false,
            CuisineVegetarian: false,
            CuisineVegan: false,
            CuisineOther: null,
            ExtraOwnIngredients: false,
            ExtraOwnTransport: false,
            ExtraWeekendAvailable: false
          };
        }

        if (service.ServiceCategorySlug === 'ElderCare' && !service.ElderCareServiceDetails) {
          service.ElderCareServiceDetails = {
            Id: '',
            PendingServiceId: service.Id,
            LocationId: null,
            PricePerHour: null,
            PricePerDay: null,
            ExperienceYears: null,
            AvailabilityWeekdays: false,
            AvailabilityWeekends: false,
            AvailabilityEvenings: false,
            AvailabilityFullTime: false,
            AvailabilityPartTime: false,
            AvailabilityOccasional: false,
            ServicePersonalCare: false,
            ServiceMedicalSupport: false,
            ServiceCompanionship: false,
            ServiceHousekeeping: false,
            ServiceMeals: false,
            ServiceTransport: false,
            ServiceShopping: false,
            ServiceMobility: false,
            ExtraFirstAid: false,
            ExtraMedicalTraining: false,
            ExtraOwnTransport: false,
            ExtraLanguages: null,
            ExtraSpecialNeeds: false,
            ExtraOvernightCare: false
          };
        }

        if (service.ServiceCategorySlug === 'Tutoring' && !service.TutoringServiceDetails) {
          service.TutoringServiceDetails = {
            Id: '',
            PendingServiceId: service.Id,
            LocationId: null,
            PricePerHour: null,
            PricePerDay: null,
            ExperienceYears: null,
            AvailabilityWeekdays: false,
            AvailabilityWeekends: false,
            AvailabilityEvenings: false,
            ServiceAfterSchool: false,
            ServiceHomeworkHelp: false,
            ServiceIndividualLessons: false,
            Grades_1_4: false,
            Grades_5_8: false,
            Grades_9_12: false,
            SubjectRomanian: false,
            SubjectMath: false,
            SubjectEnglish: false,
            SubjectOther: null,
            FormatOnline: false,
            FormatOwnHome: false,
            FormatChildHome: false,
            ExtraGames: false,
            ExtraSnack: false,
            ExtraTransport: false,
            ExtraSupervisedHomework: false
          };
        }

        if (service.ServiceCategorySlug === 'Cleaning' && !service.CleaningServiceDetails) {
          service.CleaningServiceDetails = {
            Id: '',
            PendingServiceId: service.Id,
            LocationId: null,
            PricePerHour: null,
            PricePerDay: null,
            ExperienceYears: null,
            AvailabilityWeekdays: false,
            AvailabilityWeekends: false,
            AvailabilityEvenings: false,
            AvailabilityFullTime: false,
            AvailabilityPartTime: false,
            AvailabilityOccasional: false,
            ServiceRegularCleaning: false,
            ServiceDeepCleaning: false,
            ServiceWindowCleaning: false,
            ServiceCarpetCleaning: false,
            ServiceLaundry: false,
            ServiceIroning: false,
            ServiceOrganizing: false,
            ServicePostConstruction: false,
            ExtraOwnSupplies: false,
            ExtraEcoFriendly: false,
            ExtraOwnTransport: false,
            ExtraInsured: false,
            ExtraWeekendAvailable: false,
            ExtraEmergencyService: false
          };
        }

        if (service.ServiceCategorySlug === 'Nanny' && !service.NannyServiceDetails) {
          service.NannyServiceDetails = {
            Id: '',
            PendingServiceId: service.Id,
            LocationId: null,
            PricePerHour: null,
            PricePerDay: null,
            ExperienceYears: null,
            AvailabilityWeekdays: false,
            AvailabilityWeekends: false,
            AvailabilityEvenings: false,
            PreferredAge_0_2: false,
            PreferredAge_3_6: false,
            PreferredAge_7_plus: false,
            AvailabilityFullTime: false,
            AvailabilityPartTime: false,
            AvailabilityOccasional: false,
            ServiceBabysitting: false,
            ServicePlaytime: false,
            ServiceMeals: false,
            ServiceBedtime: false,
            ServiceEducational: false,
            ServiceOutdoor: false,
            ServiceTransport: false,
            ServiceHousework: false,
            ExtraFirstAid: false,
            ExtraOwnTransport: false,
            ExtraCooking: false,
            ExtraLanguages: null,
            ExtraSpecialNeeds: false,
            ExtraOvernightCare: false
          };
        }
      }
    }

    return res.json({ request: providerRequestFromDb });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error fetching provider request status';
    console.error('[API /my-status] Error:', errorMessage, error);
    return res.status(500).json({ request: null, message: `Error fetching status: ${errorMessage}` });
  }
});

router.post('/submit', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
        return res.status(401).json({ message: 'User not authenticated' });
    }

    const { userName, userEmail, requestedServices } = req.body;
    const userId = parseInt(req.user.id, 10);

    if (!userId || !userName || !userEmail || !Array.isArray(requestedServices) || requestedServices.length === 0) {
      return res.status(400).json({ message: 'Date incomplete pentru cererea de prestator.' });
    }

    // Convert requestedServices to the new granular format
    const services: ServiceSubmissionData[] = requestedServices.map((service: RequestServiceDetailPayload) => {
      const { categoryId, serviceCategorySlug, serviceName, description, experienceYears, DocBuletinFile, DocDiplomeFiles, DocRecomandariFiles, ...otherFields } = service;

      return {
        categoryId,
        serviceCategorySlug,
        serviceName,
        description,
        experienceYears: parseInt(String(experienceYears), 10) || 0,

        // File uploads
        DocBuletinFile: DocBuletinFile || null,
        DocDiplomeFiles: DocDiplomeFiles || null,
        DocRecomandariFiles: DocRecomandariFiles || null,

        // Pass through all other service-specific fields
        ...otherFields
      };
    });

    // Use the new granular registration service
    const result = await ProviderRegistrationService.submitRegistration({
      userId,
      userName,
      userEmail,
      services
    });

    if (!result.success) {
      return res.status(400).json({
        message: result.errors?.join(', ') || 'Failed to submit registration'
      });
    }

    // Get the final request with services for response
    const finalRequest = await prisma.providerRegistrationRequest.findUnique({
      where: { Id: result.requestId },
      include: {
        PendingServices: {
          include: {
            Category: true,
            NannyServiceDetails: true,
            ElderCareServiceDetails: true,
            CleaningServiceDetails: true,
            TutoringServiceDetails: true,
            CookingServiceDetails: true,
          }
        }
      }
    });

    // Notify admins about the new/updated request
    const admins = await prisma.user.findMany({
      where: {
        UserRoles: {
          some: {
            Role: {
              Name: UserRole.Admin
            }
          }
        }
      }
    });

    for (const admin of admins) {
      await prisma.notification.create({
        data: {
          UserId: admin.Id,
          Message: `Cerere nouă de prestator de la ${userName} cu ${result.servicesCreated} servicii.`,
          Link: `/admin/provider-requests`,
          Type: NotificationType.NewProviderRequest
        },
      });
    }

    const successMessage = `Cererea de înregistrare a fost trimisă cu succes! ${result.servicesCreated} servicii create.`;

    return res.status(201).json({
      message: successMessage,
      request: finalRequest,
      servicesCreated: result.servicesCreated
    });

  } catch (error) {
    console.error('[API /provider-requests/submit] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la trimiterea cererii.';
    return res.status(500).json({ message: errorMessage });
  }
});

export default router;
