
"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

interface StepperProgressProps {
  currentStep: number; // 0-indexed
  steps: { label: string }[];
  className?: string;
}

export const StepperProgress = ({ currentStep, steps, className }: StepperProgressProps) => {
  const totalSteps = steps.length;
  const progressPercentage = totalSteps <= 1 ? (currentStep >= totalSteps -1 ? 100 : 0) : (currentStep / Math.max(1, totalSteps - 1)) * 100;


  return (
    <div className={cn("w-full mb-8 md:mb-12", className)}>
      <div className="flex items-start justify-between">
        {steps.map((step, index) => {
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;
          return (
            <React.Fragment key={index}>
              <div className="flex flex-col items-center text-center w-1/5 max-w-[120px] sm:max-w-none">
                <div
                  className={cn(
                    "w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 shrink-0",
                    isActive
                      ? "bg-primary border-primary text-primary-foreground font-semibold"
                      : isCompleted
                      ? "bg-primary/90 border-primary/90 text-primary-foreground"
                      : "bg-muted border-muted-foreground/30 text-muted-foreground"
                  )}
                >
                  {isCompleted ? <Check className="w-5 h-5 sm:w-6 sm:h-6" /> : index + 1}
                </div>
                <p
                  className={cn(
                    "text-xs sm:text-sm mt-2 transition-colors duration-300 line-clamp-2", // Allows for two lines, adjust as needed
                    isActive ? "text-primary font-semibold" : isCompleted ? "text-foreground/90" : "text-muted-foreground"
                  )}
                  style={{ minHeight: '2.5em' }} // Reserve space for two lines of text
                >
                  {step.label}
                </p>
              </div>
              {index < totalSteps - 1 && (
                <div
                  className={cn(
                    "flex-1 h-0.5 mt-4 sm:mt-5 transition-colors duration-300 mx-1 sm:mx-2", // Adjusted margin for smaller screens
                    isCompleted ? "bg-primary" : "bg-muted-foreground/30"
                  )}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
      <div className="mt-3 h-1.5 w-full bg-muted rounded-full overflow-hidden">
          <div
            className="h-full bg-primary rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
      </div>
    </div>
  );
};
