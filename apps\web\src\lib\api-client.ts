import 'server-only';

const API_URL = process.env.NEXT_PUBLIC_API_URL;
const API_KEY = process.env.INTERNAL_API_KEY;

type FetchOptions = Omit<RequestInit, 'headers'> & {
  headers?: Record<string, string>;
  jwt?: string | null; // Adaugă opțiunea de a pasa un JWT
};

async function apiFetch(endpoint: string, options: FetchOptions = {}): Promise<Response> {
  if (!API_URL || !API_KEY) {
    throw new Error('API URL or API Key is not configured.');
  }

  const headers: Record<string, string> = {
    'X-API-Key': API_KEY, // Cheia statică, prezentă mereu
    ...options.headers,
  };

  // Only set Content-Type for non-FormData requests
  // FormData requests need the browser to set the correct Content-Type with boundary
  if (!(options.body instanceof FormData)) {
    headers['Content-Type'] = 'application/json';
  }

  // Adaugă header-ul de autorizare doar dacă un JWT este furnizat
  if (options.jwt) {
    headers['Authorization'] = `Bearer ${options.jwt}`;
  }

  const response = await fetch(`${API_URL}/api/${endpoint}`, {
    ...options,
    headers,
  });

  return response;
}

export default apiFetch;