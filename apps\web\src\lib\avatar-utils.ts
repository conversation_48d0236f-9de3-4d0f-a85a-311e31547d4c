/**
 * Utility functions for handling avatar URLs
 */

/**
 * Converts a relative avatar URL to an absolute URL pointing to the backend server
 * @param avatarUrl - The avatar URL from the database (e.g., "/uploads/profile-photos/profile-3-123.jpg")
 * @returns The absolute URL pointing to the backend server
 */
export function getAvatarUrl(avatarUrl: string | null | undefined): string | undefined {
  if (!avatarUrl) {
    return undefined;
  }

  // If it's already an absolute URL (starts with http), return as-is
  if (avatarUrl.startsWith('http')) {
    return avatarUrl;
  }

  // If it's a relative URL starting with /uploads, convert to backend URL
  if (avatarUrl.startsWith('/uploads/')) {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001';
    return `${backendUrl}${avatarUrl}`;
  }

  // For any other relative URLs, assume they need to be served from backend
  if (avatarUrl.startsWith('/')) {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001';
    return `${backendUrl}${avatarUrl}`;
  }

  // If it doesn't start with /, assume it's a filename and prepend the full path
  const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001';
  return `${backendUrl}/uploads/profile-photos/${avatarUrl}`;
}
