"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { CheckCircle, Clock, AlertTriangle, Loader2, Star } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { format } from "date-fns";

interface PendingCompletionBooking {
  id: number;
  eventStartDateTime: string;
  provider: {
    fullName: string;
  };
  service: {
    serviceName: string;
  };
}

interface BookingCompletionConfirmationProps {
  bookings: PendingCompletionBooking[];
  onConfirmCompletion: (bookingId: number) => Promise<void>;
  onOpenReview: (bookingId: number) => void;
  isLoading?: boolean;
  className?: string;
}

const completionTranslations = {
  pendingConfirmation: { ro: "Confirmă Finalizarea", ru: "Подтвердить завершение", en: "Confirm Completion" },
  serviceCompleted: { ro: "Serviciu Finalizat?", ru: "Услуга завершена?", en: "Service Completed?" },
  confirmCompletionDesc: { ro: "Confirmă că serviciul a fost finalizat cu succes", ru: "Подтвердите, что услуга была успешно завершена", en: "Confirm that the service has been completed successfully" },
  confirmButton: { ro: "Confirmă Finalizarea", ru: "Подтвердить завершение", en: "Confirm Completion" },
  leaveReviewButton: { ro: "Lasă o Recenzie", ru: "Оставить отзыв", en: "Leave a Review" },
  serviceWith: { ro: "Serviciu cu", ru: "Услуга с", en: "Service with" },
  completedOn: { ro: "Finalizat pe", ru: "Завершено", en: "Completed on" },
  awaitingConfirmation: { ro: "Așteaptă confirmarea", ru: "Ожидает подтверждения", en: "Awaiting confirmation" },
  noServicesAwaitingConfirmation: { ro: "Nu ai servicii care așteaptă confirmarea", ru: "У вас нет услуг, ожидающих подтверждения", en: "No services awaiting confirmation" },
  error: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  confirmationSuccess: { ro: "Serviciul a fost confirmat ca finalizat", ru: "Услуга подтверждена как завершенная", en: "Service confirmed as completed" },
};

export function BookingCompletionConfirmation({
  bookings,
  onConfirmCompletion,
  onOpenReview,
  isLoading = false,
  className
}: BookingCompletionConfirmationProps) {
  const { translate } = useLanguage();
  const [confirmingBookingId, setConfirmingBookingId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleConfirmCompletion = async (bookingId: number) => {
    setConfirmingBookingId(bookingId);
    setError(null);

    try {
      await onConfirmCompletion(bookingId);
    } catch (error) {
      console.error('Error confirming completion:', error);
      setError('A apărut o eroare la confirmarea finalizării serviciului.');
    } finally {
      setConfirmingBookingId(null);
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>Se încarcă serviciile...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (bookings.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            {translate(completionTranslations, 'pendingConfirmation')}
          </CardTitle>
          <CardDescription>
            Servicii care necesită confirmarea finalizării
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <p className="text-muted-foreground">
              {translate(completionTranslations, 'noServicesAwaitingConfirmation')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-orange-500" />
          {translate(completionTranslations, 'pendingConfirmation')}
        </CardTitle>
        <CardDescription>
          Servicii care necesită confirmarea finalizării
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{translate(completionTranslations, 'error')}</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-4">
          {bookings.map((booking) => (
            <Card key={booking.id} className="border-l-4 border-l-orange-500">
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{booking.service.serviceName}</h4>
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        {translate(completionTranslations, 'awaitingConfirmation')}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground">
                      {translate(completionTranslations, 'serviceWith')} {booking.provider.fullName}
                    </p>
                    
                    <p className="text-sm text-muted-foreground">
                      {translate(completionTranslations, 'completedOn')} {format(new Date(booking.eventStartDateTime), "PPP")}
                    </p>
                  </div>

                  <div className="flex flex-col gap-2 ml-4">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                          disabled={confirmingBookingId === booking.id}
                        >
                          {confirmingBookingId === booking.id ? (
                            <>
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                              Se confirmă...
                            </>
                          ) : (
                            <>
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Confirmă
                            </>
                          )}
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>{translate(completionTranslations, 'serviceCompleted')}</DialogTitle>
                          <DialogDescription>
                            {translate(completionTranslations, 'confirmCompletionDesc')}
                          </DialogDescription>
                        </DialogHeader>
                        
                        <div className="space-y-4">
                          <div className="bg-muted/50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">{booking.service.serviceName}</h4>
                            <p className="text-sm text-muted-foreground">
                              {translate(completionTranslations, 'serviceWith')} {booking.provider.fullName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {format(new Date(booking.eventStartDateTime), "PPP 'la' HH:mm")}
                            </p>
                          </div>

                          <div className="flex gap-2 justify-end">
                            <Button variant="outline">
                              Anulează
                            </Button>
                            <Button
                              onClick={() => handleConfirmCompletion(booking.id)}
                              disabled={confirmingBookingId === booking.id}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              {confirmingBookingId === booking.id ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                  Se confirmă...
                                </>
                              ) : (
                                translate(completionTranslations, 'confirmButton')
                              )}
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onOpenReview(booking.id)}
                      className="text-yellow-600 border-yellow-600 hover:bg-yellow-50"
                    >
                      <Star className="w-3 h-3 mr-1" />
                      {translate(completionTranslations, 'leaveReviewButton')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
