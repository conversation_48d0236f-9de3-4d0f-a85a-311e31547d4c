"use client";

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import Link from 'next/link';

const errorTranslations = {
  somethingWentWrong: { ro: "Ceva nu a mers bine", ru: "Что-то пошло не так", en: "Something went wrong" },
  errorOccurred: { ro: "A apărut o eroare neașteptată", ru: "Произошла неожиданная ошибка", en: "An unexpected error occurred" },
  tryAgain: { ro: "Încearcă din nou", ru: "Попробовать снова", en: "Try again" },
  goHome: { ro: "<PERSON>rg<PERSON> a<PERSON>", ru: "На главную", en: "Go home" },
  roleLoadingError: { ro: "<PERSON><PERSON>re la încărcarea rolului", ru: "Ошибка загрузки роли", en: "Error loading role" },
  navigationError: { ro: "Eroare de navigare", ru: "Ошибка навигации", en: "Navigation error" },
  permissionDenied: { ro: "Acces interzis", ru: "Доступ запрещен", en: "Access denied" },
  notAuthorized: { ro: "Nu ești autorizat să accesezi această pagină", ru: "Вы не авторизованы для доступа к этой странице", en: "You are not authorized to access this page" },
};

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class DashboardErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error Boundary caught an error:', error, errorInfo);
    
    // Log error to analytics
    if (typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.track('dashboard_error', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: Date.now()
      });
    }

    this.setState({
      error,
      errorInfo,
    });
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.retry} />;
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.retry} />;
    }

    return this.props.children;
  }
}

// Default error fallback component
function DefaultErrorFallback({ error, retry }: { error: Error; retry: () => void }) {
  const { translate } = useLanguage();

  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-destructive/10 rounded-full w-fit">
            <AlertTriangle className="w-8 h-8 text-destructive" />
          </div>
          <CardTitle className="text-xl">
            {translate(errorTranslations, 'somethingWentWrong')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-center">
          <p className="text-muted-foreground">
            {translate(errorTranslations, 'errorOccurred')}
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <details className="text-left text-xs bg-muted p-3 rounded">
              <summary className="cursor-pointer font-medium">Error Details</summary>
              <pre className="mt-2 whitespace-pre-wrap">{error.message}</pre>
            </details>
          )}
          
          <div className="flex gap-2 justify-center">
            <Button onClick={retry} variant="outline" className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4" />
              {translate(errorTranslations, 'tryAgain')}
            </Button>
            <Button asChild>
              <Link href="/dashboard" className="flex items-center gap-2">
                <Home className="w-4 h-4" />
                {translate(errorTranslations, 'goHome')}
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Role switching error component
export function RoleSwitchError({ error, onRetry }: { error: string; onRetry: () => void }) {
  const { translate } = useLanguage();

  return (
    <Card className="border-destructive/50 bg-destructive/5">
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <AlertTriangle className="w-5 h-5 text-destructive flex-shrink-0" />
          <div className="flex-1">
            <p className="font-medium text-destructive">
              {translate(errorTranslations, 'roleLoadingError')}
            </p>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
          </div>
          <Button size="sm" variant="outline" onClick={onRetry}>
            {translate(errorTranslations, 'tryAgain')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Navigation error component
export function NavigationError({ message, onRetry }: { message: string; onRetry?: () => void }) {
  const { translate } = useLanguage();

  return (
    <div className="p-4 border border-destructive/20 bg-destructive/5 rounded-lg">
      <div className="flex items-start gap-3">
        <AlertTriangle className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h4 className="font-medium text-destructive">
            {translate(errorTranslations, 'navigationError')}
          </h4>
          <p className="text-sm text-muted-foreground mt-1">{message}</p>
          {onRetry && (
            <Button size="sm" variant="ghost" onClick={onRetry} className="mt-2 p-0 h-auto">
              {translate(errorTranslations, 'tryAgain')}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// Permission denied component
export function PermissionDenied({ message }: { message?: string }) {
  const { translate } = useLanguage();

  return (
    <div className="flex items-center justify-center min-h-[300px] p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto mb-4 p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-full w-fit">
            <AlertTriangle className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
          </div>
          <CardTitle>{translate(errorTranslations, 'permissionDenied')}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            {message || translate(errorTranslations, 'notAuthorized')}
          </p>
          <Button asChild>
            <Link href="/dashboard">
              {translate(errorTranslations, 'goHome')}
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
