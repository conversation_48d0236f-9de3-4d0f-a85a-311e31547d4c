import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';

interface FieldDisplayProps {
  label: string;
  value: any;
  type: 'text' | 'boolean' | 'number' | 'array' | 'decimal';
  unit?: string;
}

const FieldDisplay: React.FC<FieldDisplayProps> = ({ label, value, type, unit }) => {
  const { translate } = useLanguage();

  const renderValue = () => {
    switch (type) {
      case 'boolean':
        return (
          <div className="flex items-center gap-2">
            <Switch checked={Boolean(value)} disabled />
            <span className="text-sm">
              {value ? translate(commonTranslations, 'adminFieldYes') : translate(commonTranslations, 'adminFieldNo')}
            </span>
          </div>
        );
      case 'array':
        if (!value || (Array.isArray(value) && value.length === 0)) {
          return <span className="text-muted-foreground italic">{translate(commonTranslations, 'adminFieldNoneSpecified')}</span>;
        }
        return (
          <div className="flex flex-wrap gap-1">
            {value.map((item: string, index: number) => (
              <Badge key={index} variant="outline" className="text-xs">
                {item}
              </Badge>
            ))}
          </div>
        );
      case 'number':
      case 'decimal':
        if (value === null || value === undefined) {
          return <span className="text-muted-foreground italic">{translate(commonTranslations, 'adminFieldNotProvided')}</span>;
        }
        return <span>{value}{unit ? ` ${unit}` : ''}</span>;
      case 'text':
      default:
        if (!value || value === '') {
          return <span className="text-muted-foreground italic">{translate(commonTranslations, 'adminFieldNotProvided')}</span>;
        }
        return <span>{value}</span>;
    }
  };

  return (
    <div className="space-y-1">
      <p className="text-xs font-medium text-muted-foreground">{label}</p>
      <div className="text-sm">{renderValue()}</div>
    </div>
  );
};

interface ComprehensiveFieldRendererProps {
  serviceType: 'nanny' | 'eldercare' | 'cleaning' | 'tutoring' | 'cooking';
  details: any;
}

export const ComprehensiveFieldRenderer: React.FC<ComprehensiveFieldRendererProps> = ({
  serviceType,
  details
}) => {
  const { translate } = useLanguage();

  if (!details) {
    return (
      <div className="p-4 bg-orange-50 border border-orange-200 rounded-md">
        <p className="text-orange-700 text-sm">{translate(commonTranslations, 'adminNoServiceDetailsAvailable')}</p>
      </div>
    );
  }

  const renderCommonFields = () => (
    <div className="space-y-4">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'adminBasicInformation')}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <FieldDisplay label={translate(commonTranslations, 'locationLabel')} value={details.LocationId ? `Location ID: ${details.LocationId}` : 'Not specified'} type="text" />
        <FieldDisplay label={translate(commonTranslations, 'adminExperienceYears')} value={details.ExperienceYears} type="number" unit={translate(commonTranslations, 'yearsSuffix')} />
        <FieldDisplay label={translate(commonTranslations, 'descriptionLabel')} value={details.Description} type="text" />
      </div>

      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2 mt-6">{translate(commonTranslations, 'adminPricing')}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <FieldDisplay label={translate(commonTranslations, 'pricePerHourLabel')} value={details.PricePerHour} type="decimal" unit="MDL" />
        <FieldDisplay label={translate(commonTranslations, 'pricePerDayLabel')} value={details.PricePerDay} type="decimal" unit="MDL" />
        <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionAmount')} value={details.PriceSubscriptionAmount} type="decimal" unit="MDL" />
        <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionUnit')} value={details.PriceSubscriptionUnit} type="text" />
        <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionText')} value={details.PriceSubscriptionText} type="text" />
        <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionDetails')} value={details.SubscriptionDetails} type="text" />
      </div>

      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2 mt-6">{translate(commonTranslations, 'generalAvailabilityLabel')}</h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FieldDisplay label={translate(commonTranslations, 'availabilityWeekdays')} value={details.AvailabilityWeekdays} type="boolean" />
        <FieldDisplay label={translate(commonTranslations, 'availabilityWeekends')} value={details.AvailabilityWeekends} type="boolean" />
        <FieldDisplay label={translate(commonTranslations, 'availabilityEvenings')} value={details.AvailabilityEvenings} type="boolean" />
      </div>

      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2 mt-6">{translate(commonTranslations, 'adminDocuments')}</h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FieldDisplay label={translate(commonTranslations, 'adminBulletinFile')} value={details.DocBuletinFileName} type="text" />
        <FieldDisplay label={translate(commonTranslations, 'adminDiplomaFiles')} value={details.DocDiplomeFileNames} type="array" />
        <FieldDisplay label={translate(commonTranslations, 'adminRecommendationFiles')} value={details.DocRecomandariFileNames} type="array" />
      </div>
    </div>
  );

  const renderNannySpecificFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'childcareTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcarePreferredAgeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAge0_2')} value={details.PreferredAge_0_2} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAge3_6')} value={details.PreferredAge_3_6} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAge7_plus')} value={details.PreferredAge_7_plus} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcareAvailabilityTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityFullTime')} value={details.AvailabilityFullTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityPartTime')} value={details.AvailabilityPartTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOccasional')} value={details.AvailabilityOccasional} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminBabysitting')} value={details.ServiceBabysitting} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminPlaytime')} value={details.ServicePlaytime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMeals')} value={details.ServiceMeals} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminBedtime')} value={details.ServiceBedtime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEducational')} value={details.ServiceEducational} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOutdoorActivities')} value={details.ServiceOutdoor} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminTransport')} value={details.ServiceTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHousework')} value={details.ServiceHousework} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHomeworkHelp')} value={details.ServiceHomeworkHelp} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalQualifications')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareFirstAidLabel')} value={details.ExtraFirstAid} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransport')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCooking')} value={details.ExtraCooking} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSpecialNeeds')} value={details.ExtraSpecialNeeds} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOvernightCare')} value={details.ExtraOvernightCare} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminAdditionalLanguages')} value={details.ExtraLanguages} type="text" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderElderCareSpecificFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'elderCareTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcareAvailabilityTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityFullTime')} value={details.AvailabilityFullTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityPartTime')} value={details.AvailabilityPartTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOccasional')} value={details.AvailabilityOccasional} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAvailabilitySchedule')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminWeekdays')} value={details.AvailabilityWeekdays} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWeekends')} value={details.AvailabilityWeekends} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEvenings')} value={details.AvailabilityEvenings} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminPersonalCare')} value={details.ServicePersonalCare} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMedicalSupport')} value={details.ServiceMedicalSupport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCompanionship')} value={details.ServiceCompanionship} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHousekeeping')} value={details.ServiceHousekeeping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMeals')} value={details.ServiceMeals} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminTransport')} value={details.ServiceTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminShopping')} value={details.ServiceShopping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMobilityAssistance')} value={details.ServiceMobility} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'elderCareTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'elderCareTypeMobil')} value={details.TypeMobil} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'elderCareTypePartialImobilizat')} value={details.TypePartialImobilizat} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'elderCareTypeCompletImobilizat')} value={details.TypeCompletImobilizat} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'elderCareMedicalKnowledgeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'elderCareMedicalKnowledgeBasicLabel')} value={details.MedicalKnowledgeBasic} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'elderCareMedicalKnowledgeAdvancedLabel')} value={details.MedicalKnowledgeAdvanced} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'elderCareMedicationAdminLabel')} value={details.MedicationAdmin} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminPersonalCare')} value={details.ServicePersonalCare} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMedicalSupport')} value={details.ServiceMedicalSupport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCompanionship')} value={details.ServiceCompanionship} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHousekeeping')} value={details.ServiceHousekeeping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMeals')} value={details.ServiceMeals} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminTransport')} value={details.ServiceTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminShopping')} value={details.ServiceShopping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMobility')} value={details.ServiceMobility} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalServices')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminFirstAid')} value={details.ExtraFirstAid} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminMedicalTrainingExtra')} value={details.ExtraMedicalTraining} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransportExtra')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSpecialNeedsExtra')} value={details.ExtraSpecialNeeds} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOvernightCareExtra')} value={details.ExtraOvernightCare} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminLanguagesExtra')} value={details.ExtraLanguages} type="text" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderCleaningSpecificFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'cleaningTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'childcareAvailabilityTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityFullTime')} value={details.AvailabilityFullTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareAvailabilityPartTime')} value={details.AvailabilityPartTime} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOccasional')} value={details.AvailabilityOccasional} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'cleaningPropertyTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'cleaningPropertyTypeApartments')} value={details.PropertyTypeApartments} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'cleaningPropertyTypeHouses')} value={details.PropertyTypeHouses} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'cleaningPropertyTypeOffices')} value={details.PropertyTypeOffices} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminRegularCleaning')} value={details.ServiceRegularCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminDeepCleaning')} value={details.ServiceDeepCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWindowCleaning')} value={details.ServiceWindowCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCarpetCleaning')} value={details.ServiceCarpetCleaning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminLaundry')} value={details.ServiceLaundry} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminIroning')} value={details.ServiceIroning} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOrganizing')} value={details.ServiceOrganizing} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminPostConstruction')} value={details.ServicePostConstruction} type="boolean" />

          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalServices')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminOwnSupplies')} value={details.ExtraOwnSupplies} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEcoFriendlyProducts')} value={details.ExtraEcoFriendly} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransportExtra')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminInsuranceCoverage')} value={details.ExtraInsured} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWeekendAvailable')} value={details.ExtraWeekendAvailable} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEmergencyService')} value={details.ExtraEmergencyService} type="boolean" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderTutoringSpecificFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'categoryTutoring')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAvailabilitySchedule')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminWeekdays')} value={details.AvailabilityWeekdays} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWeekends')} value={details.AvailabilityWeekends} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEvenings')} value={details.AvailabilityEvenings} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminAfterSchool')} value={details.ServiceAfterSchool} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminHomeworkHelp')} value={details.ServiceHomeworkHelp} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminIndividualLessons')} value={details.ServiceIndividualLessons} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminGradeLevels')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminGrades1_4')} value={details.Grades_1_4} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminGrades5_8')} value={details.Grades_5_8} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminGrades9_12')} value={details.Grades_9_12} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminSubjects')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminSubjectRomanian')} value={details.SubjectRomanian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSubjectMath')} value={details.SubjectMath} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSubjectEnglish')} value={details.SubjectEnglish} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSubjectOther')} value={details.SubjectOther} type="text" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminFormatOptions')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminFormatOnline')} value={details.FormatOnline} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminFormatOwnHome')} value={details.FormatOwnHome} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminFormatChildHome')} value={details.FormatChildHome} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalServices')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminGames')} value={details.ExtraGames} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSnackProvision')} value={details.ExtraSnack} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminTransport')} value={details.ExtraTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSupervisedHomework')} value={details.ExtraSupervisedHomework} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'childcareFirstAidLabel')} value={details.ExtraFirstAid} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransport')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSpecialNeeds')} value={details.ExtraSpecialNeeds} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminAdditionalLanguages')} value={details.ExtraLanguages} type="text" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderCookingSpecificFields = () => (
    <div className="space-y-4 mt-6">
      <h4 className="font-medium text-sm text-muted-foreground border-b pb-2">{translate(commonTranslations, 'cookingTitle')}</h4>

      <div className="space-y-4">
        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAvailabilitySchedule')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminWeekdays')} value={details.AvailabilityWeekdays} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWeekends')} value={details.AvailabilityWeekends} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminEvenings')} value={details.AvailabilityEvenings} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminPricingSubscription')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionAmount')} value={details.PriceSubscriptionAmount} type="text" unit="MDL" />
            <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionUnit')} value={details.PriceSubscriptionUnit} type="text" />
            <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionText')} value={details.PriceSubscriptionText} type="text" />
            <FieldDisplay label={translate(commonTranslations, 'adminSubscriptionDetails')} value={details.SubscriptionDetails} type="text" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminServicesOffered')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminMealPrep')} value={details.ServiceMealPrep} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCatering')} value={details.ServiceCatering} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminSpecialDiet')} value={details.ServiceSpecialDiet} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminBaking')} value={details.ServiceBaking} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminGroceryShopping')} value={details.ServiceGroceryShopping} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminKitchenCleanup')} value={details.ServiceKitchenCleanup} type="boolean" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'cookingCuisineTypeLabel')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminCuisineRomanian')} value={details.CuisineRomanian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCuisineItalian')} value={details.CuisineItalian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCuisineFrench')} value={details.CuisineFrench} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCuisineAsian')} value={details.CuisineAsian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'cookingCuisineTypeVegetarian')} value={details.CuisineVegetarian} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCuisineVegan')} value={details.CuisineVegan} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminCuisineOther')} value={details.CuisineOther} type="text" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminMealDetailsExtras')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'cookingPricePerMealLabel')} value={details.PricePerMeal} type="text" unit="MDL" />
            <FieldDisplay label={translate(commonTranslations, 'cookingMinPortionsLabel')} value={details.MinPortions} type="text" />
          </div>
        </div>

        <div>
          <h5 className="text-xs font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAdditionalServices')}</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldDisplay label={translate(commonTranslations, 'adminOwnIngredients')} value={details.ExtraOwnIngredients} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminOwnTransportExtra')} value={details.ExtraOwnTransport} type="boolean" />
            <FieldDisplay label={translate(commonTranslations, 'adminWeekendAvailable')} value={details.ExtraWeekendAvailable} type="boolean" />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {renderCommonFields()}

      {serviceType === 'nanny' && renderNannySpecificFields()}
      {serviceType === 'eldercare' && renderElderCareSpecificFields()}
      {serviceType === 'cleaning' && renderCleaningSpecificFields()}
      {serviceType === 'tutoring' && renderTutoringSpecificFields()}
      {serviceType === 'cooking' && renderCookingSpecificFields()}
    </div>
  );
};
