
// src/components/layout/navbar.tsx
"use client";

import Link from 'next/link';
import { useState, useEffect, useCallback } from 'react';
import { Menu, X, Globe, LogOut, User as UserIcon, LogIn, UserPlus, LayoutDashboard, Briefcase, Bell, Loader2, Settings, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle, SheetFooter } from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';
import { Logo } from '@/components/logo';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useRouter, usePathname } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { useLanguage, type Language } from '@/contexts/language-context';
import { formatDistanceToNowStrict } from 'date-fns';
import { ro, ru, enUS as en } from 'date-fns/locale';
import { Skeleton } from "@/components/ui/skeleton";
import { signOut } from "next-auth/react";
import { useUser } from '@/contexts/user-context';
import { getAvatarUrl } from '@/lib/avatar-utils';
import { RoleToggle, useRoleToggle } from '@/components/dashboard/role-toggle';

const dateFnsLocalesMap: Record<string, Locale> = { ro, ru, en };

const navLinkDefinitions = [
  { href: '/', translationKey: 'home' },
  { href: '/search', translationKey: 'services' },
  { href: '/about', translationKey: 'about' },
  { href: '/contact', translationKey: 'contact' },
];

const navTranslations = {
  home: { ro: 'Acasă', ru: 'Главная', en: 'Home' },
  services: { ro: 'Servicii', ru: 'Услуги', en: 'Services' },
  about: { ro: 'Despre noi', ru: 'О нас', en: 'About Us' },
  contact: { ro: 'Contact', ru: 'Контакты', en: 'Contact' },
  userPanel: { ro: 'Panou Utilizator', ru: 'Панель пользователя', en: 'User Panel'},
  becomeProvider: { ro: 'Devino Prestator', ru: 'Стать поставщиком', en: 'Become Provider'},
  logout: { ro: 'Deconectare', ru: 'Выйти', en: 'Logout'},
  login: { ro: 'Conectare', ru: 'Войти', en: 'Login'},
  register: { ro: 'Înregistrare', ru: 'Регистрация', en: 'Register'},
  language: { ro: 'Limbă', ru: 'Язык', en: 'Language'},
  mobileMenuTitle: { ro: 'Meniu Principal', ru: 'Главное меню', en: 'Main Menu' },
  notifications: { ro: "Notificări", ru: "Уведомления", en: "Notifications" },
  noNewNotifications: { ro: "Nicio notificare nouă", ru: "Нет новых уведомлений", en: "No new notifications" },
  markAllAsRead: { ro: "Marchează totul ca citit", ru: "Отметить все как прочитанные", en: "Mark all as read" },
  viewAllNotifications: { ro: "Vezi toate notificările", ru: "Посмотреть все уведомления", en: "View all notifications" },
  myMessages: { ro: "Mesajele Mele", ru: "Мои сообщения", en: "My Messages" },
  profileSettings: { ro: "Setări Profil", ru: "Настройки профиля", en: "Profile Settings" },
};

interface Notification {
  id: number;
  message: string;
  link?: string | null;
  isRead: boolean;
  createdAt: string; // ISO date string
}

const getInitials = (name: string | null | undefined): string => {
  if (!name || typeof name !== 'string') return '';
  const cleanedName = name.trim();
  if (!cleanedName) return '';

  const words = cleanedName.split(/\s+/).filter(Boolean);

  if (words.length === 0) return '';

  if (words.length === 1) {
    const word = words[0];
    if (word.length >= 2) return word.substring(0, 2).toUpperCase();
    return word.toUpperCase();
  } else {
    return ( (words[0][0] || '') + (words[words.length - 1][0] || '') ).toUpperCase();
  }
};


export function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, isLoading: isUserLoading, clearUser } = useUser();
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const { currentLanguage, setCurrentLanguage, languages, translate } = useLanguage();
  const selectedDateLocale = dateFnsLocalesMap[currentLanguage.code] || ro;

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);

  const isLoggedIn = !!user;
  const displayName = user?.name;
  const userEmailState = user?.email;
  const avatarUrlState = user?.image;
  const isProvider = user?.isProvider || false;
  const isAdmin = user?.isAdmin || false;
  const userId = user?.id;

  // Role toggle functionality
  const { currentRole, switchRole, isLoading: roleLoading, error: roleError } = useRoleToggle();
  const isDashboardPage = pathname.startsWith('/dashboard');

  // Only fetch notifications when user opens the dropdown
  const [lastFetchTime, setLastFetchTime] = useState(0);
  const handleNotificationDropdownOpen = async () => {
    const now = Date.now();
    if (now - lastFetchTime < 1000) { // Prevent fetching more than once per second
      return;
    }
    if (!isLoggedIn || !userId) return;

    setLastFetchTime(now);
    setIsLoadingNotifications(true);
    try {
      const response = await fetch(`/api/proxy/notifications`);
      const data = await response.json();
      setNotifications(data.notifications || []);
      setUnreadCount(data.unreadCount || 0);
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  // Reset notifications when user logs out
  useEffect(() => {
    if (!isLoggedIn) {
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [isLoggedIn]);

  const handleLanguageChange = (langCode: string) => {
    const selectedLang = languages.find(l => l.code === langCode);
    if (selectedLang) {
      setCurrentLanguage(selectedLang);
    }
    setIsMobileMenuOpen(false); 
  };

  const handleLogout = async () => {
    console.log('[Navbar] Logout initiated');
    setIsMobileMenuOpen(false);

    try {
      // Show success toast
      toast({ title: "Succes!", description: "Deconectare reușită." });

      // Use NextAuth's built-in redirect - this handles the logout cleanly
      await signOut({ callbackUrl: '/' });

    } catch (error) {
      console.error('[Navbar] Logout error:', error);
      toast({
        title: "Eroare!",
        description: "A apărut o eroare la deconectare.",
        variant: "destructive"
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;
    const prevNotifications = [...notifications];
    const prevUnreadCount = unreadCount;
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
    setUnreadCount(0);
    try {
      const res = await fetch(`/api/proxy/notifications/mark-as-read`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ markAll: true }),
      });
      if (!res.ok) {
        setNotifications(prevNotifications);
        setUnreadCount(prevUnreadCount);
        toast({ variant: "destructive", title: "Eroare", description: "Nu s-au putut marca notificările ca citite." });
      }
    } catch (error) {
        setNotifications(prevNotifications);
        setUnreadCount(prevUnreadCount);
        toast({ variant: "destructive", title: "Eroare", description: "A apărut o problemă de rețea." });
    }
  };
  
  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.isRead) {
      setNotifications(prev => prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n));
      setUnreadCount(prev => Math.max(0, prev - 1));
      try {
        await fetch(`/api/proxy/notifications/mark-as-read`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ notificationId: notification.id }),
        });
      } catch (error) {
        console.error("Failed to mark notification as read on server", error);
      }
    }
    if (notification.link) {
      router.push(notification.link);
    }
  };

  const translatedNavLinks = navLinkDefinitions.map(link => ({
    ...link,
    label: translate(navTranslations, link.translationKey as keyof typeof navTranslations)
  }));

  const renderDesktopAuthControls = () => {
    // Show loading indicator during authentication check
    if (isUserLoading) {
      return (
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        </div>
      );
    }
    
    const languageSelectorDropdown = (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <Globe className="h-5 w-5" />
            <span className="sr-only">{translate(navTranslations, 'language')}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>{translate(navTranslations, 'language')}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {languages.map((lang) => (
            <DropdownMenuItem
              key={lang.code}
              onSelect={() => handleLanguageChange(lang.code)}
              className={cn("cursor-pointer", currentLanguage.code === lang.code && "bg-accent text-accent-foreground")}
            >
              {lang.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );

    const notificationDropdown = isLoggedIn && (
      <DropdownMenu onOpenChange={(open) => open && handleNotificationDropdownOpen()}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-destructive text-destructive-foreground text-xs">
                {unreadCount > 9 ? '9+' : unreadCount}
              </span>
            )}
            <span className="sr-only">{translate(navTranslations, 'notifications')}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-80">
          <DropdownMenuLabel className="flex justify-between items-center">
            <span>{translate(navTranslations, 'notifications')}</span>
            {unreadCount > 0 && (
              <Button variant="link" size="sm" className="p-0 h-auto text-xs" onClick={handleMarkAllAsRead}>
                {translate(navTranslations, 'markAllAsRead')}
              </Button>
            )}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {isLoadingNotifications ? (
            <DropdownMenuItem disabled className="flex justify-center py-2">
              <Loader2 className="h-4 w-4 animate-spin" />
            </DropdownMenuItem>
          ) : notifications.length === 0 ? (
            <DropdownMenuItem disabled>{translate(navTranslations, 'noNewNotifications')}</DropdownMenuItem>
          ) : (
            notifications.slice(0, 5).map((notif) => (
              <DropdownMenuItem 
                key={notif.id} 
                onSelect={() => handleNotificationClick(notif)}
                className={cn("flex items-start gap-2 py-2 px-3 cursor-pointer", !notif.isRead && "font-semibold")}
              >
                {!notif.isRead && <span className="mt-1 flex h-2 w-2 rounded-full bg-primary" />}
                <div className={cn("flex-1 space-y-0.5", !notif.isRead && "pl-0", notif.isRead && "pl-4")}>
                    <p className="text-xs text-foreground/90 leading-tight line-clamp-2">{notif.message}</p>
                    <p className="text-xs text-muted-foreground">
                        {formatDistanceToNowStrict(new Date(notif.createdAt), { addSuffix: true, locale: selectedDateLocale })}
                    </p>
                </div>
              </DropdownMenuItem>
            ))
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href="/dashboard/notifications" className="flex justify-center cursor-pointer">
              {translate(navTranslations, 'viewAllNotifications')}
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );

    const userMenuDropdown = (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="flex items-center space-x-2 px-3 py-2 h-10">
            <Avatar className="h-8 w-8">
              <AvatarImage src={getAvatarUrl(avatarUrlState)} alt={displayName || "User Avatar"} />
              <AvatarFallback>
                {getInitials(displayName) ? getInitials(displayName) : <UserIcon className="h-5 w-5" />}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium hidden sm:inline">{displayName || "Contul Meu"}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel className="font-normal py-2 px-3">
            <div className="flex flex-col space-y-0.5">
                <p className="text-sm font-medium leading-none">{displayName}</p>
                {userEmailState && <p className="text-xs leading-none text-muted-foreground">{userEmailState}</p>}
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href={isAdmin ? "/admin" : "/dashboard"} className="flex items-center cursor-pointer">
              <LayoutDashboard className="mr-2 h-4 w-4" />
              {translate(navTranslations, 'userPanel')}
            </Link>
          </DropdownMenuItem>
          {/* Only show dashboard-related links for non-admin users */}
          {!isAdmin && (
            <>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/chat" className="flex items-center cursor-pointer">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  {translate(navTranslations, 'myMessages')}
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/settings" className="flex items-center cursor-pointer">
                  <Settings className="mr-2 h-4 w-4" />
                  {translate(navTranslations, 'profileSettings')}
                </Link>
              </DropdownMenuItem>
            </>
          )}
          {isLoggedIn && !isProvider && !isAdmin && (
            <DropdownMenuItem asChild>
              <Link href="/register-provider" className="flex items-center cursor-pointer">
                <Briefcase className="mr-2 h-4 w-4" />
                {translate(navTranslations, 'becomeProvider')}
              </Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout} className="flex items-center cursor-pointer text-destructive focus:text-destructive focus:bg-destructive/10">
            <LogOut className="mr-2 h-4 w-4" />
            {translate(navTranslations, 'logout')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );

    if (isLoggedIn) {
      return (
        <div className="flex items-center space-x-2">
          {notificationDropdown}
          {/* Show role toggle only on dashboard pages and for providers */}
          {isDashboardPage && isProvider && (
            <RoleToggle
              currentRole={currentRole}
              availableRoles={['client', 'provider']}
              onRoleChange={switchRole}
              isLoading={roleLoading}
              className="hidden sm:flex"
            />
          )}
          {languageSelectorDropdown}
          {userMenuDropdown}
        </div>
      );
    }
    // Not logged in
    return (
      <div className="flex items-center space-x-2">
        <Button variant="ghost" asChild>
          <Link href="/login">{translate(navTranslations, 'login')}</Link>
        </Button>
        <Button variant="default" asChild>
          <Link href="/auth/register">{translate(navTranslations, 'register')}</Link>
        </Button>
        {languageSelectorDropdown}
      </div>
    );
  };

  const renderMobileAuthControls = () => {
    // Show loading indicator during authentication check
    if (isUserLoading) {
       return (
         <div className="flex items-center justify-center py-4">
           <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
           <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
         </div>
       );
    }
    if (isLoggedIn) {
      return (
        <div>
          <Link
            href={isAdmin ? "/admin" : "/dashboard"}
            className="flex items-center font-medium text-foreground hover:text-accent transition-colors py-2 text-lg"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <LayoutDashboard className="mr-2 h-5 w-5" />
            {translate(navTranslations, 'userPanel')}
          </Link>
          {/* Only show dashboard-related links for non-admin users */}
          {!isAdmin && (
            <>
              <Link
                href="/dashboard/chat"
                className="flex items-center font-medium text-foreground hover:text-accent transition-colors py-2 text-lg"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <MessageSquare className="mr-2 h-5 w-5" />
                {translate(navTranslations, 'myMessages')}
              </Link>
              <Link
                href="/dashboard/settings"
                className="flex items-center font-medium text-foreground hover:text-accent transition-colors py-2 text-lg"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Settings className="mr-2 h-5 w-5" />
                {translate(navTranslations, 'profileSettings')}
              </Link>
            </>
          )}
          {isLoggedIn && !isProvider && !isAdmin && (
            <Link
              href="/register-provider"
              className="flex items-center font-medium text-foreground hover:text-accent transition-colors py-2 text-lg"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <Briefcase className="mr-2 h-5 w-5" />
              {translate(navTranslations, 'becomeProvider')}
            </Link>
          )}
          <Separator className="my-2"/>
          <div className="py-2 text-lg">
            <p className="font-medium text-foreground mb-1">{translate(navTranslations, 'language')}:</p>
            {languages.map((lang) => (
              <Button
                key={lang.code}
                variant="ghost"
                className={cn(
                  "w-full justify-start text-lg py-1 px-0 font-normal",
                  currentLanguage.code === lang.code && "text-accent font-semibold"
                )}
                onClick={() => handleLanguageChange(lang.code)}
              >
                {lang.label}
              </Button>
            ))}
          </div>
          <Separator className="my-2"/>
          <Button variant="ghost" onClick={handleLogout} className="w-full justify-start font-medium text-destructive hover:text-destructive hover:bg-destructive/10 transition-colors py-2 text-lg px-0">
            <LogOut className="mr-2 h-5 w-5" />
            {translate(navTranslations, 'logout')}
          </Button>
        </div>
      );
    }
    return (
      <div>
        <Link
          href="/login"
          className="flex items-center font-medium text-foreground hover:text-accent transition-colors py-2 text-lg"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <LogIn className="mr-2 h-5 w-5" />
          {translate(navTranslations, 'login')}
        </Link>
        <Link
          href="/auth/register"
          className="flex items-center font-medium text-foreground hover:text-accent transition-colors py-2 text-lg"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <UserPlus className="mr-2 h-5 w-5" />
          {translate(navTranslations, 'register')}
        </Link>
        <Separator className="my-2"/>
        <div className="py-2 text-lg">
            <p className="font-medium text-foreground mb-1">{translate(navTranslations, 'language')}:</p>
            {languages.map((lang) => (
              <Button
                key={lang.code}
                variant="ghost"
                className={cn(
                  "w-full justify-start text-lg py-1 px-0 font-normal",
                  currentLanguage.code === lang.code && "text-accent font-semibold"
                )}
                onClick={() => handleLanguageChange(lang.code)}
              >
                {lang.label}
              </Button>
            ))}
        </div>
      </div>
    );
  };

  return (
    <nav className="bg-card shadow-md sticky top-0 z-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Logo />
          </div>

          <div className="hidden md:flex items-center space-x-6">
            {translatedNavLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="font-medium text-foreground hover:text-accent transition-colors"
              >
                {link.label}
              </Link>
            ))}
          </div>

          <div className="flex items-center space-x-2">
            <div className="hidden md:flex items-center space-x-1">
              {renderDesktopAuthControls()}
            </div>
            
            <div className="md:hidden flex items-center">
              {isLoggedIn && !isUserLoading && (
                <DropdownMenu onOpenChange={(open) => open && handleNotificationDropdownOpen()}>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="relative mr-1">
                      <Bell className="h-5 w-5" />
                      {unreadCount > 0 && (
                        <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-destructive text-destructive-foreground text-xs">
                          {unreadCount > 9 ? '9+' : unreadCount}
                        </span>
                      )}
                      <span className="sr-only">{translate(navTranslations, 'notifications')}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-80">
                    <DropdownMenuLabel className="flex justify-between items-center">
                      <span>{translate(navTranslations, 'notifications')}</span>
                      {unreadCount > 0 && (
                        <Button variant="link" size="sm" className="p-0 h-auto text-xs" onClick={handleMarkAllAsRead}>
                          {translate(navTranslations, 'markAllAsRead')}
                        </Button>
                      )}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {isLoadingNotifications ? (
                      <DropdownMenuItem disabled className="flex justify-center py-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </DropdownMenuItem>
                    ) : notifications.length === 0 ? (
                      <DropdownMenuItem disabled>{translate(navTranslations, 'noNewNotifications')}</DropdownMenuItem>
                    ) : (
                      notifications.slice(0, 5).map((notif) => (
                        <DropdownMenuItem 
                          key={notif.id} 
                          onSelect={() => { setIsMobileMenuOpen(false); handleNotificationClick(notif);}}
                          className={cn("flex items-start gap-2 py-2 px-3 cursor-pointer", !notif.isRead && "font-semibold")}
                        >
                          {!notif.isRead && <span className="mt-1 flex h-2 w-2 rounded-full bg-primary" />}
                          <div className={cn("flex-1 space-y-0.5", !notif.isRead && "pl-0", notif.isRead && "pl-4")}>
                              <p className="text-xs text-foreground/90 leading-tight line-clamp-2">{notif.message}</p>
                              <p className="text-xs text-muted-foreground">
                                  {formatDistanceToNowStrict(new Date(notif.createdAt), { addSuffix: true, locale: selectedDateLocale })}
                              </p>
                          </div>
                        </DropdownMenuItem>
                      ))
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard/notifications" className="flex justify-center cursor-pointer" onClick={() => setIsMobileMenuOpen(false)}>
                        {translate(navTranslations, 'viewAllNotifications')}
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon">
                    {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                    <span className="sr-only">Open menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[280px] bg-card p-0 flex flex-col">
                  <SheetHeader className="p-4 border-b">
                    <SheetTitle className="text-left">
                      <Logo onClick={() => setIsMobileMenuOpen(false)} />
                    </SheetTitle>
                  </SheetHeader>
                  <nav className="flex-grow flex flex-col space-y-2 p-4">
                    {translatedNavLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className="font-medium text-foreground hover:text-accent transition-colors py-2 text-lg"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {link.label}
                      </Link>
                    ))}
                  </nav>
                  <Separator />
                  <div className="p-4 space-y-3">
                    {renderMobileAuthControls()}
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
