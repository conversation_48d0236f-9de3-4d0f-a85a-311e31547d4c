
import { type NextRequest, NextResponse } from 'next/server';
import apiFetch from '@/lib/api-client'; // Import the api-client helper

const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]).{8,}$/;

const validatePasswordComplexity = (password: string): boolean => {
  return PASSWORD_REGEX.test(password);
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, newPassword } = body;

    if (!userId || !newPassword) {
      return NextResponse.json({ success: false, message: 'ID utilizator și parolă nouă sunt obligatorii.' }, { status: 400 });
    }

    if (isNaN(parseInt(userId, 10))) {
        return NextResponse.json({ success: false, message: 'ID utilizator invalid.' }, { status: 400 });
    }

    if (!validatePasswordComplexity(newPassword)) {
      return NextResponse.json({ 
        success: false, 
        message: 'Parola nu îndeplinește cerințele de complexitate: minim 8 caractere, o literă mare, o literă mică, o cifră și un caracter special.' 
      }, { status: 400 });
    }

    // Call the backend API instead of using prisma directly
    const apiResponse = await apiFetch('user/change-password-initial', {
        method: 'POST',
        body: JSON.stringify({ userId: parseInt(userId, 10), newPassword }),
        // This endpoint will be called from the client, so it relies on the proxy which adds the JWT
    });

    const responseData = await apiResponse.json();

    if (!apiResponse.ok) {
        return NextResponse.json({ success: false, message: responseData.message || 'Eroare la schimbarea parolei.' }, { status: apiResponse.status });
    }

    console.log(`[Web API ChangePasswordInitial] Parola a fost schimbată cu succes pentru utilizatorul ID: ${userId}`);
    return NextResponse.json({ success: true, message: 'Parola a fost schimbată cu succes!' });

  } catch (error) {
    console.error('[Web API ChangePasswordInitial] Eroare la schimbarea parolei:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la schimbarea parolei.';
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
