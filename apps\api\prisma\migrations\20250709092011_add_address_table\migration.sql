-- CreateTable
CREATE TABLE "Addresses" (
    "Id" SERIAL NOT NULL,
    "UserId" INTEGER NOT NULL,
    "Label" TEXT NOT NULL,
    "Street" TEXT NOT NULL,
    "City" TEXT NOT NULL,
    "Region" TEXT,
    "PostalCode" TEXT,
    "Country" TEXT NOT NULL DEFAULT 'Republica Moldova',
    "IsDefault" BOOLEAN NOT NULL DEFAULT false,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Addresses_pkey" PRIMARY KEY ("Id")
);

-- AddForeignKey
ALTER TABLE "Addresses" ADD CONSTRAINT "Addresses_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;
