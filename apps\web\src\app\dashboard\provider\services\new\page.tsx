
"use client";

import { useState, useEffect, useMemo } from 'react';
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from '@/components/ui/skeleton';
import { AlertTriangle, ListChecks, Baby, HeartHandshake, Sparkles, GraduationCap, CookingPot, ArrowRight, ArrowLeft } from "lucide-react";
import type { AdvertisedService as PrismaServiceType, ServiceCategory as PrismaServiceCategory } from '@prisma/client';

// Map slugs to their corresponding URL paths
const slugToPathMap: Record<string, string> = {
  'Nanny': 'nanny',
  'ElderCare': 'elder-care',
  'Cleaning': 'cleaning',
  'Tutoring': 'tutoring',
  'Cooking': 'cooking',
};

export default function SelectServiceToAddPage() {
    const { translate } = useLanguage();
    const { data: session, status: sessionStatus } = useSession();
    
    const [allCategories, setAllCategories] = useState<PrismaServiceCategory[]>([]);
    const [existingServices, setExistingServices] = useState<PrismaServiceType[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [apiError, setApiError] = useState<string | null>(null);

    useEffect(() => {
        if (sessionStatus !== 'authenticated' || !session?.user) {
            if (sessionStatus !== 'loading') {
                setIsLoading(false);
                setApiError("Trebuie să fii autentificat pentru a adăuga un serviciu.");
            }
            return;
        }
        const providerId = (session.user as any).id;

        const fetchInitialData = async () => {
            setIsLoading(true);
            setApiError(null);
            try {
                const [servicesRes, categoriesRes] = await Promise.all([
                    fetch(`/api/proxy/provider/services?providerId=${providerId}`),
                    fetch(`/api/proxy/service-categories`)
                ]);

                if (!servicesRes.ok || !categoriesRes.ok) {
                    throw new Error("Failed to fetch initial data for service creation.");
                }
                
                const servicesData = await servicesRes.json();
                const categoriesData = await categoriesRes.json();
                
                setExistingServices(servicesData.services || []);
                setAllCategories(categoriesData || []);

            } catch (err) {
                setApiError(err instanceof Error ? err.message : "An unknown error occurred.");
            } finally {
                setIsLoading(false);
            }
        };
        fetchInitialData();
    }, [sessionStatus, session]);

    const availableCategories = useMemo(() => {
        const existingSlugs = new Set(existingServices.map(s => s.ServiceCategorySlug));
        return allCategories.filter(cat => !existingSlugs.has(cat.Slug));
    }, [allCategories, existingServices]);

    const getIconForCategory = (slug: string) => {
        const iconProps = { className: "w-8 h-8 text-primary" };
        switch (slug) {
            case 'Nanny': return <Baby {...iconProps} />;
            case 'ElderCare': return <HeartHandshake {...iconProps} />;
            case 'Cleaning': return <Sparkles {...iconProps} />;
            case 'Tutoring': return <GraduationCap {...iconProps} />;
            case 'Cooking': return <CookingPot {...iconProps} />;
            default: return <ListChecks {...iconProps} />;
        }
    };
    
    if (isLoading) {
        return (
            <div className="space-y-4">
                <Skeleton className="h-10 w-1/3" />
                <Skeleton className="h-8 w-2/3" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                </div>
            </div>
        );
    }
    
    if (apiError) {
        return (
            <Card className="text-center">
                <CardHeader>
                    <AlertTriangle className="mx-auto h-12 w-12 text-destructive" />
                    <CardTitle className="mt-4">{translate(commonTranslations, 'toastErrorTitle')}</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground">{apiError}</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            <Button variant="outline" asChild>
                <Link href="/dashboard/provider/services">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    {translate(commonTranslations, 'backToMyServices')}
                </Link>
            </Button>
            <Card>
                <CardHeader>
                    <CardTitle>{translate(commonTranslations, 'regProvAddServicePageTitle')}</CardTitle>
                    <CardDescription>{translate(commonTranslations, 'regProvAddServicePageDescription')}</CardDescription>
                </CardHeader>
                <CardContent>
                    {availableCategories.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {availableCategories.map(category => (
                                <Button key={category.Id} asChild variant="outline" className="h-auto p-4 justify-start text-left">
                                    <Link href={`/dashboard/provider/services/${slugToPathMap[category.Slug]}/new`}>
                                        <div className="flex items-center gap-4">
                                            {getIconForCategory(category.Slug)}
                                            <div className="flex-1">
                                                <p className="font-semibold text-base">{translate(commonTranslations, category.NameKey as keyof typeof commonTranslations)}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {translate(commonTranslations, 'regProvAddServiceAction').replace('{categoryName}', translate(commonTranslations, category.NameKey as keyof typeof commonTranslations))}
                                                </p>
                                            </div>
                                            <ArrowRight className="w-5 h-5 text-muted-foreground" />
                                        </div>
                                    </Link>
                                </Button>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-8">
                            <ListChecks className="mx-auto h-12 w-12 text-muted-foreground" />
                            <p className="mt-4 text-muted-foreground">
                                {translate(commonTranslations, 'regProvAddServiceNoMoreCategories')}
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
