
"use client";

import { useState, type FormEvent } from "react";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/language-context";

const comingSoonPageTranslations = {
  pageTitle: { ro: "bonami se Lansează în Curând!", ru: "bonami скоро запускается!", en: "bonami is Launching Soon!" },
  pageDescription: { 
    ro: "Fii printre primii care află despre lansarea noastră! Înregistrează-te mai jos pentru a primi noutăți și oferte exclusive.", 
    ru: "Будьте среди первых, кто узнает о нашем запуске! Зарегистрируйтесь ниже, чтобы получать новости и эксклюзивные предложения.", 
    en: "Be among the first to know about our launch! Register below to receive news and exclusive offers." 
  },
  formTitle: { ro: "Înregistrează-ți Interesul", ru: "Зарегистрируйте свой интерес", en: "Register Your Interest" },
  fullNameLabel: { ro: "Nume Complet", ru: "Полное имя", en: "Full Name" },
  fullNamePlaceholder: { ro: "Numele și Prenumele tău", ru: "Ваше имя и фамилия", en: "Your First and Last Name" },
  emailLabel: { ro: "Adresă de Email", ru: "Адрес электронной почты", en: "Email Address" },
  emailPlaceholder: { ro: "<EMAIL>", ru: "<EMAIL>", en: "<EMAIL>" },
  userTypeLabel: { ro: "Sunt interesat ca:", ru: "Я заинтересован как:", en: "I am interested as a:" },
  userTypePlaceholder: { ro: "Selectează tipul", ru: "Выберите тип", en: "Select type" },
  userTypeClient: { ro: "Client (Caut servicii)", ru: "Клиент (Ищу услуги)", en: "Client (Looking for services)" },
  userTypeProvider: { ro: "Furnizor (Ofer servicii)", ru: "Поставщик (Предлагаю услуги)", en: "Provider (Offering services)" },
  submitButton: { ro: "Trimite Înregistrarea", ru: "Отправить регистрацию", en: "Submit Registration" },
  submittingButton: { ro: "Se trimite...", ru: "Отправка...", en: "Submitting..." },
  toastSuccessTitle: { ro: "Mulțumim!", ru: "Спасибо!", en: "Thank You!" },
  toastSuccessDescription: { ro: "Interesul tău a fost înregistrat. Te vom contacta în curând!", ru: "Ваш интерес зарегистрирован. Мы свяжемся с вами в ближайшее время!", en: "Your interest has been registered. We will contact you soon!" },
};

export default function ComingSoonPage() {
  const { translate } = useLanguage();
  const { toast } = useToast();

  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [userType, setUserType] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const userTypeOptions = [
    { value: "client", label: translate(comingSoonPageTranslations, 'userTypeClient') },
    { value: "provider", label: translate(comingSoonPageTranslations, 'userTypeProvider') },
  ];

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    console.log("Pre-registration Data:", { fullName, email, userType });
    await new Promise(resolve => setTimeout(resolve, 1000));

    toast({
      title: translate(comingSoonPageTranslations, 'toastSuccessTitle'),
      description: translate(comingSoonPageTranslations, 'toastSuccessDescription'),
    });

    // Reset form
    setFullName("");
    setEmail("");
    setUserType("");
    setIsLoading(false);
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Navbar />
      <main className="flex-grow container mx-auto py-16 px-4 flex flex-col items-center justify-center">
        <div className="text-center mb-12 max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 font-headline text-primary">
            {translate(comingSoonPageTranslations, 'pageTitle')}
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground">
            {translate(comingSoonPageTranslations, 'pageDescription')}
          </p>
        </div>

        <Card className="w-full max-w-lg shadow-2xl">
          <CardHeader>
            <CardTitle className="text-2xl font-headline text-center">{translate(comingSoonPageTranslations, 'formTitle')}</CardTitle>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="fullName">{translate(comingSoonPageTranslations, 'fullNameLabel')}</Label>
                <Input
                  id="fullName"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder={translate(comingSoonPageTranslations, 'fullNamePlaceholder')}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">{translate(comingSoonPageTranslations, 'emailLabel')}</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={translate(comingSoonPageTranslations, 'emailPlaceholder')}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="userType">{translate(comingSoonPageTranslations, 'userTypeLabel')}</Label>
                <Select value={userType} onValueChange={setUserType} required>
                  <SelectTrigger id="userType">
                    <SelectValue placeholder={translate(comingSoonPageTranslations, 'userTypePlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    {userTypeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {translate(comingSoonPageTranslations, 'submittingButton')}
                  </>
                ) : (
                  translate(comingSoonPageTranslations, 'submitButton')
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
