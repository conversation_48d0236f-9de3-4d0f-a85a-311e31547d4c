
"use client";

import { useState, useEffect, Suspense } from 'react';
import { useSearchPara<PERSON>, useRouter, usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';
import { SafeAreaView, View, ActivityIndicator, Text } from 'react-native';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs-mobile'; // Assuming a mobile-specific or adapted Tabs component
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';
import type { ServiceCategory } from '@prisma/client';

// Assuming a way to fetch categories, for now mocked
const fetchServiceCategories = async (): Promise<ServiceCategory[]> => {
  // In a real scenario, this would be an API call
  return [
    { Id: 1, Slug: 'Nanny', NameKey: 'categoryNanny', AiExpectedValue: 'Nanny', DefaultImageHint: '' },
    { Id: 2, Slug: 'ElderCare', NameKey: 'categoryElderCare', AiExpectedValue: 'Elder Care', DefaultImageHint: '' },
    { Id: 3, Slug: 'Cleaning', Name<PERSON>ey: 'categoryCleaning', AiExpectedValue: 'Cleaning', DefaultImageHint: '' },
    { Id: 4, Slug: 'Tutoring', NameKey: 'categoryTutoring', AiExpectedValue: 'Tutoring', DefaultImageHint: '' },
    { Id: 5, Slug: 'Cooking', NameKey: 'categoryCooking', AiExpectedValue: 'Cooking', DefaultImageHint: '' },
  ];
};

const SearchPageSkeleton = () => (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
    </View>
);

// Lazy load service-specific search components
const NannySearchPage = dynamic(() => import('./search/service-pages/nanny/nanny-search').then(mod => mod.NannySearchPage), {
  loading: () => <SearchPageSkeleton />,
});
const ElderCareSearchPage = dynamic(() => import('./search/service-pages/elder-care/elder-care-search').then(mod => mod.ElderCareSearchPage), {
  loading: () => <SearchPageSkeleton />,
});
const CleaningSearchPage = dynamic(() => import('./search/service-pages/cleaning/cleaning-search').then(mod => mod.CleaningSearchPage), {
  loading: () => <SearchPageSkeleton />,
});
const TutoringSearchPage = dynamic(() => import('./search/service-pages/tutoring/tutoring-search').then(mod => mod.TutoringSearchPage), {
    loading: () => <SearchPageSkeleton />,
});
const CookingSearchPage = dynamic(() => import('./search/service-pages/cooking/cooking-search').then(mod => mod.CookingSearchPage), {
    loading: () => <SearchPageSkeleton />,
});

const serviceComponentMap: { [key: string]: React.ComponentType } = {
  Nanny: NannySearchPage,
  ElderCare: ElderCareSearchPage,
  Cleaning: CleaningSearchPage,
  Tutoring: TutoringSearchPage,
  Cooking: CookingSearchPage,
};


function SearchPageComponent() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { translate } = useLanguage();
  
  const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const activeTab = searchParams.get('service') || (serviceCategories.length > 0 ? serviceCategories[0].Slug : '');

  useEffect(() => {
    async function getCategories() {
      setIsLoading(true);
      try {
        const data = await fetchServiceCategories();
        setServiceCategories(data || []);
        
        const currentService = searchParams.get('service');
        const isValidService = data.some((cat: ServiceCategory) => cat.Slug === currentService);
        if ((!currentService || !isValidService) && data.length > 0) {
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.set('service', data[0].Slug);
            router.replace(`${pathname}?${newParams.toString()}`);
        }
      } catch (error) {
        console.error("Error fetching service categories:", error);
      } finally {
        setIsLoading(false);
      }
    }
    getCategories();
  }, []);

  const handleTabChange = (value: string) => {
    const newParams = new URLSearchParams();
    newParams.set('service', value);
    router.replace(`${pathname}?${newParams.toString()}`);
  };
  
  const ActiveServiceComponent = serviceComponentMap[activeTab];

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#F0F2F5' }}>
        {isLoading ? (
            <SearchPageSkeleton />
        ) : (
            <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList>
                    {serviceCategories.map((category) => (
                        <TabsTrigger key={category.Slug} value={category.Slug} label={translate(commonTranslations, category.NameKey as keyof typeof commonTranslations)}>
                        </TabsTrigger>
                    ))}
                </TabsList>
                <View style={{ flex: 1, marginTop: 10 }}>
                    {ActiveServiceComponent ? <ActiveServiceComponent /> : <SearchPageSkeleton />}
                </View>
            </Tabs>
        )}
    </SafeAreaView>
  );
}

export default function SearchPage() {
    return (
        <Suspense fallback={<View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><ActivityIndicator size="large" /></View>}>
            <SearchPageComponent />
        </Suspense>
    )
}
