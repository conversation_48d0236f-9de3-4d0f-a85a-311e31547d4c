
export const clientDashboardTranslations = {
  welcomeMessage: { ro: "Bun venit, {name}!", ru: "Добро пожаловать, {name}!", en: "Welcome, {name}!" },
  dashboardDescription: { ro: "Acesta este panoul tău personal. Aici poți gestiona informațiile și activitatea ta pe bonami.", ru: "Это ваша личная панель. Здесь вы можете управлять своей информацией и активностью на bonami.", en: "This is your personal panel. Here you can manage your information and activity on bonami." },
  memberSince: { ro: "Membru din: {date}", ru: "Участник с: {date}", en: "Member since: {date}" },
  
  adminDashboardCardTitle: { ro: "<PERSON><PERSON><PERSON> Tă<PERSON> de Administrare", ru: "Ваша Панель Администратора", en: "Your Administration Panel" },
  adminDashboardCardDescription: { ro: "Gestionează utilizatorii, serviciile și setările platformei.", ru: "Управляйте пользователями, услугами и настройками платформы.", en: "Manage users, services, and platform settings." },
  goToAdminPanelButton: { ro: "Mergi la Panoul de Administrare", ru: "Перейти в Панель Администратора", en: "Go to Admin Panel" },

  clientPanelTitle: { ro: "Panoul Meu de Client", ru: "Моя клиентская панель", en: "My Client Panel" },
  clientPanelDescription: { ro: "Gestionează serviciile pe care le cauți și rezervările tale.", ru: "Управляйте услугами, которые вы ищете, и вашими бронированиями.", en: "Manage the services you are looking for and your bookings." },
  searchServicesCardTitle: { ro: "Caută Servicii", ru: "Поиск услуг", en: "Search Services" },
  searchServicesCardDesc: { ro: "Găsește bone, îngrijitori pentru bătrâni, servicii de curățenie sau meditații.", ru: "Найдите нянь, сиделок, услуги по уборке или репетиторов.", en: "Find nannies, elderly caregivers, cleaning services, or tutors." },
  goToSearchButton: { ro: "Mergi la Căutare", ru: "Перейти к поиску", en: "Go to Search" },
  viewBookingsCardTitle: { ro: "Vezi Rezervările", ru: "Просмотр бронирований", en: "View Bookings" },
  viewBookingsCardDesc: { ro: "Ai {count} rezervări recente. Verifică detaliile și statusul lor.", ru: "У вас {count} недавних бронирований. Проверьте их детали и статус.", en: "You have {count} recent bookings. Check their details and status." },
  myBookingsButton: { ro: "Rezervările Mele", ru: "Мои бронирования", en: "My Bookings" },
  addReviewCardTitle: { ro: "Adaugă Recenzie", ru: "Добавить отзыв", en: "Add Review" },
  addReviewCardDesc: { ro: "Ai {count} servicii finalizate pentru care poți lăsa o recenzie.", ru: "У вас {count} завершенных услуг, для которых вы можете оставить отзыв.", en: "You have {count} completed services for which you can leave a review." },
  writeReviewsButton: { ro: "Scrie Recenzii", ru: "Написать отзывы", en: "Write Reviews" },
  
  offerServicesCardTitle: { ro: "Devino Furnizor", ru: "Стать поставщиком", en: "Become a Provider" },
  offerServicesCardDesc: { ro: "Alătură-te comunității noastre de profesioniști și oferă serviciile tale.", ru: "Присоединяйтесь к нашему сообществу профессионалов и предлагайте свои услуги.", en: "Join our community of professionals and offer your services." },
  becomeProviderButton: { ro: "Aplică acum", ru: "Подать заявку", en: "Apply Now" },
  
  loadingUserData: { ro: "Se încarcă datele utilizatorului...", ru: "Загрузка данных пользователя...", en: "Loading user data..." },
  errorFetchingStats: { ro: "Eroare la preluarea statisticilor.", ru: "Ошибка при загрузке статистики.", en: "Error fetching stats." },
  
  providerApplicationStatusTitle: { ro: "Status Cerere Furnizor", ru: "Статус заявки поставщика", en: "Provider Application Status" },
  requestStatusPendingTitle: { ro: "Cererea ta este în curs de procesare", ru: "Ваша заявка находится на рассмотрении", en: "Your application is being processed" },
  requestStatusPendingDescription: { ro: "Vei fi notificat când statusul se schimbă.", ru: "Вы будете уведомлены об изменении статуса.", en: "You will be notified when the status changes." },
  requestStatusRejectedTitle: { ro: "Cererea ta a fost respinsă", ru: "Ваша заявка была отклонена", en: "Your application was rejected" },
  requestStatusRejectedDescription: { ro: "Motiv: {adminNotes}. Poți aplica din nou dacă dorești.", ru: "Причина: {adminNotes}. При желании вы можете подать новую заявку.", en: "Reason: {adminNotes}. You can apply again if you wish." },
  noAdminNotes: { ro: "Niciun motiv specificat de administrator.", ru: "Администратор не указал причину.", en: "No reason specified by administrator." },

  // Request details and status management
  viewRequestDetailsButton: { ro: "Vezi detaliile cererii", ru: "Посмотреть детали заявки", en: "View Request Details" },
  checkingRequestStatus: { ro: "Se verifică statusul cererii...", ru: "Проверка статуса заявки...", en: "Checking request status..." },
  requestStatusError: { ro: "Eroare la verificarea statusului cererii", ru: "Ошибка при проверке статуса заявки", en: "Error checking request status" },
  requestStatusErrorDesc: { ro: "Nu s-a putut verifica statusul cererii de prestator.", ru: "Не удалось проверить статус заявки поставщика.", en: "Could not check provider request status." },
  requestStatusApprovedTitle: { ro: "Cererea ta a fost aprobată", ru: "Ваша заявка была одобрена", en: "Your application was approved" },
  requestStatusApprovedDescription: { ro: "Felicitări! Acum ești un furnizor activ pe platformă.", ru: "Поздравляем! Теперь вы активный поставщик на платформе.", en: "Congratulations! You are now an active provider on the platform." },
  goToProviderDashboard: { ro: "Mergi la panoul furnizorului", ru: "Перейти к панели поставщика", en: "Go to Provider Dashboard" },
  reapplyAsProvider: { ro: "Aplică din nou", ru: "Подать заявку снова", en: "Apply Again" },
  providerStatusActiveTitle: { ro: "Ești un furnizor activ", ru: "Вы активный поставщик", en: "You are an active provider" },
  providerStatusActiveDescription: { ro: "Gestionează serviciile tale și vezi rezervările clienților.", ru: "Управляйте своими услугами и просматривайте бронирования клиентов.", en: "Manage your services and view client bookings." },
};
