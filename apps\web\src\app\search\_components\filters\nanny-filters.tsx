"use client";

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useLanguage } from '@/contexts/language-context';
import { BooleanFilterGroup, FilterBadges, getActiveFilters } from './filter-components';

const nannyFilterTranslations = {
  agePreferences: { ro: "Preferințe Vârstă", ru: "Возрастные предпочтения", en: "Age Preferences" },
  age0to2: { ro: "0-2 ani", ru: "0-2 года", en: "0-2 years" },
  age3to6: { ro: "3-6 ani", ru: "3-6 лет", en: "3-6 years" },
  age7plus: { ro: "7+ ani", ru: "7+ лет", en: "7+ years" },

  availabilityType: { ro: "Tip Disponibilitate", ru: "Тип доступности", en: "Availability Type" },
  fullTime: { ro: "Program complet", ru: "Полный рабочий день", en: "Full-time" },
  partTime: { ro: "Program parțial", ru: "Неполный рабочий день", en: "Part-time" },

  specialSkills: { ro: "Abilități Speciale", ru: "Специальные навыки", en: "Special Skills" },
  firstAid: { ro: "Primul ajutor", ru: "Первая помощь", en: "First Aid" },
  schoolPickup: { ro: "Ridicare de la școală", ru: "Забрать из школы", en: "School Pickup" },

  activities: { ro: "Activități", ru: "Деятельность", en: "Activities" },
  walks: { ro: "Plimbări", ru: "Прогулки", en: "Walks" },
  games: { ro: "Jocuri", ru: "Игры", en: "Games" },
  feeding: { ro: "Hrănire", ru: "Кормление", en: "Feeding" },
  sleep: { ro: "Supraveghere somn", ru: "Присмотр за сном", en: "Sleep Supervision" },
};

interface NannyFiltersState {
  // Age preferences
  PreferredAge_0_2: boolean;
  PreferredAge_3_6: boolean;
  PreferredAge_7_plus: boolean;

  // Availability type
  AvailabilityFullTime: boolean;
  AvailabilityPartTime: boolean;

  // Special skills
  FirstAid: boolean;
  SchoolPickup: boolean;

  // Activities
  ActivityWalks: boolean;
  ActivityGames: boolean;
  ActivityFeeding: boolean;
  ActivitySleep: boolean;
}

export function NannyFilters() {
  const { translate } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<NannyFiltersState>({
    PreferredAge_0_2: searchParams.get('PreferredAge_0_2') === 'true',
    PreferredAge_3_6: searchParams.get('PreferredAge_3_6') === 'true',
    PreferredAge_7_plus: searchParams.get('PreferredAge_7_plus') === 'true',
    AvailabilityFullTime: searchParams.get('AvailabilityFullTime') === 'true',
    AvailabilityPartTime: searchParams.get('AvailabilityPartTime') === 'true',
    FirstAid: searchParams.get('FirstAid') === 'true',
    SchoolPickup: searchParams.get('SchoolPickup') === 'true',
    ActivityWalks: searchParams.get('ActivityWalks') === 'true',
    ActivityGames: searchParams.get('ActivityGames') === 'true',
    ActivityFeeding: searchParams.get('ActivityFeeding') === 'true',
    ActivitySleep: searchParams.get('ActivitySleep') === 'true',
  });

  // Update URL when filters change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams.toString());

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, 'true');
      } else {
        newParams.delete(key);
      }
    });

    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  }, [filters, router, pathname, searchParams]);

  const handleFilterChange = (key: string, checked: boolean) => {
    setFilters(prev => ({ ...prev, [key]: checked }));
  };

  const handleClearAll = () => {
    setFilters({
      PreferredAge_0_2: false,
      PreferredAge_3_6: false,
      PreferredAge_7_plus: false,
      AvailabilityFullTime: false,
      AvailabilityPartTime: false,
      FirstAid: false,
      SchoolPickup: false,
      ActivityWalks: false,
      ActivityGames: false,
      ActivityFeeding: false,
      ActivitySleep: false,
    });
  };

  const handleRemoveFilter = (key: string) => {
    setFilters(prev => ({ ...prev, [key]: false }));
  };

  // Create filter labels
  const filterLabels = {
    PreferredAge_0_2: translate(nannyFilterTranslations, 'age0to2'),
    PreferredAge_3_6: translate(nannyFilterTranslations, 'age3to6'),
    PreferredAge_7_plus: translate(nannyFilterTranslations, 'age7plus'),
    AvailabilityFullTime: translate(nannyFilterTranslations, 'fullTime'),
    AvailabilityPartTime: translate(nannyFilterTranslations, 'partTime'),
    FirstAid: translate(nannyFilterTranslations, 'firstAid'),
    SchoolPickup: translate(nannyFilterTranslations, 'schoolPickup'),
    ActivityWalks: translate(nannyFilterTranslations, 'walks'),
    ActivityGames: translate(nannyFilterTranslations, 'games'),
    ActivityFeeding: translate(nannyFilterTranslations, 'feeding'),
    ActivitySleep: translate(nannyFilterTranslations, 'sleep'),
  };

  // Get active filters for badges
  const activeFilters = getActiveFilters(filters, filterLabels, 'nanny');

  return (
    <div className="space-y-4">
      <FilterBadges
        activeFilters={activeFilters}
        onRemove={handleRemoveFilter}
        onClearAll={handleClearAll}
      />

      <BooleanFilterGroup
        title={translate(nannyFilterTranslations, 'agePreferences')}
        options={[
          { key: 'PreferredAge_0_2', label: filterLabels.PreferredAge_0_2, checked: filters.PreferredAge_0_2 },
          { key: 'PreferredAge_3_6', label: filterLabels.PreferredAge_3_6, checked: filters.PreferredAge_3_6 },
          { key: 'PreferredAge_7_plus', label: filterLabels.PreferredAge_7_plus, checked: filters.PreferredAge_7_plus },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(nannyFilterTranslations, 'availabilityType')}
        options={[
          { key: 'AvailabilityFullTime', label: filterLabels.AvailabilityFullTime, checked: filters.AvailabilityFullTime },
          { key: 'AvailabilityPartTime', label: filterLabels.AvailabilityPartTime, checked: filters.AvailabilityPartTime },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(nannyFilterTranslations, 'specialSkills')}
        options={[
          { key: 'FirstAid', label: filterLabels.FirstAid, checked: filters.FirstAid },
          { key: 'SchoolPickup', label: filterLabels.SchoolPickup, checked: filters.SchoolPickup },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(nannyFilterTranslations, 'activities')}
        options={[
          { key: 'ActivityWalks', label: filterLabels.ActivityWalks, checked: filters.ActivityWalks },
          { key: 'ActivityGames', label: filterLabels.ActivityGames, checked: filters.ActivityGames },
          { key: 'ActivityFeeding', label: filterLabels.ActivityFeeding, checked: filters.ActivityFeeding },
          { key: 'ActivitySleep', label: filterLabels.ActivitySleep, checked: filters.ActivitySleep },
        ]}
        onChange={handleFilterChange}
      />
    </div>
  );
}
