"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, Activity, UserPlus, Calendar, Briefcase, UserCheck, ChevronLeft, ChevronRight } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const activityTranslations = {
  recentActivity: { ro: "Activitate recentă", ru: "Недавняя активность", en: "Recent Activity" },
  systemActivity: { ro: "Activitatea sistemului", ru: "Активность системы", en: "System Activity" },
  loadingActivity: { ro: "Se încarcă activitatea...", ru: "Загрузка активности...", en: "Loading activity..." },
  errorLoadingActivity: { ro: "<PERSON><PERSON>re la încărcarea activității", ru: "Ошибка загрузки активности", en: "Error loading activity" },
  noActivityFound: { ro: "Nu s-a găsit activitate", ru: "Активность не найдена", en: "No activity found" },
  userRegistration: { ro: "Înregistrare utilizator", ru: "Регистрация пользователя", en: "User Registration" },
  bookingCreated: { ro: "Rezervare nouă", ru: "Новое бронирование", en: "New Booking" },
  serviceCreated: { ro: "Serviciu nou", ru: "Новая услуга", en: "New Service" },
  providerRequest: { ro: "Cerere prestator", ru: "Запрос поставщика", en: "Provider Request" },
  ago: { ro: "în urmă", ru: "назад", en: "ago" },
  minutes: { ro: "minute", ru: "минут", en: "minutes" },
  hours: { ro: "ore", ru: "часов", en: "hours" },
  days: { ro: "zile", ru: "дней", en: "days" },
  showingResults: { ro: "Se afișează", ru: "Показано", en: "Showing" },
  of: { ro: "din", ru: "из", en: "of" },
  activities: { ro: "activități", ru: "активностей", en: "activities" },
  previous: { ro: "Anterior", ru: "Предыдущий", en: "Previous" },
  next: { ro: "Următorul", ru: "Следующий", en: "Next" },
};

interface ActivityItem {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  metadata: any;
}

interface RecentActivityFeedProps {
  refreshTrigger: Date;
}

export function RecentActivityFeed({ refreshTrigger }: RecentActivityFeedProps) {
  const { translate } = useLanguage();
  const [data, setData] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Calculate pagination
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = data.slice(startIndex, endIndex);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      setCurrentPage(1); // Reset to first page on refresh

      try {
        const response = await fetch('/api/proxy/admin/analytics/recent-activity?limit=50');
        if (!response.ok) {
          let errorMessage = 'Failed to fetch recent activity data';
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch {
            // If JSON parsing fails, use default message
          }
          throw new Error(errorMessage);
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error fetching recent activity data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registration':
        return <UserPlus className="w-4 h-4 text-blue-500" />;
      case 'booking_created':
        return <Calendar className="w-4 h-4 text-green-500" />;
      case 'service_created':
        return <Briefcase className="w-4 h-4 text-purple-500" />;
      case 'provider_request':
        return <UserCheck className="w-4 h-4 text-orange-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getActivityBadgeVariant = (type: string) => {
    switch (type) {
      case 'user_registration':
        return 'default';
      case 'booking_created':
        return 'secondary';
      case 'service_created':
        return 'outline';
      case 'provider_request':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${translate(activityTranslations, 'minutes')} ${translate(activityTranslations, 'ago')}`;
    } else if (diffInMinutes < 1440) { // 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ${translate(activityTranslations, 'hours')} ${translate(activityTranslations, 'ago')}`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} ${translate(activityTranslations, 'days')} ${translate(activityTranslations, 'ago')}`;
    }
  };

  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'user_registration':
        return translate(activityTranslations, 'userRegistration');
      case 'booking_created':
        return translate(activityTranslations, 'bookingCreated');
      case 'service_created':
        return translate(activityTranslations, 'serviceCreated');
      case 'provider_request':
        return translate(activityTranslations, 'providerRequest');
      default:
        return type;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            {translate(activityTranslations, 'recentActivity')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-5 h-5 animate-spin" />
              {translate(activityTranslations, 'loadingActivity')}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            {translate(activityTranslations, 'recentActivity')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(activityTranslations, 'errorLoadingActivity')}</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            {translate(activityTranslations, 'recentActivity')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(activityTranslations, 'noActivityFound')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="w-5 h-5" />
          {translate(activityTranslations, 'recentActivity')}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {translate(activityTranslations, 'systemActivity')}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {currentData.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
              <div className="flex-shrink-0 mt-0.5">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                  <Badge variant={getActivityBadgeVariant(activity.type)} className="text-xs w-fit">
                    {getActivityTypeLabel(activity.type)}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {formatTimeAgo(activity.timestamp)}
                  </span>
                </div>
                <p className="text-sm font-medium text-foreground line-clamp-1">
                  {activity.title}
                </p>
                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                  {activity.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between pt-4 mt-4 border-t">
            <div className="text-sm text-muted-foreground">
              {translate(activityTranslations, 'showingResults')} {startIndex + 1}-{Math.min(endIndex, data.length)} {translate(activityTranslations, 'of')} {data.length} {translate(activityTranslations, 'activities')}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="flex items-center gap-1"
              >
                <ChevronLeft className="w-4 h-4" />
                <span className="hidden sm:inline">{translate(activityTranslations, 'previous')}</span>
              </Button>
              <span className="text-sm font-medium">
                {currentPage} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1"
              >
                <span className="hidden sm:inline">{translate(activityTranslations, 'next')}</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
