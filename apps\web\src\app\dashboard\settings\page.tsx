
"use client";

import { useState, useRef, type ChangeEvent, useEffect, type FormEvent, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { useSession } from 'next-auth/react';
import type { ExtendedNextAuthUser } from '@repo/auth';
import { AVAILABLE_LANGUAGES } from '@/types/profile-setup';
import { getAvatarUrl } from '@/lib/avatar-utils';

const settingsPageTranslations = {
  profileSettingsTitle: { ro: "Setări Profil", ru: "Настройки профиля", en: "Profile Settings" },
  profileSettingsDescription: { ro: "Actualizează informațiile tale personale și fotografia de profil.", ru: "Обновите вашу личную информацию и фотографию профиля.", en: "Update your personal information and profile picture." },
  changePhotoButton: { ro: "Schimbă Fotografia", ru: "Изменить фотографию", en: "Change Photo" },
  fullNameLabel: { ro: "Nume Complet", ru: "Полное имя", en: "Full Name" },
  emailAddressLabel: { ro: "Adresă Email", ru: "Адрес электронной почты", en: "Email Address" },
  phoneNumberLabel: { ro: "Număr Telefon", ru: "Номер телефона", en: "Phone Number" },
  phonePlaceholder: { ro: "Ex: 069123456 sau +37369123456", ru: "Пример: 069123456 или +37369123456", en: "Ex: 069123456 or +37369123456"},
  shortDescriptionLabel: { ro: "Scurtă Descriere (Bio)", ru: "Краткое описание (Bio)", en: "Short Description (Bio)" },
  bioPlaceholder: { ro: "Spune-ne ceva despre tine...", ru: "Расскажите что-нибудь о себе...", en: "Tell us something about yourself..." },
  spokenLanguagesTitle: { ro: "Limbi Vorbite", ru: "Разговорные языки", en: "Spoken Languages" },
  updateProfileButton: { ro: "Actualizează Profilul", ru: "Обновить профиль", en: "Update Profile" },
  updatingProfileButton: { ro: "Se actualizează...", ru: "Обновление...", en: "Updating..." },
  changePasswordTitle: { ro: "Schimbă Parola", ru: "Изменить пароль", en: "Change Password" },
  changePasswordDescription: { ro: "Alege o parolă nouă și puternică pentru contul tău.", ru: "Выберите новый надежный пароль для вашей учетной записи.", en: "Choose a new, strong password for your account." },
  newPasswordLabel: { ro: "Parolă Nouă", ru: "Новый пароль", en: "New Password" },
  confirmNewPasswordLabel: { ro: "Confirmă Parola Nouă", ru: "Подтвердите новый пароль", en: "Confirm New Password" },
  changePasswordButton: { ro: "Schimbă Parola", ru: "Изменить пароль", en: "Change Password" },
  changingPasswordButton: { ro: "Se schimbă parola...", ru: "Изменение пароля...", en: "Changing password..." },
  toastSuccessTitle: { ro: "Succes!", ru: "Успех!", en: "Success!" },
  toastProfileUpdated: { ro: "Profilul tău a fost actualizat.", ru: "Ваш профиль обновлен.", en: "Your profile has been updated." },
  toastPasswordChanged: { ro: "Parola ta a fost schimbată.", ru: "Ваш пароль изменен.", en: "Your password has been changed." },
  toastPhotoChanged: { ro: "Fotografia de profil a fost schimbată (simulare).", ru: "Фотография профиля изменена (симуляция).", en: "Profile photo changed (simulated)." },
  toastErrorTitle: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  toastPasswordsMismatch: { ro: "Parolele nu se potrivesc.", ru: "Пароли не совпадают.", en: "Passwords do not match." },
  toastPasswordTooShort: { ro: "Parola trebuie să conțină cel puțin 6 caractere.", ru: "Пароль должен содержать не менее 6 символов.", en: "Password must be at least 6 characters long." },
};

// Use the unified language configuration from profile-setup

export default function SettingsPage() {
  const { translate } = useLanguage();
  const { data: session, status: sessionStatus, update: updateSession } = useSession();
  const user = session?.user as ExtendedNextAuthUser | undefined;

  const [formData, setFormData] = useState<Partial<ExtendedNextAuthUser>>({});
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [isLoadingPassword, setIsLoadingPassword] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Function to fetch fresh user data from the database
  const fetchUserData = useCallback(async () => {
    if (!user?.id) return;

    setIsLoadingData(true);
    try {
      const response = await fetch('/api/user/profile');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.user) {
          const userData = data.user;
          setFormData({
            name: userData.fullName || '',
            email: userData.email || '',
            phone: userData.phone || '',
            bio: userData.bio || '',
            image: userData.avatarUrl || userData.image || `https://placehold.co/100x100.png?text=${(userData.fullName || 'U').substring(0,2).toUpperCase()}`,
            SpokenLanguages: userData.SpokenLanguages || [],
            provider: userData.provider,
          });
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch user data');
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      toast({
        variant: "destructive",
        title: translate(settingsPageTranslations, 'toastErrorTitle'),
        description: error instanceof Error ? error.message : "Eroare la încărcarea datelor profilului.",
      });
    } finally {
      setIsLoadingData(false);
    }
  }, [user?.id, toast, translate]);

  // Load fresh data when component mounts or user changes
  useEffect(() => {
    if (user?.id) {
      fetchUserData();
    }
  }, [user?.id, fetchUserData]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSpokenLanguageChange = (langCode: string, checked: boolean) => {
    setFormData(prev => {
      const currentLanguages = prev.SpokenLanguages || [];
      if (checked) {
        return { ...prev, SpokenLanguages: [...currentLanguages, langCode] };
      } else {
        return { ...prev, SpokenLanguages: currentLanguages.filter(lang => lang !== langCode) };
      }
    });
  };

  const handleAvatarChange = async (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];

      try {
        const formData = new FormData();
        formData.append('photo', file);

        const response = await fetch('/api/user/profile/photo', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to upload photo');
        }

        const result = await response.json();

        // Update the form data with the new avatar URL
        setFormData(prev => ({...prev, image: result.avatarUrl}));

        // Update the session to reflect the new avatar
        await updateSession({ image: result.avatarUrl });

        // Refresh the user data to get the latest information
        await fetchUserData();

        toast({
          title: translate(settingsPageTranslations, 'toastSuccessTitle'),
          description: translate(settingsPageTranslations, 'toastPhotoChanged'),
        });
      } catch (error) {
        console.error('Error uploading photo:', error);
        toast({
          variant: "destructive",
          title: translate(settingsPageTranslations, 'toastErrorTitle'),
          description: error instanceof Error ? error.message : 'Eroare la încărcarea fotografiei.',
        });
      }
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleProfileUpdate = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoadingProfile(true);

    try {
      // Prepare the data for the API call
      const updateData = {
        phone: formData.phone || '',
        bio: formData.bio || '',
        spokenLanguages: formData.SpokenLanguages || [],
      };

      const response = await fetch('/api/user/profile/update', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update profile');
      }

      const result = await response.json();

      // Update the session with the new data
      await updateSession({
        phone: result.phone,
        bio: result.bio,
        SpokenLanguages: result.spokenLanguages,
      });

      // Refresh the form data to reflect the latest changes
      await fetchUserData();

      toast({
        title: translate(settingsPageTranslations, 'toastSuccessTitle'),
        description: translate(settingsPageTranslations, 'toastProfileUpdated'),
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        variant: "destructive",
        title: translate(settingsPageTranslations, 'toastErrorTitle'),
        description: error instanceof Error ? error.message : 'Eroare la actualizarea profilului.',
      });
    } finally {
      setIsLoadingProfile(false);
    }
  };

  const handlePasswordChange = async (e: FormEvent) => {
    e.preventDefault();
    if (newPassword !== confirmPassword) {
      toast({ variant: "destructive", title: translate(settingsPageTranslations, 'toastErrorTitle'), description: translate(settingsPageTranslations, 'toastPasswordsMismatch') });
      return;
    }
    if (newPassword.length < 6) { 
        toast({ variant: "destructive", title: translate(settingsPageTranslations, 'toastErrorTitle'), description: translate(settingsPageTranslations, 'toastPasswordTooShort') });
        return;
    }
    setIsLoadingPassword(true);
    // TODO: Implement API call to /api/auth/change-password
    console.log("Password change requested (mock)");
    await new Promise(resolve => setTimeout(resolve, 1500));
    toast({ title: translate(settingsPageTranslations, 'toastSuccessTitle'), description: translate(settingsPageTranslations, 'toastPasswordChanged') });
    setNewPassword('');
    setConfirmPassword('');
    setIsLoadingPassword(false);
  };
  
  if (sessionStatus === 'loading' || isLoadingData) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <p className="ml-3">Se încarcă...</p>
      </div>
    );
  }

  const showChangePasswordCard = formData.provider === 'credentials';

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-headline">{translate(settingsPageTranslations, 'profileSettingsTitle')}</CardTitle>
          <CardDescription>{translate(settingsPageTranslations, 'profileSettingsDescription')}</CardDescription>
        </CardHeader>
        <form onSubmit={handleProfileUpdate}>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-4">
              <Avatar className="w-20 h-20">
                <AvatarImage src={getAvatarUrl(formData.image)} alt={formData.name || "User Avatar"} data-ai-hint="person avatar" />
                <AvatarFallback>{(formData.name || 'U').substring(0, 2).toUpperCase()}</AvatarFallback>
              </Avatar>
              <Button type="button" variant="outline" onClick={triggerFileInput}>
                {translate(settingsPageTranslations, 'changePhotoButton')}
              </Button>
              <input type="file" ref={fileInputRef} onChange={handleAvatarChange} accept="image/*" className="hidden" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">{translate(settingsPageTranslations, 'fullNameLabel')}</Label>
                <Input id="name" name="name" value={formData.name || ''} onChange={handleInputChange} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">{translate(settingsPageTranslations, 'emailAddressLabel')}</Label>
                <Input id="email" name="email" type="email" value={formData.email || ''} disabled />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">{translate(settingsPageTranslations, 'phoneNumberLabel')}</Label>
              <Input id="phone" name="phone" type="tel" value={formData.phone || ''} onChange={handleInputChange} placeholder={translate(settingsPageTranslations, 'phonePlaceholder')} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bio">{translate(settingsPageTranslations, 'shortDescriptionLabel')}</Label>
              <Textarea id="bio" name="bio" value={formData.bio || ''} onChange={handleInputChange} placeholder={translate(settingsPageTranslations, 'bioPlaceholder')} rows={3}/>
            </div>
             <div className="space-y-3">
              <Label className="text-base font-medium">{translate(settingsPageTranslations, 'spokenLanguagesTitle')}</Label>
              <div className="space-y-2 rounded-md border p-4 grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3">
                {AVAILABLE_LANGUAGES.map(lang => (
                  <div key={lang.code} className="flex items-center space-x-2">
                    <Checkbox
                      id={`lang-${lang.code}`}
                      checked={(formData.SpokenLanguages || []).includes(lang.code)}
                      onCheckedChange={(checked) => handleSpokenLanguageChange(lang.code, !!checked)}
                    />
                    <Label htmlFor={`lang-${lang.code}`} className="font-normal text-sm cursor-pointer">
                      {lang.nativeLabel}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isLoadingProfile}>
              {isLoadingProfile && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isLoadingProfile ? translate(settingsPageTranslations, 'updatingProfileButton') : translate(settingsPageTranslations, 'updateProfileButton')}
            </Button>
          </CardFooter>
        </form>
      </Card>

      {showChangePasswordCard && (
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-headline">{translate(settingsPageTranslations, 'changePasswordTitle')}</CardTitle>
            <CardDescription>{translate(settingsPageTranslations, 'changePasswordDescription')}</CardDescription>
          </CardHeader>
          <form onSubmit={handlePasswordChange}>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="newPassword">{translate(settingsPageTranslations, 'newPasswordLabel')}</Label>
                <Input id="newPassword" type="password" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">{translate(settingsPageTranslations, 'confirmNewPasswordLabel')}</Label>
                <Input id="confirmPassword" type="password" value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} />
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" disabled={isLoadingPassword}>
                {isLoadingPassword && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isLoadingPassword ? translate(settingsPageTranslations, 'changingPasswordButton') : translate(settingsPageTranslations, 'changePasswordButton')}
              </Button>
            </CardFooter>
          </form>
        </Card>
      )}
    </div>
  );
}
