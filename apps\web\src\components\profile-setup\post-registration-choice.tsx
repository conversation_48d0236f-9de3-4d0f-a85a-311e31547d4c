"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, User, Phone, MessageSquare, Globe, ArrowRight, SkipForward } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';

interface PostRegistrationChoiceProps {
  userName?: string;
  onCompleteProfile: () => void;
  onSkipForNow: () => void;
}

const postRegistrationTranslations = {
  en: {
    welcomeTitle: "Welcome to Bonami!",
    welcomeSubtitle: "Your account has been created successfully",
    completeProfileTitle: "Complete Your Profile",
    completeProfileDescription: "Help others get to know you better and unlock all features",
    skipTitle: "Skip for Now",
    skipDescription: "You can complete your profile later from your dashboard",
    whyCompleteTitle: "Why complete your profile?",
    requiredForBookings: "Required for making reservations",
    requiredForProvider: "Required to become a service provider",
    betterMatching: "Better matching with service providers",
    trustAndSafety: "Builds trust and safety in the community",
    completeProfileButton: "Complete Profile",
    skipForNowButton: "Skip for Now",
    recommendedBadge: "Recommended",
    profileSteps: "What we'll ask for:",
    stepPhoto: "Profile photo (optional)",
    stepPhone: "Phone number",
    stepBio: "Short description (optional)",
    stepLanguages: "Spoken languages",
    estimatedTime: "Takes about 3 minutes"
  },
  ro: {
    welcomeTitle: "Bun venit la Bonami!",
    welcomeSubtitle: "Contul tău a fost creat cu succes",
    completeProfileTitle: "Completează-ți Profilul",
    completeProfileDescription: "Ajută-i pe alții să te cunoască mai bine și deblochează toate funcțiile",
    skipTitle: "Sari pentru Acum",
    skipDescription: "Poți completa profilul mai târziu din panoul tău de control",
    whyCompleteTitle: "De ce să îți completezi profilul?",
    requiredForBookings: "Necesar pentru efectuarea rezervărilor",
    requiredForProvider: "Necesar pentru a deveni furnizor de servicii",
    betterMatching: "Potrivire mai bună cu furnizorii de servicii",
    trustAndSafety: "Construiește încredere și siguranță în comunitate",
    completeProfileButton: "Completează Profilul",
    skipForNowButton: "Sari pentru Acum",
    recommendedBadge: "Recomandat",
    profileSteps: "Ce îți vom cere:",
    stepPhoto: "Fotografie de profil (opțional)",
    stepPhone: "Număr de telefon",
    stepBio: "Scurtă descriere (opțional)",
    stepLanguages: "Limbile vorbite",
    estimatedTime: "Durează aproximativ 3 minute"
  },
  ru: {
    welcomeTitle: "Добро пожаловать в Bonami!",
    welcomeSubtitle: "Ваш аккаунт был успешно создан",
    completeProfileTitle: "Заполните Ваш Профиль",
    completeProfileDescription: "Помогите другим узнать вас лучше и разблокируйте все функции",
    skipTitle: "Пропустить Пока",
    skipDescription: "Вы можете заполнить профиль позже из вашей панели управления",
    whyCompleteTitle: "Зачем заполнять профиль?",
    requiredForBookings: "Необходимо для бронирования",
    requiredForProvider: "Необходимо чтобы стать поставщиком услуг",
    betterMatching: "Лучшее соответствие с поставщиками услуг",
    trustAndSafety: "Создает доверие и безопасность в сообществе",
    completeProfileButton: "Заполнить Профиль",
    skipForNowButton: "Пропустить Пока",
    recommendedBadge: "Рекомендуется",
    profileSteps: "Что мы спросим:",
    stepPhoto: "Фото профиля (необязательно)",
    stepPhone: "Номер телефона",
    stepBio: "Краткое описание (необязательно)",
    stepLanguages: "Разговорные языки",
    estimatedTime: "Займет около 3 минут"
  }
};

export function PostRegistrationChoice({ userName, onCompleteProfile, onSkipForNow }: PostRegistrationChoiceProps) {
  const { currentLanguage, translate } = useLanguage();

  const benefits = [
    {
      icon: CheckCircle,
      text: translate(postRegistrationTranslations, 'requiredForBookings')
    },
    {
      icon: CheckCircle,
      text: translate(postRegistrationTranslations, 'requiredForProvider')
    },
    {
      icon: CheckCircle,
      text: translate(postRegistrationTranslations, 'betterMatching')
    },
    {
      icon: CheckCircle,
      text: translate(postRegistrationTranslations, 'trustAndSafety')
    }
  ];

  const profileSteps = [
    {
      icon: User,
      text: translate(postRegistrationTranslations, 'stepPhoto')
    },
    {
      icon: Phone,
      text: translate(postRegistrationTranslations, 'stepPhone')
    },
    {
      icon: MessageSquare,
      text: translate(postRegistrationTranslations, 'stepBio')
    },
    {
      icon: Globe,
      text: translate(postRegistrationTranslations, 'stepLanguages')
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl mx-auto">
        {/* Welcome Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold font-headline text-foreground mb-2">
            {translate(postRegistrationTranslations, 'welcomeTitle')}
          </h1>
          <p className="text-lg text-muted-foreground">
            {translate(postRegistrationTranslations, 'welcomeSubtitle')}
            {userName && <span className="font-medium text-foreground">, {userName}</span>}!
          </p>
        </div>

        {/* Choice Cards */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {/* Complete Profile Card */}
          <Card className="relative border-2 border-primary/20 hover:border-primary/40 transition-colors">
            <div className="absolute -top-3 left-4">
              <Badge variant="default" className="bg-primary text-primary-foreground">
                {translate(postRegistrationTranslations, 'recommendedBadge')}
              </Badge>
            </div>
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <User className="w-5 h-5 text-primary" />
                {translate(postRegistrationTranslations, 'completeProfileTitle')}
              </CardTitle>
              <CardDescription className="text-base">
                {translate(postRegistrationTranslations, 'completeProfileDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Benefits */}
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-3">
                  {translate(postRegistrationTranslations, 'whyCompleteTitle')}
                </h4>
                <div className="space-y-2">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <benefit.icon className="w-4 h-4 text-green-600 flex-shrink-0" />
                      <span>{benefit.text}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Steps Preview */}
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-3">
                  {translate(postRegistrationTranslations, 'profileSteps')}
                </h4>
                <div className="space-y-2">
                  {profileSteps.map((step, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <step.icon className="w-4 h-4 text-primary flex-shrink-0" />
                      <span>{step.text}</span>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground mt-3">
                  {translate(postRegistrationTranslations, 'estimatedTime')}
                </p>
              </div>

              <Button 
                onClick={onCompleteProfile}
                className="w-full"
                size="lg"
              >
                {translate(postRegistrationTranslations, 'completeProfileButton')}
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          {/* Skip Card */}
          <Card className="border-2 border-muted hover:border-muted-foreground/20 transition-colors">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <SkipForward className="w-5 h-5 text-muted-foreground" />
                {translate(postRegistrationTranslations, 'skipTitle')}
              </CardTitle>
              <CardDescription className="text-base">
                {translate(postRegistrationTranslations, 'skipDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={onSkipForNow}
                variant="outline"
                className="w-full"
                size="lg"
              >
                {translate(postRegistrationTranslations, 'skipForNowButton')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
