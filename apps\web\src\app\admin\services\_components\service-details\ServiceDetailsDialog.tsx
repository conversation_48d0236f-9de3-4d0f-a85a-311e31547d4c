
"use client";

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription as DialogPrimitiveDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { AdvertisedService as PrismaAdvertisedService, User as PrismaUser, NannyServiceDetails, ElderCareServiceDetails, CleaningServiceDetails, TutoringServiceDetails, CookingServiceDetails, ServiceCategory } from '@prisma/client';
import { getFullLocationPathDisplayHelper } from '@/lib/locations';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { DetailItem } from './DetailItem';
import { RenderSpecificDetails } from './render-specific-details';

interface ServiceWithDetails extends PrismaAdvertisedService {
  Provider: Pick<PrismaUser, 'FullName'> | null;
  Category: Pick<ServiceCategory, 'NameKey' | 'Slug'> | null;
  NannyServiceDetails: NannyServiceDetails | null;
  ElderCareServiceDetails: ElderCareServiceDetails | null;
  CleaningServiceDetails: CleaningServiceDetails | null;
  TutoringServiceDetails: TutoringServiceDetails | null;
  CookingServiceDetails: CookingServiceDetails | null;
}

interface ServiceDetailsDialogProps {
  serviceId: number | null;
  isOpen: boolean;
  onClose: () => void;
}

const getStatusBadgeClass = (status: string) => {
    switch(status) {
      case 'Activ': return 'bg-green-100 text-green-700 border-green-200';
      case 'PendingReview': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Rejected': return 'bg-red-100 text-red-700 border-red-200';
      case 'Inactiv': return 'bg-gray-100 text-gray-700 border-gray-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
  }
}

export function ServiceDetailsDialog({ serviceId, isOpen, onClose }: ServiceDetailsDialogProps) {
  const { translate } = useLanguage();
  const [service, setService] = useState<ServiceWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && serviceId) {
        const fetchServiceDetails = async () => {
            setIsLoading(true);
            setError(null);
            setService(null);
            try {
                const response = await fetch(`/api/proxy/provider/services/${serviceId}`);
                if (!response.ok) {
                    const errData = await response.json().catch(() => ({}));
                    throw new Error(errData.message || "Nu s-au putut prelua detaliile serviciului.");
                }
                const data = await response.json();
                setService(data.service);
            } catch (err) {
                const msg = err instanceof Error ? err.message : "Eroare necunoscută.";
                setError(msg);
            } finally {
                setIsLoading(false);
            }
        };
        fetchServiceDetails();
    }
  }, [isOpen, serviceId]);
  
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-48">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col justify-center items-center h-48 text-destructive">
          <AlertTriangle className="w-8 h-8 mb-2" />
          <p>{error}</p>
        </div>
      );
    }
    
    if (service) {
      const details = service.NannyServiceDetails || service.ElderCareServiceDetails || service.CleaningServiceDetails || service.TutoringServiceDetails || service.CookingServiceDetails;
      const statusKey = service.Status ? (`status${service.Status}` as keyof typeof commonTranslations) : null;
      const translatedStatus = statusKey ? translate(commonTranslations, statusKey, service.Status) : 'N/A';

      return (
        <ScrollArea className="h-full pr-6 -mr-6">
          <div className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row justify-between items-start">
                  <div>
                      <CardTitle className="text-lg">{service.ServiceName}</CardTitle>
                      <CardDescription>
                          ID: {service.Id} | {translate(commonTranslations, 'adminTableProviderName')}: {service.Provider?.FullName || 'N/A'}
                      </CardDescription>
                  </div>
                  <Badge variant="outline" className={cn("text-xs font-semibold", getStatusBadgeClass(service.Status))}>
                      {translatedStatus}
                  </Badge>
              </CardHeader>
              <CardContent>
                <dl className="space-y-1 text-sm">
                  <div className="py-2 px-3 odd:bg-muted/30 rounded-md grid grid-cols-1 md:grid-cols-3 gap-1 items-start">
                      <dt className="font-medium text-muted-foreground">Descriere</dt>
                      <dd className="md:col-span-2 text-foreground break-words whitespace-pre-line">{service.Description || 'N/A'}</dd>
                  </div>
                   {details && (
                    <>
                      <DetailItem label="Experiență (ani)" value={details.ExperienceYears ?? 'Nespecificat'} />
                      <DetailItem label="Locație" value={getFullLocationPathDisplayHelper(details.LocationValue || 'all', translate, commonTranslations)} />
                      <DetailItem label="Preț/oră (MDL)" value={details.PricePerHour ? String(details.PricePerHour) : 'Nespecificat'} />
                      <DetailItem label="Preț/zi (MDL)" value={details.PricePerDay ? String(details.PricePerDay) : 'Nespecificat'} />
                    </>
                  )}
                </dl>
              </CardContent>
            </Card>
            <RenderSpecificDetails service={service} />
          </div>
        </ScrollArea>
      )
    }

    return (
        <div className="flex flex-col justify-center items-center h-48 text-muted-foreground">
          <AlertTriangle className="w-8 h-8 mb-2" />
          <p>Nu s-au putut încărca detaliile serviciului.</p>
        </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Detalii Serviciu</DialogTitle>
          <DialogPrimitiveDescription>Vizualizare completă a detaliilor serviciului.</DialogPrimitiveDescription>
        </DialogHeader>
        <div className="flex-grow overflow-hidden py-2">
            {renderContent()}
        </div>
        <DialogFooter className="mt-auto pt-4 border-t sm:justify-end">
          <DialogClose asChild>
            <Button variant="secondary">Închide</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
