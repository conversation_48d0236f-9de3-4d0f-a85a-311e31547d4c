import { type NextRequest, NextResponse } from 'next/server';
import apiFetch from '@/lib/api-client'; // Import the centralized api-client

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fullName, email, password } = body;

    if (!fullName || !email || !password) {
        return NextResponse.json({ success: false, message: 'Numele complet, email-ul și parola sunt obligatorii.' }, { status: 400 });
    }

    // Call the backend API to handle registration
    const apiResponse = await apiFetch('auth/register', {
        method: 'POST',
        body: JSON.stringify({ fullName, email, password }),
        // This request is unauthenticated, so no JWT is passed. The proxy adds the API key.
    });

    const responseData = await apiResponse.json();

    if (!apiResponse.ok) {
        // Forward the error from the backend API
        return NextResponse.json({ success: false, message: responseData.message || '<PERSON>roare la înregistrare.' }, { status: apiResponse.status });
    }

    console.log(`[Web API Register] User registration successful for ${email}, proxied to backend.`);
    
    // The backend now handles creating the user. The frontend just proxies and returns the result.
    // Session creation will be handled by NextAuth on subsequent login.
    return NextResponse.json(responseData, { status: 201 });

  } catch (error) {
    console.error('[Web API Register] Error proxying registration request:', error);
    const errorMessage = error instanceof Error ? error.message : 'A apărut o eroare la înregistrare.';
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
