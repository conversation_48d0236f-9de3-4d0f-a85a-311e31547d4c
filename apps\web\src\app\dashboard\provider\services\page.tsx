
"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusCircle, Trash2, Edit, AlertTriangle, ListChecks, Loader2, Baby, HeartHandshake, Sparkles, GraduationCap, CookingPot } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import type { AdvertisedService as PrismaServiceType, ServiceCategory as PrismaServiceCategory } from '@prisma/client';
import { commonTranslations } from '@repo/translations';
import { useSession } from "next-auth/react";
import { Skeleton } from '@/components/ui/skeleton';
import { Label } from '@/components/ui/label';
import React from 'react'; // Import React for cloneElement
import { useUser } from '@/contexts/user-context'; // Import useUser

// Map slugs to their corresponding URL paths for editing
const slugToPathMap: Record<string, string> = {
  'Nanny': 'nanny',
  'ElderCare': 'elder-care',
  'Cleaning': 'cleaning',
  'Tutoring': 'tutoring',
  'Cooking': 'cooking',
};

export default function MyServicesPage() {
  const { translate } = useLanguage();
  const { user } = useUser(); // Use the new user context
  const { toast } = useToast();

  const [offeredServices, setOfferedServices] = useState<PrismaServiceType[]>([]);
  const [allCategories, setAllCategories] = useState<PrismaServiceCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchServices = useCallback(async (providerId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const [servicesResponse, categoriesResponse] = await Promise.all([
        fetch(`/api/proxy/provider/services?providerId=${providerId}`),
        fetch(`/api/proxy/service-categories`)
      ]);

      if (!servicesResponse.ok) throw new Error(translate(commonTranslations, 'errorFetchingServices'));
      if (!categoriesResponse.ok) throw new Error(translate(commonTranslations, 'errorFetchingCategories'));

      const servicesData = await servicesResponse.json();
      const categoriesData = await categoriesResponse.json();

      setOfferedServices(servicesData.services || []);
      setAllCategories(categoriesData || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'A apărut o eroare necunoscută.');
    } finally {
      setIsLoading(false);
    }
  }, [translate]);

  useEffect(() => {
    if (user?.id) {
      fetchServices(String(user.id));
    } else {
        setIsLoading(false);
    }
  }, [user, fetchServices]);

  const handleStatusChange = async (serviceId: number, newStatus: "Activ" | "Inactiv") => {
    toast({
      title: "Funcționalitate în dezvoltare",
      description: `Schimbarea statusului pentru serviciul ${serviceId} la ${newStatus} nu este încă implementată.`,
    });
  };

  const handleDeleteService = async (serviceId: number) => {
    toast({
      title: "Funcționalitate în dezvoltare",
      description: `Ștergerea serviciului ${serviceId} nu este încă implementată.`,
    });
  };

  const getCategoryNameBySlug = (slug: string) => {
    const category = allCategories.find(c => c.Slug === slug);
    return category ? translate(commonTranslations, category.NameKey as keyof typeof commonTranslations) : slug;
  };
  
  const getIconForService = (slug: string) => {
    const iconProps = { className: "w-6 h-6 text-primary" };
    switch (slug) {
        case 'Nanny':
            return <Baby {...iconProps} />;
        case 'ElderCare':
            return <HeartHandshake {...iconProps} />;
        case 'Cleaning':
            return <Sparkles {...iconProps} />;
        case 'Tutoring':
            return <GraduationCap {...iconProps} />;
        case 'Cooking':
            return <CookingPot {...iconProps} />;
        default:
            return <ListChecks {...iconProps} />;
    }
  };

  const availableCategories = useMemo(() => {
    const existingSlugs = new Set(offeredServices.map(s => s.ServiceCategorySlug));
    return allCategories.filter(cat => !existingSlugs.has(cat.Slug));
  }, [allCategories, offeredServices]);
  
  const allCategoriesAdded = useMemo(() => {
    if (isLoading || allCategories.length === 0) return false;
    return availableCategories.length === 0;
  }, [isLoading, allCategories, availableCategories]);
  
  const renderLoadingSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <Card key={i} className="p-4">
          <div className="flex justify-between items-center">
             <div className="flex items-center gap-4">
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="space-y-2">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-32" />
                </div>
            </div>
            <div className="flex items-center gap-4">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-10 w-10" />
              <Skeleton className="h-10 w-10" />
            </div>
          </div>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row justify-between items-start">
          <div>
            <CardTitle className="font-headline text-xl flex items-center">
              <ListChecks className="w-6 h-6 mr-2 text-primary"/>
              {translate(commonTranslations, 'myServicesPageTitle')}
            </CardTitle>
            <CardDescription>{translate(commonTranslations, 'myServicesPageDescription')}</CardDescription>
          </div>
          
            <TooltipProvider>
              <Tooltip open={allCategoriesAdded ? undefined : false}>
                <TooltipTrigger asChild>
                  <div tabIndex={allCategoriesAdded ? 0 : -1}>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button disabled={allCategoriesAdded}>
                          <PlusCircle className="w-5 h-5 mr-2" />
                          {translate(commonTranslations, 'addNewServiceButton')}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {availableCategories.map((category) => (
                          <DropdownMenuItem key={category.Id} asChild className="cursor-pointer">
                            <Link href={`/dashboard/provider/services/${slugToPathMap[category.Slug]}/new`}>
                              {React.cloneElement(getIconForService(category.Slug), { className: "w-4 h-4 mr-2 text-primary" })}
                              <span>{translate(commonTranslations, category.NameKey as keyof typeof commonTranslations)}</span>
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TooltipTrigger>
                {allCategoriesAdded && (
                  <TooltipContent>
                    <p>{translate(commonTranslations, 'addServiceDisabledTooltip')}</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          
        </CardHeader>
      </Card>
      
      {isLoading ? renderLoadingSkeleton() : error ? (
        <Card>
          <CardContent className="text-center py-10 text-destructive">
            <AlertTriangle className="mx-auto h-10 w-10 mb-2" />
            <p>{error}</p>
          </CardContent>
        </Card>
      ) : offeredServices.length > 0 ? (
        <div className="space-y-4">
          {offeredServices.map(service => (
            <Card key={service.Id} className="p-4 bg-muted/30">
              <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
                <div className="flex items-center gap-4">
                    <div className="bg-background p-3 rounded-lg shadow-sm">
                        {getIconForService(service.ServiceCategorySlug)}
                    </div>
                    <div>
                        <h3 className="font-semibold">{service.ServiceName}</h3>
                        <p className="text-sm text-muted-foreground">{getCategoryNameBySlug(service.ServiceCategorySlug)}</p>
                    </div>
                </div>
                <div className="flex items-center gap-2 sm:gap-4 ml-auto">
                   <div className="flex items-center space-x-2">
                    <Switch
                      id={`status-${service.Id}`}
                      checked={service.Status === 'Activ'}
                      onCheckedChange={(checked) => handleStatusChange(service.Id, checked ? 'Activ' : 'Inactiv')}
                      aria-label="Service Status"
                    />
                    <Label htmlFor={`status-${service.Id}`} className="text-sm">{service.Status}</Label>
                  </div>
                  <Button variant="outline" size="icon" asChild>
                    <Link href={`/dashboard/provider/services/${slugToPathMap[service.ServiceCategorySlug]}/edit/${service.Id}`}>
                      <Edit className="w-4 h-4" />
                    </Link>
                  </Button>
                   <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="icon">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>{translate(commonTranslations, 'deleteDialogTitle')}</AlertDialogTitle>
                        <AlertDialogDescription>
                          {translate(commonTranslations, 'deleteDialogDescription').replace('{serviceName}', service.ServiceName)}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>{translate(commonTranslations, 'cancelButton')}</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleDeleteService(service.Id)}>
                          {translate(commonTranslations, 'confirmDeleteButton')}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <ListChecks className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{translate(commonTranslations, 'noServicesMessage')}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
