"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Play, 
  CheckCircle, 
  Clock, 
  User, 
  Calendar, 
  MapPin, 
  MessageSquare, 
  Loader2,
  AlertTriangle
} from "lucide-react";
import { format } from "date-fns";
import { useLanguage } from '@/contexts/language-context';

interface ConfirmedBooking {
  id: number;
  clientId: number;
  advertisedServiceId: number;
  eventStartDateTime: string;
  eventEndDateTime?: string;
  status: string;
  clientNotes?: string;
  createdAt: string;
  client: {
    fullName: string;
    email: string;
  };
  service: {
    serviceName: string;
  };
}

interface ProviderServiceDeliveryProps {
  className?: string;
}

const serviceDeliveryTranslations = {
  confirmedBookings: { ro: "Rezervări Confirmate", ru: "Подтвержденные бронирования", en: "Confirmed Bookings" },
  readyForService: { ro: "Gata pentru serviciu", ru: "Готов к обслуживанию", en: "Ready for service" },
  startService: { ro: "Începe Serviciul", ru: "Начать услугу", en: "Start Service" },
  markComplete: { ro: "Marchează ca Finalizat", ru: "Отметить как завершенное", en: "Mark as Complete" },
  serviceStarted: { ro: "Serviciu Început", ru: "Услуга начата", en: "Service Started" },
  serviceCompleted: { ro: "Serviciu Finalizat", ru: "Услуга завершена", en: "Service Completed" },
  client: { ro: "Client", ru: "Клиент", en: "Client" },
  service: { ro: "Serviciu", ru: "Услуга", en: "Service" },
  dateTime: { ro: "Data și Ora", ru: "Дата и время", en: "Date & Time" },
  duration: { ro: "Durată", ru: "Продолжительность", en: "Duration" },
  clientNotes: { ro: "Notițe Client", ru: "Заметки клиента", en: "Client Notes" },
  noConfirmedBookings: { ro: "Nu ai rezervări confirmate pentru astăzi", ru: "У вас нет подтвержденных бронирований на сегодня", en: "No confirmed bookings for today" },
  error: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  loading: { ro: "Se încarcă...", ru: "Загрузка...", en: "Loading..." },
  confirmStart: { ro: "Confirmă Începerea", ru: "Подтвердить начало", en: "Confirm Start" },
  confirmComplete: { ro: "Confirmă Finalizarea", ru: "Подтвердить завершение", en: "Confirm Completion" },
  startServiceDesc: { ro: "Marchează acest serviciu ca început", ru: "Отметить эту услугу как начатую", en: "Mark this service as started" },
  completeServiceDesc: { ro: "Marchează acest serviciu ca finalizat", ru: "Отметить эту услугу как завершенную", en: "Mark this service as completed" },
  minutes: { ro: "minute", ru: "минут", en: "minutes" },
  noNotes: { ro: "Fără notițe", ru: "Без заметок", en: "No notes" },
  close: { ro: "Închide", ru: "Закрыть", en: "Close" },
  inProgress: { ro: "În Desfășurare", ru: "В процессе", en: "In Progress" },
  confirmed: { ro: "Confirmat", ru: "Подтверждено", en: "Confirmed" },
};

export function ProviderServiceDelivery({ className }: ProviderServiceDeliveryProps) {
  const { translate } = useLanguage();
  const { data: session } = useSession();
  
  const [confirmedBookings, setConfirmedBookings] = useState<ConfirmedBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<ConfirmedBooking | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [actionType, setActionType] = useState<'start' | 'complete' | null>(null);

  // Load confirmed bookings
  useEffect(() => {
    const loadConfirmedBookings = async () => {
      if (!session?.user) return;
      
      setIsLoading(true);
      try {
        const providerId = (session.user as any).id;
        const response = await fetch(`/api/proxy/bookings/provider/${providerId}/confirmed`);
        
        if (response.ok) {
          const data = await response.json();
          setConfirmedBookings(data.bookings || []);
        } else {
          setError('Eroare la încărcarea rezervărilor confirmate.');
        }
      } catch (error) {
        console.error('Error loading confirmed bookings:', error);
        setError('Eroare la încărcarea rezervărilor confirmate.');
      } finally {
        setIsLoading(false);
      }
    };

    loadConfirmedBookings();
  }, [session]);

  const handleServiceAction = async (booking: ConfirmedBooking, action: 'start' | 'complete') => {
    setSelectedBooking(booking);
    setActionType(action);
    setIsDialogOpen(true);
  };

  const confirmServiceAction = async () => {
    if (!selectedBooking || !actionType) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const endpoint = actionType === 'start' ? 'start' : 'complete';
      const response = await fetch(`/api/proxy/bookings/${selectedBooking.id}/${endpoint}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Update the booking status in the list
        setConfirmedBookings(prev => prev.map(booking => 
          booking.id === selectedBooking.id 
            ? { ...booking, status: actionType === 'start' ? 'InProgress' : 'Completed' }
            : booking
        ));
        
        setIsDialogOpen(false);
        setSelectedBooking(null);
        setActionType(null);
      } else {
        setError(data.message || `Eroare la ${actionType === 'start' ? 'începerea' : 'finalizarea'} serviciului.`);
      }
    } catch (error) {
      console.error('Service action error:', error);
      setError(`Eroare la ${actionType === 'start' ? 'începerea' : 'finalizarea'} serviciului.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDateTime = (dateTimeStr: string) => {
    const date = new Date(dateTimeStr);
    return format(date, "PPP 'la' HH:mm");
  };

  const calculateDuration = (start: string, end?: string) => {
    if (!end) return null;
    const startDate = new Date(start);
    const endDate = new Date(end);
    const diffMinutes = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60));
    return diffMinutes;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Confirmed':
        return <Badge className="bg-green-100 text-green-800">{translate(serviceDeliveryTranslations, 'confirmed')}</Badge>;
      case 'InProgress':
        return <Badge className="bg-blue-100 text-blue-800">{translate(serviceDeliveryTranslations, 'inProgress')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>{translate(serviceDeliveryTranslations, 'loading')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {translate(serviceDeliveryTranslations, 'confirmedBookings')}
          </CardTitle>
          <CardDescription>
            {translate(serviceDeliveryTranslations, 'readyForService')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>{translate(serviceDeliveryTranslations, 'error')}</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {confirmedBookings.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {translate(serviceDeliveryTranslations, 'noConfirmedBookings')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {confirmedBookings.map((booking) => (
                <Card key={booking.id} className="border-l-4 border-l-green-500">
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="font-medium">{booking.client.fullName}</span>
                          {getStatusBadge(booking.status)}
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDateTime(booking.eventStartDateTime)}</span>
                          {booking.eventEndDateTime && (
                            <>
                              <span>•</span>
                              <Clock className="w-3 h-3" />
                              <span>{calculateDuration(booking.eventStartDateTime, booking.eventEndDateTime)} {translate(serviceDeliveryTranslations, 'minutes')}</span>
                            </>
                          )}
                        </div>

                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-medium">{booking.service.serviceName}</span>
                        </div>

                        {booking.clientNotes && (
                          <div className="flex items-start gap-2 text-sm">
                            <MessageSquare className="w-3 h-3 mt-0.5 text-muted-foreground" />
                            <span className="text-muted-foreground italic">
                              "{booking.clientNotes}"
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col gap-2 ml-4">
                        {booking.status === 'Confirmed' && (
                          <Button
                            size="sm"
                            onClick={() => handleServiceAction(booking, 'start')}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <Play className="w-3 h-3 mr-1" />
                            {translate(serviceDeliveryTranslations, 'startService')}
                          </Button>
                        )}
                        
                        {booking.status === 'InProgress' && (
                          <Button
                            size="sm"
                            onClick={() => handleServiceAction(booking, 'complete')}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <CheckCircle className="w-3 h-3 mr-1" />
                            {translate(serviceDeliveryTranslations, 'markComplete')}
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Service Action Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {actionType === 'start' 
                ? translate(serviceDeliveryTranslations, 'confirmStart')
                : translate(serviceDeliveryTranslations, 'confirmComplete')
              }
            </DialogTitle>
            <DialogDescription>
              {actionType === 'start'
                ? translate(serviceDeliveryTranslations, 'startServiceDesc')
                : translate(serviceDeliveryTranslations, 'completeServiceDesc')
              }
            </DialogDescription>
          </DialogHeader>

          {selectedBooking && (
            <div className="space-y-4">
              <div className="bg-muted/50 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{translate(serviceDeliveryTranslations, 'client')}:</span>
                  <span className="font-medium">{selectedBooking.client.fullName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{translate(serviceDeliveryTranslations, 'service')}:</span>
                  <span className="font-medium">{selectedBooking.service.serviceName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{translate(serviceDeliveryTranslations, 'dateTime')}:</span>
                  <span className="font-medium">{formatDateTime(selectedBooking.eventStartDateTime)}</span>
                </div>
                {selectedBooking.clientNotes && (
                  <div className="pt-2 border-t">
                    <span className="text-muted-foreground text-sm">{translate(serviceDeliveryTranslations, 'clientNotes')}:</span>
                    <p className="text-sm italic mt-1">"{selectedBooking.clientNotes}"</p>
                  </div>
                )}
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  {translate(serviceDeliveryTranslations, 'close')}
                </Button>
                <Button
                  onClick={confirmServiceAction}
                  disabled={isSubmitting}
                  className={actionType === 'start' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-green-600 hover:bg-green-700'}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Se procesează...
                    </>
                  ) : (
                    actionType === 'start' 
                      ? translate(serviceDeliveryTranslations, 'startService')
                      : translate(serviceDeliveryTranslations, 'markComplete')
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
