import type { Address, AddressPayload } from '@repo/types';

async function _getAddresses(): Promise<Address[]> {
  const response = await fetch(`/api/proxy/address`);
  if (!response.ok) {
    throw new Error('Failed to fetch addresses');
  }
  const data = await response.json();
  return data.addresses.map((addr: any) => ({
    id: addr.Id,
    userId: addr.UserId,
    label: addr.Label,
    street: addr.Street,

    // Enhanced location architecture
    countryId: addr.CountryId,
    regionId: addr.RegionId,
    cityId: addr.CityId,
    sectorId: addr.SectorId,

    // Backward compatibility fields
    city: addr.City,
    region: addr.Region,
    country: addr.Country,

    postalCode: addr.PostalCode,
    isDefault: addr.IsDefault,
  }));
}

async function _createAddress(payload: AddressPayload): Promise<Address> {
  const response = await fetch(`/api/proxy/address`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message || 'Failed to create address');
  }
  return data.address;
}

async function _updateAddress(id: number, payload: AddressPayload): Promise<Address> {
  const response = await fetch(`/api/proxy/address/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message || 'Failed to update address');
  }
  return data.address;
}

async function _deleteAddress(id: number): Promise<void> {
  const response = await fetch(`/api/proxy/address/${id}`, {
    method: 'DELETE',
  });
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message || 'Failed to delete address');
  }
}

export const AddressService = {
  getAddresses: _getAddresses,
  createAddress: _createAddress,
  updateAddress: _updateAddress,
  deleteAddress: _deleteAddress,
};
