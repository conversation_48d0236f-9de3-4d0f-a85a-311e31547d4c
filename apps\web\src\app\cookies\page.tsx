
"use client";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { useLanguage } from '@/contexts/language-context';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const cookiesPageTranslations = {
  pageTitle: { ro: "Politică Cookie-uri", ru: "Политика использования файлов cookie", en: "Cookies Policy" },
  lastUpdated: { ro: "Ultima actualizare: 5 Iunie 2024", ru: "Последнее обновление: 5 июня 2024 г.", en: "Last updated: June 5, 2024" },

  introTitle: { ro: "Introducere", ru: "Введение", en: "Introduction" },
  introContent: { 
    ro: "Această Politică privind Cookie-urile explică ce sunt cookie-urile și cum le folosim pe site-ul bonami. Ar trebui să citiți această politică pentru a înțelege ce tipuri de cookie-uri folosim, informațiile pe care le colectăm folosind cookie-uri și cum sunt utilizate aceste informații.",
    ru: "Эта Политика использования файлов cookie объясняет, что такое файлы cookie и как мы их используем на сайте bonami. Вам следует прочитать эту политику, чтобы понять, какие типы файлов cookie мы используем, какую информацию мы собираем с помощью файлов cookie и как эта информация используется.",
    en: "This Cookies Policy explains what cookies are and how we use them on the bonami website. You should read this policy to understand what types of cookies we use, the information we collect using cookies, and how that information is used."
  },

  whatAreCookiesTitle: { ro: "Ce Sunt Cookie-urile?", ru: "Что такое файлы cookie?", en: "What Are Cookies?" },
  whatAreCookiesContent: {
    ro: "Cookie-urile sunt fișiere text mici care sunt stocate pe computerul sau dispozitivul dvs. mobil atunci când vizitați un site web. Acestea sunt utilizate pe scară largă pentru a face site-urile web să funcționeze sau să funcționeze mai eficient, precum și pentru a furniza informații proprietarilor site-ului.",
    ru: "Файлы cookie — это небольшие текстовые файлы, которые сохраняются на вашем компьютере или мобильном устройстве при посещении веб-сайта. Они широко используются для обеспечения работы или повышения эффективности веб-сайтов, а также для предоставления информации владельцам сайта.",
    en: "Cookies are small text files that are stored on your computer or mobile device when you visit a website. They are widely used to make websites work or work more efficiently, as well as to provide information to the owners of the site."
  },

  howWeUseCookiesTitle: { ro: "Cum Folosim Cookie-urile?", ru: "Как мы используем файлы cookie?", en: "How We Use Cookies?" },
  howWeUseCookiesContent: {
    ro: "Folosim cookie-uri pentru diverse scopuri, cum ar fi: autentificarea utilizatorilor, reținerea preferințelor utilizatorilor, înțelegerea modului în care utilizatorii interacționează cu site-ul nostru și îmbunătățirea experienței generale a utilizatorului. De asemenea, putem folosi cookie-uri de la terți pentru analize și publicitate.",
    ru: "Мы используем файлы cookie для различных целей, таких как: аутентификация пользователей, запоминание предпочтений пользователей, понимание того, как пользователи взаимодействуют с нашим сайтом, и улучшение общего пользовательского опыта. Мы также можем использовать сторонние файлы cookie для аналитики и рекламы.",
    en: "We use cookies for various purposes, such as: authenticating users, remembering user preferences, understanding how users interact with our site, and improving the overall user experience. We may also use third-party cookies for analytics and advertising."
  },

  typesOfCookiesTitle: { ro: "Tipuri de Cookie-uri Folosite", ru: "Типы используемых файлов cookie", en: "Types of Cookies We Use" },
  typesOfCookiesContent: {
    ro: "Cookie-uri Esențiale: Aceste cookie-uri sunt necesare pentru funcționarea site-ului și nu pot fi dezactivate în sistemele noastre. Cookie-uri de Performanță și Analiză: Aceste cookie-uri ne permit să numărăm vizitele și sursele de trafic, astfel încât să putem măsura și îmbunătăți performanța site-ului nostru. Cookie-uri Funcționale: Aceste cookie-uri permit site-ului web să ofere funcționalități și personalizare îmbunătățite.",
    ru: "Основные файлы cookie: Эти файлы cookie необходимы для работы сайта и не могут быть отключены в наших системах. Файлы cookie производительности и аналитики: Эти файлы cookie позволяют нам подсчитывать посещения и источники трафика, чтобы мы могли измерять и улучшать производительность нашего сайта. Функциональные файлы cookie: Эти файлы cookie позволяют веб-сайту предоставлять расширенные функциональные возможности и персонализацию.",
    en: "Essential Cookies: These cookies are necessary for the website to function and cannot be switched off in our systems. Performance and Analytics Cookies: These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. Functional Cookies: These cookies enable the website to provide enhanced functionality and personalization."
  },

  yourChoicesTitle: { ro: "Opțiunile Dvs. Referitoare la Cookie-uri", ru: "Ваши варианты выбора в отношении файлов cookie", en: "Your Choices Regarding Cookies" },
  yourChoicesContent: {
    ro: "Majoritatea browserelor web permit un anumit control asupra majorității cookie-urilor prin setările browserului. Pentru a afla mai multe despre cookie-uri, inclusiv cum să vedeți ce cookie-uri au fost setate și cum să le gestionați și să le ștergeți, vizitați www.aboutcookies.org sau www.allaboutcookies.org.",
    ru: "Большинство веб-браузеров позволяют в определенной степени контролировать большинство файлов cookie через настройки браузера. Чтобы узнать больше о файлах cookie, включая то, как просмотреть, какие файлы cookie были установлены, и как управлять ими и удалять их, посетите www.aboutcookies.org или www.allaboutcookies.org.",
    en: "Most web browsers allow some control of most cookies through the browser settings. To find out more about cookies, including how to see what cookies have been set and how to manage and delete them, visit www.aboutcookies.org or www.allaboutcookies.org."
  },
  
  contactTitle: { ro: "Contact", ru: "Контакт", en: "Contact" },
  contactContent: {
    ro: "Dacă aveți întrebări despre această Politică privind Cookie-urile, ne puteți contacta la [adresa de email pentru contact].",
    ru: "Если у вас есть какие-либо вопросы по поводу этой Политики использования файлов cookie, вы можете связаться с нами по адресу [адрес электронной почты для контактов].",
    en: "If you have any questions about this Cookies Policy, you can contact us at [contact email address]."
  }
};

export default function CookiesPage() {
  const { translate } = useLanguage();

  const sections = [
    { titleKey: 'introTitle', contentKey: 'introContent' },
    { titleKey: 'whatAreCookiesTitle', contentKey: 'whatAreCookiesContent' },
    { titleKey: 'howWeUseCookiesTitle', contentKey: 'howWeUseCookiesContent' },
    { titleKey: 'typesOfCookiesTitle', contentKey: 'typesOfCookiesContent' },
    { titleKey: 'yourChoicesTitle', contentKey: 'yourChoicesContent' },
    { titleKey: 'contactTitle', contentKey: 'contactContent' },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <Card className="max-w-3xl mx-auto mt-10 shadow-lg">
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center font-headline">
              {translate(cookiesPageTranslations, 'pageTitle')}
            </CardTitle>
            <p className="text-sm text-muted-foreground text-center">{translate(cookiesPageTranslations, 'lastUpdated')}</p>
          </CardHeader>
          <CardContent className="space-y-6 text-foreground/80">
            {sections.map(section => (
              <div key={section.titleKey}>
                <h2 className="text-xl font-semibold mb-2 font-headline text-foreground">
                  {translate(cookiesPageTranslations, section.titleKey)}
                </h2>
                <p className="whitespace-pre-line">
                  {translate(cookiesPageTranslations, section.contentKey)}
                </p>
              </div>
            ))}
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
