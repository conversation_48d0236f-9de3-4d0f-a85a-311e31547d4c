
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import { createServer } from 'http';
import { Server, Socket } from 'socket.io';
import prisma from './lib/db';
import { ChatMessageStatus, NotificationType } from '@prisma/client';

import adminRoutes from './routes/admin';
import adminServicesRoutes from './routes/admin-services';
import publicRoutes from './routes/public';
import userRoutes from './routes/user';
import notificationRoutes from './routes/notifications';
import bookingRoutes from './routes/bookings';
import chatRoutes from './routes/chat';
import clientRoutes from './routes/client';
import dashboardRoutes from './routes/dashboard';
import providerRoutes from './routes/provider';
import providerRequestsRoutes from './routes/provider-requests';
import servicesRoutes from './routes/services';
import addressRoutes from './routes/address'; // Import address routes
import locationsRoutes from './routes/locations'; // Import locations routes
import authRoutes from './routes/auth'; // Import auth routes
import reviewRoutes from './routes/reviews'; // Import review routes

import { authenticate } from './middleware/auth';

dotenv.config();

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.WEB_APP_URL || "http://localhost:9002",
    methods: ["GET", "POST"]
  }
});

const port = process.env.PORT || 9001;

app.use(cors());
app.use(express.json());

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

// Routes
const apiRouter = express.Router();

// Public routes (no authentication needed)
apiRouter.use('/', publicRoutes);
apiRouter.use('/locations', locationsRoutes); // Use locations routes (public)
apiRouter.use('/auth', authRoutes); // Use auth routes

// Authenticated routes
apiRouter.use(authenticate);

apiRouter.use('/admin', adminRoutes);
apiRouter.use('/admin/provider-services', adminServicesRoutes);
apiRouter.use('/user', userRoutes);
apiRouter.use('/notifications', notificationRoutes);
apiRouter.use('/bookings', bookingRoutes);
apiRouter.use('/chat', chatRoutes);
apiRouter.use('/client', clientRoutes);
apiRouter.use('/dashboard', dashboardRoutes);
apiRouter.use('/provider', providerRoutes);
apiRouter.use('/provider-requests', providerRequestsRoutes);
apiRouter.use('/services', servicesRoutes);
apiRouter.use('/address', addressRoutes);
apiRouter.use('/reviews', reviewRoutes);

app.use('/api', apiRouter);

app.get('/', (req, res) => {
  res.send('API Server with Socket.IO is running!');
});

// Socket.IO logic
io.on('connection', (socket: Socket) => {
  console.log(`[Socket.IO] User connected: ${socket.id}`);

  socket.on('joinRoom', (roomId: string) => {
    socket.join(roomId);
    console.log(`[Socket.IO] User ${socket.id} joined room: ${roomId}`);
  });

  socket.on('sendMessage', async (data: { chatRoomId: string; senderId: number; recipientId: number; content: string }) => {
    try {
      const { chatRoomId, senderId, recipientId, content } = data;

      const newMessage = await prisma.chatMessage.create({
        data: {
          ChatRoomId: chatRoomId,
          SenderId: senderId,
          RecipientId: recipientId,
          Content: content,
          Status: ChatMessageStatus.Sent,
        },
      });
      
      await prisma.chatRoom.update({
        where: { Id: chatRoomId },
        data: { UpdatedAt: new Date() }
      });
      
      io.to(chatRoomId).emit('receiveMessage', newMessage);
      console.log(`[Socket.IO] Message sent in room ${chatRoomId}`);

      const roomSockets = await io.in(chatRoomId).fetchSockets();
      if (roomSockets.length <= 1) {
          const sender = await prisma.user.findUnique({ where: { Id: senderId }, select: { FullName: true }});
          await prisma.notification.create({
              data: {
                  UserId: recipientId,
                  Message: `Mesaj nou de la ${sender?.FullName || 'un utilizator'}.`,
                  Link: `/dashboard/chat/${chatRoomId}`,
                  Type: NotificationType.NewMessage
              }
          });
          console.log(`[Socket.IO] Sent notification to offline user ${recipientId}`);
      }

    } catch (error) {
      console.error('[Socket.IO] Error sending message:', error);
      socket.emit('sendMessageError', { message: 'Failed to send message.' });
    }
  });

  socket.on('leaveRoom', (roomId: string) => {
    socket.leave(roomId);
    console.log(`[Socket.IO] User ${socket.id} left room: ${roomId}`);
  });

  socket.on('disconnect', () => {
    console.log(`[Socket.IO] User disconnected: ${socket.id}`);
  });
});

httpServer.listen(port, () => {
  console.log(`[api] Server is running on http://localhost:${port}`);
});
