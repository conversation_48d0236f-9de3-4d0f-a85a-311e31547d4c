import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions, type ExtendedSession } from '@repo/auth';
import jwt from 'jsonwebtoken';
import apiFetch from '@/lib/api-client';
import type { ProfileUpdateResponse } from '@/types/profile-setup';

export async function PATCH(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: 'Nu ești autentificat.' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { phone, bio, spokenLanguages } = body;

    // Validate the input data
    if (phone && typeof phone !== 'string') {
      return NextResponse.json(
        { success: false, message: 'Numărul de telefon trebuie să fie text.' },
        { status: 400 }
      );
    }

    if (bio && typeof bio !== 'string') {
      return NextResponse.json(
        { success: false, message: 'Biografia trebuie să fie text.' },
        { status: 400 }
      );
    }

    if (spokenLanguages && !Array.isArray(spokenLanguages)) {
      return NextResponse.json(
        { success: false, message: 'Limbile vorbite trebuie să fie o listă.' },
        { status: 400 }
      );
    }

    // Validate phone format if provided
    if (phone) {
      const phonePattern = /^[\+]?[0-9\s\-\(\)]+$/;
      if (!phonePattern.test(phone) || phone.length < 8 || phone.length > 15) {
        return NextResponse.json(
          { success: false, message: 'Numărul de telefon nu este valid.' },
          { status: 400 }
        );
      }
    }

    // Validate bio length if provided
    if (bio && bio.length > 500) {
      return NextResponse.json(
        { success: false, message: 'Biografia nu poate depăși 500 de caractere.' },
        { status: 400 }
      );
    }

    // Validate spoken languages if provided
    if (spokenLanguages && spokenLanguages.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Selectează cel puțin o limbă.' },
        { status: 400 }
      );
    }

    // Prepare the update data (only include fields that are provided)
    const updateData: any = {};
    if (phone !== undefined) updateData.phone = phone;
    if (bio !== undefined) updateData.bio = bio;
    if (spokenLanguages !== undefined) updateData.spokenLanguages = spokenLanguages;

    // Create JWT token for backend authentication
    const internalApiJwt = jwt.sign(
      {
        id: session.user.id,
        roles: session.user.roles,
        isAdmin: session.user.isAdmin,
      },
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '5m' }
    );

    // Call the backend API to update the user profile
    const apiResponse = await apiFetch(`user/${session.user.id}/profile`, {
      method: 'PATCH',
      body: JSON.stringify(updateData),
      jwt: internalApiJwt,
    });

    const responseData = await apiResponse.json();

    if (!apiResponse.ok) {
      console.error('[Profile Update API] Backend error:', responseData);
      return NextResponse.json(
        { success: false, message: responseData.message || 'Eroare la actualizarea profilului.' },
        { status: apiResponse.status }
      );
    }

    console.log(`[Profile Update API] Profile updated successfully for user ${session.user.id}`);
    
    const response: ProfileUpdateResponse = {
      success: true,
      message: 'Profilul a fost actualizat cu succes.',
      user: {
        id: session.user.id,
        phone: responseData.phone,
        bio: responseData.bio,
        spokenLanguages: responseData.spokenLanguages,
        avatarUrl: responseData.avatarUrl,
      },
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('[Profile Update API] Unexpected error:', error);
    return NextResponse.json(
      { success: false, message: 'A apărut o eroare neașteptată.' },
      { status: 500 }
    );
  }
}
