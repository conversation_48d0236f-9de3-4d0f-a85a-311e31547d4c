
import { type NextRequest, NextResponse } from 'next/server';

const SESSION_COOKIE_NAME = 'bonami-app-session';

export async function GET(request: NextRequest) {
  try {
    const sessionTokenCookie = request.cookies.get(SESSION_COOKIE_NAME);

    if (sessionTokenCookie && sessionTokenCookie.value) {
      // În aplicație reală, aici s-ar valida token-ul (ex: JWT verify)
      // Pentru simulare, prezența cookie-ului este suficientă.
      console.log(`[API /session-status] Session cookie "${SESSION_COOKIE_NAME}" found and valid.`);
      return NextResponse.json({ authenticated: true }, { status: 200 });
    } else {
      console.log(`[API /session-status] Session cookie "${SESSION_COOKIE_NAME}" not found or empty.`);
      return NextResponse.json({ authenticated: false, message: 'No active session.' }, { status: 401 });
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error in session status check';
    console.error('[API /session-status] Error:', errorMessage, error);
    return NextResponse.json({ authenticated: false, message: `Error checking session: ${errorMessage}` }, { status: 500 });
  }
}
