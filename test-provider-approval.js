// Test script for provider approval workflow
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:9001';
const TEST_REQUEST_ID = 'cmdo7mo9m0002io2rr7gyoo0y';

// Mock admin authentication - you'll need to replace this with actual auth
const ADMIN_HEADERS = {
  'Content-Type': 'application/json',
  // Add your admin auth headers here
};

async function testBulkApproveAll() {
  console.log('\n=== Testing Bulk Approve All ===');
  try {
    const response = await fetch(`${API_BASE}/admin/provider-requests/bulk-action`, {
      method: 'POST',
      headers: ADMIN_HEADERS,
      body: JSON.stringify({
        requestId: TEST_REQUEST_ID,
        action: 'approve-all',
        adminNotes: 'Test bulk approve all'
      })
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function testBulkRejectAll() {
  console.log('\n=== Testing Bulk Reject All ===');
  try {
    const response = await fetch(`${API_BASE}/admin/provider-requests/bulk-action`, {
      method: 'POST',
      headers: ADMIN_HEADERS,
      body: JSON.stringify({
        requestId: TEST_REQUEST_ID,
        action: 'reject-all',
        adminNotes: 'Test bulk reject all'
      })
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function testBulkRequiresChanges() {
  console.log('\n=== Testing Bulk Requires Changes ===');
  try {
    const response = await fetch(`${API_BASE}/admin/provider-requests/bulk-action`, {
      method: 'POST',
      headers: ADMIN_HEADERS,
      body: JSON.stringify({
        requestId: TEST_REQUEST_ID,
        action: 'requires-changes-all',
        adminNotes: 'Test bulk requires changes'
      })
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function testMixedActions() {
  console.log('\n=== Testing Mixed Actions ===');
  try {
    const response = await fetch(`${API_BASE}/admin/provider-requests/mixed-action`, {
      method: 'POST',
      headers: ADMIN_HEADERS,
      body: JSON.stringify({
        requestId: TEST_REQUEST_ID,
        serviceActions: [
          { serviceId: 1, status: 'Approved', adminNotes: 'Test approve service 1' },
          { serviceId: 2, status: 'Rejected', adminNotes: 'Test reject service 2' },
          { serviceId: 3, status: 'RequiresChanges', adminNotes: 'Test requires changes service 3' }
        ]
      })
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function testInvalidRequests() {
  console.log('\n=== Testing Invalid Requests ===');
  
  // Test invalid action
  try {
    const response = await fetch(`${API_BASE}/admin/provider-requests/bulk-action`, {
      method: 'POST',
      headers: ADMIN_HEADERS,
      body: JSON.stringify({
        requestId: TEST_REQUEST_ID,
        action: 'invalid-action',
        adminNotes: 'Test invalid action'
      })
    });

    const data = await response.json();
    console.log('Invalid action response:', response.status, data.message);
  } catch (error) {
    console.error('Error:', error.message);
  }

  // Test missing requestId
  try {
    const response = await fetch(`${API_BASE}/admin/provider-requests/bulk-action`, {
      method: 'POST',
      headers: ADMIN_HEADERS,
      body: JSON.stringify({
        action: 'approve-all',
        adminNotes: 'Test missing request ID'
      })
    });

    const data = await response.json();
    console.log('Missing requestId response:', response.status, data.message);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function runTests() {
  console.log('Starting Provider Approval Workflow Tests...');
  console.log('Note: These tests require proper authentication headers');
  
  // Uncomment the tests you want to run
  // await testBulkApproveAll();
  // await testBulkRejectAll();
  // await testBulkRequiresChanges();
  // await testMixedActions();
  await testInvalidRequests();
  
  console.log('\nTests completed!');
}

runTests();
