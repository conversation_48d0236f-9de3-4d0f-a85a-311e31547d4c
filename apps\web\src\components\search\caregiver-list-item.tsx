"use client";
import Image from 'next/image';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, MapPin, DollarSign, ChevronRight } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { cn } from '@/lib/utils';
import { getAvatarUrl } from '@/lib/avatar-utils';
import type { CaregiverSearchResult } from '@repo/types';
import { UserSilhouetteIcon } from '@/components/ui/user-silhouette-icon';

interface CaregiverListItemProps {
  caregiver: CaregiverSearchResult;
  onBookService?: () => void;
}

export function CaregiverListItem({ caregiver, onBookService }: CaregiverListItemProps) {
  const { translate } = useLanguage();
  const canViewProfile = caregiver.serviceIdForLink !== -1;
  const avatarUrl = getAvatarUrl(caregiver.imageUrl);

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <div className="grid grid-cols-1 sm:grid-cols-[auto,1fr,auto] items-center gap-4 p-4">
        {/* Column 1: Image */}
        <div className="relative w-24 h-24 sm:w-28 sm:h-28 shrink-0 bg-muted/50 flex items-center justify-center rounded-lg mx-auto sm:mx-0">
          {avatarUrl ? (
            <Image
              src={avatarUrl}
              alt={caregiver.name}
              fill
              sizes="(max-width: 640px) 96px, 112px"
              className="rounded-lg object-cover"
              data-ai-hint={"portrait person"}
            />
          ) : (
            <UserSilhouetteIcon className="w-16 h-16 text-muted-foreground/50" />
          )}
        </div>

        {/* Column 2: Content */}
        <div className="flex flex-col text-center sm:text-left">
          <div className="flex flex-wrap items-center justify-center sm:justify-start gap-x-3 gap-y-1">
            <h3 className="text-lg font-semibold font-headline">{caregiver.name}</h3>
            <Badge variant="secondary" className="text-xs whitespace-nowrap">
              {caregiver.serviceType}
            </Badge>
          </div>
          <div className="flex items-center justify-center sm:justify-start space-x-1 text-xs text-muted-foreground mt-1">
            <MapPin className="w-3 h-3" />
            <span>{caregiver.location}</span>
          </div>
          <div className="flex items-center justify-center sm:justify-start space-x-3 text-xs mt-2">
            <div className="flex items-center">
              <Star className="w-3 h-3 text-yellow-400 fill-yellow-400 mr-0.5" />
              <span>{caregiver.rating.toFixed(1)} ({caregiver.reviewsCount} {translate(commonTranslations, 'reviewsSuffix')})</span>
            </div>
            <div className="flex items-center text-green-600">
              <DollarSign className="w-3 h-3 mr-0.5" />
              <span className="text-muted-foreground">{caregiver.priceRate}</span>
            </div>
          </div>
          <p className="text-xs text-muted-foreground line-clamp-2 mt-2 hidden md:block">
            {caregiver.description}
          </p>
        </div>

        {/* Column 3: Actions */}
        <div className="sm:pl-4 flex flex-col gap-2 justify-center">
            {canViewProfile ? (
              <>
                <Button asChild size="sm" variant="outline">
                  <Link href={`/profile/${caregiver.id}/${caregiver.serviceIdForLink}`}>
                    {translate(commonTranslations, 'viewProfileButton')}
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </Link>
                </Button>
                {onBookService && (
                  <Button size="sm" onClick={onBookService}>
                    Rezervă
                  </Button>
                )}
              </>
            ) : (
              <Button size="sm" className={cn(!canViewProfile && "opacity-50 cursor-not-allowed")} disabled>
                  {translate(commonTranslations, 'noServiceAvailable')}
              </Button>
            )}
        </div>
      </div>
    </Card>
  );
}
