
'use server';

import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { authOptions } from '@repo/auth'; // Import authOptions
import type { ExtendedNextAuthUser } from '@repo/auth';

const PROTECTED_ROUTES_PREFIXES = ['/dashboard', '/admin', '/register-provider', '/change-password-initial'];
const LOGIN_PATH = '/login';
const PUBLIC_ONLY_ROUTES = [LOGIN_PATH, '/auth/register']; // Routes logged-in users should NOT see

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  const token = await getToken({ req: request, secret: authOptions.secret });
  const user = token as ExtendedNextAuthUser | null;

  const isProtectedRoute = PROTECTED_ROUTES_PREFIXES.some(prefix => pathname.startsWith(prefix));
  const isPublicOnlyRoute = PUBLIC_ONLY_ROUTES.includes(pathname);
  const isAdminRoute = pathname.startsWith('/admin');
  const isDashboardRoute = pathname.startsWith('/dashboard');

  console.log('[Middleware]', {
    pathname,
    hasUser: !!user,
    isAdmin: user?.isAdmin,
    isProtectedRoute,
    isPublicOnlyRoute,
    isAdminRoute,
    isDashboardRoute
  });

  // --- Rule 1: Redirect authenticated users from public-only routes ---
  if (user && isPublicOnlyRoute) {
    // Redirect to their appropriate dashboard
    const redirectUrl = user.isAdmin ? '/admin' : '/dashboard';
    console.log('[Middleware] Redirecting authenticated user from public route to:', redirectUrl);
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  // --- Rule 2: Protect routes for unauthenticated users ---
  if (!user && isProtectedRoute) {
    const loginUrl = new URL(LOGIN_PATH, request.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    console.log('[Middleware] Redirecting unauthenticated user to login');
    return NextResponse.redirect(loginUrl);
  }

  // --- Rule 2.5: CRITICAL - Admin route protection ---
  if (user && isAdminRoute && !user.isAdmin) {
    console.log('[Middleware] Non-admin user attempting admin access - redirecting to dashboard');
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // --- Rule 2.6: CRITICAL - Dashboard route protection for admin users ---
  if (user && isDashboardRoute && user.isAdmin) {
    console.log('[Middleware] Admin user attempting dashboard access - redirecting to admin');
    return NextResponse.redirect(new URL('/admin', request.url));
  }

  // --- Rule 2.7: Dashboard URL backward compatibility redirects ---
  if (user && isDashboardRoute && !user.isAdmin) {
    const redirects: Record<string, string> = {
      '/dashboard/bookings': '/dashboard/client/bookings',
      '/dashboard/settings': '/dashboard/client/settings',
      '/dashboard/settings/address': '/dashboard/client/addresses',
    };

    // Check for exact matches first
    if (redirects[pathname]) {
      console.log('[Middleware] Redirecting legacy dashboard URL:', pathname, '->', redirects[pathname]);
      return NextResponse.redirect(new URL(redirects[pathname], request.url));
    }

    // Chat URLs remain unified - no redirection needed
  }

  // --- Rule 3: Handle forced password change for authenticated users ---
  if (user) {
    const mustChangePassword = user.MustChangePassword ?? false;

    if (mustChangePassword && pathname !== '/change-password-initial') {
        return NextResponse.redirect(new URL('/change-password-initial', request.url));
    }
    if (!mustChangePassword && pathname === '/change-password-initial') {
        const redirectUrl = user.isAdmin ? '/admin' : '/dashboard';
        return NextResponse.redirect(new URL(redirectUrl, request.url));
    }
  }


  // If no rules match, allow the request to proceed.
  return NextResponse.next();
}

export const config = {
  // Apply middleware to all routes except for static assets and API routes.
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$).*)'
  ],
};
