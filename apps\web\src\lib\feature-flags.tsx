"use client";

import { useState, useEffect, createContext, useContext } from 'react';
import React from 'react';

// Feature flag definitions
export interface FeatureFlags {
  // Role toggle feature flags
  roleToggleEnabled: boolean;
  roleToggleHeaderPosition: boolean;
  roleToggleOnboarding: boolean;
  roleToggleAnimations: boolean;
  roleToggleMobileOptimized: boolean;
  
  // Dashboard feature flags
  dashboardNewLayout: boolean;
  dashboardErrorBoundary: boolean;
  dashboardLoadingStates: boolean;
  
  // Analytics feature flags
  roleToggleAnalytics: boolean;
  navigationAnalytics: boolean;
  
  // A/B testing flags
  abTestRoleToggleVariant: 'control' | 'treatment';
  abTestOnboardingFlow: 'control' | 'treatment';
}

// Default feature flag values
const defaultFeatureFlags: FeatureFlags = {
  roleToggleEnabled: true,
  roleToggleHeaderPosition: true,
  roleToggleOnboarding: true,
  roleToggleAnimations: true,
  roleToggleMobileOptimized: true,
  dashboardNewLayout: true,
  dashboardErrorBoundary: true,
  dashboardLoadingStates: true,
  roleToggleAnalytics: true,
  navigationAnalytics: true,
  abTestRoleToggleVariant: 'treatment',
  abTestOnboardingFlow: 'treatment',
};

// Feature flag context
const FeatureFlagContext = createContext<FeatureFlags>(defaultFeatureFlags);

// Feature flag provider
export function FeatureFlagProvider({ children }: { children: React.ReactNode }) {
  const [featureFlags, setFeatureFlags] = useState<FeatureFlags>(defaultFeatureFlags);

  useEffect(() => {
    // Load feature flags from various sources
    loadFeatureFlags().then(setFeatureFlags);
  }, []);

  return (
    <FeatureFlagContext.Provider value={featureFlags}>
      {children}
    </FeatureFlagContext.Provider>
  );
}

// Hook to use feature flags
export function useFeatureFlags(): FeatureFlags {
  const context = useContext(FeatureFlagContext);
  if (!context) {
    throw new Error('useFeatureFlags must be used within a FeatureFlagProvider');
  }
  return context;
}

// Hook to check a specific feature flag
export function useFeatureFlag(flagName: keyof FeatureFlags): boolean {
  const flags = useFeatureFlags();
  return flags[flagName] as boolean;
}

// Load feature flags from multiple sources
async function loadFeatureFlags(): Promise<FeatureFlags> {
  const flags = { ...defaultFeatureFlags };

  try {
    // 1. Load from environment variables
    const envFlags = loadFromEnvironment();
    Object.assign(flags, envFlags);

    // 2. Load from localStorage (for development/testing)
    const localFlags = loadFromLocalStorage();
    Object.assign(flags, localFlags);

    // 3. Load from remote config (if available)
    const remoteFlags = await loadFromRemoteConfig();
    Object.assign(flags, remoteFlags);

    // 4. Apply user-specific overrides
    const userFlags = await loadUserSpecificFlags();
    Object.assign(flags, userFlags);

  } catch (error) {
    console.warn('Error loading feature flags, using defaults:', error);
  }

  return flags;
}

// Load feature flags from environment variables
function loadFromEnvironment(): Partial<FeatureFlags> {
  const flags: Partial<FeatureFlags> = {};

  // Check for environment-based feature flags
  if (typeof window !== 'undefined') {
    // Client-side environment checks
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isProduction = process.env.NODE_ENV === 'production';

    // Enable all features in development
    if (isDevelopment) {
      Object.keys(defaultFeatureFlags).forEach(key => {
        if (key.startsWith('roleToggle') || key.startsWith('dashboard')) {
          (flags as any)[key] = true;
        }
      });
    }

    // Production overrides
    if (isProduction) {
      // More conservative defaults for production
      flags.roleToggleAnimations = true;
      flags.dashboardErrorBoundary = true;
    }
  }

  return flags;
}

// Load feature flags from localStorage (for testing)
function loadFromLocalStorage(): Partial<FeatureFlags> {
  if (typeof window === 'undefined') return {};

  try {
    const stored = localStorage.getItem('bonami-feature-flags');
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn('Error loading feature flags from localStorage:', error);
  }

  return {};
}

// Load feature flags from remote configuration
async function loadFromRemoteConfig(): Promise<Partial<FeatureFlags>> {
  try {
    // In a real implementation, this would fetch from your feature flag service
    // For now, we'll simulate with a simple API call
    const response = await fetch('/api/feature-flags', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });

    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    // Silently fail - feature flags should be resilient
    console.debug('Remote feature flags not available:', error);
  }

  return {};
}

// Load user-specific feature flag overrides
async function loadUserSpecificFlags(): Promise<Partial<FeatureFlags>> {
  try {
    // Check if user is in beta group, has specific role, etc.
    const userFlags: Partial<FeatureFlags> = {};

    // Example: Enable beta features for specific users
    const userId = getUserId();
    if (userId && isBetaUser(userId)) {
      userFlags.roleToggleAnimations = true;
      userFlags.dashboardNewLayout = true;
    }

    // Example: A/B testing assignment
    const abTestVariant = getABTestVariant(userId);
    userFlags.abTestRoleToggleVariant = abTestVariant;

    return userFlags;
  } catch (error) {
    console.debug('Error loading user-specific flags:', error);
    return {};
  }
}

// Utility functions
function getUserId(): string | null {
  if (typeof window === 'undefined') return null;
  
  try {
    // Get user ID from session, localStorage, or other source
    const session = localStorage.getItem('bonami-session');
    if (session) {
      const parsed = JSON.parse(session);
      return parsed.user?.id || null;
    }
  } catch (error) {
    console.debug('Error getting user ID:', error);
  }
  
  return null;
}

function isBetaUser(userId: string): boolean {
  // Simple hash-based beta user detection
  const hash = hashString(userId);
  return hash % 10 < 2; // 20% of users are beta users
}

function getABTestVariant(userId: string | null): 'control' | 'treatment' {
  if (!userId) return 'control';
  
  // Consistent A/B test assignment based on user ID
  const hash = hashString(userId);
  return hash % 2 === 0 ? 'control' : 'treatment';
}

function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

// Development utilities
export function setFeatureFlag(flagName: keyof FeatureFlags, value: boolean | string) {
  if (typeof window === 'undefined') return;

  try {
    const stored = localStorage.getItem('bonami-feature-flags');
    const flags = stored ? JSON.parse(stored) : {};
    flags[flagName] = value;
    localStorage.setItem('bonami-feature-flags', JSON.stringify(flags));
    
    // Reload the page to apply changes
    window.location.reload();
  } catch (error) {
    console.error('Error setting feature flag:', error);
  }
}

export function clearFeatureFlags() {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem('bonami-feature-flags');
  window.location.reload();
}

// Feature flag debugging component (development only)
export function FeatureFlagDebugger() {
  const flags = useFeatureFlags();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-black text-white p-4 rounded-lg text-xs max-w-sm">
      <h4 className="font-bold mb-2">Feature Flags</h4>
      <div className="space-y-1 max-h-40 overflow-y-auto">
        {Object.entries(flags).map(([key, value]) => (
          <div key={key} className="flex justify-between">
            <span className="truncate mr-2">{key}:</span>
            <span className={value ? 'text-green-400' : 'text-red-400'}>
              {String(value)}
            </span>
          </div>
        ))}
      </div>
      <button
        onClick={clearFeatureFlags}
        className="mt-2 text-xs bg-red-600 px-2 py-1 rounded"
      >
        Reset Flags
      </button>
    </div>
  );
}
