"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Users, Clock, Calendar, CalendarDays } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const statsTranslations = {
  activeUsers: { ro: "Utilizatori activi", ru: "Активные пользователи", en: "Active Users" },
  activeToday: { ro: "Activi astăzi", ru: "Активные сегодня", en: "Active Today" },
  activeThisWeek: { ro: "Activi săptămâna aceasta", ru: "Активные на этой неделе", en: "Active This Week" },
  activeThisMonth: { ro: "Activi luna aceasta", ru: "Активные в этом месяце", en: "Active This Month" },
  loadingStats: { ro: "Se încarcă statisticile...", ru: "Загрузка статистики...", en: "Loading stats..." },
  errorLoadingStats: { ro: "Eroare la încărcarea statisticilor", ru: "Ошибка загрузки статистики", en: "Error loading stats" },
  users: { ro: "utilizatori", ru: "пользователей", en: "users" },
};

interface ActiveUsersData {
  today: number;
  thisWeek: number;
  thisMonth: number;
}

interface ActiveUsersStatsProps {
  refreshTrigger?: Date;
}

export function ActiveUsersStats({ refreshTrigger }: ActiveUsersStatsProps) {
  const { translate } = useLanguage();
  const [data, setData] = useState<ActiveUsersData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch('/api/proxy/admin/analytics/active-users');
        if (!response.ok) {
          throw new Error('Failed to fetch active users data');
        }
        
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        console.error('Error fetching active users data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {translate(statsTranslations, 'activeUsers')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-5 h-5 animate-spin" />
              {translate(statsTranslations, 'loadingStats')}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {translate(statsTranslations, 'activeUsers')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(statsTranslations, 'errorLoadingStats')}</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return null;
  }

  const stats = [
    {
      title: translate(statsTranslations, 'activeToday'),
      value: data.today,
      icon: <Clock className="w-5 h-5 text-blue-500" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950',
    },
    {
      title: translate(statsTranslations, 'activeThisWeek'),
      value: data.thisWeek,
      icon: <Calendar className="w-5 h-5 text-green-500" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-950',
    },
    {
      title: translate(statsTranslations, 'activeThisMonth'),
      value: data.thisMonth,
      icon: <CalendarDays className="w-5 h-5 text-purple-500" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-950',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {stats.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </p>
                <p className={`text-2xl font-bold ${stat.color}`}>
                  {stat.value}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {translate(statsTranslations, 'users')}
                </p>
              </div>
              <div className={`p-3 rounded-full ${stat.bgColor}`}>
                {stat.icon}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
