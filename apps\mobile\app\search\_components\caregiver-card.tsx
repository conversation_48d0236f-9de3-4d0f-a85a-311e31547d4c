
"use client";
import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { Button } from '@/components/ui/button-mobile';
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';
import type { CaregiverSearchResult } from '@repo/types';
import { MaterialIcons } from '@expo/vector-icons';

const caregiverCardTranslations = {
  reviewsSuffix: { ro: "recenzii", ru: "отзывов", en: "reviews" },
  viewProfileButton: { ro: "Vezi Profil", ru: "Смотреть профиль", en: "View Profile" },
  noServiceAvailable: { ro: "Serviciu Indisponibil", ru: "Услуга недоступна", en: "Service Unavailable" }
};

interface CaregiverCardProps {
  caregiver: CaregiverSearchResult;
}

export function CaregiverCard({ caregiver }: CaregiverCardProps) {
  const { translate } = useLanguage();
  const canViewProfile = caregiver.serviceIdForLink !== -1;

  return (
    <View style={styles.card}>
      <Image source={{ uri: caregiver.imageUrl || 'https://placehold.co/300x200.png' }} style={styles.image} />
      <View style={styles.content}>
        <Text style={styles.name}>{caregiver.name}</Text>
        <Text style={styles.serviceType}>{caregiver.serviceType}</Text>
        <View style={styles.locationContainer}>
          <MaterialIcons name="location-on" size={12} color="gray" />
          <Text style={styles.location}>{caregiver.location}</Text>
        </View>
        <View style={styles.ratingContainer}>
          <MaterialIcons name="star" size={12} color="gold" />
          <Text style={styles.rating}>{caregiver.rating.toFixed(1)} ({caregiver.reviewsCount} {translate(caregiverCardTranslations, 'reviewsSuffix')})</Text>
        </View>
        <Text style={styles.description}>{caregiver.description}</Text>
        <Button title={translate(caregiverCardTranslations, 'viewProfileButton')} disabled={!canViewProfile} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
    card: {
        flex: 1,
        margin: 8,
        backgroundColor: 'white',
        borderRadius: 8,
        overflow: 'hidden',
    },
    image: {
        width: '100%',
        height: 150,
    },
    content: {
        padding: 8,
    },
    name: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    serviceType: {
        fontSize: 12,
        color: 'gray',
    },
    locationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    location: {
        fontSize: 12,
        color: 'gray',
        marginLeft: 4,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rating: {
        fontSize: 12,
        marginLeft: 4,
    },
    description: {
        fontSize: 12,
        marginVertical: 8,
    }
})
