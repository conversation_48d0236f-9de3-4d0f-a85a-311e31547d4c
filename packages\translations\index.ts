

// src/lib/translations/common.ts

export const commonTranslations = {
  myServices: { ro: "Serviciile Mele", ru: "Мои услуги", en: "My Services" },
  myReviews: { ro: "Recenziile Mele", ru: "Мои отзывы", en: "My Reviews" },
  myServicesPageTitle: { ro: "Serviciile Mele", ru: "Мои услуги", en: "My Services" },
  myServicesPageDescription: { ro: "Gestionează toate serviciile pe care le oferi.", ru: "Управляйте всеми предлагаемыми вами услугами.", en: "Manage all the services you offer." },
  addNewServiceButton: { ro: "Adaugă Serviciu Nou", ru: "Добавить новую услугу", en: "Add New Service" },
  addServiceDisabledTooltip: { ro: "Ai adăugat deja toate serviciile disponibile. Pentru a adăuga un serviciu nou, te rugăm să ștergi un serviciu existent.", ru: "Вы уже добавили все доступные услуги. Чтобы добавить новую, удалите существующую.", en: "You have already added all available services. To add a new one, please delete an existing service." },
  currentServicesTitle: { ro: "Serviciile Tale Curente", ru: "Ваши текущие услуги", en: "Your Current Services" },
  noServicesMessage: { ro: "Nu ai niciun serviciu adăugat încă. Apasă pe butonul de mai sus pentru a adăuga primul tău serviciu.", ru: "У вас еще нет добавленных услуг. Нажмите кнопку выше, чтобы добавить свою первую услугу.", en: "You haven't added any services yet. Click the button above to add your first service." },
  statusLabel: { ro: "Status", ru: "Статус", en: "Status" },
  editButton: { ro: "Editează", ru: "Редактировать", en: "Edit" },
  deleteButton: { ro: "Șterge", ru: "Удалить", en: "Delete" },
  deleteDialogTitle: { ro: "Ești sigur?", ru: "Вы уверены?", en: "Are you sure?" },
  deleteDialogDescription: { ro: "Această acțiune nu poate fi anulată. Serviciul \"{serviceName}\" va fi eliminat permanent.", ru: "Это действие необратимо. Услуга \"{serviceName}\" будет удалена навсегда.", en: "This action cannot be undone. The service \"{serviceName}\" will be permanently deleted." },
  toastServiceDeletedTitle: { ro: "Serviciu Șters", ru: "Услуга удалена", en: "Service Deleted" },
  toastServiceDeletedDescription: { ro: "Serviciul a fost șters cu succes.", ru: "Услуга была успешно удалена.", en: "The service has been successfully deleted." },
  toastStatusUpdatedTitle: { ro: "Status Actualizat", ru: "Статус обновлен", en: "Status Updated" },
  toastStatusUpdatedDescription: { ro: "Statusul serviciului a fost actualizat.", ru: "Статус услуги обновлен.", en: "The service status has been updated." },
  errorFetchingServices: { ro: "Eroare la preluarea serviciilor.", ru: "Ошибка при загрузке услуг.", en: "Error fetching services." },
  errorFetchingCategories: { ro: "Eroare la preluarea categoriilor.", ru: "Ошибка при загрузке категорий.", en: "Error fetching categories." },

  // Service Categories (matching nameKey from mockServiceCategories)
  categoryNanny: { ro: "Bonă", ru: "Няня", en: "Nanny" },
  categoryElderCare: { ro: "Îngrijire bătrâni", ru: "Уход за пожилыми", en: "Elder Care" },
  categoryCleaning: { ro: "Curățenie", ru: "Уборка", en: "Cleaning" },
  categoryTutoring: { ro: "Educație & After-school", ru: "Образование и Продленка", en: "Education & After-school" },
  categoryCooking: { ro: "Gătit / Mâncare la domiciliu", ru: "Приготовление пищи / Еда на дом", en: "Cooking / Home Meals" },
  
  // Generic service labels
  serviceAll: { ro: "Toate Serviciile", ru: "Все услуги", en: "All Services" },

  // Locations (matching translationKey from allMoldovaLocations)
  locAllMoldova: { ro: "Toată Moldova", ru: "Вся Молдова", en: "All Moldova" },
  
  locChisinauMunicipalityLabel: { ro: "Municipiul Chișinău", ru: "Муниципий Кишинев", en: "Chisinau Municipality" },
  locChisinauMunicipalityScope: { ro: "Municipiul Chișinău", ru: "Муниципий Кишинев", en: "Chisinau Municipality" },
  locSectorsLabel: { ro: "Sectoare", ru: "Сектора", en: "Sectors" },
  locSuburbsLabel: { ro: "Suburbii / Comune", ru: "Пригороды / Коммуны", en: "Suburbs / Communes" },
  otherMajorCitiesLabel: { ro: "Alte Orașe / Raioane", ru: "Другие города / Районы", en: "Other Cities / Regions" },

  locChisinauCity: { ro: "Chișinău", ru: "Кишинев", en: "Chisinau" },
  locChisinauBotanica: { ro: "Botanica", ru: "Ботаника", en: "Botanica" },
  locChisinauBuiucani: { ro: "Buiucani", ru: "Буюканы", en: "Buiucani" },
  locChisinauCentru: { ro: "Centru", ru: "Центр", en: "Centru" },
  locChisinauCiocana: { ro: "Ciocana", ru: "Чокана", en: "Ciocana" },
  locChisinauRascani: { ro: "Râșcani", ru: "Рышкановка", en: "Rascani" },
  locChisinauTelecentru: { ro: "Telecentru", ru: "Телецентр", en: "Telecentru" },
  
  locCodru: { ro: "Codru", ru: "Кодру", en: "Codru" },
  locCricova: { ro: "Cricova", ru: "Крикова", en: "Cricova" },
  locDurlesti: { ro: "Durlești", ru: "Дурлешты", en: "Durlesti" },
  locGhidighici: { ro: "Ghidighici", ru: "Гидигич", en: "Ghidighici" },
  locSingera: { ro: "Sîngera", ru: "Сынжера", en: "Singera" },
  locStauceni: { ro: "Stăuceni", ru: "Ставчены", en: "Stauceni" },
  locVadulLuiVoda: { ro: "Vadul lui Vodă", ru: "Вадул-луй-Водэ", en: "Vadul lui Voda" },
  locVatra: { ro: "Vatra", ru: "Ватра", en: "Vatra" },
  locBacioi: { ro: "Băcioi", ru: "Бачой", en: "Bacioi" },
  locBubuieci: { ro: "Bubuieci", ru: "Бубуечь", en: "Bubuieci" },
  locGratiesti: { ro: "Grătiești", ru: "Гратиешты", en: "Gratiesti" },
  locTruseni: { ro: "Trușeni", ru: "Трушены", en: "Truseni" },

  locBalti: { ro: "Bălți", ru: "Бельцы", en: "Balti" },
  locTiraspol: { ro: "Tiraspol", ru: "Тирасполь", en: "Tiraspol" },
  locBender: { ro: "Bender (Tighina)", ru: "Бендеры (Тигина)", en: "Bender (Tighina)" },
  locRibnita: { ro: "Ribnita", ru: "Рыбница", en: "Ribnita" },
  locCahul: { ro: "Cahul", ru: "Кагул", en: "Cahul" },
  locUngheni: { ro: "Ungheni", ru: "Унгены", en: "Ungheni" },
  locSoroca: { ro: "Soroca", ru: "Сороки", en: "Soroca" },
  locOrhei: { ro: "Orhei", ru: "Оргеев", en: "Orhei" },
  locComrat: { ro: "Comrat", ru: "Комрат", en: "Comrat" },
  locDubasari: { ro: "Dubăsari", ru: "Дубоссары", en: "Dubasari" },
  locStraseni: { ro: "Strășeni", ru: "Страшены", en: "Straseni" },
  locCauseni: { ro: "Căușeni", ru: "Каушаны", en: "Causeni" },
  locDrochia: { ro: "Drochia", ru: "Дрокия", en: "Drochia" },
  locEdinet: { ro: "Edineț", ru: "Единцы", en: "Edinet" },
  locHincesti: { ro: "Hîncești", ru: "Хынчешты", en: "Hincesti" },
  locIaloveni: { ro: "Ialoveni", ru: "Яловены", en: "Ialoveni" },
  locFloresti: { ro: "Florești", ru: "Флорешты", en: "Floresti" },
  locCimislia: { ro: "Cimișlia", ru: "Чимишлия", en: "Cimislia" },
  locRezina: { ro: "Rezina", ru: "Резина", en: "Rezina" },
  locAneniiNoi: { ro: "Anenii Noi", ru: "Анений Ной", en: "Anenii Noi" },
  locBasarabeasca: { ro: "Basarabeasca", ru: "Басарабяска", en: "Basarabeasca" },
  locBriceni: { ro: "Briceni", ru: "Бричаны", en: "Briceni" },
  locCantemir: { ro: "Cantemir", ru: "Кантемир", en: "Cantemir" },
  locCalarasi: { ro: "Călărași", ru: "Калараш", en: "Calarasi" },
  locCeadirLunga: { ro: "Ceadîr-Lunga", ru: "Чадыр-Лунга", en: "Ceadir-Lunga" },
  locCriuleni: { ro: "Criuleni", ru: "Криуляны", en: "Criuleni" },
  locDonduseni: { ro: "Dondușeni", ru: "Дондюшаны", en: "Donduseni" },
  locFalesti: { ro: "Fălești", ru: "Фалешты", en: "Falesti" },
  locGlodeni: { ro: "Glodeni", ru: "Глодяны", en: "Glodeni" },
  locLeova: { ro: "Leova", ru: "Леова", en: "Leova" },
  locNisporeni: { ro: "Nisporeni", ru: "Ниспорены", en: "Nisporeni" },
  locOcnita: { ro: "Ocnița", ru: "Окница", en: "Ocnita" },
  locSingerei: { ro: "Sîngerei", ru: "Сынжерей", en: "Singerei" },
  locSoldanesti: { ro: "Șoldănești", ru: "Шолданешты", en: "Soldanesti" },
  locStefanVoda: { ro: "Ștefan Vodă", ru: "Штефан-Водэ", en: "Stefan Voda" },
  locTaraclia: { ro: "Taraclia", ru: "Тараклия", en: "Taraclia" },
  locTelenesti: { ro: "Telenești", ru: "Теленешты", en: "Telenesti" },
  locVulcanesti: { ro: "Vulcănești", ru: "Вулканешты", en: "Vulcanesti" },
  
  allStatuses: { ro: "Toate Statusurile", ru: "Все статусы", en: "All Statuses" },
  statusActiv: { ro: "Activ", ru: "Активно", en: "Active" },
  statusInactiv: { ro: "Inactiv", ru: "Неактивно", en: "Inactive" },
  statusPendingReview: { ro: "În Așteptarea Revizuirii", ru: "Ожидает проверки", en: "Pending Review" },
  statusRejected: { ro: "Respins", ru: "Отклонено", en: "Rejected" },

  backToSearchButton: { ro: "Înapoi la Căutare", ru: "Назад к поиску", en: "Back to Search" },
  backToMyServices: { ro: "Înapoi la Serviciile Mele", ru: "Назад к Моим Услугам", en: "Back to My Services" },
  generalExperienceLabel: { ro: "Experiență Generală", ru: "Общий опыт", en: "General Experience" },
  yearSingular: { ro: "an", ru: "год", en: "year" },
  yearsSuffix: { ro: "ani", ru: "лет", en: "years" },
  generalAvailabilityLabel: { ro: "Disponibilitate Generală", ru: "Общая доступность", en: "General Availability" },
  availabilityLabel: { ro: "Disponibilitate", ru: "Доступность", en: "Availability" },
  availabilityWeekdays: { ro: "Zile lucrătoare", ru: "Будние дни", en: "Weekdays" },
  availabilityWeekends: { ro: "Weekenduri", ru: "Выходные", en: "Weekends" },
  availabilityEvenings: { ro: "Seri", ru: "Вечера", en: "Evenings" },
  availabilityNotSpecified: { ro: "Nespecificată", ru: "Не указана", en: "Not specified" },
  sendMessageButton: { ro: "Trimite Mesaj", ru: "Отправить сообщение", en: "Send Message" },
  aboutProviderLabel: { ro: "Despre", ru: "О", en: "About" },
  servicesOfferedByLabel: { ro: "Servicii Oferite de", ru: "Услуги, предлагаемые", en: "Services Offered by" },
  serviceDetailsLabel: { ro: "Detalii Serviciu", ru: "Детали Услуги", en: "Service Details" },
  categoryLabel: { ro: "Categorie", ru: "Категория", en: "Category" },
  priceLabel: { ro: "Preț", ru: "Цена", en: "Price" },
  pricePerHourLabel: { ro: "Preț/oră (MDL)", ru: "Цена/час (MDL)", en: "Price/hour (MDL)" },
  pricePerDayLabel: { ro: "Preț/zi (MDL)", ru: "Цена/день (MDL)", en: "Price/day (MDL)" },
  priceContactFor: { ro: "Contactați pentru preț", ru: "Свяжитесь для уточнения цены", en: "Contact for price" },
  operationalDetailsLabel: { ro: "Detalii Operaționale Serviciu", ru: "Операционные детали услуги", en: "Service Operational Details" },
  locationLabel: { ro: "Locație", ru: "Местоположение", en: "Location" },
  locationPlaceholder: { ro: "Selectează locație", ru: "Выберите местоположение", en: "Select location" },
  serviceLocationLabel: { ro: "Locația Serviciului", ru: "Местоположение Услуги", en: "Service Location" },
  serviceExperienceLabel: { ro: "Experiență în acest serviciu", ru: "Опыт в данной услуге", en: "Experience in this service" },
  serviceAvailabilityLabel: { ro: "Disponibilitate pentru acest serviciu", ru: "Доступность для этой услуги", en: "Availability for this service" },
  categorySpecificDetailsLabel: { ro: "Detalii Specifice Serviciului", ru: "Детали, специфичные для услуги", en: "Service Specific Details" },
  
  childcareTitle: { ro: "Detalii Serviciu Bonă", ru: "Детали услуги няни", en: "Nanny Service Details" },
  childcarePreferredAgeLabel: { ro: "Vârstă preferată copii", ru: "Предпочтительный возраст детей", en: "Preferred children's age" },
  childcareAge0_2: { ro: "0–2 ani", ru: "0–2 года", en: "0–2 years" },
  childcareAge3_6: { ro: "3–6 ani", ru: "3–6 лет", en: "3–6 years" },
  childcareAge7_plus: { ro: "7+ ani", ru: "7+ лет", en: "7+ years" },
  childcareAvailabilityTypeLabel: { ro: "Tip disponibilitate bonă", ru: "Тип доступности няни", en: "Nanny availability type" },
  childcareAvailabilityFullTime: { ro: "Full-time", ru: "Полный рабочий день", en: "Full-time" },
  childcareAvailabilityPartTime: { ro: "Part-time", ru: "Частичная занятость", en: "Part-time" },
  childcareFirstAidLabel: { ro: "Cunoștințe prim ajutor", ru: "Знание первой помощи", en: "First aid knowledge" },
  childcareSchoolPickupLabel: { ro: "Preluare de la școală/grădiniță", ru: "Забираю из школы/сада", en: "School/kindergarten pickup" },
  childcareActivitiesOfferedLabel: { ro: "Activități oferite copiilor", ru: "Предлагаемые мероприятия для детей", en: "Activities offered for children" },
  childcareActivityWalks: { ro: "Plimbări afară", ru: "Прогулки на свежем воздухе", en: "Outdoor walks" },
  childcareActivityGames: { ro: "Jocuri educative/recreative", ru: "Развивающие/развлекательные игры", en: "Educational/recreational games" },
  childcareActivityFeeding: { ro: "Asistență la hrănire", ru: "Помощь в кормлении", en: "Feeding assistance" },
  childcareActivitySleep: { ro: "Supervizare somn", ru: "Надзор за сном", en: "Sleep supervision" },

  elderCareTitle: { ro: "Detalii Serviciu Îngrijire Bătrâni", ru: "Детали услуги по уходу за пожилыми", en: "Elder Care Service Details" },
  elderCareTypeLabel: { ro: "Tip îngrijire vârstnici", ru: "Тип ухода за пожилыми", en: "Type of elder care" },
  elderCareTypeMobil: { ro: "Persoane mobile", ru: "Мобильные лица", en: "Mobile individuals" },
  elderCareTypePartialImobilizat: { ro: "Persoane parțial imobilizate", ru: "Частично иммобилизованные лица", en: "Partially immobilized individuals" },
  elderCareTypeCompletImobilizat: { ro: "Persoane complet imobilizate", ru: "Полностью иммобилизованные лица", en: "Completely immobilized individuals" },
  elderCareMedicalKnowledgeLabel: { ro: "Cunoștințe medicale", ru: "Медицинские знания", en: "Medical knowledge" },
  elderCareMedicalKnowledgeBasicLabel: { ro: "Cunoștințe medicale de bază", ru: "Базовые медицинские знания", en: "Basic medical knowledge" },
  elderCareMedicalKnowledgeAdvancedLabel: { ro: "Cunoștințe medicale avansate", ru: "Продвинутые медицинские знания", en: "Advanced medical knowledge" },
  elderCareMedicationAdminLabel: { ro: "Administrare medicamente", ru: "Прием лекарств", en: "Medication administration" },
  elderCareDrivingLicenseLabel: { ro: "Permis de conducere", ru: "Водительские права", en: "Driving license" },
  elderCareActivitiesLabel: { ro: "Activități suplimentare oferite", ru: "Предлагаемые дополнительные мероприятия", en: "Additional activities offered" },
  elderCareActivityCooking: { ro: "Gătit pentru vârstnic", ru: "Приготовление пищи для пожилого", en: "Cooking for the elderly" },
  elderCareActivityCleaningLight: { ro: "Curățenie ușoară în spațiul vârstnicului", ru: "Легкая уборка в помещении пожилого", en: "Light cleaning in the elder's space" },
  elderCareActivityCompanionship: { ro: "Companie și conversație", ru: "Компания и беседы", en: "Companionship and conversation" },

  cleaningTitle: { ro: "Detalii Serviciu Curățenie", ru: "Детали услуги по уборке", en: "Cleaning Service Details" },
  cleaningPropertyTypeLabel: { ro: "Tip proprietăți deservite", ru: "Тип обслуживаемой недвижимости", en: "Property types served" },
  cleaningPropertyTypeApartments: { ro: "Apartamente", ru: "Квартиры", en: "Apartments" },
  cleaningPropertyTypeHouses: { ro: "Case/Vile", ru: "Дома/Виллы", en: "Houses/Villas" },
  cleaningPropertyTypeOffices: { ro: "Birouri/Spații comerciale", ru: "Офисы/Коммерческие помещения", en: "Offices/Commercial spaces" },
  cleaningOwnProductsLabel: { ro: "Utilizează produse proprii de curățenie", ru: "Использует собственные чистящие средства", en: "Uses own cleaning products" },
  cleaningTypesOfferedLabel: { ro: "Tipuri de curățenie efectuate", ru: "Выполняемые типы уборки", en: "Types of cleaning performed" },
  cleaningTypeGeneral: { ro: "Curățenie generală", ru: "Генеральная уборка", en: "General cleaning" },
  cleaningTypePostRenovation: { ro: "Curățenie după renovare", ru: "Уборка после ремонта", en: "Post-renovation cleaning" },
  cleaningTypeOccasional: { ro: "Curățenie ocazională/de întreținere", ru: "Разовая/поддерживающая уборка", en: "Occasional/maintenance cleaning" },
  cleaningTypeRegular: { ro: "Curățenie periodică (abonament)", ru: "Периодическая уборка (абонемент)", en: "Regular cleaning (subscription)" },
  cleaningExtraServicesLabel: { ro: "Servicii suplimentare de curățenie", ru: "Дополнительные услуги по уборке", en: "Additional cleaning services" },
  cleaningExtraIroning: { ro: "Călcat rufe", ru: "Глажка белья", en: "Ironing" },
  cleaningExtraWindows: { ro: "Spălat geamuri", ru: "Мытье окон", en: "Window washing" },
  cleaningExtraDisinfection: { ro: "Dezinfectare suprafețe", ru: "Дезинфекция поверхностей", en: "Surface disinfection" },

  cookingTitle: { ro: "Detalii Serviciu Gătit", ru: "Детали услуги по приготовлению пищи", en: "Cooking Service Details" },
  cookingCuisineTypeLabel: { ro: "Tip de bucătărie preparată", ru: "Тип приготовляемой кухни", en: "Type of cuisine prepared" },
  cookingCuisineTypeTraditional: { ro: "Tradițională Moldovenească/Românească", ru: "Традиционная молдавская/румынская", en: "Traditional Moldovan/Romanian" },
  cookingCuisineTypeVegetarian: { ro: "Vegetariană/Vegană", ru: "Вегетарианская/Веганская", en: "Vegetarian/Vegan" },
  cookingCuisineTypeKids: { ro: "Meniu pentru copii", ru: "Детское меню", en: "Children's menu" },
  cookingCuisineTypeDiet: { ro: "Dietetică/Restricții alimentare", ru: "Диетическая/Пищевые ограничения", en: "Dietetic/Food restrictions" },
  cookingOffersDeliveryLabel: { ro: "Oferă livrare la domiciliu", ru: "Предлагает доставку на дом", en: "Offers home delivery" },
  cookingLocationLabel: { ro: "Locul preparării mâncării", ru: "Место приготовления пищи", en: "Cooking location" },
  cookingLocationClient: { ro: "La domiciliul clientului", ru: "На дому у клиента", en: "At client's home" },
  cookingLocationOwn: { ro: "La domiciliul propriu (cu livrare/ridicare)", ru: "На собственном дому (с доставкой/самовывозом)", en: "At own home (with delivery/pickup)" },
  cookingOwnProductsLabel: { ro: "Achiziționează produsele necesare", ru: "Закупает необходимые продукты", en: "Purchases necessary ingredients" },
  cookingMinPortionsLabel: { ro: "Număr minim de porții/comanda", ru: "Минимальное количество порций/заказ", en: "Minimum portions/order" },
  cookingMinPortionsPlaceholder: { ro: "Ex: 2", ru: "Пример: 2", en: "Ex: 2" },
  cookingWeeklySubscriptionLabel: { ro: "Posibilitate abonament săptămânal/lunar", ru: "Возможность недельной/месячной подписки", en: "Weekly/monthly subscription available" },
  cookingPricePerMealLabel: { ro: "Preț/masă sau porție (MDL)", ru: "Цена/блюдо или порция (MDL)", en: "Price/meal or portion (MDL)" },
  cookingPricePerMealPlaceholder: { ro: "Ex: 75", ru: "Пример: 75", en: "Ex: 75" },
  cookingMealDetailsLabel: { ro: "Detalii masă/porție", ru: "Детали блюда/порции", en: "Meal/portion details" },
  cookingMealDetailsPlaceholder: { ro: "Ex: Include fel principal și garnitură", ru: "Пример: Включает основное блюдо и гарнир", en: "Ex: Includes main course and side dish" },
  cookingLocationLabelDetails: { ro: "Loc de gătit (specific acestui serviciu)", ru: "Место приготовления (для этой услуги)", en: "Cooking location (for this service)" },

  tutoringTitle: { ro: "Detalii Serviciu Meditații", ru: "Детали услуги репетиторства", en: "Tutoring Service Details" },
  tutoringServicesOfferedLabel: { ro: "Servicii educaționale", ru: "Образовательные услуги", en: "Educational services" },
  tutoringServiceAfterSchool: { ro: "Program after-school/supraveghere teme", ru: "Программа продленного дня/надзор за домашними заданиями", en: "After-school program/homework supervision" },
  tutoringServiceHomeworkHelp: { ro: "Ajutor specific la teme", ru: "Конкретная помощь с домашними заданиями", en: "Specific homework help" },
  tutoringServiceIndividualLessons: { ro: "Lecții individuale/Meditații", ru: "Индивидуальные уроки/Репетиторство", en: "Individual lessons/Tutoring" },
  tutoringGradesCoveredLabel: { ro: "Nivel/Clase predate", ru: "Уровень/Преподаваемые классы", en: "Level/Grades taught" },
  tutoringGrades_1_4: { ro: "Clasele primare (I-IV)", ru: "Начальные классы (I-IV)", en: "Primary grades (I-IV)" },
  tutoringGrades_5_8: { ro: "Gimnaziu (V-VIII/IX)", ru: "Гимназия (V-VIII/IX)", en: "Middle school (V-VIII/IX)" },
  tutoringGrades_9_12: { ro: "Liceu (IX-XII)", ru: "Лицей (IX-XII)", en: "High school (IX-XII)" },
  tutoringSubjectsTaughtLabel: { ro: "Materii predate", ru: "Преподаваемые предметы", en: "Subjects taught" },
  tutoringSubjectRomanian: { ro: "Limba și Literatura Română", ru: "Румынский язык и литература", en: "Romanian Language and Literature" },
  tutoringSubjectMath: { ro: "Matematică", ru: "Математика", en: "Mathematics" },
  tutoringSubjectEnglish: { ro: "Limba Engleză", ru: "Английский язык", en: "English Language" },
  tutoringSubjectOtherLabel: { ro: "Alte materii (specificați)", ru: "Другие предметы (укажите)", en: "Other subjects (specify)" },
  tutoringSubjectOtherPlaceholder: { ro: "Specificați", ru: "Укажите", en: "Specify" },
  tutoringFormatLabel: { ro: "Formatul lecțiilor", ru: "Формат уроков", en: "Lesson format" },
  tutoringFormatOnline: { ro: "Online (videoconferință)", ru: "Онлайн (видеоконференция)", en: "Online (video conference)" },
  tutoringFormatOwnHome: { ro: "La domiciliul meditatorului", ru: "На дому у репетитора", en: "At tutor's home" },
  tutoringFormatChildHome: { ro: "La domiciliul copilului", ru: "У ребенка дома", en: "At child's home" },
  tutoringExtraActivitiesLabel: { ro: "Activități extra (program after-school)", ru: "Дополнительные занятия (программа продленного дня)", en: "Extra activities (after-school program)" },
  tutoringExtraGames: { ro: "Jocuri educative", ru: "Развивающие игры", en: "Educational games" },
  tutoringExtraSnack: { ro: "Gustare (dacă se oferă)", ru: "Перекус (если предлагается)", en: "Snack (if offered)" },
  tutoringExtraTransport: { ro: "Transport de la/la școală (dacă se oferă)", ru: "Транспорт из/в школу (если предлагается)", en: "Transport from/to school (if offered)" },
  tutoringExtraSupervisedHomework: { ro: "Asistență supravegheată la teme", ru: "Надзор за выполнением домашних заданий", en: "Supervised homework assistance" },
  
  documentsTitle: { ro: "Documente Atașate", ru: "Прикрепленные документы", en: "Attached Documents" },
  fileUploadNote: { ro: "Atașează documentele relevante pentru a-ți susține cererea. Fișierele acceptate: PDF, JPG, PNG (max 5MB fiecare).", ru: "Прикрепите соответствующие документы для поддержки вашей заявки. Принимаемые файлы: PDF, JPG, PNG (макс. 5МБ каждый).", en: "Attach relevant documents to support your application. Accepted files: PDF, JPG, PNG (max 5MB each)." },
  docBuletinLabel: { ro: "Buletin de identitate", ru: "Удостоверение личности", en: "ID Card" },
  docDiplomeLabel: { ro: "Diplome/Certificate", ru: "Дипломы/Сертификаты", en: "Diplomas/Certificates" },
  docRecomandariLabel: { ro: "Recomandări", ru: "Рекомендации", en: "Recommendations" },
  documentNotAttached: { ro: "Nu este atașat", ru: "Не прикреплен", en: "Not attached" },
  noDocumentsAttached: { ro: "Prestatorul nu a atașat niciun document.", ru: "Поставщик не прикрепил никаких документов.", en: "The provider has not attached any documents." },
  documentDownloadStarted: { ro: "S-a inițiat descărcarea {fileName} (Simulare)", ru: "Началась загрузка {fileName} (Симуляция)", en: "Download started for {fileName} (Simulation)" },
  downloadFileHint: { ro: "Descarcă/Vezi fișierul", ru: "Скачать/Посмотреть файл", en: "Download/View file" },

  // File upload related translations
  fileSelectButton: { ro: "Selectează fișier", ru: "Выбрать файл", en: "Select file" },
  fileSelectMultipleButton: { ro: "Selectează fișiere", ru: "Выбрать файлы", en: "Select files" },
  fileRemoveButton: { ro: "Elimină", ru: "Удалить", en: "Remove" },
  fileReplaceButton: { ro: "Înlocuiește", ru: "Заменить", en: "Replace" },
  fileUploadSuccess: { ro: "Fișier încărcat cu succes", ru: "Файл успешно загружен", en: "File uploaded successfully" },
  fileUploadError: { ro: "Eroare la încărcarea fișierului", ru: "Ошибка загрузки файла", en: "File upload error" },
  fileInvalidType: { ro: "Tip de fișier invalid. Acceptate: PDF, JPG, PNG", ru: "Недопустимый тип файла. Принимаются: PDF, JPG, PNG", en: "Invalid file type. Accepted: PDF, JPG, PNG" },
  fileTooLarge: { ro: "Fișierul este prea mare. Dimensiunea maximă: 5MB", ru: "Файл слишком большой. Максимальный размер: 5МБ", en: "File is too large. Maximum size: 5MB" },
  fileSelected: { ro: "fișier selectat", ru: "файл выбран", en: "file selected" },
  filesSelected: { ro: "fișiere selectate", ru: "файлов выбрано", en: "files selected" },
  
  toastSuccessTitle: { ro: "Succes!", ru: "Успех!", en: "Success!"},
  toastErrorTitle: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  toastErrorUnknownFetch: { ro: "A apărut o eroare de rețea sau la procesarea răspunsului.", ru: "Произошла сетевая ошибка или ошибка при обработке ответа.", en: "A network error or response processing error occurred." },
  toastErrorApiService: { ro: "Eroare la comunicarea cu serverul de servicii.", ru: "Ошибка при связи с сервером услуг.", en: "Error communicating with the services server." },
  apiServiceFetchErrorDetailed: { ro: "Serviciul API a returnat o eroare. Vă rugăm încercați mai târziu.", ru: "Сервис API вернул ошибку. Пожалуйста, попробуйте позже.", en: "The API service returned an error. Please try again later." },
  apiErrorTitle: { ro: "Eroare API", ru: "Ошибка API", en: "API Error" },
  apiErrorContactSupport: { ro: "Dacă problema persistă, vă rugăm contactați suportul tehnic.", ru: "Если проблема не исчезнет, обратитесь в техническую поддержку.", en: "If the problem persists, please contact technical support." },
  
  optionYes: { ro: "Da", ru: "Да", en: "Yes" },
  optionNo: { ro: "Nu", ru: "Нет", en: "No" },
  
  chooseServicePrompt: { ro: "Alegeți un serviciu pentru detalii", ru: "Выберите услугу для просмотра деталей", en: "Choose a service for details" },
  selectServicePlaceholder: { ro: "Selectați un serviciu din lista de mai sus pentru a vedea detaliile complete.", ru: "Выберите услугу из списка выше, чтобы увидеть полную информацию.", en: "Select a service from the list above to see full details." },
  reviewsSuffix: { ro: "recenzii", ru: "отзывов", en: "reviews" },
  noSpecificDetailsAvailable: { ro: "Niciun detaliu specific pentru această categorie nu a fost adăugat de prestator.", ru: "Поставщик не добавил никаких конкретных деталей для этой категории.", en: "No specific details for this category have been added by the provider." },
  providerHasNoActiveServices: { ro: "Acest prestator nu are servicii active în prezent.", ru: "У этого поставщика в настоящее время нет активных услуг.", en: "This provider currently has no active services." },

  profileLoading: { ro: "Se încarcă profilul...", ru: "Загрузка профиля...", en: "Loading profile..." },
  profileIdMissingError: { ro: "ID-ul profilului lipsește din URL.", ru: "ID профиля отсутствует в URL.", en: "Profile ID is missing from URL." },
  serviceIdMissingError: { ro: "ID-ul serviciului lipsește din URL.", ru: "ID услуги отсутствует в URL.", en: "Service ID is missing from URL." },
  profileNotFoundError: { ro: "Profilul solicitat nu a fost găsit sau nu este un prestator.", ru: "Запрошенный профиль не найден или не является поставщиком.", en: "The requested profile was not found or is not a provider." },
  profileFetchError: { ro: "A apărut o eroare la preluarea datelor profilului.", ru: "Произошла ошибка при загрузке данных профиля.", en: "An error occurred while fetching profile data." },
  profileNotFoundErrorTitle: { ro: "Profil Negăsit", ru: "Профиль не найден", en: "Profile Not Found" },
  profileNotFoundDescription: { ro: "Ne pare rău, profilul pe care îl cauți nu a putut fi găsit sau este posibil să nu mai fie activ.", ru: "К сожалению, запрашиваемый профиль не найден или больше не активен.", en: "Sorry, the profile you are looking for could not be found or may no longer be active." },
  serviceNotFoundErrorTitle: { ro: "Serviciu Negăsit", ru: "Услуга не найдена", en: "Service Not Found"},
  serviceNotFoundErrorForProvider: {ro: "Serviciul solicitat nu a fost găsit pentru acest prestator sau ID-ul serviciului este invalid.", ru: "Запрошенная услуга не найдена для этого поставщика или ID услуги недействителен.", en: "The requested service was not found for this provider or the service ID is invalid."},
  goToSearchPageButton: { ro: "Mergi la Pagina de Căutare", ru: "Перейти на страницу поиска", en: "Go to Search Page" },

  closeButton: { ro: "Închide", ru: "Закрыть", en: "Close" },
  bookedDayLabel: { ro: "Zi ocupată", ru: "Занятый день", en: "Booked day" },
  selectedDayAppointments: { ro: "Programări pe {date}", ru: "Записи на {date}", en: "Appointments on {date}" },
  noAppointmentsForSelectedDate: { ro: "Nicio programare înregistrată pentru această dată.", ru: "Нет зарегистрированных записей на эту дату.", en: "No appointments registered for this date." },
  appointmentAtTime: { ro: "Serviciu la ora:", ru: "Услуга в:", en: "Service at:" },
  loading: { ro: "Se încarcă...", ru: "Загрузка...", en: "Loading..." },

  requestBookingButton: { ro: "Solicită o Rezervare", ru: "Запросить бронирование", en: "Request a Booking" },
  bookingAndAvailabilityModalTitle: { ro: "Rezervare și Disponibilitate - {providerName}", ru: "Бронирование и доступность - {providerName}", en: "Booking & Availability - {providerName}" },
  bookingAndAvailabilityModalDescription: { ro: "Verifică zilele ocupate în calendar și selectează data și ora dorită pentru rezervare.", ru: "Проверьте занятые дни в календаре и выберите желаемую дату и время для бронирования.", en: "Check booked days on the calendar and select your desired date and time for the booking." },
  selectBookingDetailsLabel: { ro: "Completează detaliile rezervării", ru: "Заполните детали бронирования", en: "Complete booking details"},
  loadingAppointments: { ro: "Se încarcă programările...", ru: "Загрузка записей...", en: "Loading appointments..."},

  selectDateLabel: { ro: "Selectează data", ru: "Выберите дату", en: "Select the date"},
  selectTimeLabel: { ro: "Selectează ora (HH:mm)", ru: "Выберите время (ЧЧ:мм)", en: "Select the time (HH:mm)"},
  notesLabel: { ro: "Notițe suplimentare (opțional)", ru: "Дополнительные заметки (необязательно)", en: "Additional notes (optional)"},
  notesPlaceholder: { ro: "Ex: Detalii despre adresă, nevoi speciale etc.", ru: "Пример: Детали адреса, особые потребности и т.д.", en: "Ex: Address details, special needs, etc."},
  sendRequestButton: { ro: "Trimite Cererea", ru: "Отправить запрос", en: "Send Request"},
  bookingFormIncompleteError: { ro: "Te rugăm să selectezi o dată și o oră pentru rezervare.", ru: "Пожалуйста, выберите дату и время для бронирования.", en: "Please select a date and time for the booking."},
  bookingRequestSuccessToast: { ro: "Cererea de rezervare a fost trimisă cu succes! Prestatorul va fi notificat.", ru: "Запрос на бронирование успешно отправлен! Поставщик будет уведомлен.", en: "Booking request sent successfully! The provider will be notified."},
  bookingRequestErrorToast: { ro: "Eroare la trimiterea cererii de rezervare. Te rugăm să încerci din nou.", ru: "Ошибка при отправке запроса на бронирование. Пожалуйста, попробуйте еще раз.", en: "Error sending booking request. Please try again."},
  
  gridViewTitle: { ro: "Vizualizare Grilă", ru: "Вид сетки", en: "Grid View" },
  listViewTitle: { ro: "Vizualizare Listă", ru: "Вид списка", en: "List View" },

  viewProfileButton: { ro: "Vezi Profil", ru: "Смотреть профиль", en: "View Profile" },
  noServiceAvailable: { ro: "Serviciu Indisponibil", ru: "Услуга недоступна", en: "Service Unavailable" },

  adminPanelUser: { ro: "Super Admin", ru: "Супер Админ", en: "Super Admin" },
  adminDashboardNav: { ro: "Panou Principal Admin", ru: "Главная панель администратора", en: "Admin Dashboard" },
  adminAnalyticsNav: { ro: "Analiză și Statistici", ru: "Аналитика и статистика", en: "Analytics & Statistics" },
  adminUsersNav: { ro: "Utilizatori", ru: "Пользователи", en: "Users" },
  adminServicesNav: { ro: "Servicii", ru: "Услуги", en: "Services" },
  adminProviderRequestsNav: { ro: "Cereri Prestatori", ru: "Запросы поставщиков", en: "Provider Requests"},
  adminDashboardTitle: { ro: "Panou de Control Administrator", ru: "Панель управления администратора", en: "Administrator Control Panel" },
  adminTotalUsersStat: { ro: "Total Utilizatori", ru: "Всего пользователей", en: "Total Users" },
  adminTotalProvidersStat: { ro: "Total Prestatori", ru: "Всего поставщиков", en: "Total Providers" },
  adminTotalServicesStat: { ro: "Total Servicii (Toate)", ru: "Всего услуг (Все)", en: "Total Services (All)" },
  adminActiveServicesStat: { ro: "Servicii Active", ru: "Активные услуги", en: "Active Services" },
  pendingProviderRequestsStat: { ro: "Cereri Prestatori în Așteptare", ru: "Запросы поставщиков в ожидании", en: "Pending Provider Requests"},
  adminQuickActionsTitle: { ro: "Acțiuni Rapide", ru: "Быстрые действия", en: "Quick Actions" },
  adminQuickActionsDescription: { ro: "Mai multe acțiuni rapide vor fi disponibile aici în curând.", ru: "Дополнительные быстрые действия скоро появятся здесь.", en: "More quick actions will be available here soon." },
  adminErrorFetchingStats: { ro: "Eroare la preluarea statisticilor.", ru: "Ошибка при загрузке статистики.", en: "Error fetching statistics." },
  adminErrorUnknown: { ro: "A apărut o eroare necunoscută.", ru: "Произошла неизвестная ошибка.", en: "An unknown error occurred." },
  adminErrorLoadingData: { ro: "Eroare la încărcarea datelor", ru: "Ошибка загрузки данных", en: "Error loading data" },
  adminErrorFetchingServices: { ro: "Eroare la preluarea listei de servicii.", ru: "Ошибка при загрузке списка услуг.", en: "Error fetching services list." },
  adminErrorFetchingRequests: { ro: "Eroare la preluarea cererilor de prestatori.", ru: "Ошибка при загрузке запросов поставщиков.", en: "Error fetching provider requests." },
  adminNoServicesFound: { ro: "Niciun serviciu găsit.", ru: "Услуги не найдены.", en: "No services found." },

  adminUserManagementTitle: { ro: "Management Utilizatori", ru: "Управление пользователями", en: "User Management" },
  adminUserManagementDescription: { ro: "Vizualizează și gestionează toți utilizatorii platformei.", ru: "Просмотр и управление всеми пользователями платформы.", en: "View and manage all platform users." },
  adminErrorFetchingUsers: { ro: "Eroare la preluarea listei de utilizatori.", ru: "Ошибка при загрузке списка пользователей.", en: "Error fetching user list." },
  adminTableUserId: { ro: "ID Utilizator", ru: "ID Пользователя", en: "User ID" },
  adminTableFullName: { ro: "Nume Complet", ru: "Полное имя", en: "Full Name" },
  adminTableEmail: { ro: "Email", ru: "Электронная почта", en: "Email" },
  adminTableRoles: { ro: "Roluri", ru: "Роли", en: "Roles" },
  adminTableActions: { ro: "Acțiuni", ru: "Действия", en: "Actions" },
  adminViewDetailsButton: { ro: "Vezi Detalii", ru: "Посмотреть детали", en: "View Details" },
  adminNoUsersFound: { ro: "Niciun utilizator găsit.", ru: "Пользователи не найдены.", en: "No users found." },
  roleClient: { ro: "Client", ru: "Клиент", en: "Client" },
  roleProvider: { ro: "Prestator", ru: "Поставщик", en: "Provider" },
  roleAdmin: { ro: "Admin", ru: "Админ", en: "Admin" },

  adminServiceManagementTitle: { ro: "Management Servicii", ru: "Управление услугами", en: "Service Management" },
  adminServiceManagementDescription: { ro: "Vizualizează și gestionează toate serviciile de pe platformă.", ru: "Просмотр и управление всеми услугами на платформе.", en: "View and manage all services on the platform." },
  adminTableServiceId: { ro: "ID Serviciu", ru: "ID Услуги", en: "Service ID" },
  adminTableServiceName: { ro: "Nume Serviciu", ru: "Название услуги", en: "Service Name" },
  adminTableProviderName: { ro: "Nume Prestator", ru: "Имя поставщика", en: "Provider Name" },
  adminTableCategory: { ro: "Categorie", ru: "Категория", en: "Category" },
  adminTableStatus: { ro: "Status", ru: "Статус", en: "Status" },
  allCategories: { ro: "Toate categoriile", ru: "Все категории", en: "All categories" },
  searchPlaceholder: { ro: "Caută...", ru: "Поиск...", en: "Search..." },

  adminProviderRequestsTitle: { ro: "Cereri Înregistrare Prestatori", ru: "Запросы на регистрацию поставщиков", en: "Provider Registration Requests" },
  adminProviderRequestsDescription: { ro: "Analizează și aprobă/respinge cererile utilizatorilor de a deveni prestatori.", ru: "Рассматривайте и утверждайте/отклоняйте запросы пользователей на становление поставщиками.", en: "Review and approve/reject user requests to become providers." },
  adminRequestsTableRequestID: { ro: "ID Cerere", ru: "ID Запроса", en: "Request ID" },
  adminRequestsTableUserID: { ro: "ID Utilizator", ru: "ID Пользователя", en: "User ID" },
  adminRequestsTableUserName: { ro: "Nume Utilizator", ru: "Имя пользователя", en: "User Name" },
  adminRequestsTableUserEmail: { ro: "Email Utilizator", ru: "Email пользователя", en: "User Email" },
  adminRequestsTableServiceDetails: { ro: "Detalii Servicii Solicitate", ru: "Детали запрошенных услуг", en: "Requested Service Details" },
  adminRequestsTableRequestedServices: { ro: "Servicii Solicitate", ru: "Запрошенные услуги", en: "Requested Services" },
  adminExperienceLabel: { ro: "Experiență", ru: "Опыт", en: "Experience" },
  adminRequestsTableRequestDate: { ro: "Data Cererii", ru: "Дата запроса", en: "Request Date" },
  adminRequestsTableStatus: { ro: "Status Cerere", ru: "Статус запроса", en: "Request Status" },
  adminRequestsApproveButton: { ro: "Aprobă", ru: "Утвердить", en: "Approve" },
  adminRequestsRejectButton: { ro: "Respinge", ru: "Отклонить", en: "Reject" },
  adminNoRequestsFound: { ro: "Nicio cerere de prestator în așteptare.", ru: "Нет ожидающих запросов от поставщиков.", en: "No pending provider requests." },
  adminRequestApprovedSuccess: { ro: "Cererea a fost aprobată. Utilizatorul are acum rol de prestator.", ru: "Запрос утвержден. Пользователь теперь имеет роль поставщика.", en: "Request approved. User now has provider role." },
  adminRequestRejectedSuccess: { ro: "Cererea a fost respinsă.", ru: "Запрос отклонен.", en: "Request rejected." },
  adminRequestUpdateError: { ro: "Eroare la actualizarea statusului cererii.", ru: "Ошибка при обновлении статуса запроса.", en: "Error updating request status." },
  statusPending: { ro: "În așteptare", ru: "В ожидании", en: "Pending" },
  statusApproved: { ro: "Aprobată", ru: "Утверждена", en: "Approved" },
  adminServiceApprovedSuccess: { ro: "Serviciul a fost aprobat și este acum activ.", ru: "Услуга утверждена и теперь активна.", en: "The service has been approved and is now active." },
  adminServiceRejectedSuccess: { ro: "Serviciul a fost respins.", ru: "Услуга отклонена.", en: "The service has been rejected." },
  
  viewDescriptionLink: { ro: "Vezi descrierea", ru: "Посмотреть описание", en: "View description"},
  requestDescriptionTitle: { ro: "Descrierea serviciilor oferite de", ru: "Описание предлагаемых услуг от", en: "Description of services offered by"},
  adminRejectReasonDialogTitle: { ro: "Motivul Respingerii Cererii", ru: "Причина отклонения заявки", en: "Reason for Request Rejection" },
  adminRejectReasonDialogDescription: { ro: "Vă rugăm introduceți motivul pentru care această cerere este respinsă. Acest mesaj va fi vizibil utilizatorului.", ru: "Пожалуйста, укажите причину отклонения этой заявки. Это сообщение будет видно пользователю.", en: "Please enter the reason why this request is being rejected. This message will be visible to the user." },
  adminRejectReasonLabel: { ro: "Motivul Respingerii", ru: "Причина отклонения", en: "Reason for Rejection" },
  adminRejectReasonPlaceholder: { ro: "Ex: Documente incomplete, experiență insuficientă, etc.", ru: "Пример: Неполные документы, недостаточный опыт и т.д.", en: "Ex: Incomplete documents, insufficient experience, etc." },
  adminConfirmRejectButton: { ro: "Confirmă Respingerea", ru: "Подтвердить отклонение", en: "Confirm Rejection" },
  cancelButton: { ro: "Anulează", ru: "Отмена", en: "Cancel" },

  errorCategoryAvailabilityMissing: { ro: "Te rugăm să selectezi cel puțin o opțiune de disponibilitate pentru \"{categoryName}\".", ru: "Пожалуйста, выберите хотя бы один вариант доступности для \"{categoryName}\".", en: "Please select at least one availability option for \"{categoryName}\"." },
  availabilityForCategoryLabel: { ro: "Disponibilitate pentru {categoryName}", ru: "Доступность для {categoryName}", en: "Availability for {categoryName}" },
  
  // Sorting Options
  sortRelevance: { ro: "Relevanță", ru: "Релевантность", en: "Relevance" },
  sortRatingDesc: { ro: "Rating (Cel mai mare)", ru: "Рейтинг (Высший)", en: "Rating (Highest)" },
  sortRatingAsc: { ro: "Rating (Cel mai mic)", ru: "Рейтинг (Низший)", en: "Rating (Lowest)" },
  sortNameAsc: { ro: "Nume (A-Z)", ru: "Имя (А-Я)", en: "Name (A-Z)" },
  sortNameDesc: { ro: "Nume (Z-A)", ru: "Имя (Я-А)", en: "Name (Z-A)" },
  
  // API Errors
  apiPrismaLibSslError: { ro: "Eroare critică de configurare server: Bibliotecile necesare pentru baza de date (libssl) nu sunt disponibile. Contactați administratorul.", ru: "Критическая ошибка конфигурации сервера: необходимые библиотеки для базы данных (libssl) недоступны. Обратитесь к администратору.", en: "Critical server configuration error: Required database libraries (libssl) are unavailable. Contact the administrator." },

  // Dashboard Provider Application Status
  providerApplicationStatusTitle: { ro: "Status Cerere Prestator", ru: "Статус заявки поставщика", en: "Provider Application Status" },
  
  notifications: { ro: "Notificări", ru: "Уведомления", en: "Notifications" },
  noNewNotifications: { ro: "Nicio notificare nouă", ru: "Нет новых уведомлений", en: "No new notifications" },
  markAllAsRead: { ro: "Marchează totul ca citit", ru: "Отметить все как прочитанные", en: "Mark all as read" },
  viewAllNotifications: { ro: "Vezi toate notificările", ru: "Посмотреть все уведомления", en: "View all notifications" },

  forcePasswordChangeTitle: { ro: "Schimbare Parolă Necesară", ru: "Требуется смена пароля", en: "Password Change Required" },
  forcePasswordChangeDescription: { ro: "Pentru securitatea contului, vă rugăm să setați o nouă parolă.", ru: "Для безопасности учетной записи, пожалуйста, установите новый пароль.", en: "For account security, please set a new password." },

  providerActiveServicesStat: { ro: "Servicii Active", ru: "Активные услуги", en: "Active Services" },
  providerPendingBookingsStat: { ro: "Rezervări în Așteptare", ru: "Ожидающие бронирования", en: "Pending Bookings" },
  viewCalendarButton: { ro: "Vezi Calendar", ru: "Посмотреть Календарь", en: "View Calendar" },
  
  statusIconActiveTitle: { ro: "Există servicii active", ru: "Есть активные услуги", en: "Active services present" },
  statusIconInactiveTitle: { ro: "Toate serviciile sunt inactive", ru: "Все услуги неактивны", en: "All services are inactive" },
  statusIconNoServicesTitle: { ro: "Niciun serviciu adăugat", ru: "Нет добавленных услуг", en: "No services added" },

  chatMessagesLink: { ro: "Mesaje", ru: "Сообщения", en: "Messages" },
  chatModalTitle: { ro: "Trimite mesaj către {providerName}", ru: "Отправить сообщение {providerName}", en: "Send message to {providerName}" },
  chatModalDescription: { ro: "Referitor la serviciul: {serviceName}", ru: "Относительно услуги: {serviceName}", en: "Regarding service: {serviceName}" },
  chatMessageLabel: { ro: "Mesajul tău", ru: "Ваше сообщение", en: "Your message" },
  chatMessagePlaceholder: { ro: "Scrie aici mesajul tău...", ru: "Напишите ваше сообщение здесь...", en: "Type your message here..." },
  chatSendButton: { ro: "Trimite Mesajul", ru: "Отправить сообщение", en: "Send Message" },
  chatMessageEmptyError: { ro: "Mesajul nu poate fi gol.", ru: "Сообщение не может быть пустым.", en: "Message cannot be empty." },
  chatInitiateError: { ro: "Nu s-a putut iniția conversația.", ru: "Не удалось начать беседу.", en: "Could not initiate conversation." },
  chatMessageSentSuccess: { ro: "Mesaj trimis cu succes!", ru: "Сообщение успешно отправлено!", en: "Message sent successfully!" },
  chatCannotSelfMessageError: { ro: "Nu poți trimite mesaje ție însuți.", ru: "Вы не можете отправлять сообщения самому себе.", en: "You cannot send messages to yourself." },
  chatLoginToMessageOrCannotSelf: { ro: "Autentifică-te pentru a trimite un mesaj sau nu poți trimite mesaje ție însuți.", ru: "Войдите, чтобы отправить сообщение, или вы не можете отправлять сообщения самому себе.", en: "Log in to send a message, or you cannot message yourself." },

  langRomanian: { ro: "Română", ru: "Румынский", en: "Romanian" },
  langRussian: { ro: "Rusă", ru: "Русский", en: "Russian" },
  langUkrainian: { ro: "Ucraineană", ru: "Украинский", en: "Ukrainian" },
  langGagauz: { ro: "Găgăuză", ru: "Гагаузский", en: "Gagauz" },
  langBulgarian: { ro: "Bulgară", ru: "Болгарский", en: "Bulgarian" },
  langEnglish: { ro: "Engleză", ru: "Английский", en: "English" },
  langItalian: { ro: "Italiană", ru: "Итальянский", en: "Italian" },
  langSpanish: { ro: "Spaniolă", ru: "Испанский", en: "Spanish" },
  langFrench: { ro: "Franceză", ru: "Французский", en: "French" },
  langGerman: { ro: "Germană", ru: "Немецкий", en: "German" },
  langPortuguese: { ro: "Portugheză", ru: "Португальский", en: "Portuguese" },
  langTurkish: { ro: "Turcă", ru: "Турецкий", en: "Turkish" },
  langArabic: { ro: "Arabă", ru: "Арабский", en: "Arabic" },
  langPolish: { ro: "Poloneză", ru: "Польский", en: "Polish" },

  regProvPageTitle: { ro: "Devino Prestator", ru: "Стать поставщиком", en: "Become a Provider" },
  regProvPageDescription: { ro: "Trimite o cerere pentru a oferi serviciile tale pe platforma noastră.", ru: "Отправьте заявку, чтобы предлагать свои услуги на нашей платформе.", en: "Submit a request to offer your services on our platform." },
  regProvStep1Title: { ro: "Pasul 1: Selectează Categoriile de Servicii", ru: "Шаг 1: Выберите категории услуг", en: "Step 1: Select Service Categories" },
  regProvStep1Description: { ro: "Alege categoriile de servicii pe care dorești să le oferi.", ru: "Выберите категории услуг, которые вы хотите предложить.", en: "Choose the service categories you wish to offer." },
  regProvStep1TitleShort: { ro: "Selectare Servicii", ru: "Выбор Услуг", en: "Select Services" },
  regProvStep2Title: { ro: "Pasul {currentSubStep} din {totalSubSteps}: Detalii pentru {categoryName}", ru: "Шаг {currentSubStep} из {totalSubSteps}: Детали для {categoryName}", en: "Step {currentSubStep} of {totalSubSteps}: Details for {categoryName}" },
  regProvStep2Description: { ro: "Completează informațiile specifice pentru serviciul de {categoryName}.", ru: "Заполните конкретную информацию для услуги {categoryName}.", en: "Complete the specific information for the {categoryName} service." },
  regProvTermsStepTitle: { ro: "Pasul Final: Termeni și Condiții", ru: "Последний шаг: Условия и положения", en: "Final Step: Terms and Conditions" },
  regProvTermsStepTitleShort: { ro: "Termeni", ru: "Условия", en: "Terms" },
  regProvTermsStepDescription: { ro: "Te rugăm să citești și să accepți termenii și condițiile pentru a finaliza cererea.", ru: "Пожалуйста, прочитайте и примите условия и положения, чтобы завершить заявку.", en: "Please read and accept the terms and conditions to complete your application." },
  regProvExperienceLabel: { ro: "Ani de experiență", ru: "Опыт работы (лет)", en: "Years of experience" },
  experienceYearsLabel: { ro: "Ani de experiență", ru: "Опыт работы (лет)", en: "Years of experience" },
  experiencePlaceholder: { ro: "Ex: 5", ru: "Пример: 5", en: "Ex: 5" },
  descriptionLabel: { ro: "Descriere detaliată", ru: "Подробное описание", en: "Detailed description" },
  descriptionPlaceholder: { ro: "Descrie serviciul oferit, experiența ta, ce include, etc.", ru: "Опишите предлагаемую услугу, ваш опыт, что включено и т.д.", en: "Describe the service offered, your experience, what it includes, etc." },
  
  regProvTermsLink: { ro: "Termenii și Condițiile pentru Prestatori", ru: "Условиями и Положениями для Поставщиков", en: "Provider Terms and Conditions" },
  regProvNextButton: { ro: "Următorul Pas", ru: "Следующий шаг", en: "Next Step" },
  regProvPrevButton: { ro: "Pasul Anterior", ru: "Предыдущий шаг", en: "Previous Step" },
  regProvSubmitRequestButton: { ro: "Trimite Cererea", ru: "Отправить заявку", en: "Submit Request" },
  regProvSubmittingRequestButton: { ro: "Se trimite cererea...", ru: "Отправка заявки...", en: "Submitting request..." },
  regProvErrorAlertTitle: { ro: "Eroare Validare", ru: "Ошибка валидации", en: "Validation Error" },
  regProvErrorNoCategorySelected: { ro: "Te rugăm să selectezi cel puțin o categorie de servicii.", ru: "Пожалуйста, выберите хотя бы одну категорию услуг.", en: "Please select at least one service category." },
  regProvErrorCategoryDetailsMissing: { ro: "Te rugăm să completezi toate câmpurile obligatorii pentru categoria \"{categoryName}\".", ru: "Пожалуйста, заполните все обязательные поля для категории \"{categoryName}\".", en: "Please complete all required fields for category \"{categoryName}\"." },
  regProvErrorCategoryAvailabilityMissing: { ro: "Te rugăm să selectezi cel puțin o opțiune de disponibilitate generală pentru \"{categoryName}\".", ru: "Пожалуйста, выберите хотя бы один вариант общей доступности для \"{categoryName}\".", en: "Please select at least one general availability option for \"{categoryName}\"." },
  regProvErrorLocationRequired: { ro: "Locația este obligatorie pentru categoria \"{categoryName}\".", ru: "Местоположение обязательно для категории \"{categoryName}\".", en: "Location is required for category \"{categoryName}\"."},
  regProvErrorTermsNotAccepted: { ro: "Trebuie să accepți Termenii și Condițiile pentru Prestatori.", ru: "Вы должны принять Условия и Положения для Поставщиков.", en: "You must accept the Provider Terms and Conditions." },
  regProvToastSuccessTitle: { ro: "Succes!", ru: "Успех!", en: "Success!" },
  regProvToastSuccessDescription: { ro: "Cererea ta a fost trimisă cu succes și va fi analizată. Vei fi notificat ulterior.", ru: "Ваша заявка успешно отправлена и будет рассмотрена. Вы будете уведомлены позже.", en: "Your request has been successfully sent and will be reviewed. You will be notified later." },
  regProvToastErrorTitle: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  regProvErrorLoadingUserData: { ro: "Eroare la încărcarea datelor utilizatorului. Asigură-te că ești autentificat.", ru: "Ошибка загрузки данных пользователя. Убедитесь, что вы вошли в систему.", en: "Error loading user data. Please ensure you are logged in." },
  regProvLoadingUserData: { ro: "Se încarcă datele utilizatorului...", ru: "Загрузка данных пользователя...", en: "Loading user data..."},
  regProvLoadingRequestStatus: { ro: "Se verifică statusul cererii...", ru: "Проверка статуса заявки...", en: "Checking request status..." },
  regProvRequestStatusPendingTitle: { ro: "Cererea ta este în curs de procesare", ru: "Ваша заявка находится на рассмотрении", en: "Your application is being processed" },
  regProvRequestStatusPendingDescription: { ro: "Te vom anunța când statusul se va schimba. Poți verifica statusul și în panoul tău de control.", ru: "Мы сообщим вам, когда статус изменится. Вы также можете проверить статус в своей панели управления.", en: "We will notify you when the status changes. You can also check the status in your dashboard." },
  regProvRequestStatusApprovedTitle: { ro: "Cerere Aprobată!", ru: "Заявка одобрена!", en: "Request Approved!" },
  regProvRequestStatusApprovedDescription: { ro: "Felicitări! Cererea ta a fost aprobată. Acum ești prestator pe platforma noastră.", ru: "Поздравляем! Ваша заявка одобрена. Теперь вы являетесь поставщиком на нашей платформе.", en: "Congratulations! Your request has been approved. You are now a provider on our platform." },
  regProvRequestStatusRejectedTitle: { ro: "Cerere Respinsă", ru: "Заявка отклонена", en: "Request Rejected" },
  regProvRequestStatusRejectedDescription: { ro: "Cererea ta de a deveni prestator a fost respinsă. Motiv: {adminNotes}. Poți trimite o nouă cerere dacă dorești.", ru: "Ваша заявка на становление поставщиком была отклонена. Причина: {adminNotes}. При желании вы можете подать новую заявку.", en: "Your request to become a provider has been rejected. Reason: {adminNotes}. You can submit a new request if you wish." },
  regProvNoAdminNotes: { ro: "Niciun motiv specificat.", ru: "Причина не указана.", en: "No reason specified." },
  regProvGoToDashboardButton: { ro: "Mergi la Panoul Principal", ru: "Перейти в Панель управления", en: "Go to Dashboard" },
  regProvLoadingCategories: { ro: "Se încarcă categoriile...", ru: "Загрузка категорий...", en: "Loading categories..." },
  regProvReapplyButton: { ro: "Aplică Din Nou", ru: "Подать заявку снова", en: "Apply Again" },
  regProvPriceSubscriptionSectionTitle: { ro: "Detalii Abonament/Pachet (Opțional)", ru: "Детали абонемента/пакета (опционально)", en: "Subscription/Package Details (Optional)"},
  regProvPriceSubscriptionAmountLabel: { ro: "Preț numeric abonament (MDL)", ru: "Числовая цена абонемента (MDL)", en: "Numeric subscription price (MDL)" },
  regProvPriceSubscriptionUnitLabel: { ro: "Unitate abonament", ru: "Единица абонемента", en: "Subscription unit" },
  regProvPriceSubscriptionUnitPlaceholder: { ro: "Ex: lună, săptămână, pachet, sesiune", ru: "Пример: месяц, неделя, пакет, сессия", en: "Ex: month, week, package, session" },
  regProvPriceSubscriptionTextLabel: { ro: "Text liber preț abonament", ru: "Свободный текст цены абонемента", en: "Free text subscription price" },
  regProvPriceSubscriptionTextPlaceholder: { ro: "Ex: De la 500 MDL/lună, Negociabil", ru: "Пример: От 500 MDL/месяц, Договорная", en: "Ex: From 500 MDL/month, Negotiable" },
  regProvSubscriptionDetailsLabel: { ro: "Ce include abonamentul/pachetul?", ru: "Что включает абонемент/пакет?", en: "What does the subscription/package include?" },
  regProvSubscriptionDetailsPlaceholder: { ro: "Ex: 10 ședințe, curățenie bilunară, etc.", ru: "Пример: 10 сеансов, уборка раз в две недели и т.д.", en: "Ex: 10 sessions, bi-weekly cleaning, etc." },
  regProvAddServicePageTitle: { ro: "Adaugă un Serviciu Nou", ru: "Добавить новую услугу", en: "Add a New Service" },
  regProvAddServicePageDescription: { ro: "Selectează categoria de serviciu pe care dorești să o adaugi.", ru: "Выберите категорию услуг, которую вы хотите добавить.", en: "Select the service category you want to add." },
  regProvAddServiceNoMoreCategories: { ro: "Ai adăugat deja servicii pentru toate categoriile disponibile.", ru: "Вы уже добавили услуги для всех доступных категорий.", en: "You have already added services for all available categories." },
  regProvAddServiceAction: { ro: "Adaugă Serviciu de {categoryName}", ru: "Добавить услугу {categoryName}", en: "Add {categoryName} Service" },
  
  // Full Terms and Conditions for main page
  termsPage_pageTitle: { ro: "Termeni și Condiții", ru: "Условия и Положения", en: "Terms and Conditions" },
  termsPage_lastUpdated: { ro: "Ultima actualizare: 5 Iunie 2024", ru: "Последнее обновление: 5 июня 2024 г.", en: "Last updated: June 5, 2024" },
  termsPage_introductionTitle: { ro: "1. Introducere", ru: "1. Введение", en: "1. Introduction" },
  termsPage_introductionContent: { 
    ro: "Acești Termeni și Condiții guvernează utilizarea de către dumneavoastră a platformei mesami și a serviciilor oferite. Prin accesarea sau utilizarea site-ului nostru, sunteți de acord să respectați acești Termeni și Condiții. Dacă nu sunteți de acord cu vreo parte a acestora, nu trebuie să utilizați serviciile noastre.",
    ru: "Настоящие Условия и Положения регулируют использование вами платформы mesami и предлагаемых услуг. Получая доступ к нашему сайту или используя его, вы соглашаетесь соблюдать настоящие Условия и Положения. Если вы не согласны с какой-либо их частью, вы не должны использовать наши услуги.",
    en: "These Terms and Conditions govern your use of the mesami platform and the services offered. By accessing or using our site, you agree to comply with these Terms and Conditions. If you do not agree with any part of them, you must not use our services."
  },
  termsPage_servicesTitle: { ro: "2. Serviciile Noastre", ru: "2. Наши Услуги", en: "2. Our Services" },
  termsPage_servicesContent: {
    ro: "mesami este o platformă online care conectează utilizatorii care caută servicii de îngrijire personală (Clienți) cu persoane fizice sau juridice care oferă astfel de servicii (Prestatori). Rolul nostru este de a facilita această conexiune. Nu angajăm direct Prestatorii și nu suntem responsabili pentru serviciile prestate de aceștia.",
    ru: "mesami — это онлайн-платформа, которая связывает пользователей, ищущих услуги личного ухода (Клиентов), с физическими или юридическими лицами, предлагающими такие услуги (Поставщиков). Наша роль заключается в облегчении этой связи. Мы не нанимаем Поставщиков напрямую и не несем ответственности за предоставляемые ими услуги.",
    en: "mesami is an online platform that connects users seeking personal care services (Clients) with individuals or entities offering such services (Providers). Our role is to facilitate this connection. We do not directly employ Providers and are not responsible for the services they render."
  },
  termsPage_userObligationsTitle: { ro: "3. Obligațiile Utilizatorului", ru: "3. Обязательства Пользователя", en: "3. User Obligations" },
  termsPage_userObligationsContent: {
    ro: "Prin utilizarea platformei, sunteți de acord să: furnizați informații corecte și complete; utilizați platforma în scopuri legale; respectați drepturile altor utilizatori; nu publicați conținut defăimător, ilegal sau ofensator. Atât Clienții, cât și Prestatorii sunt responsabili pentru propriile interacțiuni și acorduri.",
    ru: "Используя платформу, вы соглашаетесь: предоставлять точную и полную информацию; использовать платформу в законных целях; уважать права других пользователей; не публиковать дискредитирующий, незаконный или оскорбительный контент. Как Клиенты, так и Поставщики несут ответственность за свои взаимодействия и соглашения.",
    en: "By using the platform, you agree to: provide accurate and complete information; use the platform for lawful purposes; respect the rights of other users; not post defamatory, illegal, or offensive content. Both Clients and Providers are responsible for their own interactions and agreements."
  },
  termsPage_providerSpecificTermsTitle: { ro: "3.1 Termeni Specifici pentru Prestatori", ru: "3.1 Особые условия для Поставщиков", en: "3.1 Specific Terms for Providers" },
  termsPage_providerSpecificTermsContent: {
    ro: "Dacă vă înregistrați ca Prestator, sunteți de acord să: oferiți servicii conform descrierii și la standardele profesionale așteptate; mențineți informațiile de profil actualizate și corecte; respectați toate legile și reglementările aplicabile activității dumneavoastră; comunicați prompt și profesionist cu Clienții; nu utilizați platforma pentru activități frauduloase sau înșelătoare. mesami își rezervă dreptul de a suspenda sau elimina conturile Prestatorilor care încalcă acești termeni sau care primesc plângeri justificate repetate.",
    ru: "Если вы регистрируетесь в качестве Поставщика, вы соглашаетесь: предоставлять услуги в соответствии с описанием и ожидаемыми профессиональными стандартами; поддерживать актуальность и точность информации в профиле; соблюдать все применимые законы и нормативные акты, касающиеся вашей деятельности; оперативно и профессионально общаться с Клиентами; не использовать платформу для мошеннических или вводящих в заблуждение действий. mesami оставляет за собой право приостанавливать или удалять учетные записи Поставщиков, нарушающих настоящие условия или получающих повторные обоснованные жалобы.",
    en: "If you register as a Provider, you agree to: offer services as described and to expected professional standards; keep your profile information updated and accurate; comply with all applicable laws and regulations concerning your activity; communicate promptly and professionally with Clients; not use the platform for fraudulent or misleading activities. mesami reserves the right to suspend or remove Provider accounts that violate these terms or receive repeated justified complaints."
  },
  termsPage_disclaimerTitle: { ro: "4. Limitarea Răspunderii", ru: "4. Ограничение ответственности", en: "4. Disclaimer of Liability" },
  termsPage_disclaimerContent: {
    ro: "mesami este o platformă care facilitează legătura dintre utilizatori și prestatorii de servicii. Nu suntem parte la niciun acord, contract sau tranzacție încheiată între utilizatori și prestatori. Ca atare, mesami nu își asumă nicio răspundere pentru calitatea, siguranța, legalitatea sau orice alt aspect al serviciilor prestate de Prestatori, nici pentru acțiunile, omisiunile, declarațiile sau orice comportament al Clienților sau Prestatorilor. mesami nu verifică exhaustiv antecedentele tuturor Prestatorilor sau veridicitatea informațiilor postate. Utilizatorii sunt încurajați să efectueze propriile verificări și să ia decizii informate. Orice dispută, neînțelegere sau problemă care apare între un Client și un Prestator este strict responsabilitatea acestora de a o rezolva. mesami nu va fi trasă la răspundere pentru nicio pierdere sau daună, directă sau indirectă, care rezultă din utilizarea platformei sau din interacțiunile dintre utilizatori.",
    ru: "mesami — это онлайн-платформа, которая облегчает связь между пользователями и поставщиками услуг. Мы не являемся стороной каких-либо соглашений, контрактов или транзакций, заключенных между пользователями и поставщиками. Таким образом, mesami не несет никакой ответственности за качество, безопасность, законность или любые другие аспекты услуг, предоставляемых Поставщиками, а также за действия, бездействие, заявления или любое поведение Клиентов или Поставщиков. mesami не проводит исчерпывающую проверку данных всех Поставщиков или достоверности размещенной информации. Пользователям рекомендуется проводить собственные проверки и принимать обоснованные решения. Любые споры, недоразумения или проблемы, возникающие между Клиентом и Поставщиком, являются исключительно их ответственностью для разрешения. mesami не несет ответственности за какие-либо убытки или ущерб, прямые или косвенные, возникшие в результате использования платформы или взаимодействия между пользователями.",
    en: "mesami is a platform that facilitates connections between users and service providers. We are not a party to any agreement, contract, or transaction entered into between users and providers. As such, mesami assumes no liability for the quality, safety, legality, or any other aspect of the services rendered by Providers, nor for the actions, omissions, statements, or any conduct of Clients or Providers. mesami does not exhaustively vet all Providers or the veracity of the information posted. Users are encouraged to conduct their own due diligence and make informed decisions. Any dispute, misunderstanding, or issue arising between a Client and a Provider is strictly their responsibility to resolve. mesami shall not be held liable for any loss or damage, direct or indirect, resulting from the use of the platform or interactions between users."
  },
  termsPage_modificationsTitle: { ro: "5. Modificări ale Termenilor", ru: "5. Изменения Условий", en: "5. Modifications to Terms" },
  termsPage_modificationsContent: {
    ro: "Ne rezervăm dreptul de a modifica acești Termeni și Condiții în orice moment. Modificările vor intra în vigoare imediat după publicarea pe site. Utilizarea continuă a platformei după astfel de modificări constituie acceptul dumneavoastră.",
    ru: "Мы оставляем за собой право изменять настоящие Условия и Положения в любое время. Изменения вступают в силу немедленно после публикации на сайте. Дальнейшее использование платформы после таких изменений означает ваше согласие.",
    en: "We reserve the right to modify these Terms and Conditions at any time. Changes will become effective immediately upon posting on the site. Your continued use of the platform after such modifications constitutes your acceptance."
  },
  termsPage_contactTitle: { ro: "6. Contact", ru: "6. Контакт", en: "6. Contact" },
  termsPage_contactContent: {
    ro: "Dacă aveți întrebări despre acești Termeni și Condiții, ne puteți contacta la [adresa de email pentru contact/termeni].",
    ru: "Если у вас есть какие-либо вопросы по поводу настоящих Условий и Положений, вы можете связаться с нами по адресу [адрес электронной почты для контактов/условий].",
    en: "If you have any questions about these Terms and Conditions, you can contact us at [contact/terms email address]."
  },

  regProvFullTermsContent: {
    ro: `Acceptând acești termeni, în calitate de Prestator pe platforma mesami, vă angajați să:
1.  **Furnizați Informații Corecte:** Toate informațiile furnizate în profilul dumneavoastră și în descrierile serviciilor trebuie să fie corecte, complete și actualizate.
2.  **Profesionalism și Calitate:** Să oferiți servicii la un standard profesional înalt, conform descrierilor și competențelor declarate.
3.  **Comunicare:** Să comunicați prompt, clar și respectuos cu Clienții prin intermediul platformei.
4.  **Respectarea Legislației:** Să respectați toate legile, reglementările și normele aplicabile activității dumneavoastră profesionale și utilizării platformei.
5.  **Confidențialitate:** Să respectați confidențialitatea datelor Clienților cu care interacționați.
6.  **Responsabilitate:** Să fiți singurul responsabil pentru calitatea, siguranța și legalitatea serviciilor prestate. mesami este o platformă de intermediere și nu își asumă responsabilitatea pentru serviciile efective.
7.  **Recenzii:** Să acceptați că Clienții pot lăsa recenzii despre serviciile dumneavoastră, iar aceste recenzii vor fi publice.
8.  **Taxe și Comisioane:** Să înțelegeți și să acceptați structura de taxe sau comisioane aplicabilă Prestatorilor (dacă este cazul), așa cum va fi comunicată de mesami. (Detalii specifice vor fi adăugate ulterior).
9.  **Interacțiuni:** Să mențineți un comportament adecvat și nu utilizați platforma pentru activități ilegale, frauduloase sau înșelătoare.
10. **Suspendarea Contului:** mesami își rezervă dreptul de a suspenda sau închide contul oricărui Prestator care încalcă acești termeni sau politicile platformei, sau în cazul unor plângeri repetate și justificate din partea Clienților.

Prin bifarea căsuței "Sunt de acord", confirmați că ați citit, înțeles și sunteți de acord cu acești Termeni și Condițiile Generale ale platformei mesami, disponibile integral la /terms.`,
    ru: `Принимая настоящие условия, в качестве Поставщика на платформе mesami, вы обязуетесь:
1.  **Предоставлять достоверную информацию:** Вся информация, предоставленная в вашем профиле и в описаниях услуг, должна быть точной, полной и актуальной.
2.  **Профессионализм и качество:** Предоставлять услуги на высоком профессиональном уровне, в соответствии с описаниями и заявленными компетенциями.
3.  **Коммуникация:** Оперативно, четко и уважительно общаться с Клиентами через платформу.
4.  **Соблюдение законодательства:** Соблюдать все законы, нормативные акты и правила, применимые к вашей профессиональной деятельности и использованию платформы.
5.  **Конфиденциальность:** Соблюдать конфиденциальность данных Клиентов, с которыми вы взаимодействуете.
6.  **Ответственность:** Нести единоличную ответственность за качество, безопасность и законность предоставляемых услуг. mesami является платформой-посредником и не несет ответственности за фактически оказанные услуги.
7.  **Отзывы:** Принимать, что Клиенты могут оставлять отзывы о ваших услугах, и эти отзывы будут общедоступны.
8.  **Сборы и комиссии:** Понимать и принимать структуру сборов или комиссий, применимую к Поставщикам (если таковая имеется), как будет сообщено mesami. (Конкретные детали будут добавлены позже).
9.  **Взаимодействия:** Поддерживать надлежащее поведение и не использовать платформу для незаконной, мошеннической или вводящей в заблуждение деятельности.
10. **Приостановка учетной записи:** mesami оставляет за собой право приостанавливать или закрывать учетную запись любого Поставщика, нарушающего настоящие условия или политику платформы, или в случае повторных и обоснованных жалоб от Клиентов.

Устанавливая флажок «Я согласен», вы подтверждаете, что прочитали, поняли и согласны с настоящими Условиями и Общими положениями платформы mesami, полностью доступными по адресу /terms.`,
    en: `By accepting these terms, as a Provider on the mesami platform, you undertake to:
1.  **Provide Accurate Information:** All information provided in your profile and service descriptions must be accurate, complete, and up-to-date.
2.  **Professionalism and Quality:** Offer services to a high professional standard, consistent with your descriptions and stated competencies.
3.  **Communication:** Communicate promptly, clearly, and respectfully with Clients through the platform.
4.  **Legal Compliance:** Comply with all applicable laws and regulations concerning your professional activity and use of the platform.
5.  **Confidentiality:** Respect the confidentiality of Client data with whom you interact.
6.  **Responsibility:** Be solely responsible for the quality, safety, and legality of the services rendered. mesami is an intermediary platform and assumes no responsibility for the actual services.
7.  **Reviews:** Accept that Clients may leave reviews about your services, and these reviews will be public.
8.  **Fees and Commissions:** Understand and accept the fee or commission structure applicable to Providers (if any), as communicated by mesami. (Specific details will be added later).
9.  **Interactions:** Maintain appropriate conduct and not use the platform for illegal, fraudulent, or deceptive activities.
10. **Account Suspension:** mesami reserves the right to suspend or terminate the account of any Provider who violates these terms or platform policies, or in the event of repeated and justified complaints from Clients.

By checking the "I agree" box, you confirm that you have read, understood, and agree to these Terms and the General Terms and Conditions of the mesami platform, available in full at /terms.`
  },
  regProvTermsAgreementText: { ro: "Sunt de acord cu", ru: "Я согласен с", en: "I agree to the" },

  invalidCredentialsError: { ro: "Email sau parolă incorectă.", ru: "Неверный email или пароль.", en: "Incorrect email or password." },
  // Dashboard Menu translations
  clientDashboardNav: { ro: "Panou Client", ru: "Панель клиента", en: "Client Panel" },
  myBookingsClient: { ro: "Rezervările Mele", ru: "Мои бронирования", en: "My Bookings" },
  myMessages: { ro: "Mesaje", ru: "Сообщения", en: "Messages" },
  providerDashboardNav: { ro: "Panou Prestator", ru: "Панель поставщика", en: "Provider Panel" },
  providerCalendar: { ro: "Calendar Prestator", ru: "Календарь поставщика", en: "Provider Calendar" },
  myAddresses: { ro: "Adresele Mele", ru: "Мои адреса", en: "My Addresses" },
  profileSettings: { ro: "Setări Profil", ru: "Настройки профиля", en: "Profile Settings" },

  // Additional admin panel translations
  adminPanel: { ro: "Panou Admin", ru: "Панель администратора", en: "Admin Panel" },
  refreshData: { ro: "Actualizează Datele", ru: "Обновить данные", en: "Refresh Data" },
  loadingData: { ro: "Se încarcă datele...", ru: "Загрузка данных...", en: "Loading data..." },

  // Admin field display translations
  adminFieldYes: { ro: "Da", ru: "Да", en: "Yes" },
  adminFieldNo: { ro: "Nu", ru: "Нет", en: "No" },
  adminFieldNotProvided: { ro: "Nu este furnizat", ru: "Не предоставлено", en: "Not provided" },
  adminFieldNoneSpecified: { ro: "Nimic specificat", ru: "Ничего не указано", en: "None specified" },
  adminNoServiceDetailsAvailable: { ro: "Nu sunt disponibile detalii despre serviciu", ru: "Детали услуги недоступны", en: "No service details available" },
  adminBasicInformation: { ro: "Informații de Bază", ru: "Основная информация", en: "Basic Information" },
  adminExperienceYears: { ro: "Ani de Experiență", ru: "Лет опыта", en: "Experience Years" },
  adminPricing: { ro: "Prețuri", ru: "Цены", en: "Pricing" },
  adminSubscriptionAmount: { ro: "Suma Abonament", ru: "Сумма подписки", en: "Subscription Amount" },
  adminSubscriptionUnit: { ro: "Unitate Abonament", ru: "Единица подписки", en: "Subscription Unit" },
  adminSubscriptionText: { ro: "Text Abonament", ru: "Текст подписки", en: "Subscription Text" },
  adminSubscriptionDetails: { ro: "Detalii Abonament", ru: "Детали подписки", en: "Subscription Details" },
  adminDocuments: { ro: "Documente", ru: "Документы", en: "Documents" },
  adminBulletinFile: { ro: "Fișier Buletin", ru: "Файл справки", en: "Bulletin File" },
  adminDiplomaFiles: { ro: "Fișiere Diplome", ru: "Файлы дипломов", en: "Diploma Files" },
  adminRecommendationFiles: { ro: "Fișiere Recomandări", ru: "Файлы рекомендаций", en: "Recommendation Files" },

  // Admin service-specific translations
  adminServicesOffered: { ro: "Servicii Oferite", ru: "Предлагаемые услуги", en: "Services Offered" },
  adminAdditionalQualifications: { ro: "Calificări Suplimentare", ru: "Дополнительные квалификации", en: "Additional Qualifications" },
  adminOccasional: { ro: "Ocazional", ru: "Время от времени", en: "Occasional" },
  adminBabysitting: { ro: "Îngrijire copii", ru: "Присмотр за детьми", en: "Babysitting" },
  adminPlaytime: { ro: "Timp de joacă", ru: "Время игр", en: "Playtime" },
  adminMeals: { ro: "Mese", ru: "Питание", en: "Meals" },
  adminBedtime: { ro: "Culcare", ru: "Время сна", en: "Bedtime" },
  adminEducational: { ro: "Educațional", ru: "Образовательные", en: "Educational" },
  adminOutdoorActivities: { ro: "Activități în aer liber", ru: "Активности на свежем воздухе", en: "Outdoor Activities" },
  adminTransport: { ro: "Transport", ru: "Транспорт", en: "Transport" },
  adminHousework: { ro: "Treburi casnice", ru: "Домашние дела", en: "Housework" },
  adminHomeworkHelp: { ro: "Ajutor la teme", ru: "Помощь с домашним заданием", en: "Homework Help" },
  adminOwnTransport: { ro: "Transport propriu", ru: "Собственный транспорт", en: "Own Transport" },
  adminCooking: { ro: "Gătit", ru: "Приготовление пищи", en: "Cooking" },
  adminSpecialNeeds: { ro: "Nevoi speciale", ru: "Особые потребности", en: "Special Needs" },
  adminOvernightCare: { ro: "Îngrijire de noapte", ru: "Ночной уход", en: "Overnight Care" },
  adminAdditionalLanguages: { ro: "Limbi suplimentare", ru: "Дополнительные языки", en: "Additional Languages" },

  // Elder care specific translations
  adminPersonalCare: { ro: "Îngrijire personală", ru: "Личная гигиена", en: "Personal Care" },
  adminMedicalSupport: { ro: "Suport medical", ru: "Медицинская поддержка", en: "Medical Support" },
  adminCompanionship: { ro: "Companie", ru: "Общение", en: "Companionship" },
  adminHousekeeping: { ro: "Menaj", ru: "Ведение хозяйства", en: "Housekeeping" },
  adminShopping: { ro: "Cumpărături", ru: "Покупки", en: "Shopping" },
  adminMobilityAssistance: { ro: "Asistență mobilitate", ru: "Помощь с передвижением", en: "Mobility Assistance" },
  adminMedicalTraining: { ro: "Pregătire medicală", ru: "Медицинская подготовка", en: "Medical Training" },

  // Cleaning specific translations
  adminRegularCleaning: { ro: "Curățenie regulată", ru: "Регулярная уборка", en: "Regular Cleaning" },
  adminDeepCleaning: { ro: "Curățenie generală", ru: "Генеральная уборка", en: "Deep Cleaning" },
  adminWindowCleaning: { ro: "Spălat geamuri", ru: "Мытье окон", en: "Window Cleaning" },
  adminCarpetCleaning: { ro: "Curățat covoare", ru: "Чистка ковров", en: "Carpet Cleaning" },
  adminLaundry: { ro: "Spălat rufe", ru: "Стирка", en: "Laundry" },
  adminIroning: { ro: "Călcat", ru: "Глажка", en: "Ironing" },
  adminOrganizing: { ro: "Organizare", ru: "Организация", en: "Organizing" },
  adminPostConstruction: { ro: "După construcție", ru: "После строительства", en: "Post-Construction" },
  adminMoveInOut: { ro: "Mutare", ru: "Переезд", en: "Move-in/Move-out" },
  adminAdditionalServices: { ro: "Servicii Suplimentare", ru: "Дополнительные услуги", en: "Additional Services" },
  adminEcoFriendly: { ro: "Eco-friendly", ru: "Экологично", en: "Eco-Friendly" },
  adminInsured: { ro: "Asigurat", ru: "Застрахован", en: "Insured" },
  adminOwnSupplies: { ro: "Materiale proprii", ru: "Собственные материалы", en: "Own Supplies" },
  adminEcoFriendlyProducts: { ro: "Produse eco-friendly", ru: "Экологичные продукты", en: "Eco-Friendly Products" },
  adminOwnTransportExtra: { ro: "Transport propriu (extra)", ru: "Собственный транспорт (доп)", en: "Own Transport (Extra)" },
  adminInsuranceCoverage: { ro: "Acoperire asigurare", ru: "Страховое покрытие", en: "Insurance Coverage" },

  // Tutoring specific translations
  adminAfterSchool: { ro: "After-school", ru: "После школы", en: "After School" },
  adminIndividualLessons: { ro: "Lecții individuale", ru: "Индивидуальные уроки", en: "Individual Lessons" },
  adminGradeLevels: { ro: "Niveluri de clasă", ru: "Уровни классов", en: "Grade Levels" },
  adminGrades1_4: { ro: "Clasele 1-4", ru: "Классы 1-4", en: "Grades 1-4" },
  adminGrades5_8: { ro: "Clasele 5-8", ru: "Классы 5-8", en: "Grades 5-8" },
  adminGrades9_12: { ro: "Clasele 9-12", ru: "Классы 9-12", en: "Grades 9-12" },
  adminSubjects: { ro: "Materii", ru: "Предметы", en: "Subjects" },
  adminRomanian: { ro: "Română", ru: "Румынский", en: "Romanian" },
  adminMath: { ro: "Matematică", ru: "Математика", en: "Math" },
  adminEnglish: { ro: "Engleză", ru: "Английский", en: "English" },
  adminOtherSubjects: { ro: "Alte materii", ru: "Другие предметы", en: "Other Subjects" },
  adminLessonFormats: { ro: "Formate lecții", ru: "Форматы уроков", en: "Lesson Formats" },
  adminOnline: { ro: "Online", ru: "Онлайн", en: "Online" },
  adminOwnHome: { ro: "La domiciliul tutorelui", ru: "Дома у репетитора", en: "At Tutor's Home" },
  adminChildHome: { ro: "La domiciliul elevului", ru: "Дома у ученика", en: "At Student's Home" },
  adminGames: { ro: "Jocuri", ru: "Игры", en: "Games" },
  adminSnack: { ro: "Gustări", ru: "Закуски", en: "Snack" },
  adminSupervisedHomework: { ro: "Teme supravegheate", ru: "Контролируемые домашние задания", en: "Supervised Homework" },

  // Cooking specific translations
  adminMealPrep: { ro: "Pregătire mese", ru: "Приготовление еды", en: "Meal Prep" },
  adminCatering: { ro: "Catering", ru: "Кейтеринг", en: "Catering" },
  adminSpecialDiet: { ro: "Dietă specială", ru: "Специальная диета", en: "Special Diet" },
  adminCookingLessons: { ro: "Lecții de gătit", ru: "Уроки готовки", en: "Cooking Lessons" },
  adminBaking: { ro: "Copt", ru: "Выпечка", en: "Baking" },
  adminMealPlanning: { ro: "Planificare mese", ru: "Планирование питания", en: "Meal Planning" },
  adminCuisineInternational: { ro: "Internațională", ru: "Международная", en: "International" },
  adminCuisineVegan: { ro: "Vegană", ru: "Веганская", en: "Vegan" },
  adminServiceOptions: { ro: "Opțiuni serviciu", ru: "Варианты услуг", en: "Service Options" },
  adminMealDetailsExtras: { ro: "Detalii mese și extra", ru: "Детали блюд и дополнительно", en: "Meal Details & Extras" },
  adminOwnIngredients: { ro: "Ingrediente proprii", ru: "Собственные ингредиенты", en: "Own Ingredients" },
  adminDietaryRestrictions: { ro: "Restricții dietetice", ru: "Диетические ограничения", en: "Dietary Restrictions" },
  adminMealDelivery: { ro: "Livrare mese", ru: "Доставка еды", en: "Meal Delivery" },

  // Missing Elder Care fields
  adminAvailabilitySchedule: { ro: "Program disponibilitate", ru: "График доступности", en: "Availability Schedule" },
  adminWeekdays: { ro: "Zile lucrătoare", ru: "Будние дни", en: "Weekdays" },
  adminWeekends: { ro: "Weekend", ru: "Выходные", en: "Weekends" },
  adminEvenings: { ro: "Seara", ru: "Вечера", en: "Evenings" },
  adminFirstAid: { ro: "Primul ajutor", ru: "Первая помощь", en: "First Aid" },
  adminMedicalTrainingExtra: { ro: "Pregătire medicală (extra)", ru: "Медицинская подготовка (доп)", en: "Medical Training (Extra)" },
  adminOwnTransportExtra: { ro: "Transport propriu (extra)", ru: "Собственный транспорт (доп)", en: "Own Transport (Extra)" },
  adminLanguagesExtra: { ro: "Limbi suplimentare (extra)", ru: "Дополнительные языки (доп)", en: "Additional Languages (Extra)" },
  adminSpecialNeedsExtra: { ro: "Nevoi speciale (extra)", ru: "Особые потребности (доп)", en: "Special Needs (Extra)" },
  adminOvernightCareExtra: { ro: "Îngrijire de noapte (extra)", ru: "Ночной уход (доп)", en: "Overnight Care (Extra)" },

  // Missing Cooking fields
  adminPricingSubscription: { ro: "Prețuri abonament", ru: "Цены подписки", en: "Subscription Pricing" },
  adminSubscriptionAmount: { ro: "Suma abonament", ru: "Сумма подписки", en: "Subscription Amount" },
  adminSubscriptionUnit: { ro: "Unitate abonament", ru: "Единица подписки", en: "Subscription Unit" },
  adminSubscriptionText: { ro: "Text abonament", ru: "Текст подписки", en: "Subscription Text" },
  adminSubscriptionDetails: { ro: "Detalii abonament", ru: "Детали подписки", en: "Subscription Details" },
  adminGroceryShopping: { ro: "Cumpărături alimentare", ru: "Покупка продуктов", en: "Grocery Shopping" },
  adminKitchenCleanup: { ro: "Curățenie bucătărie", ru: "Уборка кухни", en: "Kitchen Cleanup" },
  adminCuisineTypes: { ro: "Tipuri de bucătărie", ru: "Типы кухни", en: "Cuisine Types" },
  adminCuisineRomanian: { ro: "Bucătărie românească", ru: "Румынская кухня", en: "Romanian Cuisine" },
  adminCuisineItalian: { ro: "Bucătărie italiană", ru: "Итальянская кухня", en: "Italian Cuisine" },
  adminCuisineFrench: { ro: "Bucătărie franceză", ru: "Французская кухня", en: "French Cuisine" },
  adminCuisineAsian: { ro: "Bucătărie asiatică", ru: "Азиатская кухня", en: "Asian Cuisine" },
  adminCuisineVegetarian: { ro: "Vegetariană", ru: "Вегетарианская", en: "Vegetarian" },
  adminCuisineVegan: { ro: "Vegană", ru: "Веганская", en: "Vegan" },
  adminOtherCuisine: { ro: "Alte bucătării", ru: "Другие кухни", en: "Other Cuisines" },

  // Simplified cuisine type keys (used in cooking service details)
  adminItalian: { ro: "Italiană", ru: "Итальянская", en: "Italian" },
  adminFrench: { ro: "Franceză", ru: "Французская", en: "French" },
  adminAsian: { ro: "Asiatică", ru: "Азиатская", en: "Asian" },
  adminVegetarian: { ro: "Vegetariană", ru: "Вегетарианская", en: "Vegetarian" },
  adminVegan: { ro: "Vegană", ru: "Веганская", en: "Vegan" },
  adminMealDetails: { ro: "Detalii mese", ru: "Детали блюд", en: "Meal Details" },
  adminPricePerMeal: { ro: "Preț pe masă", ru: "Цена за блюдо", en: "Price Per Meal" },
  adminMinPortions: { ro: "Porții minime", ru: "Минимальные порции", en: "Min Portions" },
  adminWeekendAvailable: { ro: "Disponibil weekend", ru: "Доступен в выходные", en: "Weekend Available" },

  // Missing Cleaning fields
  adminAdditionalFeatures: { ro: "Caracteristici suplimentare", ru: "Дополнительные функции", en: "Additional Features" },
  adminEmergencyService: { ro: "Serviciu de urgență", ru: "Экстренная служба", en: "Emergency Service" },

  // Elder Care Service fields
  adminPersonalCare: { ro: "Îngrijire personală", ru: "Личная гигиена", en: "Personal Care" },
  adminMedicalSupport: { ro: "Suport medical", ru: "Медицинская поддержка", en: "Medical Support" },
  adminCompanionship: { ro: "Companie", ru: "Общение", en: "Companionship" },
  adminHousekeeping: { ro: "Menaj", ru: "Домашнее хозяйство", en: "Housekeeping" },
  adminMeals: { ro: "Mese", ru: "Питание", en: "Meals" },
  adminTransport: { ro: "Transport", ru: "Транспорт", en: "Transport" },
  adminShopping: { ro: "Cumpărături", ru: "Покупки", en: "Shopping" },
  adminMobility: { ro: "Mobilitate", ru: "Мобильность", en: "Mobility" },

  // Admin page UI translations
  adminSuccess: { ro: "Succes", ru: "Успех", en: "Success" },
  adminError: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  adminNoServices: { ro: "Fără servicii", ru: "Нет услуг", en: "No Services" },
  adminNoServicesDescription: { ro: "Nu există servicii în așteptare pentru procesare", ru: "Нет ожидающих услуг для обработки", en: "No pending services to process" },
  adminServiceUpdateSuccess: { ro: "Serviciu actualizat cu succes", ru: "Услуга успешно обновлена", en: "Service updated successfully" },
  adminServiceUpdateError: { ro: "Eșec la actualizarea statusului serviciului", ru: "Не удалось обновить статус услуги", en: "Failed to update service status" },
  adminBulkActionSuccess: { ro: "Acțiune în masă completată cu succes", ru: "Массовое действие выполнено успешно", en: "Bulk action completed successfully" },
  adminBulkActionError: { ro: "Eșec la acțiunea în masă", ru: "Не удалось выполнить массовое действие", en: "Failed to perform bulk action" },
  adminExportComplete: { ro: "Export complet", ru: "Экспорт завершен", en: "Export Complete" },
  adminExportDescription: { ro: "Detaliile cererii furnizorului au fost exportate", ru: "Детали запроса поставщика экспортированы", en: "Provider request details have been exported" },

  // Navigation and actions
  adminBackToList: { ro: "Înapoi la listă", ru: "Назад к списку", en: "Back to List" },
  adminProviderRegistrationRequest: { ro: "Cerere de înregistrare furnizor", ru: "Запрос на регистрацию поставщика", en: "Provider Registration Request" },
  adminRefresh: { ro: "Actualizează", ru: "Обновить", en: "Refresh" },
  adminActions: { ro: "Acțiuni", ru: "Действия", en: "Actions" },
  adminApproveAllPending: { ro: "Aprobă toate în așteptare", ru: "Одобрить все ожидающие", en: "Approve All Pending" },
  adminRejectAllPending: { ro: "Respinge toate în așteptare", ru: "Отклонить все ожидающие", en: "Reject All Pending" },
  adminExportDetails: { ro: "Exportă detalii", ru: "Экспорт деталей", en: "Export Details" },

  // Provider information section
  adminProviderInformation: { ro: "Informații furnizor", ru: "Информация о поставщике", en: "Provider Information" },
  adminFullName: { ro: "Nume complet", ru: "Полное имя", en: "Full Name" },
  adminEmail: { ro: "Email", ru: "Электронная почта", en: "Email" },
  adminPhone: { ro: "Telefon", ru: "Телефон", en: "Phone" },
  adminOverallStatus: { ro: "Status general", ru: "Общий статус", en: "Overall Status" },
  adminVerified: { ro: "Verificat", ru: "Проверено", en: "Verified" },
  adminUnverified: { ro: "Neverificat", ru: "Не проверено", en: "Unverified" },
  adminAccountCreated: { ro: "Cont creat", ru: "Аккаунт создан", en: "Account Created" },
  adminRegistrationDate: { ro: "Data înregistrării", ru: "Дата регистрации", en: "Registration Date" },
  adminSpokenLanguages: { ro: "Limbi vorbite", ru: "Разговорные языки", en: "Spoken Languages" },
  adminUnknown: { ro: "Necunoscut", ru: "Неизвестно", en: "Unknown" },
  adminNotSpecified: { ro: "Nu este specificat", ru: "Не указано", en: "Not specified" },
  adminBio: { ro: "Biografie", ru: "Биография", en: "Bio" },
  adminAddresses: { ro: "Adrese", ru: "Адреса", en: "Addresses" },
  adminDefault: { ro: "Implicit", ru: "По умолчанию", en: "Default" },

  // Services overview section
  adminServicesOverview: { ro: "Prezentare generală servicii", ru: "Обзор услуг", en: "Services Overview" },
  adminTotalServices: { ro: "Total servicii", ru: "Всего услуг", en: "Total Services" },
  adminApproved: { ro: "Aprobate", ru: "Одобрено", en: "Approved" },
  adminPending: { ro: "În așteptare", ru: "Ожидающие", en: "Pending" },
  adminChangesNeeded: { ro: "Necesită modificări", ru: "Требуются изменения", en: "Changes Needed" },
  adminRejected: { ro: "Respinse", ru: "Отклонено", en: "Rejected" },
  adminApprovalProgress: { ro: "Progres aprobare", ru: "Прогресс одобрения", en: "Approval Progress" },
  adminServicesApproved: { ro: "servicii aprobate", ru: "услуг одобрено", en: "services approved" },

  // Service applications section
  adminServiceApplications: { ro: "Aplicații servicii", ru: "Заявки на услуги", en: "Service Applications" },
  adminServicesShown: { ro: "servicii afișate", ru: "услуг показано", en: "services shown" },
  adminFilterServices: { ro: "Filtrează servicii", ru: "Фильтр услуг", en: "Filter services" },
  adminAllServices: { ro: "Toate serviciile", ru: "Все услуги", en: "All Services" },
  adminSortBy: { ro: "Sortează după", ru: "Сортировать по", en: "Sort by" },
  adminDateCreated: { ro: "Data creării", ru: "Дата создания", en: "Date Created" },
  adminServiceName: { ro: "Nume serviciu", ru: "Название услуги", en: "Service Name" },
  adminCategory: { ro: "Categorie", ru: "Категория", en: "Category" },
  adminStatus: { ro: "Status", ru: "Статус", en: "Status" },
  adminStepper: { ro: "Pas cu pas", ru: "Пошагово", en: "Stepper" },
  adminList: { ro: "Listă", ru: "Список", en: "List" },

  // Bulk actions and service management
  adminServicesSelected: { ro: "servicii selectate", ru: "услуг выбрано", en: "services selected" },
  adminClearSelection: { ro: "Șterge selecția", ru: "Очистить выбор", en: "Clear Selection" },
  adminApproveSelected: { ro: "Aprobă selectate", ru: "Одобрить выбранные", en: "Approve Selected" },
  adminRejectSelected: { ro: "Respinge selectate", ru: "Отклонить выбранные", en: "Reject Selected" },
  adminQuickActions: { ro: "Acțiuni rapide", ru: "Быстрые действия", en: "Quick actions" },
  adminSelectAllVisible: { ro: "Selectează toate vizibile", ru: "Выбрать все видимые", en: "Select All Visible" },
  adminSelectForBulkAction: { ro: "Selectează pentru acțiune în masă", ru: "Выбрать для массового действия", en: "Select for bulk action" },
  adminNoServicesFound: { ro: "Nu s-au găsit servicii pentru filtrul selectat", ru: "Услуги не найдены для выбранного фильтра", en: "No services found for the selected filter" },
  adminShowAllServices: { ro: "Arată toate serviciile", ru: "Показать все услуги", en: "Show All Services" },

  // Service card actions
  adminApprove: { ro: "Aprobă", ru: "Одобрить", en: "Approve" },
  adminReject: { ro: "Respinge", ru: "Отклонить", en: "Reject" },
  adminNeedsChanges: { ro: "Necesită modificări", ru: "Требуются изменения", en: "Needs Changes" },
  adminAdminNotes: { ro: "Note admin", ru: "Заметки администратора", en: "Admin Notes" },
  adminYearsExperience: { ro: "ani experiență", ru: "лет опыта", en: "Years" },
  adminDocs: { ro: "Doc", ru: "Док", en: "Doc" },
  adminServiceDetails: { ro: "Detalii serviciu", ru: "Детали услуги", en: "Service Details" },
  adminYourServiceApplications: { ro: "Aplicațiile tale de servicii", ru: "Ваши заявки на услуги", en: "Your Service Applications" },
  adminComplete: { ro: "Complet", ru: "Завершено", en: "Complete" },

  // Provider registration page specific translations
  regProvSelectAgeHint: { ro: "Selectează vârstele cu care lucrezi", ru: "Выберите возрасты, с которыми работаете", en: "Select ages you work with" },
  regProvAvailabilityTypeHint: { ro: "Tipul de program disponibil", ru: "Тип доступного графика", en: "Type of available schedule" },
  regProvQualificationsTitle: { ro: "Calificări și Abilități", ru: "Квалификации и навыки", en: "Qualifications and Skills" },
  regProvActivitiesHint: { ro: "Activități pe care le poți oferi", ru: "Мероприятия, которые вы можете предложить", en: "Activities you can offer" },
  regProvSelectedServices: { ro: "Ai selectat {count} serviciu{plural}", ru: "Вы выбрали {count} услуг{plural}", en: "You have selected {count} service{plural}" },
  regProvAutoSaved: { ro: "Salvat automat", ru: "Автосохранено", en: "Auto saved" },
  regProvGoToAuth: { ro: "Mergi la Autentificare", ru: "Перейти к аутентификации", en: "Go to Authentication" },
  regProvGoToPanel: { ro: "Mergi la Panou", ru: "Перейти к панели", en: "Go to Panel" },

  // Service descriptions for provider registration
  regProvServiceDescNanny: { ro: "Îngrijire copii, activități educative, supraveghere și dezvoltare", ru: "Уход за детьми, образовательные мероприятия, надзор и развитие", en: "Childcare, educational activities, supervision and development" },
  regProvServiceDescElderCare: { ro: "Îngrijire persoane în vârstă, asistență medicală și companionship", ru: "Уход за пожилыми людьми, медицинская помощь и общение", en: "Elder care, medical assistance and companionship" },
  regProvServiceDescCleaning: { ro: "Curățenie locuințe, birouri și servicii specializate de mentenanță", ru: "Уборка домов, офисов и специализированные услуги по обслуживанию", en: "Home and office cleaning, specialized maintenance services" },
  regProvServiceDescCooking: { ro: "Preparare mese, catering și consultanță culinară personalizată", ru: "Приготовление еды, кейтеринг и персонализированные кулинарные консультации", en: "Meal preparation, catering and personalized culinary consulting" },
  regProvServiceDescTutoring: { ro: "Meditații, ajutor la teme și pregătire pentru examene", ru: "Репетиторство, помощь с домашними заданиями и подготовка к экзаменам", en: "Tutoring, homework help and exam preparation" },

  // Provider status dashboard translations
  provStatusUploadedDocs: { ro: "Documente încărcate", ru: "Загруженные документы", en: "Uploaded Documents" },
  provStatusSubmitted: { ro: "Trimis:", ru: "Отправлено:", en: "Submitted:" },
  provStatusLastUpdated: { ro: "Ultima actualizare:", ru: "Последнее обновление:", en: "Last Updated:" },
  provStatusAdminFeedback: { ro: "Feedback administrator", ru: "Отзыв администратора", en: "Admin Feedback" },
  provStatusRegistrationTitle: { ro: "Starea înregistrării furnizorului", ru: "Статус регистрации поставщика", en: "Provider Registration Status" },
  provStatusOverallProgress: { ro: "Progres general", ru: "Общий прогресс", en: "Overall Progress" },
  provStatusApproved: { ro: "Aprobat", ru: "Одобрено", en: "Approved" },
  provStatusUnderReview: { ro: "În revizuire", ru: "На рассмотрении", en: "Under Review" },
  provStatusNeedsChanges: { ro: "Necesită modificări", ru: "Требует изменений", en: "Needs Changes" },
  provStatusRejected: { ro: "Respins", ru: "Отклонено", en: "Rejected" },
  provStatusOverallNotes: { ro: "Note generale administrator", ru: "Общие заметки администратора", en: "Overall Admin Notes" },
  provStatusGoToDashboard: { ro: "Mergi la tabloul de bord", ru: "Перейти к панели управления", en: "Go to Dashboard" },
  provStatusServicesApproved: { ro: "{approved} din {total} servicii aprobate", ru: "{approved} из {total} услуг одобрено", en: "{approved} of {total} services approved" },

  // Status descriptions
  provStatusDescUnderReview: { ro: "Serviciul tău este în curs de revizuire de către echipa noastră", ru: "Ваша услуга рассматривается нашей командой", en: "Your service is being reviewed by our team" },
  provStatusDescApproved: { ro: "Serviciul tău a fost aprobat și este acum activ", ru: "Ваша услуга была одобрена и теперь активна", en: "Your service has been approved and is now live" },
  provStatusDescRejected: { ro: "Serviciul tău a fost respins. Te rugăm să revizuiești feedback-ul și să retrimiti", ru: "Ваша услуга была отклонена. Пожалуйста, просмотрите отзыв и отправьте повторно", en: "Your service was rejected. Please review the feedback and resubmit" },
  provStatusDescNeedsChanges: { ro: "Te rugăm să faci modificările solicitate și să retrimiti", ru: "Пожалуйста, внесите запрошенные изменения и отправьте повторно", en: "Please make the requested changes and resubmit" },

  // Additional provider status translations
  provStatusTrackProgress: { ro: "Urmărește progresul aplicațiilor tale de servicii", ru: "Отслеживайте прогресс ваших заявок на услуги", en: "Track the progress of your service applications" },
  provStatusViewProviderPanel: { ro: "Vezi panoul furnizorului", ru: "Посмотреть панель поставщика", en: "View Provider Panel" },
  provStatusDocumentNumber: { ro: "Document {number}", ru: "Документ {number}", en: "Document {number}" },

  // Service resubmission translations
  serviceResubmissionTitle: { ro: "Retrimitere serviciu", ru: "Повторная подача услуги", en: "Service Resubmission" },
  serviceResubmissionComingSoon: { ro: "Funcția de retrimitere serviciu va fi disponibilă în curând. Te rugăm să contactezi suportul pentru moment.", ru: "Функция повторной подачи услуги скоро будет доступна. Пожалуйста, свяжитесь с поддержкой пока что.", en: "Service resubmission feature coming soon. Please contact support for now." },

  // Timeline and notes
  adminOverallAdminNotes: { ro: "Note generale admin", ru: "Общие заметки администратора", en: "Overall Admin Notes" },
  adminRequestTimeline: { ro: "Cronologia cererii", ru: "Хронология запроса", en: "Request Timeline" },
  adminNoTimelineEvents: { ro: "Nu sunt disponibile evenimente cronologice", ru: "События хронологии недоступны", en: "No timeline events available" },

  // Service review stepper
  adminAllServicesOverview: { ro: "Prezentare generală toate serviciile", ru: "Обзор всех услуг", en: "All Services Overview" },
  adminReviewMode: { ro: "Mod recenzie", ru: "Режим обзора", en: "Review Mode" },
  adminServiceReviewProgress: { ro: "Progres recenzie servicii", ru: "Прогресс обзора услуг", en: "Service Review Progress" },
  adminViewAll: { ro: "Vezi toate", ru: "Посмотреть все", en: "View All" },
  adminProgress: { ro: "Progres", ru: "Прогресс", en: "Progress" },
  adminServicesReviewed: { ro: "servicii revizuite", ru: "услуг проверено", en: "services reviewed" },
  adminServiceOf: { ro: "Serviciul", ru: "Услуга", en: "Service" },
  adminOf: { ro: "din", ru: "из", en: "of" },
  adminPrevious: { ro: "Anterior", ru: "Предыдущий", en: "Previous" },
  adminNext: { ro: "Următorul", ru: "Следующий", en: "Next" },
  errorLoadingAnalytics: { ro: "Eroare la încărcarea analizei", ru: "Ошибка загрузки аналитики", en: "Error loading analytics" },
  errorLoadingUserAnalytics: { ro: "Eroare la încărcarea analizei utilizatorilor", ru: "Ошибка загрузки аналитики пользователей", en: "Error loading user analytics" },
  errorLoadingProviderAnalytics: { ro: "Eroare la încărcarea analizei prestatorilor", ru: "Ошибка загрузки аналитики поставщиков", en: "Error loading provider analytics" },
  errorLoadingServiceAnalytics: { ro: "Eroare la încărcarea analizei serviciilor", ru: "Ошибка загрузки аналитики услуг", en: "Error loading service analytics" },
  errorLoadingActivityAnalytics: { ro: "Eroare la încărcarea analizei activității", ru: "Ошибка загрузки аналитики активности", en: "Error loading activity analytics" },
  totalRegisteredUsers: { ro: "Total utilizatori înregistrați", ru: "Всего зарегистрированных пользователей", en: "Total registered users" },
  newRegistrationsToday: { ro: "Înregistrări noi astăzi", ru: "Новые регистрации сегодня", en: "New registrations today" },
  activeThisMonth: { ro: "Activi luna aceasta", ru: "Активные в этом месяце", en: "Active this month" },
  averageSessionDuration: { ro: "Durata medie a sesiunii", ru: "Средняя продолжительность сессии", en: "Average session duration" },
  totalServicesCreated: { ro: "Total servicii create", ru: "Всего создано услуг", en: "Total services created" },
  totalBookingsMade: { ro: "Total rezervări efectuate", ru: "Всего сделано бронирований", en: "Total bookings made" },
  averageServiceRating: { ro: "Rating mediu servicii", ru: "Средний рейтинг услуг", en: "Average service rating" },
  activeUsersToday: { ro: "Utilizatori activi astăzi", ru: "Активные пользователи сегодня", en: "Active users today" },
  estimatedSessions: { ro: "Sesiuni estimate", ru: "Оценочные сессии", en: "Estimated sessions" },
  averageSessionTime: { ro: "Timp mediu sesiune", ru: "Среднее время сессии", en: "Average session time" },
  estimatedPageViews: { ro: "Vizualizări pagini estimate", ru: "Оценочные просмотры страниц", en: "Estimated page views" },
  todaysApprovals: { ro: "Aprobări de astăzi", ru: "Сегодняшние одобрения", en: "Today's approvals" },
  awaitingReview: { ro: "În așteptarea revizuirii", ru: "Ожидает рассмотрения", en: "Awaiting review" },
  timeToConfirmBookings: { ro: "Timp pentru confirmarea rezervărilor", ru: "Время подтверждения бронирований", en: "Time to confirm bookings" },

  // Pagination translations
  showingResults: { ro: "Afișez {start}-{end} din {total} rezultate", ru: "Показано {start}-{end} из {total} результатов", en: "Showing {start}-{end} of {total} results" },
  noResultsFound: { ro: "Nu există rezultate", ru: "Результаты не найдены", en: "No results found" },
  servicesText: { ro: "servicii", ru: "услуг", en: "services" },
  usersText: { ro: "utilizatori", ru: "пользователей", en: "users" },
  requestsText: { ro: "cereri", ru: "запросов", en: "requests" },

  // Search and filter translations
  searchUsersPlaceholder: { ro: "Caută după nume sau email...", ru: "Поиск по имени или email...", en: "Search by name or email..." },
  searchRequestsPlaceholder: { ro: "Caută după nume, email sau notițe...", ru: "Поиск по имени, email или заметкам...", en: "Search by name, email or notes..." },
  filterByRole: { ro: "Filtrează după rol", ru: "Фильтр по роли", en: "Filter by role" },
  filterByStatus: { ro: "Filtrează după status", ru: "Фильтр по статусу", en: "Filter by status" },
  allRoles: { ro: "Toate Rolurile", ru: "Все роли", en: "All Roles" },
  allStatusesFilter: { ro: "Toate", ru: "Все", en: "All" },
  statusPendingFilter: { ro: "În așteptare", ru: "В ожидании", en: "Pending" },
  statusApprovedFilter: { ro: "Aprobate", ru: "Одобренные", en: "Approved" },
  statusRejectedFilter: { ro: "Respinse", ru: "Отклоненные", en: "Rejected" },

  // Dialog translations
  requestDetailsTitle: { ro: "Detalii Cerere", ru: "Детали запроса", en: "Request Details" },
  requestApprovedMessage: { ro: "Cererea a fost aprobată", ru: "Запрос был одобрен", en: "Request has been approved" },
  requestRejectedMessage: { ro: "Cererea a fost respinsă", ru: "Запрос был отклонен", en: "Request has been rejected" },
};

// Analytics specific translations
export const analyticsTranslations = {
  overview: { ro: "Prezentare generală", ru: "Обзор", en: "Overview" },
  users: { ro: "Utilizatori", ru: "Пользователи", en: "Users" },
  providers: { ro: "Prestatori", ru: "Поставщики", en: "Providers" },
  services: { ro: "Servicii", ru: "Услуги", en: "Services" },
  activity: { ro: "Activitate", ru: "Активность", en: "Activity" },
};
