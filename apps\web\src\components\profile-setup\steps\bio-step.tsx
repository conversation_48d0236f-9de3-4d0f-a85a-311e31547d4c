"use client";

import React, { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { MessageSquare, CheckCircle, Lightbulb } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { PROFILE_SETUP_VALIDATION } from '@/types/profile-setup';

interface BioStepProps {
  value: string;
  onChange: (bio: string) => void;
}

const bioStepTranslations = {
  en: {
    title: "Tell Us About Yourself",
    description: "Write a short description to help others get to know you better. This is optional but recommended.",
    bioLabel: "About Me",
    bioPlaceholder: "Tell others about yourself, your interests, what services you're looking for, or anything else you'd like to share...",
    characterCount: "{current} / {max} characters",
    whyUseful: "Why is a bio useful?",
    reason1: "Service providers can better understand your needs",
    reason2: "Build trust and connection with the community",
    reason3: "Get more personalized service recommendations",
    reason4: "Stand out when requesting services",
    tipsTitle: "Tips for a great bio:",
    tip1: "Mention your interests and hobbies",
    tip2: "Describe what services you're looking for",
    tip3: "Share your location or preferred areas",
    tip4: "Keep it friendly and professional",
    exampleBio: "Example: \"I'm a busy professional living in Chișinău who loves cooking but needs help with house cleaning and occasional handyman services. I appreciate reliable, quality work and clear communication.\""
  },
  ro: {
    title: "Spune-ne Despre Tine",
    description: "Scrie o scurtă descriere pentru a-i ajuta pe alții să te cunoască mai bine. Acest pas este opțional dar recomandat.",
    bioLabel: "Despre Mine",
    bioPlaceholder: "Spune-le altora despre tine, interesele tale, ce servicii cauți, sau orice altceva ai vrea să împărtășești...",
    characterCount: "{current} / {max} caractere",
    whyUseful: "De ce este utilă o biografie?",
    reason1: "Furnizorii de servicii îți pot înțelege mai bine nevoile",
    reason2: "Construiește încredere și conexiune cu comunitatea",
    reason3: "Primești recomandări de servicii mai personalizate",
    reason4: "Te evidențiezi când soliciți servicii",
    tipsTitle: "Sfaturi pentru o biografie excelentă:",
    tip1: "Menționează-ți interesele și hobby-urile",
    tip2: "Descrie ce servicii cauți",
    tip3: "Împărtășește locația sau zonele preferate",
    tip4: "Păstrează-o prietenoasă și profesională",
    exampleBio: "Exemplu: \"Sunt un profesionist ocupat din Chișinău care iubește să gătească, dar are nevoie de ajutor cu curățenia casei și servicii ocazionale de reparații. Apreciez munca de calitate, fiabilă și comunicarea clară.\""
  },
  ru: {
    title: "Расскажите о Себе",
    description: "Напишите краткое описание, чтобы помочь другим лучше узнать вас. Этот шаг необязательный, но рекомендуется.",
    bioLabel: "Обо Мне",
    bioPlaceholder: "Расскажите другим о себе, ваших интересах, какие услуги вы ищете, или что-то еще, чем хотели бы поделиться...",
    characterCount: "{current} / {max} символов",
    whyUseful: "Почему биография полезна?",
    reason1: "Поставщики услуг могут лучше понять ваши потребности",
    reason2: "Создает доверие и связь с сообществом",
    reason3: "Получайте более персонализированные рекомендации услуг",
    reason4: "Выделяйтесь при запросе услуг",
    tipsTitle: "Советы для отличной биографии:",
    tip1: "Упомяните ваши интересы и хобби",
    tip2: "Опишите, какие услуги вы ищете",
    tip3: "Поделитесь вашим местоположением или предпочитаемыми районами",
    tip4: "Будьте дружелюбны и профессиональны",
    exampleBio: "Пример: \"Я занятой профессионал из Кишинева, который любит готовить, но нуждается в помощи с уборкой дома и случайными услугами мастера. Я ценю надежную, качественную работу и четкое общение.\""
  }
};

export function BioStep({ value, onChange }: BioStepProps) {
  const { translate } = useLanguage();
  const [focused, setFocused] = useState(false);

  const maxLength = PROFILE_SETUP_VALIDATION.bio.maxLength;
  const currentLength = value.length;
  const isOverLimit = currentLength > maxLength;

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const reasons = [
    translate(bioStepTranslations, 'reason1'),
    translate(bioStepTranslations, 'reason2'),
    translate(bioStepTranslations, 'reason3'),
    translate(bioStepTranslations, 'reason4'),
  ];

  const tips = [
    translate(bioStepTranslations, 'tip1'),
    translate(bioStepTranslations, 'tip2'),
    translate(bioStepTranslations, 'tip3'),
    translate(bioStepTranslations, 'tip4'),
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <MessageSquare className="w-6 h-6 text-primary" />
          </div>
        </div>
        <h3 className="text-xl font-semibold mb-2">
          {translate(bioStepTranslations, 'title')}
        </h3>
        <p className="text-muted-foreground">
          {translate(bioStepTranslations, 'description')}
        </p>
      </div>

      {/* Bio Input */}
      <div className="space-y-2">
        <Label htmlFor="bio" className="text-sm font-medium">
          {translate(bioStepTranslations, 'bioLabel')}
        </Label>
        <div className="relative">
          <Textarea
            id="bio"
            value={value}
            onChange={handleChange}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={translate(bioStepTranslations, 'bioPlaceholder')}
            className={`min-h-[120px] resize-none ${
              isOverLimit ? 'border-red-500 focus:border-red-500' : ''
            }`}
            maxLength={maxLength + 50} // Allow slight overflow for better UX
          />
        </div>
        
        {/* Character Count */}
        <div className="flex justify-between items-center">
          <div></div>
          <p className={`text-xs ${
            isOverLimit ? 'text-red-600' : 
            currentLength > maxLength * 0.8 ? 'text-yellow-600' : 
            'text-muted-foreground'
          }`}>
            {translate(bioStepTranslations, 'characterCount')
              .replace('{current}', currentLength.toString())
              .replace('{max}', maxLength.toString())}
          </p>
        </div>

        {isOverLimit && (
          <p className="text-sm text-red-600">
            {PROFILE_SETUP_VALIDATION.bio.errorMessage}
          </p>
        )}
      </div>

      {/* Why This Is Useful */}
      <Card className="bg-muted/30">
        <CardContent className="p-4">
          <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
            <MessageSquare className="w-4 h-4 text-primary" />
            {translate(bioStepTranslations, 'whyUseful')}
          </h4>
          <ul className="space-y-2">
            {reasons.map((reason, index) => (
              <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>{reason}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Tips */}
      <Card className="bg-blue-50/50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
            <Lightbulb className="w-4 h-4 text-blue-600" />
            {translate(bioStepTranslations, 'tipsTitle')}
          </h4>
          <ul className="space-y-1 mb-3">
            {tips.map((tip, index) => (
              <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                <span className="text-blue-600 font-medium">•</span>
                <span>{tip}</span>
              </li>
            ))}
          </ul>
          
          {/* Example */}
          <div className="mt-4 p-3 bg-white/50 rounded-lg border border-blue-200">
            <p className="text-xs text-blue-800 font-medium mb-1">
              {translate(bioStepTranslations, 'exampleBio').split(':')[0]}:
            </p>
            <p className="text-xs text-blue-700 italic">
              {translate(bioStepTranslations, 'exampleBio').split(':')[1]?.trim()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
