import type { Location, LocationPayload } from '@repo/types';

export interface LocationEntry {
  id: number;
  slug: string;
  translationKey: string;
  name: string;
  displayName: string;
  type: string;
  parentId: number | null;
  sortOrder: number;
  isCapital: boolean;
  specialType: string | null;
}

export interface LocationResponse {
  success: boolean;
  locations: LocationEntry[];
}

export interface SingleLocationResponse {
  success: boolean;
  location: LocationEntry;
}

async function _getLocations(options?: {
  lang?: string;
  includeAll?: boolean;
  type?: string;
}): Promise<LocationEntry[]> {
  const params = new URLSearchParams();

  if (options?.lang) {
    params.append('lang', options.lang);
  }

  if (options?.includeAll !== undefined) {
    params.append('includeAll', options.includeAll.toString());
  }

  if (options?.type) {
    params.append('type', options.type);
  }

  const queryString = params.toString();
  const url = `/api/proxy/locations${queryString ? `?${queryString}` : ''}`;

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch locations');
  }

  const data: LocationResponse = await response.json();
  return data.locations;
}

async function _getLocationById(id: number, lang?: string): Promise<LocationEntry> {
  const params = new URLSearchParams();
  if (lang) {
    params.append('lang', lang);
  }

  const queryString = params.toString();
  const url = `/api/proxy/locations/${id}${queryString ? `?${queryString}` : ''}`;

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch location');
  }

  const data: SingleLocationResponse = await response.json();
  return data.location;
}

async function _getLocationByValue(value: string, lang?: string): Promise<LocationEntry> {
  const params = new URLSearchParams();
  if (lang) {
    params.append('lang', lang);
  }

  const queryString = params.toString();
  const url = `/api/proxy/locations/by-value/${value}${queryString ? `?${queryString}` : ''}`;

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch location');
  }

  const data: SingleLocationResponse = await response.json();
  return data.location;
}

// Helper function to get locations for search pages (includes "All" option)
async function _getLocationsForSearch(lang?: string): Promise<LocationEntry[]> {
  return _getLocations({ lang, includeAll: true });
}

// Helper function to get locations for form pages (excludes "All" option)
async function _getLocationsForForms(lang?: string): Promise<LocationEntry[]> {
  return _getLocations({ lang, includeAll: false });
}

// Helper function to get location display name with hierarchy
function _getLocationDisplayName(
  slug: string,
  locations: LocationEntry[],
  translateFn?: (key: string) => string
): string {
  const location = locations.find(loc => loc.slug === slug);
  if (!location) return slug;

  // If a translate function is provided, use it; otherwise use displayName from API
  if (translateFn) {
    return translateFn(location.translationKey);
  }

  return location.displayName;
}

// Helper function to build location hierarchy path
function _getLocationPath(
  slug: string,
  locations: LocationEntry[]
): LocationEntry[] {
  const location = locations.find(loc => loc.slug === slug);
  if (!location) return [];

  const path: LocationEntry[] = [location];
  let currentLocation = location;

  while (currentLocation.parentId) {
    const parent = locations.find(loc => loc.id === currentLocation.parentId);
    if (parent) {
      path.unshift(parent);
      currentLocation = parent;
    } else {
      break;
    }
  }

  return path;
}

// Dynamic location queries to replace hardcoded arrays
async function _getChisinauDirectSuburbValues(): Promise<string[]> {
  try {
    const locations = await _getLocations();
    return locations
      .filter(loc => loc.type === 'Suburb')
      .map(loc => loc.slug);
  } catch (error) {
    console.warn('Failed to get Chisinau suburb values:', error);
    return [];
  }
}

async function _getChisinauCitySectorValues(): Promise<string[]> {
  try {
    const locations = await _getLocations();
    return locations
      .filter(loc => loc.type === 'Sector')
      .map(loc => loc.slug);
  } catch (error) {
    console.warn('Failed to get Chisinau sector values:', error);
    return [];
  }
}

export const LocationService = {
  getLocations: _getLocations,
  getLocationById: _getLocationById,
  getLocationByValue: _getLocationByValue,
  getLocationsForSearch: _getLocationsForSearch,
  getLocationsForForms: _getLocationsForForms,
  getLocationDisplayName: _getLocationDisplayName,
  getLocationPath: _getLocationPath,
  getChisinauDirectSuburbValues: _getChisinauDirectSuburbValues,
  getChisinauCitySectorValues: _getChisinauCitySectorValues,
};
