
import type { SearchApiResponse, AdvertisedServicePayload } from '@repo/types';

const ITEMS_PER_PAGE = 6;

// Helper functions to create service-specific filter objects
export function createNannyFilters(filters: NannyFilters): Record<string, boolean> {
  return Object.entries(filters)
    .filter(([_, value]) => value === true)
    .reduce((acc, [key, _]) => ({ ...acc, [key]: true }), {});
}

export function createElderCareFilters(filters: ElderCareFilters): Record<string, boolean> {
  return Object.entries(filters)
    .filter(([_, value]) => value === true)
    .reduce((acc, [key, _]) => ({ ...acc, [key]: true }), {});
}

export function createCleaningFilters(filters: CleaningFilters): Record<string, boolean> {
  return Object.entries(filters)
    .filter(([_, value]) => value === true)
    .reduce((acc, [key, _]) => ({ ...acc, [key]: true }), {});
}

export function createTutoringFilters(filters: TutoringFilters): Record<string, boolean> {
  return Object.entries(filters)
    .filter(([_, value]) => value === true)
    .reduce((acc, [key, _]) => ({ ...acc, [key]: true }), {});
}

export function createCookingFilters(filters: CookingFilters): Record<string, boolean> {
  return Object.entries(filters)
    .filter(([_, value]) => value === true)
    .reduce((acc, [key, _]) => ({ ...acc, [key]: true }), {});
}

export interface SearchFilters {
  location: string;
  priceRange: [number, number];
  minRating: number;
  availability: {
    weekdays: boolean;
    weekends: boolean;
    evenings: boolean;
  };
  serviceSpecific?: Record<string, boolean>; // Dynamic service-specific filters
}

// Service-specific filter interfaces for type safety
export interface NannyFilters {
  PreferredAge_0_2?: boolean;
  PreferredAge_3_6?: boolean;
  PreferredAge_7_plus?: boolean;
  AvailabilityFullTime?: boolean;
  AvailabilityPartTime?: boolean;
  FirstAid?: boolean;
  SchoolPickup?: boolean;
  ActivityWalks?: boolean;
  ActivityGames?: boolean;
  ActivityFeeding?: boolean;
  ActivitySleep?: boolean;
}

export interface ElderCareFilters {
  TypeMobil?: boolean;
  TypePartialImobilizat?: boolean;
  TypeCompletImobilizat?: boolean;
  MedicalKnowledgeBasic?: boolean;
  MedicalKnowledgeAdvanced?: boolean;
  MedicationAdmin?: boolean;
  DrivingLicense?: boolean;
  ActivityCooking?: boolean;
  ActivityCleaningLight?: boolean;
  ActivityCompanionship?: boolean;
}

export interface CleaningFilters {
  PropertyTypeApartments?: boolean;
  PropertyTypeHouses?: boolean;
  PropertyTypeOffices?: boolean;
  TypeGeneral?: boolean;
  TypePostRenovation?: boolean;
  TypeOccasional?: boolean;
  TypeRegular?: boolean;
  OwnProducts?: boolean;
  ExtraIroning?: boolean;
  ExtraWindows?: boolean;
  ExtraDisinfection?: boolean;
}

export interface TutoringFilters {
  ServiceAfterSchool?: boolean;
  ServiceHomeworkHelp?: boolean;
  ServiceIndividualLessons?: boolean;
  Grades_1_4?: boolean;
  Grades_5_8?: boolean;
  Grades_9_12?: boolean;
  SubjectRomanian?: boolean;
  SubjectMath?: boolean;
  SubjectEnglish?: boolean;
  FormatOnline?: boolean;
  FormatOwnHome?: boolean;
  FormatChildHome?: boolean;
  ExtraGames?: boolean;
  ExtraSnack?: boolean;
  ExtraTransport?: boolean;
  ExtraSupervisedHomework?: boolean;
}

export interface CookingFilters {
  CuisineTypeTraditional?: boolean;
  CuisineTypeVegetarian?: boolean;
  CuisineTypeKids?: boolean;
  CuisineTypeDiet?: boolean;
  OffersDelivery?: boolean;
  AtClientHome?: boolean;
  AtOwnHome?: boolean;
  cookingOwnProducts?: boolean;
  WeeklySubscription?: boolean;
}

async function _fetchCaregivers(
  serviceType: string,
  page: number,
  sort: string,
  filters: SearchFilters,
  currentUserId?: number | string | null
): Promise<SearchApiResponse> {
  const params = new URLSearchParams({
    page: String(page),
    limit: String(ITEMS_PER_PAGE),
    serviceType: serviceType,
  });

  if (sort && sort !== 'relevance') {
    params.append('sort', sort);
  }
  if (filters.location && filters.location !== "all") {
    params.append('location', filters.location);
  }
  if (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 1000) {
    params.append('minPrice', String(filters.priceRange[0]));
    params.append('maxPrice', String(filters.priceRange[1]));
  }
  if (filters.minRating > 0) {
    params.append('minRating', String(filters.minRating));
  }
  Object.entries(filters.availability).forEach(([key, value]) => {
    if (value) {
      params.append(key, 'true');
    }
  });

  // Add service-specific filters
  if (filters.serviceSpecific) {
    Object.entries(filters.serviceSpecific).forEach(([key, value]) => {
      if (value) {
        params.append(key, 'true');
      }
    });
  }

  if (currentUserId) {
    params.append('excludeProviderId', String(currentUserId));
  }

  try {
    const response = await fetch(`/api/proxy/services?${params.toString()}`);
    if (!response.ok) {
      let errorDetail = 'Failed to fetch services';
      try {
        const errorData = await response.json();
        errorDetail = errorData.message || `API Error: ${response.status} ${response.statusText}`;
      } catch (jsonError) {
        errorDetail = `API request failed with status: ${response.status}`;
      }
      throw new Error(errorDetail);
    }

    const data = await response.json();
    return {
      caregivers: data.caregivers || [],
      totalPages: data.totalPages || 0,
      totalItems: data.totalItems || 0,
    };
  } catch (error) {
    console.error(`[ServicesService] Failed to fetch services:`, error);
    throw error;
  }
}

async function _fetchServiceById(serviceId: number): Promise<any> {
    try {
        const response = await fetch(`/api/proxy/provider/services/${serviceId}`);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `Failed to fetch service with ID ${serviceId}`);
        }
        return await response.json();
    } catch (error) {
        console.error(`[ServicesService] Failed to fetch service ${serviceId}:`, error);
        throw error;
    }
}

async function _createService(payload: Partial<AdvertisedServicePayload>): Promise<any> {
    try {
        const response = await fetch(`/api/proxy/provider/services`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
        });
        const result = await response.json();
        if (!response.ok) {
            throw new Error(result.message || 'Failed to create service.');
        }
        return result;
    } catch (error) {
        console.error(`[ServicesService] Failed to create service:`, error);
        throw error;
    }
}

async function _updateService(serviceId: number, payload: Partial<AdvertisedServicePayload>): Promise<any> {
     try {
        const response = await fetch(`/api/proxy/provider/services/${serviceId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
        });
        const result = await response.json();
        if (!response.ok) {
            throw new Error(result.message || 'Failed to update service.');
        }
        return result;
    } catch (error) {
        console.error(`[ServicesService] Failed to update service ${serviceId}:`, error);
        throw error;
    }
}


export const ServicesService = {
  fetchNannies(page: number, sort: string, filters: SearchFilters, currentUserId?: number | string | null): Promise<SearchApiResponse> {
    return _fetchCaregivers('Nanny', page, sort, filters, currentUserId);
  },

  fetchElderCareProviders(page: number, sort: string, filters: SearchFilters, currentUserId?: number | string | null): Promise<SearchApiResponse> {
    return _fetchCaregivers('ElderCare', page, sort, filters, currentUserId);
  },

  fetchCleaners(page: number, sort: string, filters: SearchFilters, currentUserId?: number | string | null): Promise<SearchApiResponse> {
    return _fetchCaregivers('Cleaning', page, sort, filters, currentUserId);
  },

  fetchTutors(page: number, sort: string, filters: SearchFilters, currentUserId?: number | string | null): Promise<SearchApiResponse> {
    return _fetchCaregivers('Tutoring', page, sort, filters, currentUserId);
  },

  fetchCooks(page: number, sort: string, filters: SearchFilters, currentUserId?: number | string | null): Promise<SearchApiResponse> {
    return _fetchCaregivers('Cooking', page, sort, filters, currentUserId);
  },

  getServiceById: _fetchServiceById,
  createService: _createService,
  updateService: _updateService,
};
