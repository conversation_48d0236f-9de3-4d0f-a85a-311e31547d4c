
import React from 'react';
import { SafeAreaView, View, Text, FlatList, TouchableOpacity, StyleSheet, StatusBar } from 'react-native';
import { MaterialIcons, FontAwesome5, Ionicons } from '@expo/vector-icons';
import TestIcons from '../test-icons';

const mockServiceCategories = [
  { id: '1', name: '<PERSON>', icon: 'baby', iconFamily: 'MaterialIcons', color: '#6D28D9' },
  { id: '2', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'hands-helping', iconFamily: 'FontAwesome5', color: '#10B981' },
  { id: '3', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'sparkles', iconFamily: 'Ionicons', color: '#3B82F6' },
  { id: '4', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'school', iconFamily: 'MaterialIcons', color: '#F59E0B' },
  { id: '5', name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'restaurant', iconFamily: 'MaterialIcons', color: '#EF4444' },
];

type Category = typeof mockServiceCategories[0];

const HomeScreen = () => {
  const renderIcon = (item: Category) => {
    const iconProps = { color: item.color, size: 32 };
    switch (item.iconFamily) {
      case 'MaterialIcons':
        return <MaterialIcons name={item.icon as any} {...iconProps} />;
      case 'FontAwesome5':
        return <FontAwesome5 name={item.icon as any} {...iconProps} />;
      case 'Ionicons':
        return <Ionicons name={item.icon as any} {...iconProps} />;
      default:
        return <MaterialIcons name="help" {...iconProps} />;
    }
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity style={styles.card} activeOpacity={0.7}>
      <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
        {renderIcon(item)}
      </View>
      <Text style={styles.cardTitle}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <View style={styles.header}>
        <Text style={styles.title}>Ce serviciu cauți astăzi?</Text>
        <Text style={styles.subtitle}>Explorează categoriile de mai jos.</Text>
      </View>
      <FlatList
        data={mockServiceCategories}
        renderItem={renderCategory}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F2F5', // Light grey background
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 4,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  card: {
    flex: 1,
    margin: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    aspectRatio: 1, // Make it square
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    color: '#1F2937',
  },
});

export default HomeScreen;
