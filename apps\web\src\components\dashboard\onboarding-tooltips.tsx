"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { X, ArrowRight, Lightbulb, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';

const onboardingTranslations = {
  roleToggleTitle: { ro: "Comutare între Roluri", ru: "Переключение ролей", en: "Role Switching" },
  roleToggleDescription: { ro: "Acum poți comuta ușor între vizualizarea Client și Prestator folosind butonul din header.", ru: "Теперь вы можете легко переключаться между видом Клиента и Поставщика, используя кнопку в заголовке.", en: "You can now easily switch between Client and Provider views using the toggle in the header." },
  navigationTitle: { ro: "Navigare Contextuală", ru: "Контекстная навигация", en: "Contextual Navigation" },
  navigationDescription: { ro: "Meniul din stânga se adaptează automat în funcție de rolul selectat, afișând opțiunile relevante.", ru: "Левое меню автоматически адаптируется в зависимости от выбранной роли, показывая соответствующие опции.", en: "The left menu automatically adapts based on your selected role, showing relevant options." },
  urlTitle: { ro: "URL-uri Personalizate", ru: "Персонализированные URL", en: "Personalized URLs" },
  urlDescription: { ro: "Fiecare rol are propriile URL-uri pe care le poți salva ca bookmark pentru acces rapid.", ru: "Каждая роль имеет свои собственные URL, которые вы можете сохранить в закладки для быстрого доступа.", en: "Each role has its own URLs that you can bookmark for quick access." },
  gotIt: { ro: "Am înțeles!", ru: "Понятно!", en: "Got it!" },
  next: { ro: "Următorul", ru: "Далее", en: "Next" },
  skip: { ro: "Omite", ru: "Пропустить", en: "Skip" },
  welcomeNewProvider: { ro: "Bun venit ca Prestator!", ru: "Добро пожаловать как Поставщик!", en: "Welcome as a Provider!" },
  onboardingComplete: { ro: "Tur complet!", ru: "Тур завершен!", en: "Tour Complete!" },
};

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  targetSelector?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

interface OnboardingTooltipProps {
  isNewProvider?: boolean;
  onComplete?: () => void;
  className?: string;
}

export function OnboardingTooltips({ isNewProvider = false, onComplete, className }: OnboardingTooltipProps) {
  const { translate } = useLanguage();
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [hasSeenOnboarding, setHasSeenOnboarding] = useState(false);

  const steps: OnboardingStep[] = [
    {
      id: 'role-toggle',
      title: translate(onboardingTranslations, 'roleToggleTitle'),
      description: translate(onboardingTranslations, 'roleToggleDescription'),
      targetSelector: '[role="tablist"]',
      position: 'bottom'
    },
    {
      id: 'navigation',
      title: translate(onboardingTranslations, 'navigationTitle'),
      description: translate(onboardingTranslations, 'navigationDescription'),
      targetSelector: 'nav',
      position: 'right'
    },
    {
      id: 'url-bookmarks',
      title: translate(onboardingTranslations, 'urlTitle'),
      description: translate(onboardingTranslations, 'urlDescription'),
      position: 'top'
    }
  ];

  useEffect(() => {
    // Check if user has seen onboarding before
    const hasSeenKey = 'bonami-role-toggle-onboarding-seen';
    const seen = localStorage.getItem(hasSeenKey) === 'true';
    setHasSeenOnboarding(seen);

    // Show onboarding for new providers who haven't seen it
    if (isNewProvider && !seen) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000); // Delay to let the page load
      return () => clearTimeout(timer);
    }
  }, [isNewProvider]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleSkip = () => {
    handleComplete();
  };

  const handleComplete = () => {
    setIsVisible(false);
    localStorage.setItem('bonami-role-toggle-onboarding-seen', 'true');
    setHasSeenOnboarding(true);
    onComplete?.();
  };

  if (!isVisible || hasSeenOnboarding) {
    return null;
  }

  const currentStepData = steps[currentStep];

  return (
    <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm">
      {/* Overlay */}
      <div className="absolute inset-0" onClick={handleSkip} />
      
      {/* Tooltip Card */}
      <div className={cn(
        "absolute bg-white dark:bg-gray-900 rounded-lg shadow-xl border max-w-sm",
        "top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
        className
      )}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="p-1 bg-primary/10 rounded-full">
                <Sparkles className="w-4 h-4 text-primary" />
              </div>
              <CardTitle className="text-lg">{currentStepData.title}</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleSkip}
              className="h-6 w-6"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {currentStepData.description}
          </p>
          
          {/* Progress indicator */}
          <div className="flex items-center gap-1">
            {steps.map((_, index) => (
              <div
                key={index}
                className={cn(
                  "h-1.5 rounded-full transition-colors",
                  index <= currentStep ? "bg-primary" : "bg-muted",
                  index === currentStep ? "w-6" : "w-1.5"
                )}
              />
            ))}
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center justify-between pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSkip}
              className="text-muted-foreground"
            >
              {translate(onboardingTranslations, 'skip')}
            </Button>
            
            <Button
              onClick={handleNext}
              size="sm"
              className="flex items-center gap-1"
            >
              {currentStep === steps.length - 1 
                ? translate(onboardingTranslations, 'gotIt')
                : translate(onboardingTranslations, 'next')
              }
              {currentStep < steps.length - 1 && (
                <ArrowRight className="w-3 h-3" />
              )}
            </Button>
          </div>
        </CardContent>
      </div>
    </div>
  );
}

// Welcome banner for new providers
export function NewProviderWelcomeBanner({ onDismiss }: { onDismiss?: () => void }) {
  const { translate } = useLanguage();
  const [isVisible, setIsVisible] = useState(true);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  if (!isVisible) return null;

  return (
    <Card className="mb-4 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
              <Lightbulb className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900 dark:text-green-100">
                {translate(onboardingTranslations, 'welcomeNewProvider')}
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                {translate(onboardingTranslations, 'roleToggleDescription')}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDismiss}
            className="h-6 w-6 text-green-600 hover:text-green-700"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Hook to manage onboarding state
export function useOnboarding() {
  const [shouldShowOnboarding, setShouldShowOnboarding] = useState(false);
  const [isNewProvider, setIsNewProvider] = useState(false);

  useEffect(() => {
    // Check if this is a new provider (you can customize this logic)
    const checkNewProvider = () => {
      const hasSeenKey = 'bonami-role-toggle-onboarding-seen';
      const hasSeenOnboarding = localStorage.getItem(hasSeenKey) === 'true';
      
      // Check if user recently became a provider (within last 7 days)
      const providerSinceKey = 'bonami-provider-since';
      const providerSince = localStorage.getItem(providerSinceKey);
      const isRecentProvider = providerSince && 
        (Date.now() - parseInt(providerSince)) < 7 * 24 * 60 * 60 * 1000;

      setIsNewProvider(!!isRecentProvider);
      setShouldShowOnboarding(!hasSeenOnboarding && !!isRecentProvider);
    };

    checkNewProvider();
  }, []);

  const markAsProvider = () => {
    localStorage.setItem('bonami-provider-since', Date.now().toString());
    setIsNewProvider(true);
    setShouldShowOnboarding(true);
  };

  const completeOnboarding = () => {
    setShouldShowOnboarding(false);
  };

  return {
    shouldShowOnboarding,
    isNewProvider,
    markAsProvider,
    completeOnboarding
  };
}
