
"use client";
import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Users, ListChecks, Activity, Loader2, UserPlus } from "lucide-react"; // Added UserPlus
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';

interface AdminStats {
  totalUsers: number;
  totalProviders: number;
  totalActiveServices: number;
  totalServices: number;
  pendingProviderRequests: number; // Added this
}

// Remove local translations as they're now in commonTranslations

export default function AdminDashboardPage() {
  const { translate } = useLanguage();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/proxy/admin/stats`);
        if (!response.ok) {
          throw new Error('Failed to fetch admin stats');
        }
        const data: AdminStats = await response.json();
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    }
    fetchStats();
  }, []); // Removed translate dependency to prevent excessive re-fetching

  const statCards = stats ? [
    { titleKey: 'adminTotalUsersStat', value: stats.totalUsers, icon: <Users className="w-6 h-6 text-primary" /> },
    { titleKey: 'adminTotalProvidersStat', value: stats.totalProviders, icon: <Users className="w-6 h-6 text-green-600" /> },
    { titleKey: 'pendingProviderRequestsStat', value: stats.pendingProviderRequests, icon: <UserPlus className="w-6 h-6 text-orange-500" /> },
    { titleKey: 'adminTotalServicesStat', value: stats.totalServices, icon: <ListChecks className="w-6 h-6 text-destructive" /> },
    { titleKey: 'adminActiveServicesStat', value: stats.totalActiveServices, icon: <Activity className="w-6 h-6 text-yellow-500" /> },
  ] : [];

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-headline">{translate(commonTranslations, 'adminDashboardTitle')}</CardTitle>
        </CardHeader>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
        </div>
      )}

      {error && (
        <Card>
          <CardContent className="py-10 text-center text-destructive">
            <p>{translate(commonTranslations, 'adminErrorLoadingData')}: {error}</p>
          </CardContent>
        </Card>
      )}

      {!isLoading && !error && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6"> {/* Adjusted grid for 5 items */}
          {statCards.map(stat => (
            <Card key={stat.titleKey} className="shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {translate(commonTranslations, stat.titleKey as keyof typeof commonTranslations)}
                </CardTitle>
                {stat.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
       <Card>
        <CardHeader>
            <CardTitle className="text-lg font-headline">{translate(commonTranslations, 'adminQuickActionsTitle')}</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-wrap gap-4">
            <p className="text-sm text-muted-foreground w-full">
                {translate(commonTranslations, 'adminQuickActionsDescription')}
            </p>
            {/* Placeholder for future quick actions */}
        </CardContent>
       </Card>
    </div>
  );
}
