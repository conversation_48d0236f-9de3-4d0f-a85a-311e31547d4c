
"use client";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Phone, Mail, MapPin } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const contactPageTranslations = {
  pageTitle: { ro: "Contactează-ne", ru: "Свяжитесь с нами", en: "Contact Us" },
  intro: { ro: "Avem plăcerea să te ajutăm. Completează formularul de mai jos sau folosește detaliile de contact.", ru: "Мы рады вам помочь. Заполните форму ниже или используйте контактные данные.", en: "We are happy to help you. Complete the form below or use the contact details." },
  formTitle: { ro: "Trimite un mesaj", ru: "Отправить сообщение", en: "Send a message" },
  labelName: { ro: "Nume", ru: "Имя", en: "Name" },
  placeholderName: { ro: "Numele tău", ru: "Ваше имя", en: "Your name" },
  labelEmail: { ro: "Email", ru: "Электронная почта", en: "Email" },
  placeholderEmail: { ro: "<EMAIL>", ru: "<EMAIL>", en: "<EMAIL>" },
  labelSubject: { ro: "Subiect", ru: "Тема", en: "Subject" },
  placeholderSubject: { ro: "Subiectul mesajului", ru: "Тема сообщения", en: "Subject of the message" },
  labelMessage: { ro: "Mesaj", ru: "Сообщение", en: "Message" },
  placeholderMessage: { ro: "Mesajul tău...", ru: "Ваше сообщение...", en: "Your message..." },
  buttonSend: { ro: "Trimite Mesajul", ru: "Отправить сообщение", en: "Send Message" },
  contactDetailsTitle: { ro: "Detalii Contact", ru: "Контактная информация", en: "Contact Details" },
  address: { ro: "Str. Exemplu Nr. 123, Chișinău, Moldova", ru: "ул. Примерная, 123, Кишинев, Молдова", en: "Example St. No. 123, Chisinau, Moldova" },
  phone: { ro: "+373 (123) 456-789", ru: "+373 (123) 456-789", en: "+373 (123) 456-789" },
  emailAddress: { ro: "<EMAIL>", ru: "<EMAIL>", en: "<EMAIL>" },
  workHoursTitle: { ro: "Program de Lucru", ru: "Рабочее время", en: "Working Hours" },
  workHoursLine1: { ro: "Luni - Vineri: 9:00 - 18:00", ru: "Понедельник - Пятница: 9:00 - 18:00", en: "Monday - Friday: 9:00 AM - 6:00 PM" },
  workHoursLine2: { ro: "Sâmbătă: 10:00 - 14:00", ru: "Суббота: 10:00 - 14:00", en: "Saturday: 10:00 AM - 2:00 PM" },
  workHoursLine3: { ro: "Duminică: Închis", ru: "Воскресенье: Выходной", en: "Sunday: Closed" },
};

export default function ContactPage() {
  const { translate } = useLanguage();

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8 font-headline">{translate(contactPageTranslations, 'pageTitle')}</h1>
          <p className="text-lg text-center text-muted-foreground mb-12">
            {translate(contactPageTranslations, 'intro')}
          </p>

          <div className="grid md:grid-cols-2 gap-12">
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="font-headline">{translate(contactPageTranslations, 'formTitle')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-1">
                  <Label htmlFor="name">{translate(contactPageTranslations, 'labelName')}</Label>
                  <Input id="name" placeholder={translate(contactPageTranslations, 'placeholderName')} />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="email">{translate(contactPageTranslations, 'labelEmail')}</Label>
                  <Input id="email" type="email" placeholder={translate(contactPageTranslations, 'placeholderEmail')} />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="subject">{translate(contactPageTranslations, 'labelSubject')}</Label>
                  <Input id="subject" placeholder={translate(contactPageTranslations, 'placeholderSubject')} />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="message">{translate(contactPageTranslations, 'labelMessage')}</Label>
                  <Textarea id="message" placeholder={translate(contactPageTranslations, 'placeholderMessage')} rows={5} />
                </div>
                <Button className="w-full">{translate(contactPageTranslations, 'buttonSend')}</Button>
              </CardContent>
            </Card>

            <div className="space-y-8">
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="font-headline">{translate(contactPageTranslations, 'contactDetailsTitle')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-6 h-6 text-primary" />
                    <p>{translate(contactPageTranslations, 'address')}</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-6 h-6 text-primary" />
                    <a href={`tel:${translate(contactPageTranslations, 'phone')}`} className="hover:text-primary">{translate(contactPageTranslations, 'phone')}</a>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="w-6 h-6 text-primary" />
                    <a href={`mailto:${translate(contactPageTranslations, 'emailAddress')}`} className="hover:text-primary">{translate(contactPageTranslations, 'emailAddress')}</a>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="shadow-lg">
                 <CardHeader>
                    <CardTitle className="font-headline">{translate(contactPageTranslations, 'workHoursTitle')}</CardTitle>
                 </CardHeader>
                 <CardContent>
                    <p>{translate(contactPageTranslations, 'workHoursLine1')}</p>
                    <p>{translate(contactPageTranslations, 'workHoursLine2')}</p>
                    <p>{translate(contactPageTranslations, 'workHoursLine3')}</p>
                 </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
