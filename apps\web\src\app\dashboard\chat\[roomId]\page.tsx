
"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON>t, Send, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, CheckCheck } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { useEffect, useState, useRef, useCallback, useMemo } from "react";
import type { ChatMessageStatus } from '@prisma/client';
import io, { type Socket } from 'socket.io-client';
import { useSession } from "next-auth/react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserSilhouetteIcon } from "@/components/ui/user-silhouette-icon";
import { format } from 'date-fns';
import { ro, ru, enUS as en } from 'date-fns/locale';

interface Message {
  Id: string;
  ChatRoomId: string;
  SenderId: number;
  RecipientId: number;
  Content: string;
  CreatedAt: string; 
  Status: ChatMessageStatus;
  isOptimistic?: boolean; 
}

const chatRoomPageTranslations = {
  backToInbox: { ro: "Înapoi la Mesaje", ru: "Назад к Сообщениям", en: "Back to Messages" },
  chatWith: { ro: "Conversație cu {userName}", ru: "Беседа с {userName}", en: "Chat with {userName}" },
  serviceContext: { ro: "Serviciu: {serviceName}", ru: "Услуга: {serviceName}", en: "Service: {serviceName}" },
  typeMessagePlaceholder: { ro: "Scrie mesajul tău...", ru: "Напишите ваше сообщение...", en: "Type your message..." },
  sendButton: { ro: "Trimite", ru: "Отправить", en: "Send" },
  sendingButton: { ro: "Se trimite...", ru: "Отправка...", en: "Sending..." },
  loadingMessages: { ro: "Se încarcă mesajele...", ru: "Загрузка сообщений...", en: "Loading messages..." },
  errorFetchingMessages: { ro: "Eroare la preluarea mesajelor.", ru: "Ошибка при загрузке сообщений.", en: "Error fetching messages." },
  noMessagesYet: { ro: "Niciun mesaj în această conversație încă. Fii primul care scrie!", ru: "В этой беседе пока нет сообщений. Напишите первым!", en: "No messages in this conversation yet. Be the first to write!" },
  messageInputLabel: { ro: "Mesajul tău", ru: "Ваше сообщение", en: "Your message" },
  statusSent: {ro: "Trimis", ru: "Отправлено", en: "Sent"},
  statusDelivered: {ro: "Livrat", ru: "Доставлено", en: "Delivered"},
  statusRead: {ro: "Citit", ru: "Прочитано", en: "Read"},
  connectingToChat: {ro: "Conectare la chat...", ru: "Подключение к чату...", en: "Connecting to chat..."},
  chatConnectionError: {ro: "Eroare conexiune chat", ru: "Ошибка подключения к чату", en: "Chat Connection Error"},
};

let socket: Socket | null = null;

export default function ChatRoomPage() {
  const { translate } = useLanguage();
  const params = useParams();
  const roomId = params.roomId as string;
  const { data: session } = useSession();

  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatPartner, setChatPartner] = useState<{ id: number; name: string; avatar: string | null; } | null>(null);
  const [serviceContextName, setServiceContextName] = useState("Serviciu");
  const [socketError, setSocketError] = useState<string | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const currentUserId = useMemo(() => session?.user ? parseInt((session.user as any).id, 10) : null, [session]);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  useEffect(() => {
    if (!roomId || !currentUserId) return;
    
    const fetchChatData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/proxy/chat/${roomId}/messages`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || translate(chatRoomPageTranslations, 'errorFetchingMessages'));
        }
        const data = await response.json();
        const standardizedMessages = (data.messages || []).map((msg: any) => ({
          Id: msg.id,
          ChatRoomId: msg.chatRoomId,
          SenderId: msg.senderId,
          RecipientId: msg.recipientId,
          Content: msg.content,
          CreatedAt: msg.createdAt,
          Status: msg.status,
        }));
        setMessages(standardizedMessages);
        setChatPartner(data.partner);
        setServiceContextName(data.service.name);
      } catch (err) {
        setError(err instanceof Error ? err.message : translate(chatRoomPageTranslations, 'errorFetchingMessages'));
      } finally {
        setIsLoading(false);
      }
    };
    fetchChatData();

    const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:9001";
    if (!socket || socket.disconnected) {
        socket = io(API_URL, {
            transports: ['websocket', 'polling'],
        });
    }

    socket.on('connect', () => {
      console.log('[Socket.IO Client] Connected:', socket?.id);
      socket?.emit('joinRoom', roomId);
      setSocketError(null);
    });
    
    socket.on('connect_error', (err) => {
      console.error('[Socket.IO Client] Connection error:', err.message);
      setSocketError(`${translate(chatRoomPageTranslations, 'chatConnectionError')}: ${err.message}`);
    });

    const handleReceiveMessage = (message: Message) => {
        if (message.ChatRoomId === roomId) {
            setMessages((prevMessages) => {
                const filtered = prevMessages.filter(m => !m.isOptimistic);
                return [...filtered, message];
            });
        }
    };
    socket.on('receiveMessage', handleReceiveMessage);

    return () => {
      if (socket) {
        console.log(`[Socket.IO Client] Unmounting, leaving room ${roomId}`);
        socket.emit('leaveRoom', roomId);
        socket.off('receiveMessage', handleReceiveMessage);
        socket.off('connect');
        socket.off('connect_error');
      }
    };
  }, [roomId, currentUserId, translate]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !roomId || !currentUserId || !chatPartner?.id || !socket) return;
    
    setIsSending(true);
    
    const optimisticMessage: Message = {
      Id: `optimistic_${Date.now()}`,
      ChatRoomId: roomId,
      SenderId: currentUserId,
      RecipientId: chatPartner.id, 
      Content: newMessage,
      CreatedAt: new Date().toISOString(),
      Status: 'Sent',
      isOptimistic: true,
    };
    
    setMessages(prev => [...prev, optimisticMessage]);
    setNewMessage("");

    socket.emit('sendMessage', {
      chatRoomId: roomId,
      senderId: currentUserId,
      recipientId: chatPartner.id,
      content: optimisticMessage.Content,
    });
    
    setIsSending(false);
  };
  
  const getInitials = (name: string | null | undefined): string => name ? name.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() : 'U';

  const renderMessageStatus = (message: Message) => {
    if (message.SenderId !== currentUserId) return null; 
    switch (message.Status) {
      case 'Sent': return <Check className="h-3.5 w-3.5 ml-1 text-muted-foreground" />;
      case 'Delivered': return <CheckCheck className="h-3.5 w-3.5 ml-1 text-muted-foreground" />;
      case 'Read': return <CheckCheck className="h-3.5 w-3.5 ml-1 text-blue-500" />;
      default: return null;
    }
  };

  return (
    <div className="flex flex-col h-full">
      <Card className="flex-1 flex flex-col overflow-hidden">
        <CardHeader className="border-b p-4">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href="/dashboard/chat"><ArrowLeft className="h-5 w-5" /></Link>
            </Button>
            {chatPartner && (
              <Avatar className="h-10 w-10">
                <AvatarImage src={chatPartner.avatar || undefined} />
                <AvatarFallback>{getInitials(chatPartner.name)}</AvatarFallback>
              </Avatar>
            )}
            <div>
              <CardTitle className="text-lg font-semibold">
                {chatPartner ? translate(chatRoomPageTranslations, 'chatWith').replace('{userName}', chatPartner.name) : translate(commonTranslations, 'loading')}
              </CardTitle>
              <CardDescription className="text-xs">{translate(chatRoomPageTranslations, 'serviceContext').replace('{serviceName}', serviceContextName)}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
          {isLoading && (
            <div className="flex justify-center items-center h-full">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
              <p className="ml-3">{translate(chatRoomPageTranslations, 'loadingMessages')}</p>
            </div>
          )}
          {error && (
            <div className="flex flex-col justify-center items-center h-full text-destructive">
              <AlertTriangle className="w-8 h-8 mb-2" />
              <p>{error}</p>
            </div>
          )}
          {!isLoading && !error && messages.length === 0 && (
            <div className="flex justify-center items-center h-full">
              <p className="text-muted-foreground">{translate(chatRoomPageTranslations, 'noMessagesYet')}</p>
            </div>
          )}
          {!isLoading && !error && messages.map(msg => (
            <div key={msg.Id} className={`flex ${msg.SenderId === currentUserId ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[70%] p-2.5 rounded-lg shadow-sm ${msg.SenderId === currentUserId ? 'bg-primary text-primary-foreground' : 'bg-muted'} ${msg.isOptimistic ? 'opacity-70' : ''}`}>
                <p className="text-sm whitespace-pre-wrap">{msg.Content}</p>
                <div className={`text-xs mt-1 flex items-center ${msg.SenderId === currentUserId ? 'text-primary-foreground/70 justify-end' : 'text-muted-foreground justify-start'}`}>
                  <span>{format(new Date(msg.CreatedAt), 'HH:mm', { locale: ro })}</span>
                  {renderMessageStatus(msg)}
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </CardContent>

        <CardFooter className="p-4 border-t flex flex-col gap-2">
            {socketError && (
                <div className="w-full text-center p-2 rounded-md bg-destructive/10 text-destructive text-xs flex items-center justify-center gap-2">
                    <AlertTriangle className="w-4 h-4" />
                    <span>{socketError}</span>
                </div>
            )}
          <div className="flex w-full items-center space-x-2">
            <Textarea
              id="message"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder={translate(chatRoomPageTranslations, 'typeMessagePlaceholder')}
              className="flex-1 resize-none"
              rows={1}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              disabled={isSending || !!socketError || isLoading}
            />
            <Button type="submit" size="icon" onClick={handleSendMessage} disabled={isSending || !!socketError || isLoading || !newMessage.trim()}>
              {isSending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
              <span className="sr-only">{translate(chatRoomPageTranslations, 'sendButton')}</span>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
