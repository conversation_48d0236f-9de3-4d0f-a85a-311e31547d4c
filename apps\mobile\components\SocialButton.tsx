
import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { AntDesign } from '@expo/vector-icons'; // Assuming you have expo vector icons

interface SocialButtonProps {
  provider: 'google' | 'facebook';
  title: string;
  onPress: () => void;
}

export const SocialButton: React.FC<SocialButtonProps> = ({ provider, title, onPress }) => {
  const isGoogle = provider === 'google';
  const iconName = isGoogle ? 'google' : 'facebook-square';
  const iconColor = isGoogle ? '#DB4437' : '#4267B2';

  return (
    <TouchableOpacity style={styles.button} onPress={onPress}>
      <View style={styles.iconWrapper}>
        <AntDesign name={iconName} size={22} color={iconColor} />
      </View>
      <Text style={styles.buttonText}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    width: '100%',
  },
  iconWrapper: {
    width: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    flex: 1,
    textAlign: 'center',
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
  },
});
