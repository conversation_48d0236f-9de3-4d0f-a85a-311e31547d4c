"use client";

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ArrowLeft,
  User,
  Mail,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  Download,
  RefreshCw,
  MoreHorizontal,
  Phone,
  MapPin,
  Globe,
  Shield,
  UserCheck,
  Filter,
  Grid,
  List,
  ChevronDown,
  Loader2
} from "lucide-react";
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { GranularServiceCard } from '../components/granular-service-card';
import { RequestTimeline } from '../components/request-timeline';
import { ServiceReviewStepper } from '../components/service-review-stepper';
import Link from 'next/link';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface PendingService {
  Id: number;
  ServiceName: string;
  Description: string;
  ServiceCategorySlug: string;
  ExperienceYears: number;
  Status: 'PendingReview' | 'Approved' | 'Rejected' | 'RequiresChanges';
  AdminNotes?: string | null;
  DocumentPaths: string[];
  CreatedAt: string;
  UpdatedAt: string;
  Category: {
    Id: number;
    NameKey: string;
    Slug: string;
  };
  NannyServiceDetails?: any;
  ElderCareServiceDetails?: any;
  CleaningServiceDetails?: any;
  TutoringServiceDetails?: any;
  CookingServiceDetails?: any;
}

interface Address {
  Id: number;
  Label: string;
  Street: string;
  City: string;
  Region?: string | null;
  PostalCode?: string | null;
  Country: string;
  IsDefault: boolean;
}

interface ProviderRegistrationRequestWithServices {
  Id: string;
  UserName: string;
  UserEmail: string;
  Status: 'Pending' | 'Approved' | 'Rejected';
  RequestDate: string;
  AdminNotes?: string | null;
  PendingServices: PendingService[];
  User?: {
    Id: number;
    FullName: string;
    Email: string;
    Phone?: string | null;
    Bio?: string | null;
    SpokenLanguages: string[];
    EmailVerified?: string | null;
    CreatedAt: string;
    UpdatedAt: string;
    AvatarUrl?: string | null;
    Addresses: Address[];
  };
}

export default function ProviderRequestDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { translate } = useLanguage();
  
  const [request, setRequest] = useState<ProviderRegistrationRequestWithServices | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingServiceId, setUpdatingServiceId] = useState<number | null>(null);
  const [serviceFilter, setServiceFilter] = useState<'all' | 'PendingReview' | 'Approved' | 'Rejected' | 'RequiresChanges'>('all');
  const [isBulkActionLoading, setIsBulkActionLoading] = useState(false);
  const [selectedServices, setSelectedServices] = useState<number[]>([]);
  const [viewMode, setViewMode] = useState<'stepper' | 'list'>('stepper');
  const [sortBy, setSortBy] = useState<'name' | 'category' | 'status' | 'created'>('created');

  const requestId = params.requestId as string;

  useEffect(() => {
    fetchRequestDetails();
  }, [requestId]);

  const fetchRequestDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/proxy/admin/provider-requests/${requestId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch request details');
      }
      
      const data = await response.json();
      console.log('API Response data:', data);
      console.log('Request data:', data.request);
      console.log('Pending services:', data.request?.PendingServices);
      setRequest(data.request);
    } catch (err) {
      console.error('Error fetching request details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load request details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleServiceStatusUpdate = async (serviceId: number, status: string, adminNotes?: string) => {
    setUpdatingServiceId(serviceId);
    try {
      const response = await fetch(`/api/proxy/admin/provider-services/update`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ serviceId, status, adminNotes }),
      });
      
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || 'Failed to update service status');
      }

      // Update local state
      if (request) {
        setRequest(prev => prev ? ({
          ...prev,
          PendingServices: prev.PendingServices.map(service => 
            service.Id === serviceId 
              ? { ...service, Status: status as any, AdminNotes: adminNotes, UpdatedAt: new Date().toISOString() }
              : service
          )
        }) : null);
      }

      toast({
        title: translate(commonTranslations, 'adminSuccess'),
        description: translate(commonTranslations, 'adminServiceUpdateSuccess'),
      });
    } catch (error) {
      toast({
        title: translate(commonTranslations, 'adminError'),
        description: error instanceof Error ? error.message : translate(commonTranslations, 'adminServiceUpdateError'),
        variant: "destructive",
      });
    } finally {
      setUpdatingServiceId(null);
    }
  };

  const getServiceStats = () => {
    if (!request) return { total: 0, approved: 0, pending: 0, rejected: 0, changesNeeded: 0, progress: 0 };
    
    const total = request.PendingServices.length;
    const approved = request.PendingServices.filter(s => s.Status === 'Approved').length;
    const pending = request.PendingServices.filter(s => s.Status === 'PendingReview').length;
    const rejected = request.PendingServices.filter(s => s.Status === 'Rejected').length;
    const changesNeeded = request.PendingServices.filter(s => s.Status === 'RequiresChanges').length;
    const progress = total > 0 ? (approved / total) * 100 : 0;

    return { total, approved, pending, rejected, changesNeeded, progress };
  };

  const getFilteredServices = () => {
    if (!request) return [];

    let filtered = request.PendingServices;

    // Apply status filter
    if (serviceFilter !== 'all') {
      filtered = filtered.filter(service => service.Status === serviceFilter);
    }

    // Apply sorting
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.ServiceName.localeCompare(b.ServiceName);
        case 'category':
          return a.ServiceCategorySlug.localeCompare(b.ServiceCategorySlug);
        case 'status':
          return a.Status.localeCompare(b.Status);
        case 'created':
        default:
          return new Date(a.CreatedAt).getTime() - new Date(b.CreatedAt).getTime();
      }
    });

    return filtered;
  };

  const handleBulkAction = async (action: string) => {
    const pendingServices = request?.PendingServices.filter(s => s.Status === 'PendingReview') || [];
    if (pendingServices.length === 0) {
      toast({
        title: translate(commonTranslations, 'adminNoServices'),
        description: translate(commonTranslations, 'adminNoServicesDescription'),
        variant: "destructive",
      });
      return;
    }

    setIsBulkActionLoading(true);
    try {
      const promises = pendingServices.map(service =>
        fetch(`/api/proxy/admin/provider-services/update`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            serviceId: service.Id,
            status: action === 'approve' ? 'Approved' : 'Rejected',
            adminNotes: action === 'approve'
              ? 'Bulk approved by admin'
              : 'Bulk rejected by admin'
          }),
        })
      );

      await Promise.all(promises);

      // Update local state
      if (request) {
        setRequest(prev => prev ? ({
          ...prev,
          PendingServices: prev.PendingServices.map(service =>
            pendingServices.some(ps => ps.Id === service.Id)
              ? {
                  ...service,
                  Status: action === 'approve' ? 'Approved' : 'Rejected' as any,
                  AdminNotes: action === 'approve' ? 'Bulk approved by admin' : 'Bulk rejected by admin',
                  UpdatedAt: new Date().toISOString()
                }
              : service
          )
        }) : null);
      }

      toast({
        title: translate(commonTranslations, 'adminSuccess'),
        description: translate(commonTranslations, 'adminBulkActionSuccess'),
      });
    } catch (error) {
      toast({
        title: translate(commonTranslations, 'adminError'),
        description: translate(commonTranslations, 'adminBulkActionError'),
        variant: "destructive",
      });
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const toggleServiceSelection = (serviceId: number) => {
    setSelectedServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const selectAllFilteredServices = () => {
    const filteredIds = getFilteredServices().map(s => s.Id);
    setSelectedServices(filteredIds);
  };

  const clearSelection = () => {
    setSelectedServices([]);
  };

  const generateTimelineEvents = () => {
    if (!request) return [];

    const events = [];

    // Request created event
    events.push({
      id: 'created',
      type: 'created' as const,
      title: 'Provider Request Created',
      description: `${request.UserName} submitted a provider registration request`,
      timestamp: request.RequestDate,
      actor: request.UserName,
    });

    // Service events
    request.PendingServices.forEach(service => {
      events.push({
        id: `service-${service.Id}-created`,
        type: 'service_added' as const,
        title: `Service Added: ${service.ServiceName}`,
        description: `${service.ServiceCategorySlug} service application submitted`,
        timestamp: service.CreatedAt,
        actor: request.UserName,
      });

      if (service.Status === 'Approved') {
        events.push({
          id: `service-${service.Id}-approved`,
          type: 'service_approved' as const,
          title: `Service Approved: ${service.ServiceName}`,
          description: service.AdminNotes || 'Service has been approved by admin',
          timestamp: service.UpdatedAt,
          actor: 'Admin',
        });
      } else if (service.Status === 'Rejected') {
        events.push({
          id: `service-${service.Id}-rejected`,
          type: 'service_rejected' as const,
          title: `Service Rejected: ${service.ServiceName}`,
          description: service.AdminNotes || 'Service has been rejected by admin',
          timestamp: service.UpdatedAt,
          actor: 'Admin',
        });
      } else if (service.Status === 'RequiresChanges') {
        events.push({
          id: `service-${service.Id}-changes`,
          type: 'service_changes_requested' as const,
          title: `Changes Requested: ${service.ServiceName}`,
          description: service.AdminNotes || 'Admin has requested changes to this service',
          timestamp: service.UpdatedAt,
          actor: 'Admin',
        });
      }
    });

    // Overall request status events
    if (request.Status === 'Approved') {
      events.push({
        id: 'request-approved',
        type: 'request_approved' as const,
        title: 'Provider Request Approved',
        description: 'All services have been reviewed and the provider request has been approved',
        timestamp: request.RequestDate, // This would be the actual approval timestamp
        actor: 'Admin',
      });
    } else if (request.Status === 'Rejected') {
      events.push({
        id: 'request-rejected',
        type: 'request_rejected' as const,
        title: 'Provider Request Rejected',
        description: request.AdminNotes || 'Provider request has been rejected',
        timestamp: request.RequestDate, // This would be the actual rejection timestamp
        actor: 'Admin',
      });
    }

    // Sort events by timestamp
    return events.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  };

  const handleExportDetails = () => {
    if (!request) return;

    const exportData = {
      providerInfo: {
        name: request.UserName,
        email: request.UserEmail,
        fullName: request.User?.FullName,
        registrationDate: request.RequestDate,
        status: request.Status,
        adminNotes: request.AdminNotes,
      },
      services: request.PendingServices.map(service => ({
        id: service.Id,
        name: service.ServiceName,
        category: service.ServiceCategorySlug,
        description: service.Description,
        experienceYears: service.ExperienceYears,
        status: service.Status,
        adminNotes: service.AdminNotes,
        documents: service.DocumentPaths,
        createdAt: service.CreatedAt,
        updatedAt: service.UpdatedAt,
      })),
      timeline: generateTimelineEvents(),
      exportedAt: new Date().toISOString(),
      exportedBy: 'Admin',
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `provider-request-${request.Id}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: translate(commonTranslations, 'adminExportComplete'),
      description: translate(commonTranslations, 'adminExportDescription'),
    });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading request details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !request) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Request</h2>
          <p className="text-muted-foreground mb-4">{error || 'Request not found'}</p>
          <Button asChild>
            <Link href="/admin/provider-requests">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Provider Requests
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  const stats = getServiceStats();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-6 space-y-8 max-w-7xl">
        {/* Breadcrumb Navigation */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/provider-requests">Provider Requests</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbPage>{request.UserName}</BreadcrumbPage>
          </BreadcrumbList>
        </Breadcrumb>

      {/* Header Section */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/provider-requests">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {translate(commonTranslations, 'adminBackToList')}
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold">{request.UserName}</h1>
              <p className="text-muted-foreground">{translate(commonTranslations, 'adminProviderRegistrationRequest')}</p>
            </div>
          </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={fetchRequestDetails}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {translate(commonTranslations, 'adminRefresh')}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{translate(commonTranslations, 'adminActions')}</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => handleBulkAction('approve')}
                disabled={isBulkActionLoading}
              >
                {isBulkActionLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                {translate(commonTranslations, 'adminApproveAllPending')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleBulkAction('reject')}
                disabled={isBulkActionLoading}
              >
                {isBulkActionLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <XCircle className="h-4 w-4 mr-2" />
                )}
                {translate(commonTranslations, 'adminRejectAllPending')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleExportDetails}>
                <Download className="h-4 w-4 mr-2" />
                {translate(commonTranslations, 'adminExportDetails')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        </div>
      </div>

      {/* Provider Information Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {translate(commonTranslations, 'adminProviderInformation')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{translate(commonTranslations, 'adminFullName')}</p>
              <p className="font-medium">{request.User?.FullName || request.UserName}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{translate(commonTranslations, 'adminEmail')}</p>
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 flex-shrink-0" />
                  <span className="font-medium truncate">{request.UserEmail}</span>
                </div>
                {request.User?.EmailVerified ? (
                  <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50 w-fit">
                    <Shield className="h-3 w-3 mr-1" />
                    {translate(commonTranslations, 'adminVerified')}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-orange-600 border-orange-200 bg-orange-50 w-fit">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    {translate(commonTranslations, 'adminUnverified')}
                  </Badge>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{translate(commonTranslations, 'adminPhone')}</p>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 flex-shrink-0" />
                <span className="font-medium">{request.User?.Phone || translate(commonTranslations, 'adminFieldNotProvided')}</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{translate(commonTranslations, 'adminOverallStatus')}</p>
              <Badge
                variant={request.Status === 'Approved' ? 'default' : request.Status === 'Rejected' ? 'destructive' : 'secondary'}
                className="w-fit"
              >
                {request.Status}
              </Badge>
            </div>
          </div>

          {/* Account Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 pt-6 border-t">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{translate(commonTranslations, 'adminAccountCreated')}</p>
              <div className="flex items-center gap-2">
                <UserCheck className="h-4 w-4 flex-shrink-0" />
                <span className="font-medium">
                  {request.User?.CreatedAt ? new Date(request.User.CreatedAt).toLocaleDateString() : translate(commonTranslations, 'adminUnknown')}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{translate(commonTranslations, 'adminRegistrationDate')}</p>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 flex-shrink-0" />
                <span className="font-medium">{new Date(request.RequestDate).toLocaleDateString()}</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{translate(commonTranslations, 'adminSpokenLanguages')}</p>
              <div className="flex flex-wrap gap-1">
                {request.User?.SpokenLanguages?.map((lang, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    <Globe className="h-3 w-3 mr-1" />
                    {lang.toUpperCase()}
                  </Badge>
                )) || <span className="text-muted-foreground">{translate(commonTranslations, 'adminNotSpecified')}</span>}
              </div>
            </div>
          </div>

          {/* Bio Section */}
          {request.User?.Bio && (
            <div className="pt-4 border-t">
              <p className="text-sm font-medium text-muted-foreground mb-2">{translate(commonTranslations, 'adminBio')}</p>
              <div className="bg-muted/50 p-3 rounded-md">
                <p className="text-sm">{request.User.Bio}</p>
              </div>
            </div>
          )}

          {/* Addresses Section */}
          {request.User?.Addresses && request.User.Addresses.length > 0 && (
            <div className="pt-4 border-t">
              <p className="text-sm font-medium text-muted-foreground mb-3">{translate(commonTranslations, 'adminAddresses')}</p>
              <div className="grid gap-3">
                {request.User.Addresses.map((address) => (
                  <div key={address.Id} className="flex items-start gap-2 p-3 bg-muted/30 rounded-md">
                    <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">{address.Label}</span>
                        {address.IsDefault && (
                          <Badge variant="outline" className="text-xs">{translate(commonTranslations, 'adminDefault')}</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {address.Street}, {address.City}
                        {address.Region && `, ${address.Region}`}
                        {address.PostalCode && `, ${address.PostalCode}`}
                        , {address.Country}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Services Overview Section */}
      <Card>
        <CardHeader>
          <CardTitle>{translate(commonTranslations, 'adminServicesOverview')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'adminTotalServices')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'adminApproved')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'adminPending')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.changesNeeded}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'adminChangesNeeded')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
              <div className="text-xs text-muted-foreground">{translate(commonTranslations, 'adminRejected')}</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{translate(commonTranslations, 'adminApprovalProgress')}</span>
              <span>{stats.approved} {translate(commonTranslations, 'adminOf')} {stats.total} {translate(commonTranslations, 'adminServicesApproved')}</span>
            </div>
            <Progress value={stats.progress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Individual Services Section */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex flex-col space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <CardTitle className="text-xl">{translate(commonTranslations, 'adminServiceApplications')}</CardTitle>
                <div className="text-sm text-muted-foreground">
                  {getFilteredServices().length} {translate(commonTranslations, 'adminOf')} {stats.total} {translate(commonTranslations, 'adminServicesShown')}
                </div>
              </div>

              {/* Controls Row */}
              <div className="flex flex-col sm:flex-row gap-3">
                {/* Filter Controls */}
                <Select value={serviceFilter} onValueChange={(value: any) => setServiceFilter(value)}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder={translate(commonTranslations, 'adminFilterServices')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{translate(commonTranslations, 'adminAllServices')} ({stats.total})</SelectItem>
                    <SelectItem value="PendingReview">{translate(commonTranslations, 'adminPending')} ({stats.pending})</SelectItem>
                    <SelectItem value="Approved">{translate(commonTranslations, 'adminApproved')} ({stats.approved})</SelectItem>
                    <SelectItem value="RequiresChanges">{translate(commonTranslations, 'adminChangesNeeded')} ({stats.changesNeeded})</SelectItem>
                    <SelectItem value="Rejected">{translate(commonTranslations, 'adminRejected')} ({stats.rejected})</SelectItem>
                  </SelectContent>
                </Select>

                {/* Sort Controls */}
                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="w-full sm:w-[140px]">
                    <SelectValue placeholder={translate(commonTranslations, 'adminSortBy')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created">{translate(commonTranslations, 'adminDateCreated')}</SelectItem>
                    <SelectItem value="name">{translate(commonTranslations, 'adminServiceName')}</SelectItem>
                    <SelectItem value="category">{translate(commonTranslations, 'adminCategory')}</SelectItem>
                    <SelectItem value="status">{translate(commonTranslations, 'adminStatus')}</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode Toggle */}
                <div className="flex items-center border rounded-md w-full sm:w-auto">
                  <Button
                    variant={viewMode === 'stepper' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('stepper')}
                    className="rounded-r-none flex-1 sm:flex-none"
                  >
                    <Grid className="h-4 w-4 sm:mr-2" />
                    <span className="hidden sm:inline">{translate(commonTranslations, 'adminStepper')}</span>
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none flex-1 sm:flex-none"
                  >
                    <List className="h-4 w-4 sm:mr-2" />
                    <span className="hidden sm:inline">{translate(commonTranslations, 'adminList')}</span>
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Bulk Actions */}
        {selectedServices.length > 0 && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium">
                    {selectedServices.length} {translate(commonTranslations, 'adminServicesSelected')}
                  </span>
                  <Button variant="outline" size="sm" onClick={clearSelection}>
                    {translate(commonTranslations, 'adminClearSelection')}
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleBulkAction('approve')}
                    disabled={isBulkActionLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isBulkActionLoading ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : <CheckCircle className="h-4 w-4 mr-1" />}
                    {translate(commonTranslations, 'adminApproveSelected')}
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleBulkAction('reject')}
                    disabled={isBulkActionLoading}
                  >
                    {isBulkActionLoading ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : <XCircle className="h-4 w-4 mr-1" />}
                    {translate(commonTranslations, 'adminRejectSelected')}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        {stats.pending > 0 && (
          <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-md">
            <span className="text-sm text-muted-foreground">{translate(commonTranslations, 'adminQuickActions')}:</span>
            <Button variant="outline" size="sm" onClick={selectAllFilteredServices}>
              {translate(commonTranslations, 'adminSelectAllVisible')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction('approve')}
              disabled={isBulkActionLoading}
            >
              {translate(commonTranslations, 'adminApproveAllPending')}
            </Button>
          </div>
        )}

        {viewMode === 'stepper' ? (
          <ServiceReviewStepper
            services={getFilteredServices()}
            onStatusUpdate={handleServiceStatusUpdate}
            updatingServiceId={updatingServiceId}
          />
        ) : (
          <div className="space-y-6">
            {getFilteredServices().map(service => (
              <div key={service.Id} className="space-y-2">
                {/* Selection Checkbox - moved outside card to prevent overlap */}
                {service.Status === 'PendingReview' && (
                  <div className="flex items-center gap-2 px-1">
                    <input
                      type="checkbox"
                      checked={selectedServices.includes(service.Id)}
                      onChange={() => toggleServiceSelection(service.Id)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm text-muted-foreground">{translate(commonTranslations, 'adminSelectForBulkAction')}</span>
                  </div>
                )}
                <GranularServiceCard
                  service={service}
                  onStatusUpdate={handleServiceStatusUpdate}
                  isUpdating={updatingServiceId === service.Id}
                />
              </div>
            ))}
            {getFilteredServices().length === 0 && (
              <div className="text-center text-muted-foreground py-12">
                <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                <p>{translate(commonTranslations, 'adminNoServicesFound')}</p>
                <Button variant="outline" size="sm" className="mt-2" onClick={() => setServiceFilter('all')}>
                  {translate(commonTranslations, 'adminShowAllServices')}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Admin Notes Section */}
      {request.AdminNotes && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {translate(commonTranslations, 'adminOverallAdminNotes')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/50 p-4 rounded-md">
              <p className="text-sm">{request.AdminNotes}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Request Timeline */}
      <RequestTimeline events={generateTimelineEvents()} />
      </div>
    </div>
  );
}
