{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noEmit": true, "paths": {"@repo/translations": ["./packages/translations/index.ts"], "@repo/base": ["./packages/base/index.ts"], "@repo/auth": ["./packages/auth/index.ts"], "@repo/services": ["./packages/services/index.ts"], "@repo/types": ["./packages/types/index.ts"], "@/*": ["./apps/web/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}