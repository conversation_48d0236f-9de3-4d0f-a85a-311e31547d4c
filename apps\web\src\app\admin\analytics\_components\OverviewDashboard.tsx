"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Users,
  UserCheck,
  Briefcase,
  Calendar,
  TrendingUp,
  TrendingDown,
  Activity,
  Eye,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const overviewTranslations = {
  overviewTitle: { ro: "Prezentare generală", ru: "Обзор", en: "Overview" },
  keyMetrics: { ro: "<PERSON><PERSON><PERSON> cheie", ru: "Ключевые показатели", en: "Key Metrics" },
  totalUsers: { ro: "Total utilizatori", ru: "Всего пользователей", en: "Total Users" },
  totalProviders: { ro: "Total prestatori", ru: "Всего поставщиков", en: "Total Providers" },
  activeServices: { ro: "Servicii active", ru: "Активные услуги", en: "Active Services" },
  totalBookings: { ro: "Total rezervări", ru: "Всего бронирований", en: "Total Bookings" },
  recentActivity: { ro: "Activitate recentă", ru: "Недавняя активность", en: "Recent Activity" },
  quickStats: { ro: "Statistici rapide", ru: "Быстрая статистика", en: "Quick Stats" },
  growth: { ro: "Creștere", ru: "Рост", en: "Growth" },
  decline: { ro: "Scădere", ru: "Снижение", en: "Decline" },
  noChange: { ro: "Fără modificări", ru: "Без изменений", en: "No Change" },
  vsLastPeriod: { ro: "vs perioada anterioară", ru: "по сравнению с предыдущим периодом", en: "vs last period" },
  loadingOverview: { ro: "Se încarcă prezentarea generală...", ru: "Загрузка обзора...", en: "Loading overview..." },
};

interface OverviewStats {
  totalUsers: number;
  totalProviders: number;
  activeServices: number;
  totalBookings: number;
  userGrowth: number;
  providerGrowth: number;
  serviceGrowth: number;
  bookingGrowth: number;
}

interface OverviewDashboardProps {
  timePeriod: string;
  refreshTrigger: Date;
}

export function OverviewDashboard({ timePeriod, refreshTrigger }: OverviewDashboardProps) {
  const { translate } = useLanguage();
  const [stats, setStats] = useState<OverviewStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOverviewStats = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch overview analytics data
        const overviewRes = await fetch(`/api/proxy/admin/analytics/overview?period=${timePeriod}`);

        if (!overviewRes.ok) {
          throw new Error('Failed to fetch overview analytics');
        }

        const overviewData = await overviewRes.json();
        setStats(overviewData);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error fetching overview stats:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOverviewStats();
  }, [timePeriod, refreshTrigger]);

  const formatGrowth = (growth: number) => {
    const isPositive = growth > 0;
    const isNegative = growth < 0;
    
    return {
      value: Math.abs(growth),
      isPositive,
      isNegative,
      icon: isPositive ? ArrowUpRight : isNegative ? ArrowDownRight : null,
      color: isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-muted-foreground'
    };
  };

  const StatCard = ({ 
    title, 
    value, 
    growth, 
    icon: Icon, 
    color = "text-primary" 
  }: { 
    title: string; 
    value: number; 
    growth: number; 
    icon: any; 
    color?: string; 
  }) => {
    const growthData = formatGrowth(growth);
    
    return (
      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <Icon className={`h-4 w-4 ${color}`} />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{value.toLocaleString()}</div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
            {growthData.icon && (
              <growthData.icon className={`h-3 w-3 ${growthData.color}`} />
            )}
            <span className={growthData.color}>
              {growthData.value}%
            </span>
            <span>{translate(overviewTranslations, 'vsLastPeriod')}</span>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-muted-foreground">
            <p>Error loading overview data</p>
            {error && <p className="text-sm mt-1">{error}</p>}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title={translate(overviewTranslations, 'totalUsers')}
          value={stats.totalUsers}
          growth={stats.userGrowth}
          icon={Users}
          color="text-blue-600"
        />
        <StatCard
          title={translate(overviewTranslations, 'totalProviders')}
          value={stats.totalProviders}
          growth={stats.providerGrowth}
          icon={UserCheck}
          color="text-green-600"
        />
        <StatCard
          title={translate(overviewTranslations, 'activeServices')}
          value={stats.activeServices}
          growth={stats.serviceGrowth}
          icon={Briefcase}
          color="text-purple-600"
        />
        <StatCard
          title={translate(overviewTranslations, 'totalBookings')}
          value={stats.totalBookings}
          growth={stats.bookingGrowth}
          icon={Calendar}
          color="text-orange-600"
        />
      </div>

      {/* Quick Overview Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Platform Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-muted-foreground py-8">
              Growth chart will be implemented here
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              {translate(overviewTranslations, 'recentActivity')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-muted-foreground py-8">
              Recent activity summary will be implemented here
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
