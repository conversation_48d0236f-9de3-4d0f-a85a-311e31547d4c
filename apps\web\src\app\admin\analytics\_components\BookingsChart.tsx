"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Loader2, Calendar } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const chartTranslations = {
  bookings: { ro: "<PERSON><PERSON>v<PERSON><PERSON>", ru: "Бронирования", en: "Bookings" },
  bookingsTrend: { ro: "Tendința rezervărilor", ru: "Тенденция бронирований", en: "Bookings Trend" },
  pending: { ro: "În așteptare", ru: "В ожидании", en: "Pending" },
  confirmed: { ro: "Confirmate", ru: "Подтвержденные", en: "Confirmed" },
  completed: { ro: "Finalizate", ru: "Завершенные", en: "Completed" },
  cancelled: { ro: "Anulate", ru: "Отмененные", en: "Cancelled" },
  total: { ro: "Total", ru: "Всего", en: "Total" },
  date: { ro: "Data", ru: "Дата", en: "Date" },
  loadingChart: { ro: "Se încarcă graficul...", ru: "Загрузка графика...", en: "Loading chart..." },
  errorLoadingChart: { ro: "Eroare la încărcarea graficului", ru: "Ошибка загрузки графика", en: "Error loading chart" },
  noDataAvailable: { ro: "Nu sunt date disponibile", ru: "Нет доступных данных", en: "No data available" },
};

interface BookingData {
  date: string;
  pending: number;
  confirmed: number;
  completed: number;
  cancelled: number;
  total: number;
}

interface BookingsChartProps {
  period: '7d' | '30d' | '90d' | '1y';
  refreshTrigger: Date;
}

export function BookingsChart({ period, refreshTrigger }: BookingsChartProps) {
  const { translate } = useLanguage();
  const [data, setData] = useState<BookingData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/proxy/admin/analytics/bookings?period=${period}`);
        if (!response.ok) {
          throw new Error('Failed to fetch bookings data');
        }
        
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        console.error('Error fetching bookings data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [period, refreshTrigger]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ro-RO', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{`${translate(chartTranslations, 'date')}: ${formatDate(label)}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {translate(chartTranslations, 'bookings')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-5 h-5 animate-spin" />
              {translate(chartTranslations, 'loadingChart')}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {translate(chartTranslations, 'bookings')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'errorLoadingChart')}</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {translate(chartTranslations, 'bookings')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'noDataAvailable')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate totals for summary
  const totalPending = data.reduce((sum, item) => sum + item.pending, 0);
  const totalConfirmed = data.reduce((sum, item) => sum + item.confirmed, 0);
  const totalCompleted = data.reduce((sum, item) => sum + item.completed, 0);
  const totalCancelled = data.reduce((sum, item) => sum + item.cancelled, 0);
  const totalBookings = data.reduce((sum, item) => sum + item.total, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          {translate(chartTranslations, 'bookings')}
        </CardTitle>
        <div className="flex gap-4 text-sm text-muted-foreground">
          <span>{translate(chartTranslations, 'pending')}: {totalPending}</span>
          <span>{translate(chartTranslations, 'confirmed')}: {totalConfirmed}</span>
          <span>{translate(chartTranslations, 'completed')}: {totalCompleted}</span>
          <span>{translate(chartTranslations, 'cancelled')}: {totalCancelled}</span>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              tickFormatter={formatDate}
              className="text-xs"
            />
            <YAxis className="text-xs" />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar 
              dataKey="pending" 
              fill="#f59e0b" 
              name={translate(chartTranslations, 'pending')}
              radius={[0, 0, 0, 0]}
            />
            <Bar 
              dataKey="confirmed" 
              fill="#3b82f6" 
              name={translate(chartTranslations, 'confirmed')}
              radius={[0, 0, 0, 0]}
            />
            <Bar 
              dataKey="completed" 
              fill="#10b981" 
              name={translate(chartTranslations, 'completed')}
              radius={[0, 0, 0, 0]}
            />
            <Bar 
              dataKey="cancelled" 
              fill="#ef4444" 
              name={translate(chartTranslations, 'cancelled')}
              radius={[0, 0, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
