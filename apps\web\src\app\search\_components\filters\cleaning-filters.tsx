"use client";

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useLanguage } from '@/contexts/language-context';
import { BooleanFilterGroup, FilterBadges, getActiveFilters } from './filter-components';

const cleaningFilterTranslations = {
  propertyTypes: { ro: "Tipuri Proprietăți", ru: "Типы недвижимости", en: "Property Types" },
  apartments: { ro: "Apartamente", ru: "Квартиры", en: "Apartments" },
  houses: { ro: "Case", ru: "Дома", en: "Houses" },
  offices: { ro: "Birouri", ru: "Офисы", en: "Offices" },

  cleaningTypes: { ro: "Tipuri Curățenie", ru: "Типы уборки", en: "Cleaning Types" },
  general: { ro: "Curățenie generală", ru: "Общая уборка", en: "General Cleaning" },
  postRenovation: { ro: "Dup<PERSON> renovare", ru: "После ремонта", en: "Post-Renovation" },
  occasional: { ro: "Ocazională", ru: "Разовая", en: "Occasional" },
  regular: { ro: "Regulată", ru: "Регулярная", en: "Regular" },

  extraServices: { ro: "Servicii Extra", ru: "Дополнительные услуги", en: "Extra Services" },
  ownProducts: { ro: "Produse proprii", ru: "Собственные средства", en: "Own Products" },
  ironing: { ro: "Călcat", ru: "Глажка", en: "Ironing" },
  windows: { ro: "Geamuri", ru: "Окна", en: "Windows" },
  disinfection: { ro: "Dezinfecție", ru: "Дезинфекция", en: "Disinfection" },
};

interface CleaningFiltersState {
  // Property types
  PropertyTypeApartments: boolean;
  PropertyTypeHouses: boolean;
  PropertyTypeOffices: boolean;

  // Cleaning types
  TypeGeneral: boolean;
  TypePostRenovation: boolean;
  TypeOccasional: boolean;
  TypeRegular: boolean;

  // Extra services
  OwnProducts: boolean;
  ExtraIroning: boolean;
  ExtraWindows: boolean;
  ExtraDisinfection: boolean;
}

export function CleaningFilters() {
  const { translate } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<CleaningFiltersState>({
    PropertyTypeApartments: searchParams.get('PropertyTypeApartments') === 'true',
    PropertyTypeHouses: searchParams.get('PropertyTypeHouses') === 'true',
    PropertyTypeOffices: searchParams.get('PropertyTypeOffices') === 'true',
    TypeGeneral: searchParams.get('TypeGeneral') === 'true',
    TypePostRenovation: searchParams.get('TypePostRenovation') === 'true',
    TypeOccasional: searchParams.get('TypeOccasional') === 'true',
    TypeRegular: searchParams.get('TypeRegular') === 'true',
    OwnProducts: searchParams.get('OwnProducts') === 'true',
    ExtraIroning: searchParams.get('ExtraIroning') === 'true',
    ExtraWindows: searchParams.get('ExtraWindows') === 'true',
    ExtraDisinfection: searchParams.get('ExtraDisinfection') === 'true',
  });

  // Update URL when filters change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams.toString());

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, 'true');
      } else {
        newParams.delete(key);
      }
    });

    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  }, [filters, router, pathname, searchParams]);

  const handleFilterChange = (key: string, checked: boolean) => {
    setFilters(prev => ({ ...prev, [key]: checked }));
  };

  const handleClearAll = () => {
    setFilters({
      PropertyTypeApartments: false,
      PropertyTypeHouses: false,
      PropertyTypeOffices: false,
      TypeGeneral: false,
      TypePostRenovation: false,
      TypeOccasional: false,
      TypeRegular: false,
      OwnProducts: false,
      ExtraIroning: false,
      ExtraWindows: false,
      ExtraDisinfection: false,
    });
  };

  const handleRemoveFilter = (key: string) => {
    setFilters(prev => ({ ...prev, [key]: false }));
  };

  // Create filter labels
  const filterLabels = {
    PropertyTypeApartments: translate(cleaningFilterTranslations, 'apartments'),
    PropertyTypeHouses: translate(cleaningFilterTranslations, 'houses'),
    PropertyTypeOffices: translate(cleaningFilterTranslations, 'offices'),
    TypeGeneral: translate(cleaningFilterTranslations, 'general'),
    TypePostRenovation: translate(cleaningFilterTranslations, 'postRenovation'),
    TypeOccasional: translate(cleaningFilterTranslations, 'occasional'),
    TypeRegular: translate(cleaningFilterTranslations, 'regular'),
    OwnProducts: translate(cleaningFilterTranslations, 'ownProducts'),
    ExtraIroning: translate(cleaningFilterTranslations, 'ironing'),
    ExtraWindows: translate(cleaningFilterTranslations, 'windows'),
    ExtraDisinfection: translate(cleaningFilterTranslations, 'disinfection'),
  };

  // Get active filters for badges
  const activeFilters = getActiveFilters(filters, filterLabels, 'cleaning');

  return (
    <div className="space-y-4">
      <FilterBadges
        activeFilters={activeFilters}
        onRemove={handleRemoveFilter}
        onClearAll={handleClearAll}
      />

      <BooleanFilterGroup
        title={translate(cleaningFilterTranslations, 'propertyTypes')}
        options={[
          { key: 'PropertyTypeApartments', label: filterLabels.PropertyTypeApartments, checked: filters.PropertyTypeApartments },
          { key: 'PropertyTypeHouses', label: filterLabels.PropertyTypeHouses, checked: filters.PropertyTypeHouses },
          { key: 'PropertyTypeOffices', label: filterLabels.PropertyTypeOffices, checked: filters.PropertyTypeOffices },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(cleaningFilterTranslations, 'cleaningTypes')}
        options={[
          { key: 'TypeGeneral', label: filterLabels.TypeGeneral, checked: filters.TypeGeneral },
          { key: 'TypePostRenovation', label: filterLabels.TypePostRenovation, checked: filters.TypePostRenovation },
          { key: 'TypeOccasional', label: filterLabels.TypeOccasional, checked: filters.TypeOccasional },
          { key: 'TypeRegular', label: filterLabels.TypeRegular, checked: filters.TypeRegular },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(cleaningFilterTranslations, 'extraServices')}
        options={[
          { key: 'OwnProducts', label: filterLabels.OwnProducts, checked: filters.OwnProducts },
          { key: 'ExtraIroning', label: filterLabels.ExtraIroning, checked: filters.ExtraIroning },
          { key: 'ExtraWindows', label: filterLabels.ExtraWindows, checked: filters.ExtraWindows },
          { key: 'ExtraDisinfection', label: filterLabels.ExtraDisinfection, checked: filters.ExtraDisinfection },
        ]}
        onChange={handleFilterChange}
      />
    </div>
  );
}
