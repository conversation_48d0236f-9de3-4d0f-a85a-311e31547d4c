
"use client";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useLanguage } from '@/contexts/language-context';

const faqSectionTranslations = {
  sectionTitle: { ro: "Întreb<PERSON>ri frecvente", ru: "Часто задаваемые вопросы", en: "Frequently Asked Questions" },
  sectionDescription: { ro: "Răspunsuri la cele mai comune întrebări despre platforma noastră.", ru: "Ответы на самые частые вопросы о нашей платформе.", en: "Answers to the most common questions about our platform." },
  q1: { ro: "Cum sunt verificați prestatorii de servicii?", ru: "Как проверяются поставщики услуг?", en: "How are service providers verified?" },
  a1: { ro: "Facilităm un proces de verificare în mai mulți pași pentru a construi încredere. Solicităm documente de identitate și referințe, iar administratorii noștri analizează fiecare cerere de înregistrare ca prestator. Încurajăm clienții să consulte recenziile și să comunice direct cu prestatorii înainte de a lua o decizie.", ru: "Мы способствуем многоэтапному процессу проверки для укрепления доверия. Мы запрашиваем документы, удостоверяющие личность, и рекомендации, а наши администраторы рассматривают каждую заявку на регистрацию в качестве поставщика. Мы призываем клиентов читать отзывы и напрямую общаться с поставщиками перед принятием решения.", en: "We facilitate a multi-step verification process to build trust. We request identity documents and references, and our administrators review each provider registration request. We encourage clients to review ratings and communicate directly with providers before making a decision." },
  q2: { ro: "Care sunt modalitățile de plată?", ru: "Какие способы оплаты доступны?", en: "What are the payment methods?" },
  a2: { ro: "Plata pentru servicii se negociază și se efectuează direct între client și prestator. bonami acționează ca o platformă de conectare și, în viitor, ar putea oferi opțiuni de plată securizată pentru a facilita aceste tranzacții.", ru: "Оплата за услуги обсуждается и производится непосредственно между клиентом и поставщиком. bonami выступает в качестве связующей платформы и в будущем может предложить безопасные варианты оплаты для облегчения этих транзакций.", en: "Payment for services is negotiated and made directly between the client and the provider. bonami acts as a connecting platform and may offer secure payment options in the future to facilitate these transactions." },
  q3: { ro: "Ce fac dacă nu sunt mulțumit de serviciul prestat?", ru: "Что делать, если я не доволен оказанной услугой?", en: "What if I'm not satisfied with the rendered service?" },
  a3: { ro: "Încurajăm comunicarea deschisă direct cu prestatorul pentru a rezolva orice problemă. Platforma noastră oferă un sistem de recenzii unde puteți lăsa feedback onest. Pentru dispute majore, echipa noastră de suport poate oferi mediere. Rolul nostru principal este de a asigura o comunitate transparentă și de încredere.", ru: "Мы поощряем открытое общение непосредственно с поставщиком для решения любых проблем. Наша платформа предлагает систему отзывов, где вы можете оставить честный отзыв. В случае серьезных споров наша служба поддержки может предложить посредничество. Наша главная роль - обеспечивать прозрачное и надежное сообщество.", en: "We encourage open communication directly with the provider to resolve any issues. Our platform provides a review system where you can leave honest feedback. For major disputes, our support team can offer mediation. Our primary role is to ensure a transparent and trustworthy community." },
  q4: { ro: "Cum funcționează sistemul de recenzii?", ru: "Как работает система отзывов?", en: "How does the review system work?" },
  a4: { ro: "După finalizarea unui serviciu contractat prin platformă, clienții au posibilitatea de a lăsa o recenzie și o evaluare numerică prestatorului. Acest sistem ajută alți utilizatori să ia decizii informate și contribuie la menținerea unui standard înalt de calitate în cadrul comunității noastre.", ru: "После завершения услуги, заказанной через платформу, у клиентов есть возможность оставить отзыв и числовую оценку поставщику. Эта система помогает другим пользователям принимать обоснованные решения и способствует поддержанию высокого стандарта качества в нашем сообществе.", en: "After a service booked through the platform is completed, clients have the opportunity to leave a review and a numerical rating for the provider. This system helps other users make informed decisions and contributes to maintaining a high standard of quality within our community." }
};

export function FaqSection() {
  const { translate } = useLanguage();
  const faqItems = [
    { questionKey: "q1", answerKey: "a1" },
    { questionKey: "q2", answerKey: "a2" },
    { questionKey: "q3", answerKey: "a3" },
    { questionKey: "q4", answerKey: "a4" },
  ];

  return (
    <section className="py-16 md:py-20 px-6 bg-card">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-4 font-headline">{translate(faqSectionTranslations, 'sectionTitle')}</h2>
        <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">
          {translate(faqSectionTranslations, 'sectionDescription')}
        </p>
        
        <Accordion type="single" collapsible className="w-full space-y-4">
          {faqItems.map((item, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="border-b border-border last:border-b-0">
              <AccordionTrigger className="text-left font-medium text-lg hover:no-underline">
                {translate(faqSectionTranslations, item.questionKey)}
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground pt-2">
                {translate(faqSectionTranslations, item.answerKey)}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
}
