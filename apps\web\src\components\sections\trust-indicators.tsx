
"use client";
import { CheckCircle, BadgeDollarSign, ShieldCheck } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';

const trustIndicatorTranslations = {
  professionals: { ro: "Peste 5.000 de profesioniști", ru: "Более 5000 профессионалов", en: "Over 5,000 professionals" },
  securePayments: { ro: "Plăți sigure", ru: "Безопасные платежи", en: "Secure payments" },
  detailedVerification: { ro: "Verificare detaliată", ru: "Детальная проверка", en: "Detailed verification" },
};

export function TrustIndicators() {
  const { translate } = useLanguage();
  const indicators = [
    { icon: <CheckCircle className="w-6 h-6 text-primary mr-2" />, textKey: "professionals" },
    { icon: <BadgeDollarSign className="w-6 h-6 text-primary mr-2" />, textKey: "securePayments" },
    { icon: <ShieldCheck className="w-6 h-6 text-primary mr-2" />, textKey: "detailedVerification" },
  ];

  return (
    <div className="bg-muted/50 py-8">
      <div className="max-w-6xl mx-auto px-6 flex flex-wrap justify-center items-center gap-8 md:gap-16 text-muted-foreground">
        {indicators.map((indicator, index) => (
          <div key={index} className="flex items-center text-sm font-medium">
            {indicator.icon}
            <span>{translate(trustIndicatorTranslations, indicator.textKey)}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

