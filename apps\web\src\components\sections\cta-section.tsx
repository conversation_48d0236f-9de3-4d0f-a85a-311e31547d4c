
"use client";
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useLanguage } from '@/contexts/language-context';

const ctaSectionTranslations = {
  title: { ro: "Găsește ajutorul potrivit pentru familia ta", ru: "Найдите подходящую помощь для вашей семьи", en: "Find the right help for your family" },
  description: { ro: "Indiferent de nevoile tale, avem soluția perfectă. Începe acum în doar câteva minute.", ru: "Независимо от ваших потребностей, у нас есть идеальное решение. Начните прямо сейчас всего за несколько минут.", en: "Whatever your needs, we have the perfect solution. Start now in just a few minutes." },
  searchButton: { ro: "Caută un prestator", ru: "Найти поставщика", en: "Search for a provider" },
  providerButton: { ro: "Devino prestator", ru: "Стать поставщиком", en: "Become a provider" },
};

export function CtaSection() {
  const { translate } = useLanguage();
  return (
    <section className="py-16 md:py-20 px-6 hero-gradient text-white">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 font-headline">
          {translate(ctaSectionTranslations, 'title')}
        </h2>
        <p className="text-lg md:text-xl mb-8 opacity-90 max-w-2xl mx-auto">
          {translate(ctaSectionTranslations, 'description')}
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button size="lg" asChild className="bg-card text-primary hover:bg-card/90 shadow-lg transform hover:scale-105 transition-transform">
            <Link href="/search">{translate(ctaSectionTranslations, 'searchButton')}</Link>
          </Button>
          <Button 
            size="lg" 
            variant="outline" 
            asChild 
            className="bg-transparent border-2 border-white text-white hover:bg-white hover:bg-opacity-10 shadow-lg transform hover:scale-105 transition-transform"
          >
            <Link href="/register-provider">{translate(ctaSectionTranslations, 'providerButton')}</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
