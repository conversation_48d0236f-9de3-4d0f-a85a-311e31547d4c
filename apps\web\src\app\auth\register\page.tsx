
"use client";

import { useState, type FormEvent } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import Link from "next/link";
import { Loader2, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from '@/contexts/language-context';
import { useRouter } from "next/navigation"; 
import { Separator } from "@/components/ui/separator";
import { signIn } from "next-auth/react";

const GoogleIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22.56 12.25C22.56 11.47 22.49 10.72 22.35 10H12V14.5H18.31C18.03 16.36 17.01 17.91 15.21 18.99V21.6H19.42C21.5 19.68 22.56 16.78 22.56 12.25Z" fill="#4285F4"/>
    <path d="M12 23C14.97 23 17.45 22.02 19.42 20.6L15.21 17.99C14.24 18.63 13.08 19 12 19C9.27 19 6.94 17.24 6.02 14.8H1.69V17.5C3.62 20.81 7.58 23 12 23Z" fill="#34A853"/>
    <path d="M6.02 14.8C5.79 14.18 5.66 13.49 5.66 12.79C5.66 12.09 5.79 11.4 6.02 10.78V8L1.69 8C0.63 10.03 0 12.44 0 15C0 17.56 0.63 19.97 1.69 22L6.02 19.22C5.79 18.51 5.66 17.82 5.66 17.12C5.66 16.41 5.79 15.72 6.02 14.8Z" fill="#FBBC05"/>
    <path d="M12 5.5C13.66 5.5 15.07 6.13 16.16 7.17L19.5 3.83C17.45 1.92 14.97 1 12 1C7.58 1 3.62 3.19 1.69 6.5L6.02 9.22C6.94 6.76 9.27 5 12 5V5.5Z" fill="#EA4335"/>
  </svg>
);

const FacebookIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18 2H15C12.69 2 12 3.05 12 5.42V8H9V12H12V22H16V12H19L20 8H16V5.91C16 4.81 16.63 4.09 17.56 4.09H19.5V2H18Z" fill="#1877F2"/>
  </svg>
);

const PasswordStrengthMeter = ({ score }: { score: number }) => {
  const strengthColors = [
    'bg-gray-300 dark:bg-gray-600', 
    'bg-red-500',   
    'bg-orange-500',
    'bg-yellow-500',
    'bg-lime-500',  
    'bg-green-500'  
  ];
  const segments = Array(5).fill(0);

  return (
    <div className="flex h-2 rounded-full overflow-hidden mt-1" aria-label="Password strength indicator">
      {segments.map((_, index) => (
        <div
          key={index}
          className={`flex-1 transition-colors duration-300 ${
            score > index ? strengthColors[Math.min(score, 5)] : strengthColors[0]
          }`}
          role="presentation"
        />
      ))}
    </div>
  );
};

const registerPageTranslations = {
  pageTitle: { ro: "Înregistrare", ru: "Регистрация", en: "Register" },
  description: { ro: "Creează un cont nou pentru a accesa serviciile.", ru: "Создайте новый аккаунт для доступа к услугам.", en: "Create a new account to access services." },
  fullNameLabel: { ro: "Nume complet", ru: "Полное имя", en: "Full name" },
  fullNamePlaceholder: { ro: "Nume Prenume", ru: "Имя Фамилия", en: "First Lastname" },
  emailLabel: { ro: "Email", ru: "Электронная почта", en: "Email" },
  emailPlaceholder: { ro: "<EMAIL>", ru: "<EMAIL>", en: "<EMAIL>" },
  passwordLabel: { ro: "Parolă", ru: "Пароль", en: "Password" },
  passwordPlaceholder: { ro: "Minim 8 caractere, o majusculă, o minusculă, o cifră, un caracter special", ru: "Мин. 8 симв., 1 загл., 1 строч., 1 цифра, 1 спецсимвол", en: "Min 8 chars, 1 uppercase, 1 lowercase, 1 digit, 1 special" },
  confirmPasswordLabel: { ro: "Confirmă Parola", ru: "Подтвердите пароль", en: "Confirm Password" },
  termsLabel: { ro: "Sunt de acord cu", ru: "Я согласен с", en: "I agree to the" },
  termsLink: { ro: "Termenii și Condițiile", ru: "Условиями и Положениями", en: "Terms and Conditions" },
  registerButton: { ro: "Înregistrează-te", ru: "Зарегистрироваться", en: "Sign Up" },
  loadingButton: { ro: "Se înregistrează...", ru: "Регистрация...", en: "Signing up..." },
  hasAccount: { ro: "Ai deja cont?", ru: "Уже есть аккаунт?", en: "Already have an account?" },
  loginLink: { ro: "Conectează-te", ru: "Войти", en: "Log in" },
  errorAlertTitle: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  passwordMismatch: { ro: "Parolele nu se potrivesc.", ru: "Пароли не совпадают.", en: "Passwords do not match." },
  termsNotAccepted: { ro: "Trebuie să accepți Termenii și Condițiile.", ru: "Вы должны принять Условия и Положения.", en: "You must accept the Terms and Conditions." },
  toastSuccessTitle: { ro: "Succes!", ru: "Успех!", en: "Success!" },
  toastErrorTitle: { ro: "Eroare Înregistrare", ru: "Ошибка регистрации", en: "Registration Error" },
  registrationAndLoginSuccess: { ro: "Înregistrare și autentificare reușite! Vei fi redirecționat.", ru: "Регистрация и вход выполнены успешно! Вы будете перенаправлены.", en: "Registration and login successful! You will be redirected."},
  passwordTooWeak: { ro: "Parola nu este suficient de complexă. Asigură-te că are minim 8 caractere, o literă mare, o literă mică, o cifră și un caracter special.", ru: "Пароль недостаточно сложен. Убедитесь, что он содержит не менее 8 символов, одну заглавную букву, одну строчную букву, одну цифру и один специальный символ.", en: "Password is not complex enough. Ensure it has at least 8 characters, one uppercase letter, one lowercase letter, one digit, and one special character." },
  strengthLabel: { ro: "Complexitate parolă:", ru: "Сложность пароля:", en: "Password strength:" },
  strengthTooShort: { ro: "Prea scurtă", ru: "Слишком короткий", en: "Too short" },
  strengthVeryWeak: { ro: "Foarte Slabă", ru: "Очень слабый", en: "Very Weak" },
  strengthWeak: { ro: "Slabă", ru: "Слабый", en: "Weak" },
  strengthMedium: { ro: "Medie", ru: "Средний", en: "Medium" },
  strengthStrong: { ro: "Puternică", ru: "Сильный", en: "Strong" },
  strengthVeryStrong: { ro: "Foarte Puternică", ru: "Очень сильный", en: "Very Strong" },
  orSignUpWith: { ro: "Sau înregistrează-te cu", ru: "Или зарегистрироваться с", en: "Or sign up with" },
  signUpWithGoogle: { ro: "Înregistrează-te cu Google", ru: "Зарегистрироваться с Google", en: "Sign up with Google" },
  signUpWithFacebook: { ro: "Înregistrează-te cu Facebook", ru: "Зарегистрироваться с Facebook", en: "Sign up with Facebook" },
  oauthNotImplemented: { ro: "Autentificarea OAuth nu este încă implementată complet.", ru: "OAuth аутентификация еще не полностью реализована.", en: "OAuth authentication is not yet fully implemented."}
};

export default function RegisterPage() {
  const { translate } = useLanguage();
  const router = useRouter(); 
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const [passwordStrengthScore, setPasswordStrengthScore] = useState(0);
  const [passwordStrengthText, setPasswordStrengthText] = useState('');

  const handleOAuthSignUp = async (provider: 'google' | 'facebook') => {
    setIsLoading(true);
    setError(null);
    const result = await signIn(provider, { redirect: false, callbackUrl: '/profile-setup?from=registration' });
    if (result?.error) {
      setError(result.error);
      toast({ variant: "destructive", title: translate(registerPageTranslations, 'toastErrorTitle'), description: result.error });
      setIsLoading(false);
    }
    // Successful OAuth sign in, NextAuth.js handles redirection.
  };

  const calculatePasswordStrength = (currentPassword: string) => {
    let score = 0;
    if (!currentPassword) {
      setPasswordStrengthScore(0);
      setPasswordStrengthText('');
      return;
    }

    if (currentPassword.length >= 8) score++; else {
      setPasswordStrengthScore(0); 
      setPasswordStrengthText(translate(registerPageTranslations, 'strengthTooShort'));
      return; 
    }
    if (/[a-z]/.test(currentPassword)) score++;
    if (/[A-Z]/.test(currentPassword)) score++;
    if (/\d/.test(currentPassword)) score++; // Corrected: /\d/
    if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(currentPassword)) score++;
    
    setPasswordStrengthScore(score);

    switch (score) {
      case 0: 
      case 1: setPasswordStrengthText(translate(registerPageTranslations, 'strengthVeryWeak')); break;
      case 2: setPasswordStrengthText(translate(registerPageTranslations, 'strengthWeak')); break;
      case 3: setPasswordStrengthText(translate(registerPageTranslations, 'strengthMedium')); break;
      case 4: setPasswordStrengthText(translate(registerPageTranslations, 'strengthStrong')); break;
      case 5: setPasswordStrengthText(translate(registerPageTranslations, 'strengthVeryStrong')); break;
      default: setPasswordStrengthText('');
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    calculatePasswordStrength(newPassword);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);

    if (password !== confirmPassword) {
      setError(translate(registerPageTranslations, 'passwordMismatch'));
      return;
    }
    if (!termsAccepted) {
      setError(translate(registerPageTranslations, 'termsNotAccepted'));
      return;
    }
    if (passwordStrengthScore < 3) {
        setError(translate(registerPageTranslations, 'passwordTooWeak'));
        return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ fullName, email, password }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        let errorMessage = responseData.message || "A apărut o eroare la înregistrare.";
        if (responseData.message && responseData.message.includes("Parola nu îndeplinește cerințele de complexitate")) {
          errorMessage = translate(registerPageTranslations, 'passwordTooWeak');
        }
        throw new Error(errorMessage);
      }
      
      if (responseData.success && responseData.user) {
        toast({
          title: translate(registerPageTranslations, 'toastSuccessTitle'),
          description: translate(registerPageTranslations, 'registrationAndLoginSuccess'),
        });
        
        // Login with credentials provider after successful custom registration
        const signInResponse = await signIn('credentials', {
          redirect: false,
          email: email,
          password: password,
          callbackUrl: '/dashboard'
        });

        if (signInResponse?.error) {
            setError(signInResponse.error);
            toast({ variant: "destructive", title: translate(registerPageTranslations, 'toastErrorTitle'), description: signInResponse.error });
        } else if (signInResponse?.ok) {
            router.push('/profile-setup?from=registration');
        }
      } else {
         // This case should ideally not be hit if response.ok is true and success is true
        toast({
          title: translate(registerPageTranslations, 'toastErrorTitle'),
          description: responseData.message || "Înregistrare eșuată.", 
        });
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Eroare necunoscută.";
      setError(errorMessage); 
      toast({ 
        variant: "destructive",
        title: translate(registerPageTranslations, 'toastErrorTitle'),
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStrengthTextColor = () => {
    switch (passwordStrengthScore) {
      case 1: return 'text-red-600 dark:text-red-400';
      case 2: return 'text-orange-600 dark:text-orange-400';
      case 3: return 'text-yellow-600 dark:text-yellow-400';
      case 4: return 'text-lime-600 dark:text-lime-400';
      case 5: return 'text-green-600 dark:text-green-400';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold font-headline">{translate(registerPageTranslations, 'pageTitle')}</CardTitle>
            <CardDescription>{translate(registerPageTranslations, 'description')}</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">{translate(registerPageTranslations, 'fullNameLabel')}</Label>
                <Input 
                  id="fullName" 
                  type="text" 
                  placeholder={translate(registerPageTranslations, 'fullNamePlaceholder')}
                  required 
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">{translate(registerPageTranslations, 'emailLabel')}</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder={translate(registerPageTranslations, 'emailPlaceholder')}
                  required 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">{translate(registerPageTranslations, 'passwordLabel')}</Label>
                <Input 
                  id="password" 
                  type="password" 
                  required 
                  value={password}
                  onChange={handlePasswordChange}
                  placeholder={translate(registerPageTranslations, 'passwordPlaceholder')}
                />
                {password && (
                  <div className="mt-1">
                    <PasswordStrengthMeter score={passwordStrengthScore} />
                    {passwordStrengthText && (
                      <p className={`text-xs mt-1 ${getStrengthTextColor()}`}>
                        {passwordStrengthText}
                      </p>
                    )}
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">{translate(registerPageTranslations, 'confirmPasswordLabel')}</Label>
                <Input 
                  id="confirmPassword" 
                  type="password" 
                  required 
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="terms" 
                  checked={termsAccepted}
                  onCheckedChange={(checked) => setTermsAccepted(checked as boolean)}
                />
                <Label htmlFor="terms" className="text-sm text-muted-foreground">
                  {translate(registerPageTranslations, 'termsLabel')}{" "}
                  <Link href="/terms" className="underline hover:text-primary">
                    {translate(registerPageTranslations, 'termsLink')}
                  </Link>
                </Label>
              </div>
              {error && (
                <Alert variant="destructive" className="mt-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>{translate(registerPageTranslations, 'errorAlertTitle')}</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading && !email.includes('@') ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                {isLoading && !email.includes('@') ? translate(registerPageTranslations, 'loadingButton') : translate(registerPageTranslations, 'registerButton')}
              </Button>
            </CardContent>
          </form>
          
          <div className="px-6 pb-2">
            <div className="relative my-3">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  {translate(registerPageTranslations, 'orSignUpWith')}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <Button variant="outline" className="w-full" onClick={() => handleOAuthSignUp('google')} disabled={isLoading}>
                {isLoading && email.includes('@') ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <GoogleIcon />}
                <span className="ml-2">{translate(registerPageTranslations, 'signUpWithGoogle')}</span>
              </Button>
              <Button variant="outline" className="w-full" onClick={() => handleOAuthSignUp('facebook')} disabled={isLoading}>
                 {isLoading && email.includes('@') ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <FacebookIcon />}
                <span className="ml-2">{translate(registerPageTranslations, 'signUpWithFacebook')}</span>
              </Button>
            </div>
          </div>

          <CardFooter className="flex flex-col space-y-2 pt-4">
            <p className="text-sm text-center text-muted-foreground">
              {translate(registerPageTranslations, 'hasAccount')}{" "}
              <Link href="/login" className="font-medium text-primary hover:underline">
                {translate(registerPageTranslations, 'loginLink')}
              </Link>
            </p>
          </CardFooter>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
