"use client";

import { CheckCircle, XCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { commonTranslations } from "@repo/translations";
import { useLanguage } from "@/contexts/language-context";

export const DetailItem = ({ label, value }: { label: string; value: string | number | null | undefined }) => {
  if (value === null || value === undefined || value === '') return null;
  return (
    <div className="py-2 px-3 odd:bg-muted/30 rounded-md grid grid-cols-1 md:grid-cols-3 gap-1 items-center">
      <dt className="font-medium text-muted-foreground">{label}</dt>
      <dd className="md:col-span-2 text-foreground break-words">{String(value)}</dd>
    </div>
  );
};

export const BooleanDetailItem = ({ label, value }: { label: string; value: boolean | undefined | null }) => {
  return (
    <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm bg-background">
        <Label htmlFor={`switch-${label.replace(/\s+/g, '-')}`} className="text-sm font-medium text-foreground cursor-default">
            {label}
        </Label>
        <Switch
            id={`switch-${label.replace(/\s+/g, '-')}`}
            checked={!!value}
            disabled
            aria-readonly
        />
    </div>
  );
};
