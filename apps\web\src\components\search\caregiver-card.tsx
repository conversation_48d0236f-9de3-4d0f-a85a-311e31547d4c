
"use client";
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, MapPin, DollarSign } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { cn } from '@/lib/utils';
import { getAvatarUrl } from '@/lib/avatar-utils';
import type { CaregiverSearchResult } from '@repo/types';
import { UserSilhouetteIcon } from '@/components/ui/user-silhouette-icon';

const caregiverCardTranslations = {
  reviewsSuffix: { ro: "recenzii", ru: "отзывов", en: "reviews" },
  viewProfileButton: { ro: "Vezi Profil", ru: "Смотреть профиль", en: "View Profile" },
  bookServiceButton: { ro: "Rezervă Serviciul", ru: "Забронировать услугу", en: "Book Service" },
  noServiceAvailable: { ro: "Serviciu Indisponibil", ru: "Услуга недоступна", en: "Service Unavailable" }
};

interface CaregiverCardProps {
  caregiver: CaregiverSearchResult;
  onBookService?: () => void;
}

export function CaregiverCard({ caregiver, onBookService }: CaregiverCardProps) {
  const { translate } = useLanguage();
  const canViewProfile = caregiver.serviceIdForLink !== -1;
  const avatarUrl = getAvatarUrl(caregiver.imageUrl);

  return (
    <Card className="flex flex-col overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <div className="relative w-full h-48 bg-muted/50 flex items-center justify-center">
        {avatarUrl ? (
            <Image
              src={avatarUrl}
              alt={caregiver.name}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              style={{objectFit:"cover"}}
              data-ai-hint={"portrait person"}
            />
          ) : (
            <UserSilhouetteIcon className="w-24 h-24 text-muted-foreground/50" />
          )}
      </div>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-xl font-headline">{caregiver.name}</CardTitle>
          <Badge variant="outline">{caregiver.serviceType}</Badge>
        </div>
        <div className="flex items-center space-x-1 text-sm text-muted-foreground pt-1">
          <MapPin className="w-4 h-4" />
          <span>{caregiver.location}</span>
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <CardDescription className="line-clamp-3 mb-2">{caregiver.description}</CardDescription>
        <div className="flex items-center space-x-4 text-sm mb-2">
          <div className="flex items-center">
            <Star className="w-4 h-4 text-yellow-400 fill-yellow-400 mr-1" />
            <span>{caregiver.rating.toFixed(1)} ({caregiver.reviewsCount} {translate(caregiverCardTranslations, 'reviewsSuffix')})</span>
          </div>
        </div>
        <div className="flex items-center text-sm text-green-600">
          <DollarSign className="w-4 h-4 mr-1" />
          <span className="text-muted-foreground">{caregiver.priceRate}</span>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        {canViewProfile ? (
          <>
            <Button asChild variant="outline" className="flex-1">
              <Link href={`/profile/${caregiver.id}/${caregiver.serviceIdForLink}`}>
                {translate(caregiverCardTranslations, 'viewProfileButton')}
              </Link>
            </Button>
            {onBookService && (
              <Button onClick={onBookService} className="flex-1">
                {translate(caregiverCardTranslations, 'bookServiceButton')}
              </Button>
            )}
          </>
        ) : (
          <Button className={cn("w-full", !canViewProfile && "opacity-50 cursor-not-allowed")} disabled>
            {translate(caregiverCardTranslations, 'noServiceAvailable')}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
