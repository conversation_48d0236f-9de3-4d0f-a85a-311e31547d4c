
// prisma/seed.ts
import { PrismaClient, UserRole, ServiceCategorySlug, LocationType } from '@prisma/client';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

function generateRandomPassword(length: number = 16): string {
  // Exclude characters that can be confusing (I, l, 1, O, 0)
  const chars = 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789!@#$%^&*()_+-=[]{}|;:,.<>?';
  let password = '';
  const randomBytes = crypto.randomBytes(length);
  for (let i = 0; i < length; i++) {
    password += chars[randomBytes[i] % chars.length];
  }
  return password;
}

async function main() {
  console.log("Start seeding ...");

  // --- 1. Seed Roles ---
  const rolesToSeed: UserRole[] = [UserRole.Client, UserRole.Provider, UserRole.Admin];
  for (const roleName of rolesToSeed) {
    await prisma.role.upsert({
      where: { Name: roleName },
      update: {},
      create: { Name: roleName },
    });
    console.log(`Upserted role: ${roleName}`);
  }
  const clientRole = await prisma.role.findUnique({ where: { Name: UserRole.Client } });
  const providerRole = await prisma.role.findUnique({ where: { Name: UserRole.Provider } });
  const adminRole = await prisma.role.findUnique({ where: { Name: UserRole.Admin } });

  if (!clientRole || !providerRole || !adminRole) {
    console.error("Critical error: One or more essential roles could not be found or created.");
    process.exit(1);
  }
  
  // --- 2. Seed Service Categories ---
  const categoriesToSeed = [
    { NameKey: "categoryNanny", Slug: ServiceCategorySlug.Nanny, AiExpectedValue: "Nanny", DefaultImageHint: "child playing" },
    { NameKey: "categoryElderCare", Slug: ServiceCategorySlug.ElderCare, AiExpectedValue: "Elder Care", DefaultImageHint: "senior care" },
    { NameKey: "categoryCleaning", Slug: ServiceCategorySlug.Cleaning, AiExpectedValue: "Cleaning", DefaultImageHint: "cleaning supplies" },
    { NameKey: "categoryTutoring", Slug: ServiceCategorySlug.Tutoring, AiExpectedValue: "Tutoring", DefaultImageHint: "student learning" },
    { NameKey: "categoryCooking", Slug: ServiceCategorySlug.Cooking, AiExpectedValue: "Cooking", DefaultImageHint: "food preparation" },
  ];

  for (const cat of categoriesToSeed) {
    await prisma.serviceCategory.upsert({
      where: { Slug: cat.Slug },
      update: { NameKey: cat.NameKey, AiExpectedValue: cat.AiExpectedValue, DefaultImageHint: cat.DefaultImageHint },
      create: {
        NameKey: cat.NameKey,
        Slug: cat.Slug,
        AiExpectedValue: cat.AiExpectedValue,
        DefaultImageHint: cat.DefaultImageHint
      },
    });
    console.log(`Upserted service category: ${cat.NameKey}`);
  }

  // --- 3. Seed Locations ---
  console.log("Seeding locations...");

  // Complete location data with all locations from original allMoldovaLocations array
  const locationData = [
    // Country level
    { id: 1, slug: 'all', translationKey: 'locAllMoldova', name: 'Moldova', type: LocationType.Country, parentId: null, sortOrder: 0, isCapital: true, specialType: 'country' },

    // Municipality level
    { id: 2, slug: 'chisinau-municipality-all', translationKey: 'locChisinauMunicipalityScope', name: 'Municipiul Chișinău', type: LocationType.Municipality, parentId: 1, sortOrder: 1, isCapital: true, specialType: 'capital_municipality' },

    // Chisinau City
    { id: 3, slug: 'chisinau', translationKey: 'locChisinauCity', name: 'Chișinău', type: LocationType.City, parentId: 2, sortOrder: 2, isCapital: true, specialType: 'capital_city' },

    // Chisinau City Sectors
    { id: 4, slug: 'chisinau-botanica', translationKey: 'locChisinauBotanica', name: 'Chișinău, Botanica', type: LocationType.Sector, parentId: 3, sortOrder: 3, isCapital: false, specialType: null },
    { id: 5, slug: 'chisinau-buiucani', translationKey: 'locChisinauBuiucani', name: 'Chișinău, Buiucani', type: LocationType.Sector, parentId: 3, sortOrder: 4, isCapital: false, specialType: null },
    { id: 6, slug: 'chisinau-centru', translationKey: 'locChisinauCentru', name: 'Chișinău, Centru', type: LocationType.Sector, parentId: 3, sortOrder: 5, isCapital: false, specialType: null },
    { id: 7, slug: 'chisinau-ciocana', translationKey: 'locChisinauCiocana', name: 'Chișinău, Ciocana', type: LocationType.Sector, parentId: 3, sortOrder: 6, isCapital: false, specialType: null },
    { id: 8, slug: 'chisinau-rascani', translationKey: 'locChisinauRascani', name: 'Chișinău, Râșcani', type: LocationType.Sector, parentId: 3, sortOrder: 7, isCapital: false, specialType: null },
    { id: 9, slug: 'chisinau-telecentru', translationKey: 'locChisinauTelecentru', name: 'Chișinău, Telecentru', type: LocationType.Sector, parentId: 3, sortOrder: 8, isCapital: false, specialType: null },

    // Chisinau Municipality Suburbs/Towns/Communes
    { id: 10, slug: 'codru', translationKey: 'locCodru', name: 'Municipiul Chișinău, Codru', type: LocationType.Suburb, parentId: 2, sortOrder: 9, isCapital: false, specialType: null },
    { id: 11, slug: 'cricova', translationKey: 'locCricova', name: 'Municipiul Chișinău, Cricova', type: LocationType.Suburb, parentId: 2, sortOrder: 10, isCapital: false, specialType: null },
    { id: 12, slug: 'durlesti', translationKey: 'locDurlesti', name: 'Municipiul Chișinău, Durlești', type: LocationType.Suburb, parentId: 2, sortOrder: 11, isCapital: false, specialType: null },
    { id: 13, slug: 'ghidighici', translationKey: 'locGhidighici', name: 'Municipiul Chișinău, Ghidighici', type: LocationType.Suburb, parentId: 2, sortOrder: 12, isCapital: false, specialType: null },
    { id: 14, slug: 'singera', translationKey: 'locSingera', name: 'Municipiul Chișinău, Sîngera', type: LocationType.Suburb, parentId: 2, sortOrder: 13, isCapital: false, specialType: null },
    { id: 15, slug: 'stauceni', translationKey: 'locStauceni', name: 'Municipiul Chișinău, Stăuceni', type: LocationType.Suburb, parentId: 2, sortOrder: 14, isCapital: false, specialType: null },
    { id: 16, slug: 'vadul-lui-voda', translationKey: 'locVadulLuiVoda', name: 'Municipiul Chișinău, Vadul lui Vodă', type: LocationType.Suburb, parentId: 2, sortOrder: 15, isCapital: false, specialType: null },
    { id: 17, slug: 'vatra', translationKey: 'locVatra', name: 'Municipiul Chișinău, Vatra', type: LocationType.Suburb, parentId: 2, sortOrder: 16, isCapital: false, specialType: null },
    { id: 18, slug: 'bacioi', translationKey: 'locBacioi', name: 'Municipiul Chișinău, Băcioi', type: LocationType.Suburb, parentId: 2, sortOrder: 17, isCapital: false, specialType: null },
    { id: 19, slug: 'bubuieci', translationKey: 'locBubuieci', name: 'Municipiul Chișinău, Bubuieci', type: LocationType.Suburb, parentId: 2, sortOrder: 18, isCapital: false, specialType: null },
    { id: 20, slug: 'gratiesti', translationKey: 'locGratiesti', name: 'Municipiul Chișinău, Grătiești', type: LocationType.Suburb, parentId: 2, sortOrder: 19, isCapital: false, specialType: null },
    { id: 21, slug: 'truseni', translationKey: 'locTruseni', name: 'Municipiul Chișinău, Trușeni', type: LocationType.Suburb, parentId: 2, sortOrder: 20, isCapital: false, specialType: null },

    // Major Cities
    { id: 30, slug: 'balti', translationKey: 'locBalti', name: 'Bălți', type: LocationType.City, parentId: 1, sortOrder: 30, isCapital: false, specialType: 'major_city' },
    { id: 31, slug: 'tiraspol', translationKey: 'locTiraspol', name: 'Tiraspol', type: LocationType.City, parentId: 1, sortOrder: 31, isCapital: false, specialType: 'major_city' },
    { id: 32, slug: 'bender', translationKey: 'locBender', name: 'Bender (Tighina)', type: LocationType.City, parentId: 1, sortOrder: 32, isCapital: false, specialType: 'major_city' },
    { id: 33, slug: 'ribnita', translationKey: 'locRibnita', name: 'Rîbnița', type: LocationType.City, parentId: 1, sortOrder: 33, isCapital: false, specialType: 'major_city' },
    { id: 34, slug: 'cahul', translationKey: 'locCahul', name: 'Cahul', type: LocationType.City, parentId: 1, sortOrder: 34, isCapital: false, specialType: 'major_city' },
    { id: 35, slug: 'ungheni', translationKey: 'locUngheni', name: 'Ungheni', type: LocationType.City, parentId: 1, sortOrder: 35, isCapital: false, specialType: 'major_city' },
    { id: 36, slug: 'soroca', translationKey: 'locSoroca', name: 'Soroca', type: LocationType.City, parentId: 1, sortOrder: 36, isCapital: false, specialType: 'major_city' },
    { id: 37, slug: 'orhei', translationKey: 'locOrhei', name: 'Orhei', type: LocationType.City, parentId: 1, sortOrder: 37, isCapital: false, specialType: 'major_city' },
    { id: 38, slug: 'comrat', translationKey: 'locComrat', name: 'Comrat', type: LocationType.City, parentId: 1, sortOrder: 38, isCapital: false, specialType: 'major_city' },

    // Other Cities and Towns
    { id: 40, slug: 'edinet', translationKey: 'locEdinet', name: 'Edineț', type: LocationType.City, parentId: 1, sortOrder: 40, isCapital: false, specialType: null },
    { id: 41, slug: 'drochia', translationKey: 'locDrochia', name: 'Drochia', type: LocationType.Town, parentId: 1, sortOrder: 41, isCapital: false, specialType: null },
    { id: 42, slug: 'floresti', translationKey: 'locFloresti', name: 'Florești', type: LocationType.Town, parentId: 1, sortOrder: 42, isCapital: false, specialType: null },
    { id: 43, slug: 'glodeni', translationKey: 'locGlodeni', name: 'Glodeni', type: LocationType.Town, parentId: 1, sortOrder: 43, isCapital: false, specialType: null },
    { id: 44, slug: 'donduseni', translationKey: 'locDonduseni', name: 'Dondușeni', type: LocationType.Town, parentId: 1, sortOrder: 44, isCapital: false, specialType: null },
    { id: 45, slug: 'singerei', translationKey: 'locSingerei', name: 'Sîngerei', type: LocationType.Town, parentId: 1, sortOrder: 45, isCapital: false, specialType: null },
    { id: 46, slug: 'rezina', translationKey: 'locRezina', name: 'Rezina', type: LocationType.Town, parentId: 1, sortOrder: 46, isCapital: false, specialType: null },
    { id: 47, slug: 'riscani', translationKey: 'locRiscani', name: 'Rîșcani', type: LocationType.Town, parentId: 1, sortOrder: 47, isCapital: false, specialType: null },
    { id: 48, slug: 'falesti', translationKey: 'locFalesti', name: 'Fălești', type: LocationType.Town, parentId: 1, sortOrder: 48, isCapital: false, specialType: null },
    { id: 49, slug: 'ungheni-raion', translationKey: 'locUngheniRaion', name: 'Ungheni (raion)', type: LocationType.Town, parentId: 1, sortOrder: 49, isCapital: false, specialType: null },
    { id: 50, slug: 'nisporeni', translationKey: 'locNisporeni', name: 'Nisporeni', type: LocationType.Town, parentId: 1, sortOrder: 50, isCapital: false, specialType: null },
    { id: 51, slug: 'straseni', translationKey: 'locStraseni', name: 'Strășeni', type: LocationType.Town, parentId: 1, sortOrder: 51, isCapital: false, specialType: null },
    { id: 52, slug: 'calarasi', translationKey: 'locCalarasi', name: 'Călărași', type: LocationType.Town, parentId: 1, sortOrder: 52, isCapital: false, specialType: null },
    { id: 53, slug: 'criuleni', translationKey: 'locCriuleni', name: 'Criuleni', type: LocationType.Town, parentId: 1, sortOrder: 53, isCapital: false, specialType: null },
    { id: 54, slug: 'dubasari', translationKey: 'locDubasari', name: 'Dubăsari', type: LocationType.Town, parentId: 1, sortOrder: 54, isCapital: false, specialType: null },
    { id: 55, slug: 'anenii-noi', translationKey: 'locAneniiNoi', name: 'Anenii Noi', type: LocationType.Town, parentId: 1, sortOrder: 55, isCapital: false, specialType: null },
    { id: 56, slug: 'ialoveni', translationKey: 'locIaloveni', name: 'Ialoveni', type: LocationType.Town, parentId: 1, sortOrder: 56, isCapital: false, specialType: null },
    { id: 57, slug: 'hincesti', translationKey: 'locHincesti', name: 'Hîncești', type: LocationType.Town, parentId: 1, sortOrder: 57, isCapital: false, specialType: null },
    { id: 58, slug: 'leova', translationKey: 'locLeova', name: 'Leova', type: LocationType.Town, parentId: 1, sortOrder: 58, isCapital: false, specialType: null },
    { id: 59, slug: 'cantemir', translationKey: 'locCantemir', name: 'Cantemir', type: LocationType.Town, parentId: 1, sortOrder: 59, isCapital: false, specialType: null },
    { id: 60, slug: 'causeni', translationKey: 'locCauseni', name: 'Căușeni', type: LocationType.Town, parentId: 1, sortOrder: 60, isCapital: false, specialType: null },
    { id: 61, slug: 'stefan-voda', translationKey: 'locStefanVoda', name: 'Ștefan Vodă', type: LocationType.Town, parentId: 1, sortOrder: 61, isCapital: false, specialType: null },
    { id: 62, slug: 'slobozia', translationKey: 'locSlobozia', name: 'Slobozia', type: LocationType.Town, parentId: 1, sortOrder: 62, isCapital: false, specialType: null },
    { id: 63, slug: 'vulcanesti', translationKey: 'locVulcanesti', name: 'Vulcănești', type: LocationType.Town, parentId: 1, sortOrder: 63, isCapital: false, specialType: null },
    { id: 64, slug: 'taraclia', translationKey: 'locTaraclia', name: 'Taraclia', type: LocationType.Town, parentId: 1, sortOrder: 64, isCapital: false, specialType: null },
    { id: 65, slug: 'ceadir-lunga', translationKey: 'locCeadirLunga', name: 'Ceadîr-Lunga', type: LocationType.Town, parentId: 1, sortOrder: 65, isCapital: false, specialType: null },
    { id: 66, slug: 'basarabeasca', translationKey: 'locBasarabeasca', name: 'Basarabeasca', type: LocationType.Town, parentId: 1, sortOrder: 66, isCapital: false, specialType: null },
    { id: 67, slug: 'cimislia', translationKey: 'locCimislia', name: 'Cimișlia', type: LocationType.Town, parentId: 1, sortOrder: 67, isCapital: false, specialType: null },
    { id: 68, slug: 'ocnita', translationKey: 'locOcnita', name: 'Ocnița', type: LocationType.Town, parentId: 1, sortOrder: 68, isCapital: false, specialType: null },
    { id: 69, slug: 'briceni', translationKey: 'locBriceni', name: 'Briceni', type: LocationType.Town, parentId: 1, sortOrder: 69, isCapital: false, specialType: null },
    { id: 70, slug: 'lipcani', translationKey: 'locLipcani', name: 'Lipcani', type: LocationType.Town, parentId: 1, sortOrder: 70, isCapital: false, specialType: null },
  ];

  // Create locations with explicit IDs
  for (const location of locationData) {
    const existingLocation = await prisma.location.findUnique({
      where: { Id: location.id }
    });

    if (!existingLocation) {
      await prisma.location.create({
        data: {
          Id: location.id,
          Slug: location.slug,
          TranslationKey: location.translationKey,
          Name: location.name,
          Type: location.type,
          ParentId: location.parentId,
          SortOrder: location.sortOrder,
          IsCapital: location.isCapital,
          SpecialType: location.specialType,
        }
      });
      console.log(`Created location: ${location.name}`);
    } else {
      console.log(`Location already exists: ${location.name}`);
    }
  }

  console.log("Locations seeding completed.");

  // --- 4. Seed Initial Admin User ---
  const adminEmail = process.env.INITIAL_ADMIN_EMAIL;
  let adminPasswordEnv = process.env.INITIAL_ADMIN_PASSWORD;
  let adminPasswordToUse = '';
  let mustChangePassword = false;

  if (!adminEmail) {
    console.warn("INITIAL_ADMIN_EMAIL not set in .env. Skipping admin user creation.");
  } else {
    if (!adminPasswordEnv) {
      adminPasswordToUse = generateRandomPassword(16);
      mustChangePassword = true;
      console.log(`INITIAL_ADMIN_PASSWORD not set. Generated a random password for admin ${adminEmail}: ${adminPasswordToUse}`);
      console.log("IMPORTANT: You will need to use this password for the first login and then change it if the 'force password reset' feature is implemented.");
    } else {
      adminPasswordToUse = adminPasswordEnv;
    }

    const hashedPassword = bcrypt.hashSync(adminPasswordToUse, 10);

    const adminUser = await prisma.user.upsert({
      where: { Email: adminEmail },
      update: {
        UserRoles: {
          deleteMany: {}, // Remove existing roles
          create: [
            { RoleId: adminRole.Id },
            { RoleId: clientRole.Id }
          ]
        }
      },
      create: {
        Email: adminEmail,
        FullName: "Administrator Bonami",
        Password: hashedPassword,
        MustChangePassword: mustChangePassword,
        UserRoles: {
          create: [
            { RoleId: adminRole.Id },
            { RoleId: clientRole.Id }
          ]
        },
        AvatarUrl: "https://placehold.co/100x100.png?text=ADMIN",
      },
    });
    console.log(`Upserted admin user: ${adminUser.Email}`);
  }

  console.log("Seeding finished.");
}

main()
  .catch(async (e) => {
    console.error("Error during seeding:", e);
    await prisma.$disconnect();
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

    

    