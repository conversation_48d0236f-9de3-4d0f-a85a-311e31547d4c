"use client";

import { useEffect, useRef } from 'react';
import { useRoleToggleAnalytics } from '@/lib/analytics/role-toggle-analytics';

// Performance monitoring for role toggle functionality
export class RoleToggleMonitoring {
  private performanceObserver: PerformanceObserver | null = null;
  private errorCount = 0;
  private successCount = 0;
  private startTime = Date.now();

  constructor() {
    this.initializeMonitoring();
  }

  private initializeMonitoring() {
    if (typeof window === 'undefined') return;

    // Monitor performance metrics
    this.setupPerformanceMonitoring();
    
    // Monitor JavaScript errors
    this.setupErrorMonitoring();
    
    // Monitor Core Web Vitals
    this.setupWebVitalsMonitoring();
    
    // Monitor user interactions
    this.setupInteractionMonitoring();
  }

  private setupPerformanceMonitoring() {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes('dashboard') || entry.name.includes('role')) {
            this.trackPerformanceMetric({
              name: entry.name,
              duration: entry.duration,
              startTime: entry.startTime,
              type: entry.entryType,
            });
          }
        });
      });

      this.performanceObserver.observe({ 
        entryTypes: ['navigation', 'measure', 'paint'] 
      });
    }
  }

  private setupErrorMonitoring() {
    window.addEventListener('error', (event) => {
      this.errorCount++;
      this.trackError({
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        type: 'javascript_error',
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.errorCount++;
      this.trackError({
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        type: 'promise_rejection',
      });
    });
  }

  private setupWebVitalsMonitoring() {
    // Monitor Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.trackWebVital('LCP', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // Monitor First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.trackWebVital('FID', (entry as any).processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Monitor Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        });
        this.trackWebVital('CLS', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  private setupInteractionMonitoring() {
    // Monitor role toggle interactions
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (target.closest('[role="tablist"]') || target.closest('[data-role-toggle]')) {
        this.trackInteraction('role_toggle_click', {
          element: target.tagName,
          timestamp: Date.now(),
        });
      }
    });

    // Monitor navigation interactions
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const navLink = target.closest('nav a');
      if (navLink) {
        this.trackInteraction('navigation_click', {
          href: (navLink as HTMLAnchorElement).href,
          text: navLink.textContent,
          timestamp: Date.now(),
        });
      }
    });
  }

  private trackPerformanceMetric(metric: {
    name: string;
    duration: number;
    startTime: number;
    type: string;
  }) {
    // Send to monitoring service
    this.sendToMonitoringService('performance_metric', metric);
  }

  private trackError(error: {
    message: string;
    filename?: string;
    lineno?: number;
    colno?: number;
    stack?: string;
    type: string;
  }) {
    // Send to error tracking service
    this.sendToMonitoringService('error', error);
    
    // Check if error rate is too high
    const errorRate = this.errorCount / (this.errorCount + this.successCount);
    if (errorRate > 0.01) { // 1% error rate threshold
      this.triggerAlert('high_error_rate', {
        errorRate,
        errorCount: this.errorCount,
        successCount: this.successCount,
      });
    }
  }

  private trackWebVital(name: string, value: number) {
    const thresholds = {
      LCP: 2500, // 2.5 seconds
      FID: 100,  // 100 milliseconds
      CLS: 0.1,  // 0.1 cumulative score
    };

    const isGood = value <= thresholds[name as keyof typeof thresholds];
    
    this.sendToMonitoringService('web_vital', {
      name,
      value,
      isGood,
      timestamp: Date.now(),
    });

    // Alert if threshold exceeded
    if (!isGood) {
      this.triggerAlert('web_vital_threshold_exceeded', {
        metric: name,
        value,
        threshold: thresholds[name as keyof typeof thresholds],
      });
    }
  }

  private trackInteraction(type: string, data: any) {
    this.successCount++;
    this.sendToMonitoringService('interaction', {
      type,
      ...data,
    });
  }

  private async sendToMonitoringService(eventType: string, data: any) {
    try {
      await fetch('/api/monitoring/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType,
          data,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        }),
      });
    } catch (error) {
      console.debug('Monitoring service not available:', error);
    }
  }

  private triggerAlert(alertType: string, data: any) {
    // Send alert to monitoring service
    this.sendToMonitoringService('alert', {
      alertType,
      severity: this.getAlertSeverity(alertType),
      data,
    });
  }

  private getAlertSeverity(alertType: string): 'low' | 'medium' | 'high' | 'critical' {
    const severityMap: Record<string, 'low' | 'medium' | 'high' | 'critical'> = {
      high_error_rate: 'critical',
      web_vital_threshold_exceeded: 'medium',
      role_toggle_failure: 'high',
      navigation_error: 'medium',
    };

    return severityMap[alertType] || 'low';
  }

  // Public methods for manual tracking
  trackRoleToggleSuccess(duration: number) {
    this.successCount++;
    this.sendToMonitoringService('role_toggle_success', {
      duration,
      timestamp: Date.now(),
    });
  }

  trackRoleToggleFailure(error: string) {
    this.errorCount++;
    this.sendToMonitoringService('role_toggle_failure', {
      error,
      timestamp: Date.now(),
    });
  }

  // Get current monitoring stats
  getStats() {
    return {
      errorCount: this.errorCount,
      successCount: this.successCount,
      errorRate: this.errorCount / (this.errorCount + this.successCount),
      uptime: Date.now() - this.startTime,
    };
  }

  // Cleanup
  destroy() {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }
}

// Global monitoring instance
export const roleToggleMonitoring = new RoleToggleMonitoring();

// React hook for monitoring
export function useRoleToggleMonitoring() {
  const monitoringRef = useRef(roleToggleMonitoring);
  const { trackError } = useRoleToggleAnalytics();

  useEffect(() => {
    const monitoring = monitoringRef.current;
    
    // Track page load performance
    if (typeof window !== 'undefined' && window.performance) {
      const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
      monitoring.trackRoleToggleSuccess(loadTime);
    }

    return () => {
      // Cleanup if needed
    };
  }, []);

  return {
    trackSuccess: (duration: number) => monitoringRef.current.trackRoleToggleSuccess(duration),
    trackFailure: (error: string) => {
      monitoringRef.current.trackRoleToggleFailure(error);
      trackError({
        error_type: 'monitoring_failure',
        error_message: error,
        current_role: 'client', // Default, should be passed from context
        target_role: 'provider', // Default, should be passed from context
      });
    },
    getStats: () => monitoringRef.current.getStats(),
  };
}
