"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Calendar, 
  Clock, 
  User, 
  MapPin, 
  MessageSquare, 
  CheckCircle, 
  XCircle, 
  Loader2,
  AlertTriangle,
  Eye
} from "lucide-react";
import { format } from "date-fns";
import { useLanguage } from '@/contexts/language-context';

interface PendingBooking {
  id: number;
  clientId: number;
  advertisedServiceId: number;
  eventStartDateTime: string;
  eventEndDateTime?: string;
  status: string;
  clientNotes?: string;
  createdAt: string;
  client: {
    fullName: string;
    email: string;
    avatarUrl?: string;
  };
  service: {
    serviceName: string;
    category?: {
      nameKey: string;
    };
  };
}

interface ProviderBookingManagementProps {
  className?: string;
}

const providerBookingTranslations = {
  pendingBookings: { ro: "Rezervări în Așteptare", ru: "Ожидающие бронирования", en: "Pending Bookings" },
  noPendingBookings: { ro: "Nu ai rezervări în așteptare", ru: "У вас нет ожидающих бронирований", en: "No pending bookings" },
  viewDetails: { ro: "Vezi Detalii", ru: "Посмотреть детали", en: "View Details" },
  approve: { ro: "Aprobă", ru: "Одобрить", en: "Approve" },
  reject: { ro: "Respinge", ru: "Отклонить", en: "Reject" },
  bookingDetails: { ro: "Detalii Rezervare", ru: "Детали бронирования", en: "Booking Details" },
  client: { ro: "Client", ru: "Клиент", en: "Client" },
  service: { ro: "Serviciu", ru: "Услуга", en: "Service" },
  dateTime: { ro: "Data și Ora", ru: "Дата и время", en: "Date & Time" },
  duration: { ro: "Durată", ru: "Продолжительность", en: "Duration" },
  clientNotes: { ro: "Notițe Client", ru: "Заметки клиента", en: "Client Notes" },
  providerResponse: { ro: "Răspunsul Tău", ru: "Ваш ответ", en: "Your Response" },
  responseNotesPlaceholder: { ro: "Adaugă un mesaj pentru client (opțional)...", ru: "Добавить сообщение для клиента (необязательно)...", en: "Add a message for the client (optional)..." },
  confirmApproval: { ro: "Confirmă Aprobarea", ru: "Подтвердить одобрение", en: "Confirm Approval" },
  confirmRejection: { ro: "Confirmă Respingerea", ru: "Подтвердить отклонение", en: "Confirm Rejection" },
  approvalSuccess: { ro: "Rezervare Aprobată", ru: "Бронирование одобрено", en: "Booking Approved" },
  rejectionSuccess: { ro: "Rezervare Respinsă", ru: "Бронирование отклонено", en: "Booking Rejected" },
  error: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  loading: { ro: "Se încarcă...", ru: "Загрузка...", en: "Loading..." },
  requestedOn: { ro: "Cerută pe", ru: "Запрошено", en: "Requested on" },
  noNotes: { ro: "Fără notițe", ru: "Без заметок", en: "No notes" },
  minutes: { ro: "minute", ru: "минут", en: "minutes" },
  close: { ro: "Închide", ru: "Закрыть", en: "Close" },
};

export function ProviderBookingManagement({ className }: ProviderBookingManagementProps) {
  const { translate } = useLanguage();
  const { data: session } = useSession();
  
  const [pendingBookings, setPendingBookings] = useState<PendingBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<PendingBooking | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [responseNotes, setResponseNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(null);

  // Load pending bookings
  useEffect(() => {
    const loadPendingBookings = async () => {
      if (!session?.user) return;
      
      setIsLoading(true);
      try {
        const providerId = (session.user as any).id;
        const response = await fetch(`/api/proxy/bookings/provider/${providerId}/pending`);
        
        if (response.ok) {
          const data = await response.json();
          setPendingBookings(data.bookings || []);
        } else {
          setError('Eroare la încărcarea rezervărilor în așteptare.');
        }
      } catch (error) {
        console.error('Error loading pending bookings:', error);
        setError('Eroare la încărcarea rezervărilor în așteptare.');
      } finally {
        setIsLoading(false);
      }
    };

    loadPendingBookings();
  }, [session]);

  const handleBookingAction = async (booking: PendingBooking, action: 'approve' | 'reject') => {
    setSelectedBooking(booking);
    setActionType(action);
    setResponseNotes("");
    setIsDialogOpen(true);
  };

  const confirmBookingAction = async () => {
    if (!selectedBooking || !actionType) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const endpoint = actionType === 'approve' ? 'approve' : 'reject';
      const response = await fetch(`/api/proxy/bookings/${selectedBooking.id}/${endpoint}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          providerNotes: responseNotes,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Remove the booking from pending list
        setPendingBookings(prev => prev.filter(b => b.id !== selectedBooking.id));
        setIsDialogOpen(false);
        setSelectedBooking(null);
        setActionType(null);
        setResponseNotes("");
      } else {
        setError(data.message || `Eroare la ${actionType === 'approve' ? 'aprobarea' : 'respingerea'} rezervării.`);
      }
    } catch (error) {
      console.error('Booking action error:', error);
      setError(`Eroare la ${actionType === 'approve' ? 'aprobarea' : 'respingerea'} rezervării.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDateTime = (dateTimeStr: string) => {
    const date = new Date(dateTimeStr);
    return format(date, "PPP 'la' HH:mm");
  };

  const calculateDuration = (start: string, end?: string) => {
    if (!end) return null;
    const startDate = new Date(start);
    const endDate = new Date(end);
    const diffMinutes = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60));
    return diffMinutes;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>{translate(providerBookingTranslations, 'loading')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {translate(providerBookingTranslations, 'pendingBookings')}
          </CardTitle>
          <CardDescription>
            Rezervări care necesită răspunsul tău
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>{translate(providerBookingTranslations, 'error')}</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {pendingBookings.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {translate(providerBookingTranslations, 'noPendingBookings')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingBookings.map((booking) => (
                <Card key={booking.id} className="border-l-4 border-l-yellow-500">
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="font-medium">{booking.client.fullName}</span>
                          <Badge variant="secondary">Nou</Badge>
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDateTime(booking.eventStartDateTime)}</span>
                          {booking.eventEndDateTime && (
                            <>
                              <span>•</span>
                              <Clock className="w-3 h-3" />
                              <span>{calculateDuration(booking.eventStartDateTime, booking.eventEndDateTime)} {translate(providerBookingTranslations, 'minutes')}</span>
                            </>
                          )}
                        </div>

                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-medium">{booking.service.serviceName}</span>
                          {booking.service.category && (
                            <Badge variant="outline" className="text-xs">
                              {booking.service.category.nameKey}
                            </Badge>
                          )}
                        </div>

                        {booking.clientNotes && (
                          <div className="flex items-start gap-2 text-sm">
                            <MessageSquare className="w-3 h-3 mt-0.5 text-muted-foreground" />
                            <span className="text-muted-foreground italic">
                              "{booking.clientNotes}"
                            </span>
                          </div>
                        )}

                        <div className="text-xs text-muted-foreground">
                          {translate(providerBookingTranslations, 'requestedOn')} {format(new Date(booking.createdAt), "PPP")}
                        </div>
                      </div>

                      <div className="flex flex-col gap-2 ml-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleBookingAction(booking, 'approve')}
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {translate(providerBookingTranslations, 'approve')}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleBookingAction(booking, 'reject')}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <XCircle className="w-3 h-3 mr-1" />
                          {translate(providerBookingTranslations, 'reject')}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Booking Action Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {actionType === 'approve' 
                ? translate(providerBookingTranslations, 'confirmApproval')
                : translate(providerBookingTranslations, 'confirmRejection')
              }
            </DialogTitle>
            <DialogDescription>
              {selectedBooking && (
                <div className="space-y-2 mt-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{translate(providerBookingTranslations, 'client')}:</span>
                    <span className="font-medium">{selectedBooking.client.fullName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{translate(providerBookingTranslations, 'service')}:</span>
                    <span className="font-medium">{selectedBooking.service.serviceName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{translate(providerBookingTranslations, 'dateTime')}:</span>
                    <span className="font-medium">{formatDateTime(selectedBooking.eventStartDateTime)}</span>
                  </div>
                  {selectedBooking.clientNotes && (
                    <div className="pt-2 border-t">
                      <span className="text-muted-foreground text-sm">{translate(providerBookingTranslations, 'clientNotes')}:</span>
                      <p className="text-sm italic mt-1">"{selectedBooking.clientNotes}"</p>
                    </div>
                  )}
                </div>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{translate(providerBookingTranslations, 'providerResponse')}</Label>
              <Textarea
                placeholder={translate(providerBookingTranslations, 'responseNotesPlaceholder')}
                value={responseNotes}
                onChange={(e) => setResponseNotes(e.target.value)}
                rows={3}
              />
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                {translate(providerBookingTranslations, 'close')}
              </Button>
              <Button
                onClick={confirmBookingAction}
                disabled={isSubmitting}
                variant={actionType === 'approve' ? 'default' : 'destructive'}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Se procesează...
                  </>
                ) : (
                  actionType === 'approve' 
                    ? translate(providerBookingTranslations, 'approve')
                    : translate(providerBookingTranslations, 'reject')
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
