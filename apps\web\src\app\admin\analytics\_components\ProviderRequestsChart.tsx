"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Loader2, UserCheck } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const chartTranslations = {
  providerRequests: { ro: "Cereri prestatori", ru: "Запросы поставщиков", en: "Provider Requests" },
  requestsTrend: { ro: "Tendința cererilor", ru: "Тенденция запросов", en: "Requests Trend" },
  pending: { ro: "În așteptare", ru: "В ожидании", en: "Pending" },
  approved: { ro: "Aprobate", ru: "Одобренные", en: "Approved" },
  rejected: { ro: "Respinse", ru: "Отклоненные", en: "Rejected" },
  total: { ro: "Total", ru: "Всего", en: "Total" },
  date: { ro: "Data", ru: "Дата", en: "Date" },
  loadingChart: { ro: "Se încarcă graficul...", ru: "Загрузка графика...", en: "Loading chart..." },
  errorLoadingChart: { ro: "Eroare la încărcarea graficului", ru: "Ошибка загрузки графика", en: "Error loading chart" },
  noDataAvailable: { ro: "Nu sunt date disponibile", ru: "Нет доступных данных", en: "No data available" },
};

interface ProviderRequestData {
  date: string;
  pending: number;
  approved: number;
  rejected: number;
  total: number;
}

interface ProviderRequestsChartProps {
  period: '7d' | '30d' | '90d' | '1y';
  refreshTrigger: Date;
}

export function ProviderRequestsChart({ period, refreshTrigger }: ProviderRequestsChartProps) {
  const { translate } = useLanguage();
  const [data, setData] = useState<ProviderRequestData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/proxy/admin/analytics/provider-requests?period=${period}`);
        if (!response.ok) {
          let errorMessage = 'Failed to fetch provider requests data';
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch {
            // If JSON parsing fails, use default message
          }
          throw new Error(errorMessage);
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error fetching provider requests data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [period, refreshTrigger]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ro-RO', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{`${translate(chartTranslations, 'date')}: ${formatDate(label)}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="w-5 h-5" />
            {translate(chartTranslations, 'providerRequests')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-5 h-5 animate-spin" />
              {translate(chartTranslations, 'loadingChart')}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="w-5 h-5" />
            {translate(chartTranslations, 'providerRequests')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'errorLoadingChart')}</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="w-5 h-5" />
            {translate(chartTranslations, 'providerRequests')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-muted-foreground">
              <p>{translate(chartTranslations, 'noDataAvailable')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate totals for summary
  const totalPending = data.reduce((sum, item) => sum + item.pending, 0);
  const totalApproved = data.reduce((sum, item) => sum + item.approved, 0);
  const totalRejected = data.reduce((sum, item) => sum + item.rejected, 0);
  const totalRequests = data.reduce((sum, item) => sum + item.total, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheck className="w-5 h-5" />
          {translate(chartTranslations, 'providerRequests')}
        </CardTitle>
        <div className="flex gap-4 text-sm text-muted-foreground">
          <span>{translate(chartTranslations, 'pending')}: {totalPending}</span>
          <span>{translate(chartTranslations, 'approved')}: {totalApproved}</span>
          <span>{translate(chartTranslations, 'rejected')}: {totalRejected}</span>
          <span>{translate(chartTranslations, 'total')}: {totalRequests}</span>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              tickFormatter={formatDate}
              className="text-xs"
            />
            <YAxis className="text-xs" />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Area
              type="monotone"
              dataKey="approved"
              stackId="1"
              stroke="#10b981"
              fill="#10b981"
              fillOpacity={0.6}
              name={translate(chartTranslations, 'approved')}
            />
            <Area
              type="monotone"
              dataKey="pending"
              stackId="1"
              stroke="#f59e0b"
              fill="#f59e0b"
              fillOpacity={0.6}
              name={translate(chartTranslations, 'pending')}
            />
            <Area
              type="monotone"
              dataKey="rejected"
              stackId="1"
              stroke="#ef4444"
              fill="#ef4444"
              fillOpacity={0.6}
              name={translate(chartTranslations, 'rejected')}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
