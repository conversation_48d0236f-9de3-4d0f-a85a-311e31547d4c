
import { Router, Response } from 'express';
import prisma from '../lib/db';
import { AuthenticatedRequest } from '../middleware/auth'; // Import the custom request type

const router = Router();

// GET /notifications
router.get('/', async (req: AuthenticatedRequest, res: Response) => {
  try {
    // The user object is attached by the 'authenticate' middleware
    if (!req.user || !req.user.id) {
      // This is expected for unauthenticated users. The frontend should handle this.
      return res.status(401).json({ notifications: [], unreadCount: 0, message: 'Unauthorized: User not logged in.' });
    }

    const userId = parseInt(req.user.id, 10);
    if (isNaN(userId)) {
      console.error(`[API /notifications GET] Invalid user ID in token: ${req.user.id}`);
      return res.status(400).json({ notifications: [], unreadCount: 0, message: 'Invalid user ID format.' });
    }

    console.log(`[API /notifications GET] Fetching notifications for user ID: ${userId}`);

    const notificationsFromDb = await prisma.notification.findMany({
      where: { UserId: userId },
      orderBy: { CreatedAt: 'desc' },
      take: 20,
    });

    const unreadCount = await prisma.notification.count({
      where: { UserId: userId, IsRead: false },
    });

    // API should return camelCase for consistency
    const formattedNotifications = notificationsFromDb.map(n => ({
      id: n.Id,
      message: n.Message,
      link: n.Link,
      isRead: n.IsRead,
      createdAt: n.CreatedAt.toISOString(),
      type: n.Type
    }));

    return res.json({ notifications: formattedNotifications, unreadCount });

  } catch (error) {
    console.error('[API /notifications GET] Error:', error);
    return res.status(500).json({
      notifications: [],
      unreadCount: 0,
      message: error instanceof Error ? error.message : 'Failed to fetch notifications'
    });
  }
});

// POST /notifications/mark-as-read
router.post('/mark-as-read', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      console.log(`[API /notifications/mark-as-read POST] Unauthorized attempt. No user in request.`);
      return res.status(401).json({ success: false, message: 'Unauthorized: User not logged in.' });
    }

    const userId = parseInt(req.user.id, 10);
    if (isNaN(userId)) {
      return res.status(400).json({ success: false, message: 'Invalid user ID format.' });
    }

    const { notificationId, markAll } = req.body;

    if (markAll) {
      await prisma.notification.updateMany({
        where: { UserId: userId, IsRead: false },
        data: { IsRead: true },
      });
      console.log(`[API] Marked all notifications as read for user ${userId}`);
      return res.json({ success: true, message: 'Toate notificările au fost marcate ca citite.' });
    }

    if (notificationId != null) {
      const idToMark = Number(notificationId);
      if (isNaN(idToMark)) {
        return res.status(400).json({ success: false, message: 'ID notificare invalid.' });
      }

      await prisma.notification.updateMany({
        where: { Id: idToMark, UserId: userId }, // Ensure user can only mark their own notifications
        data: { IsRead: true },
      });

      console.log(`[API] Marked notification ${idToMark} as read for user ${userId}`);
      return res.json({ success: true, message: 'Notificare marcată ca citită.' });
    }

    return res.status(400).json({ success: false, message: 'ID notificare sau "markAll" este necesar.' });

  } catch (error) {
    console.error('[API /notifications/mark-as-read POST] Error:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Eroare la marcarea notificărilor ca citite.'
    });
  }
});

export default router;
