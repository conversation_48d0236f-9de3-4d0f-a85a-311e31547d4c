
"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, PlusCircle, Edit, Trash2, MapPin, AlertTriangle } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { AddressService, LocationService, type LocationEntry } from '@repo/services';
import type { Address, AddressPayload } from '@repo/types';
import { LocationCombobox } from '@/components/ui/location-combobox';

const addressPageTranslations = {
  pageTitle: { ro: "Adresele Mele", ru: "Мои адреса", en: "My Addresses" },
  pageDescription: { ro: "Gestionează adresele tale de livrare sau de prestare a serviciilor.", ru: "Управляйте своими адресами доставки или оказания услуг.", en: "Manage your delivery or service addresses." },
  addNewAddress: { ro: "Adaugă Adresă Nouă", ru: "Добавить новый адрес", en: "Add New Address" },
  noAddresses: { ro: "Nu ai adăugat nicio adresă încă.", ru: "Вы еще не добавили ни одного адреса.", en: "You haven't added any addresses yet." },
  editAddressTitle: { ro: "Editează Adresa", ru: "Редактировать адрес", en: "Edit Address" },
  addAddressTitle: { ro: "Adaugă o Adresă Nouă", ru: "Добавить новый адрес", en: "Add a New Address" },
  labelLabel: { ro: "Etichetă (ex: Acasă, Birou)", ru: "Метка (напр. Дом, Офис)", en: "Label (e.g. Home, Office)" },
  streetLabel: { ro: "Stradă și număr", ru: "Улица и номер", en: "Street and number" },
  cityLabel: { ro: "Oraș/Localitate", ru: "Город/Населенный пункт", en: "City/Town" },
  regionLabel: { ro: "Raion/Regiune (opțional)", ru: "Район/Регион (необязательно)", en: "Region (optional)" },
  postalCodeLabel: { ro: "Cod Poștal (opțional)", ru: "Почтовый индекс (необязательно)", en: "Postal Code (optional)" },
  saveButton: { ro: "Salvează", ru: "Сохранить", en: "Save" },
  savingButton: { ro: "Se salvează...", ru: "Сохранение...", en: "Saving..." },
  deleteConfirmTitle: { ro: "Ești sigur că vrei să ștergi această adresă?", ru: "Вы уверены, что хотите удалить этот адрес?", en: "Are you sure you want to delete this address?" },
  deleteConfirmDescription: { ro: "Această acțiune este ireversibilă.", ru: "Это действие необратимо.", en: "This action is irreversible." },
};

export default function AddressSettingsPage() {
  const { translate, currentLanguage } = useLanguage();
  const { toast } = useToast();

  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<AddressPayload | null>(null);
  const [editingAddressId, setEditingAddressId] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Location state
  const [locations, setLocations] = useState<LocationEntry[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(false);

  const fetchAddresses = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await AddressService.getAddresses();
      setAddresses(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "A apărut o eroare la preluarea adreselor.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAddresses();
  }, [fetchAddresses]);

  // Load locations
  useEffect(() => {
    const loadLocations = async () => {
      setLocationsLoading(true);
      try {
        const locationData = await LocationService.getLocationsForForms(currentLanguage.code);
        setLocations(locationData);
      } catch (error) {
        console.error('Error loading locations:', error);
      } finally {
        setLocationsLoading(false);
      }
    };

    loadLocations();
  }, [currentLanguage.code]);

  const openFormForNew = () => {
    setCurrentAddress({
      label: '',
      street: '',
      city: '',
      region: '',
      postalCode: '',
      isDefault: false,
      countryId: null,
      regionId: null,
      cityId: null,
      sectorId: null
    });
    setEditingAddressId(null);
    setIsFormOpen(true);
  };

  const openFormForEdit = (address: Address) => {
    setCurrentAddress({
      label: address.label,
      street: address.street,
      city: address.city,
      region: address.region,
      postalCode: address.postalCode,
      isDefault: address.isDefault,
    });
    setEditingAddressId(address.id);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async () => {
    if (!currentAddress) return;
    setIsLoading(true);
    try {
      if (editingAddressId) {
        await AddressService.updateAddress(editingAddressId, currentAddress);
        toast({ title: "Succes", description: "Adresa a fost actualizată." });
      } else {
        await AddressService.createAddress(currentAddress);
        toast({ title: "Succes", description: "Adresa a fost adăugată." });
      }
      setIsFormOpen(false);
      fetchAddresses();
    } catch (err) {
      toast({ variant: "destructive", title: "Eroare", description: err instanceof Error ? err.message : "A apărut o eroare." });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAddress = async (id: number) => {
    try {
      await AddressService.deleteAddress(id);
      toast({ title: "Succes", description: "Adresa a fost ștearsă." });
      fetchAddresses();
    } catch (err) {
      toast({ variant: "destructive", title: "Eroare", description: err instanceof Error ? err.message : "A apărut o eroare." });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentAddress(prev => prev ? { ...prev, [name]: value } : null);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-xl font-headline flex items-center">
              <MapPin className="w-6 h-6 mr-2 text-primary" />
              {translate(addressPageTranslations, 'pageTitle')}
            </CardTitle>
            <CardDescription>{translate(addressPageTranslations, 'pageDescription')}</CardDescription>
          </div>
          <Button onClick={openFormForNew}>
            <PlusCircle className="w-4 h-4 mr-2" />
            {translate(addressPageTranslations, 'addNewAddress')}
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="text-center text-destructive py-10">
              <AlertTriangle className="mx-auto h-8 w-8 mb-2" />
              <p>{error}</p>
            </div>
          ) : addresses.length === 0 ? (
            <div className="text-center text-muted-foreground py-12">
              <p>{translate(addressPageTranslations, 'noAddresses')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {addresses.map(address => (
                <Card key={address.id} className="p-4 bg-muted/50">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-semibold">{address.label}</p>
                      <p className="text-sm text-muted-foreground">{address.street}, {address.city}</p>
                      <p className="text-sm text-muted-foreground">{address.region}, {address.postalCode}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon" onClick={() => openFormForEdit(address)}>
                        <Edit className="h-4 h-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="icon">
                            <Trash2 className="h-4 h-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>{translate(addressPageTranslations, 'deleteConfirmTitle')}</AlertDialogTitle>
                            <AlertDialogDescription>{translate(addressPageTranslations, 'deleteConfirmDescription')}</AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>{translate(commonTranslations, 'cancelButton')}</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteAddress(address.id)}>Șterge</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingAddressId ? translate(addressPageTranslations, 'editAddressTitle') : translate(addressPageTranslations, 'addAddressTitle')}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="label">{translate(addressPageTranslations, 'labelLabel')}</Label>
              <Input id="label" name="label" value={currentAddress?.label || ''} onChange={handleInputChange} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="street">{translate(addressPageTranslations, 'streetLabel')}</Label>
              <Input id="street" name="street" value={currentAddress?.street || ''} onChange={handleInputChange} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">{translate(commonTranslations, 'locationLabel')}</Label>
              <LocationCombobox
                locations={locations}
                value={currentAddress?.sectorId ? (locations.find(loc => loc.id === currentAddress.sectorId)?.slug || "") : ""}
                onValueChange={(val) => {
                  // Convert slug to location hierarchy and update all location fields
                  const location = locations.find(loc => loc.slug === val);
                  if (location) {
                    setCurrentAddress(prev => ({
                      ...prev,
                      sectorId: location.id,
                      // Also update backward compatibility fields
                      city: location.name,
                      region: location.type === 'Municipality' ? location.name : prev?.region
                    }));
                  } else {
                    setCurrentAddress(prev => ({
                      ...prev,
                      sectorId: null,
                      city: '',
                      region: ''
                    }));
                  }
                }}
                placeholder={locationsLoading ? "Loading locations..." : translate(commonTranslations, 'locationPlaceholder')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="postalCode">{translate(addressPageTranslations, 'postalCodeLabel')}</Label>
              <Input id="postalCode" name="postalCode" value={currentAddress?.postalCode || ''} onChange={handleInputChange} />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">{translate(commonTranslations, 'cancelButton')}</Button>
            </DialogClose>
            <Button onClick={handleFormSubmit} disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {translate(addressPageTranslations, 'saveButton')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
