import type { ServiceCategorySlug as PrismaServiceCategorySlug, NannyServiceDetails, ElderCareServiceDetails, CleaningServiceDetails, TutoringServiceDetails, CookingServiceDetails } from '@prisma/client';

export interface CaregiverSearchResult {
  id: number;
  name: string;
  imageUrl: string | null;
  serviceType: string;
  location: string;
  rating: number;
  reviewsCount: number;
  description: string | null;
  priceRate: string;
  serviceIdForLink: number;
}

export interface ServiceCategory {
  Id: number;
  NameKey: string;
  Slug: PrismaServiceCategorySlug;
  AiExpectedValue: string;
  DefaultImageHint: string | null;
}

export interface SearchApiResponse {
  caregivers: CaregiverSearchResult[];
  totalPages: number;
  totalItems: number;
}

export interface Address {
  id: number;
  userId: number;
  label: string;
  street: string;

  // Enhanced location architecture with foreign key relationships
  countryId?: number | null;
  regionId?: number | null;
  cityId?: number | null;
  sectorId?: number | null;

  // Backward compatibility fields (deprecated)
  city?: string | null;
  region?: string | null;
  country?: string | null;

  postalCode?: string | null;
  isDefault: boolean;
}

export type AddressPayload = Omit<Address, 'id' | 'userId'>;

export interface Location {
  id: number;
  slug: string;
  translationKey: string;
  name: string;
  displayName: string;
  type: string;
  parentId: number | null;
  sortOrder: number;
  isCapital: boolean;
  specialType: string | null;
}

export type LocationPayload = Omit<Location, 'id' | 'displayName'>;


// New Types for Service Management
// Base type for numeric fields that could be Decimal in Prisma, but number in frontend
type PrismaDecimal = {
  d: number[];
  e: number;
  s: number;
};

// Converts Prisma-specific details types to frontend-friendly payload types
// This replaces all `Decimal` types with `number`
type ToPayload<T> = {
  [K in keyof T]: T[K] extends PrismaDecimal | PrismaDecimal | null ? number | null : T[K];
};


export type NannyServiceDetailsPayload = Partial<ToPayload<Omit<NannyServiceDetails, 'Id' | 'AdvertisedServiceId'>>>;
export type ElderCareServiceDetailsPayload = Partial<ToPayload<Omit<ElderCareServiceDetails, 'Id' | 'AdvertisedServiceId'>>>;
export type CleaningServiceDetailsPayload = Partial<ToPayload<Omit<CleaningServiceDetails, 'Id' | 'AdvertisedServiceId'>>>;
export type TutoringServiceDetailsPayload = Partial<ToPayload<Omit<TutoringServiceDetails, 'Id' | 'AdvertisedServiceId'>>>;
export type CookingServiceDetailsPayload = Partial<ToPayload<Omit<CookingServiceDetails, 'Id' | 'AdvertisedServiceId'>>>;


export interface AdvertisedServicePayload {
    ProviderId: number;
    ServiceName: string;
    Description: string;
    ServiceCategorySlug: PrismaServiceCategorySlug;
    Status: 'Activ' | 'Inactiv' | 'PendingReview' | 'Rejected';
    NannyDetails?: NannyServiceDetailsPayload;
    ElderCareDetails?: ElderCareServiceDetailsPayload;
    CleaningDetails?: CleaningServiceDetailsPayload;
    TutoringDetails?: TutoringServiceDetailsPayload;
    CookingDetails?: CookingServiceDetailsPayload;
}
