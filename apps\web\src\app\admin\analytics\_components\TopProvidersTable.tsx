"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Loader2, TrendingUp, Star, ChevronLeft, ChevronRight } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';

const tableTranslations = {
  topProviders: { ro: "Top prestatori", ru: "Топ поставщики", en: "Top Providers" },
  mostActiveProviders: { ro: "Cei mai activi prestatori", ru: "Самые активные поставщики", en: "Most Active Providers" },
  providerName: { ro: "Nume prestator", ru: "Имя поставщика", en: "Provider Name" },
  email: { ro: "Email", ru: "Email", en: "Email" },
  bookings: { ro: "Rezervări", ru: "Бронирования", en: "Bookings" },
  activeServices: { ro: "Servicii active", ru: "Активные услуги", en: "Active Services" },
  rank: { ro: "Rang", ru: "Ранг", en: "Rank" },
  loadingTable: { ro: "Se încarcă tabelul...", ru: "Загрузка таблицы...", en: "Loading table..." },
  errorLoadingTable: { ro: "Eroare la încărcarea tabelului", ru: "Ошибка загрузки таблицы", en: "Error loading table" },
  noProvidersFound: { ro: "Nu s-au găsit prestatori", ru: "Поставщики не найдены", en: "No providers found" },
  showingResults: { ro: "Se afișează", ru: "Показано", en: "Showing" },
  of: { ro: "din", ru: "из", en: "of" },
  results: { ro: "rezultate", ru: "результатов", en: "results" },
  previous: { ro: "Anterior", ru: "Предыдущий", en: "Previous" },
  next: { ro: "Următorul", ru: "Следующий", en: "Next" },
};

interface TopProviderData {
  id: number;
  name: string;
  email: string;
  bookingsCount: number;
  activeServicesCount: number;
}

interface TopProvidersTableProps {
  refreshTrigger: Date;
}

export function TopProvidersTable({ refreshTrigger }: TopProvidersTableProps) {
  const { translate } = useLanguage();
  const [data, setData] = useState<TopProviderData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  // Calculate pagination
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = data.slice(startIndex, endIndex);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      setCurrentPage(1); // Reset to first page on refresh

      try {
        const response = await fetch('/api/proxy/admin/analytics/top-providers');
        if (!response.ok) {
          let errorMessage = 'Failed to fetch top providers data';
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch {
            // If JSON parsing fails, use default message
          }
          throw new Error(errorMessage);
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error fetching top providers data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger]);

  const getRankBadgeVariant = (rank: number) => {
    switch (rank) {
      case 1:
        return 'default'; // Gold
      case 2:
        return 'secondary'; // Silver
      case 3:
        return 'outline'; // Bronze
      default:
        return 'outline';
    }
  };

  const getRankIcon = (rank: number) => {
    if (rank <= 3) {
      return <Star className="w-3 h-3" />;
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            {translate(tableTranslations, 'topProviders')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-5 h-5 animate-spin" />
              {translate(tableTranslations, 'loadingTable')}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            {translate(tableTranslations, 'topProviders')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(tableTranslations, 'errorLoadingTable')}</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            {translate(tableTranslations, 'topProviders')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(tableTranslations, 'noProvidersFound')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          {translate(tableTranslations, 'topProviders')}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {translate(tableTranslations, 'mostActiveProviders')}
        </p>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">{translate(tableTranslations, 'rank')}</TableHead>
                <TableHead className="min-w-[150px]">{translate(tableTranslations, 'providerName')}</TableHead>
                <TableHead className="hidden md:table-cell min-w-[200px]">{translate(tableTranslations, 'email')}</TableHead>
                <TableHead className="text-center min-w-[100px]">{translate(tableTranslations, 'bookings')}</TableHead>
                <TableHead className="text-center min-w-[120px]">{translate(tableTranslations, 'activeServices')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((provider, index) => {
                const rank = startIndex + index + 1;
                return (
                  <TableRow key={provider.id}>
                    <TableCell>
                      <Badge
                        variant={getRankBadgeVariant(rank)}
                        className="flex items-center gap-1 w-fit"
                      >
                        {getRankIcon(rank)}
                        #{rank}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="truncate max-w-[150px]" title={provider.name || 'N/A'}>
                        {provider.name || 'N/A'}
                      </div>
                      <div className="md:hidden text-xs text-muted-foreground truncate max-w-[150px]" title={provider.email}>
                        {provider.email}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-muted-foreground">
                      <div className="truncate max-w-[200px]" title={provider.email}>
                        {provider.email}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline" className="font-mono">
                        {provider.bookingsCount}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="secondary" className="font-mono">
                        {provider.activeServicesCount}
                      </Badge>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-4 py-3 border-t">
            <div className="text-sm text-muted-foreground">
              {translate(tableTranslations, 'showingResults')} {startIndex + 1}-{Math.min(endIndex, data.length)} {translate(tableTranslations, 'of')} {data.length} {translate(tableTranslations, 'results')}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="flex items-center gap-1"
              >
                <ChevronLeft className="w-4 h-4" />
                <span className="hidden sm:inline">{translate(tableTranslations, 'previous')}</span>
              </Button>
              <span className="text-sm font-medium">
                {currentPage} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1"
              >
                <span className="hidden sm:inline">{translate(tableTranslations, 'next')}</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
