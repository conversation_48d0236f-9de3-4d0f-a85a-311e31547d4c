"use client";

import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface FilterSectionProps {
  title: string;
  children: React.ReactNode;
  isCollapsible?: boolean;
  defaultExpanded?: boolean;
}

export function FilterSection({ title, children, isCollapsible = false, defaultExpanded = true }: FilterSectionProps) {
  return (
    <div className="space-y-3 border-b border-border pb-4 last:border-b-0">
      <h4 className="font-medium text-sm text-foreground">{title}</h4>
      <div className="space-y-2">
        {children}
      </div>
    </div>
  );
}

interface BooleanFilterGroupProps {
  title: string;
  options: Array<{
    key: string;
    label: string;
    checked: boolean;
  }>;
  onChange: (key: string, checked: boolean) => void;
}

export function BooleanFilterGroup({ title, options, onChange }: BooleanFilterGroupProps) {
  const activeCount = options.filter(opt => opt.checked).length;
  
  return (
    <FilterSection title={`${title}${activeCount > 0 ? ` (${activeCount})` : ''}`}>
      <div className="grid grid-cols-1 gap-2">
        {options.map(option => (
          <div key={option.key} className="flex items-center space-x-2">
            <Checkbox
              id={`filter-${option.key}`}
              checked={option.checked}
              onCheckedChange={(checked) => onChange(option.key, !!checked)}
            />
            <Label 
              htmlFor={`filter-${option.key}`} 
              className="font-normal text-sm cursor-pointer flex-1"
            >
              {option.label}
            </Label>
          </div>
        ))}
      </div>
    </FilterSection>
  );
}

interface MultiSelectFilterProps {
  title: string;
  options: Array<{
    key: string;
    label: string;
    checked: boolean;
  }>;
  onChange: (key: string, checked: boolean) => void;
  maxColumns?: number;
}

export function MultiSelectFilter({ title, options, onChange, maxColumns = 2 }: MultiSelectFilterProps) {
  const activeCount = options.filter(opt => opt.checked).length;
  
  return (
    <FilterSection title={`${title}${activeCount > 0 ? ` (${activeCount})` : ''}`}>
      <div className={`grid grid-cols-1 ${maxColumns === 2 ? 'sm:grid-cols-2' : ''} gap-2`}>
        {options.map(option => (
          <div key={option.key} className="flex items-center space-x-2">
            <Checkbox
              id={`multi-${option.key}`}
              checked={option.checked}
              onCheckedChange={(checked) => onChange(option.key, !!checked)}
            />
            <Label 
              htmlFor={`multi-${option.key}`} 
              className="font-normal text-sm cursor-pointer flex-1"
            >
              {option.label}
            </Label>
          </div>
        ))}
      </div>
    </FilterSection>
  );
}

interface FilterBadgesProps {
  activeFilters: Array<{
    key: string;
    label: string;
    category: string;
  }>;
  onRemove: (key: string, category: string) => void;
  onClearAll: () => void;
}

export function FilterBadges({ activeFilters, onRemove, onClearAll }: FilterBadgesProps) {
  if (activeFilters.length === 0) return null;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Active Filters ({activeFilters.length})</Label>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onClearAll}
          className="h-6 px-2 text-xs"
        >
          Clear All
        </Button>
      </div>
      <div className="flex flex-wrap gap-1">
        {activeFilters.map(filter => (
          <Badge 
            key={`${filter.category}-${filter.key}`} 
            variant="secondary" 
            className="text-xs px-2 py-1 flex items-center gap-1"
          >
            {filter.label}
            <X 
              className="h-3 w-3 cursor-pointer hover:text-destructive" 
              onClick={() => onRemove(filter.key, filter.category)}
            />
          </Badge>
        ))}
      </div>
    </div>
  );
}

interface RangeFilterProps {
  title: string;
  value: [number, number];
  min: number;
  max: number;
  step: number;
  unit?: string;
  onChange: (value: [number, number]) => void;
}

export function RangeFilter({ title, value, min, max, step, unit = '', onChange }: RangeFilterProps) {
  return (
    <FilterSection title={title}>
      <div className="space-y-2">
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{value[0]}{unit}</span>
          <span>{value[1]}{unit}</span>
        </div>
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value[0]}
          onChange={(e) => onChange([Number(e.target.value), value[1]])}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        />
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value[1]}
          onChange={(e) => onChange([value[0], Number(e.target.value)])}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        />
      </div>
    </FilterSection>
  );
}

// Helper function to create filter options from boolean fields
export function createBooleanOptions(
  fields: Record<string, boolean>,
  labels: Record<string, string>,
  onChange: (key: string, checked: boolean) => void
) {
  return Object.keys(fields).map(key => ({
    key,
    label: labels[key] || key,
    checked: fields[key],
  }));
}

// Helper function to get active filters for badges
export function getActiveFilters(
  filters: Record<string, any>,
  labels: Record<string, string>,
  category: string
): Array<{ key: string; label: string; category: string }> {
  return Object.entries(filters)
    .filter(([_, value]) => value === true)
    .map(([key, _]) => ({
      key,
      label: labels[key] || key,
      category,
    }));
}
