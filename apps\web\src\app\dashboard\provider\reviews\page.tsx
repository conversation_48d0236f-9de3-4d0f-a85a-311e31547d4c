"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Star, User, Calendar, MessageSquare, Loader2, TrendingUp } from "lucide-react";
import { format } from "date-fns";
import { useLanguage } from '@/contexts/language-context';
import { getAvatarUrl } from '@/lib/avatar-utils';

interface Review {
  id: number;
  rating: number;
  comment?: string;
  createdAt: string;
  client: {
    fullName: string;
    avatarUrl?: string;
  };
  service: {
    serviceName: string;
  };
}

interface ReviewsData {
  reviews: Review[];
  averageRating: number;
  totalReviews: number;
}

const reviewsPageTranslations = {
  pageTitle: { ro: "Recenziile Mele", ru: "Мои отзывы", en: "My Reviews" },
  pageDescription: { ro: "Vezi și gestionează recenziile primite de la clienți", ru: "Просматривайте и управляйте отзывами от клиентов", en: "View and manage reviews from clients" },
  averageRating: { ro: "Rating Mediu", ru: "Средний рейтинг", en: "Average Rating" },
  totalReviews: { ro: "Total Recenzii", ru: "Всего отзывов", en: "Total Reviews" },
  noReviews: { ro: "Nu ai încă recenzii", ru: "У вас пока нет отзывов", en: "No reviews yet" },
  noReviewsDesc: { ro: "Recenziile vor apărea aici după ce clienții vor evalua serviciile tale", ru: "Отзывы появятся здесь после того, как клиенты оценят ваши услуги", en: "Reviews will appear here after clients rate your services" },
  reviewFrom: { ro: "Recenzie de la", ru: "Отзыв от", en: "Review from" },
  serviceProvided: { ro: "Serviciu furnizat", ru: "Предоставленная услуга", en: "Service provided" },
  loading: { ro: "Se încarcă recenziile...", ru: "Загрузка отзывов...", en: "Loading reviews..." },
  error: { ro: "Eroare la încărcarea recenziilor", ru: "Ошибка загрузки отзывов", en: "Error loading reviews" },
  excellent: { ro: "Excelent", ru: "Отлично", en: "Excellent" },
  good: { ro: "Bun", ru: "Хорошо", en: "Good" },
  average: { ro: "Mediu", ru: "Средне", en: "Average" },
  poor: { ro: "Slab", ru: "Плохо", en: "Poor" },
  terrible: { ro: "Groaznic", ru: "Ужасно", en: "Terrible" },
  stars: { ro: "stele", ru: "звезд", en: "stars" },
};

const getRatingLabel = (rating: number, translate: any) => {
  if (rating >= 4.5) return translate(reviewsPageTranslations, 'excellent');
  if (rating >= 3.5) return translate(reviewsPageTranslations, 'good');
  if (rating >= 2.5) return translate(reviewsPageTranslations, 'average');
  if (rating >= 1.5) return translate(reviewsPageTranslations, 'poor');
  return translate(reviewsPageTranslations, 'terrible');
};

export default function ProviderReviewsPage() {
  const { translate } = useLanguage();
  const { data: session } = useSession();
  
  const [reviewsData, setReviewsData] = useState<ReviewsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadReviews = async () => {
      if (!session?.user) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const providerId = (session.user as any).id;
        const response = await fetch(`/api/proxy/reviews/provider/${providerId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setReviewsData({
              reviews: data.reviews,
              averageRating: data.averageRating,
              totalReviews: data.totalReviews,
            });
          } else {
            setError(data.message || translate(reviewsPageTranslations, 'error'));
          }
        } else {
          setError(translate(reviewsPageTranslations, 'error'));
        }
      } catch (error) {
        console.error('Error loading reviews:', error);
        setError(translate(reviewsPageTranslations, 'error'));
      } finally {
        setIsLoading(false);
      }
    };

    loadReviews();
  }, [session, translate]);

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'w-3 h-3',
      md: 'w-4 h-4',
      lg: 'w-5 h-5'
    };
    
    return (
      <div className="flex items-center gap-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating 
                ? 'fill-yellow-400 text-yellow-400' 
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-headline">
              {translate(reviewsPageTranslations, 'pageTitle')}
            </CardTitle>
            <CardDescription>
              {translate(reviewsPageTranslations, 'pageDescription')}
            </CardDescription>
          </CardHeader>
        </Card>
        
        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="ml-3">{translate(reviewsPageTranslations, 'loading')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-headline">
              {translate(reviewsPageTranslations, 'pageTitle')}
            </CardTitle>
            <CardDescription>
              {translate(reviewsPageTranslations, 'pageDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-destructive">
              <p>{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-headline">
            {translate(reviewsPageTranslations, 'pageTitle')}
          </CardTitle>
          <CardDescription>
            {translate(reviewsPageTranslations, 'pageDescription')}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Statistics */}
      {reviewsData && reviewsData.totalReviews > 0 && (
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {translate(reviewsPageTranslations, 'averageRating')}
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className="text-2xl font-bold">
                  {reviewsData.averageRating.toFixed(1)}
                </div>
                {renderStars(reviewsData.averageRating, 'lg')}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {getRatingLabel(reviewsData.averageRating, translate)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {translate(reviewsPageTranslations, 'totalReviews')}
              </CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reviewsData.totalReviews}</div>
              <p className="text-xs text-muted-foreground">
                {translate(reviewsPageTranslations, 'stars')}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Reviews List */}
      <Card>
        <CardHeader>
          <CardTitle>Recenzii</CardTitle>
        </CardHeader>
        <CardContent>
          {!reviewsData || reviewsData.reviews.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {translate(reviewsPageTranslations, 'noReviews')}
              </h3>
              <p className="text-muted-foreground">
                {translate(reviewsPageTranslations, 'noReviewsDesc')}
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {reviewsData.reviews.map((review, index) => (
                <div key={review.id}>
                  <div className="flex space-x-4">
                    <Avatar className="w-10 h-10">
                      <AvatarImage 
                        src={getAvatarUrl(review.client.avatarUrl)} 
                        alt={review.client.fullName} 
                      />
                      <AvatarFallback>
                        <User className="w-5 h-5" />
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{review.client.fullName}</p>
                          <p className="text-sm text-muted-foreground">
                            {translate(reviewsPageTranslations, 'serviceProvided')}: {review.service.serviceName}
                          </p>
                        </div>
                        <div className="text-right">
                          {renderStars(review.rating)}
                          <p className="text-xs text-muted-foreground mt-1">
                            {format(new Date(review.createdAt), "PPP")}
                          </p>
                        </div>
                      </div>
                      
                      {review.comment && (
                        <div className="bg-muted/50 p-3 rounded-lg">
                          <p className="text-sm italic">"{review.comment}"</p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {index < reviewsData.reviews.length - 1 && (
                    <Separator className="mt-6" />
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
