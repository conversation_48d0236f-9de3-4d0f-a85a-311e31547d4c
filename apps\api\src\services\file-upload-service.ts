import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface FileUploadResult {
  success: boolean;
  filePath?: string;
  error?: string;
}

export interface MultipleFileUploadResult {
  success: boolean;
  filePaths?: string[];
  errors?: string[];
}

/**
 * File upload service for handling document uploads in provider registration
 */
export class FileUploadService {
  private static readonly UPLOAD_DIR = 'uploads/provider-documents';
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly ALLOWED_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];

  /**
   * Initialize upload directory
   */
  static async initializeUploadDir(): Promise<void> {
    try {
      await fs.mkdir(this.UPLOAD_DIR, { recursive: true });
    } catch (error) {
      console.error('Failed to create upload directory:', error);
      throw new Error('Failed to initialize file upload service');
    }
  }

  /**
   * Validate file before upload
   */
  static validateFile(file: Express.Multer.File): string | null {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return 'File is too large. Maximum size is 5MB.';
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.mimetype)) {
      return 'Invalid file type. Only PDF, JPG, and PNG files are allowed.';
    }

    return null;
  }

  /**
   * Upload a single file
   */
  static async uploadSingleFile(
    file: Express.Multer.File,
    userId: number,
    serviceType: string
  ): Promise<FileUploadResult> {
    try {
      // Validate file
      const validationError = this.validateFile(file);
      if (validationError) {
        return { success: false, error: validationError };
      }

      // Generate unique filename
      const fileExtension = path.extname(file.originalname);
      const uniqueFilename = `${userId}_${serviceType}_${uuidv4()}${fileExtension}`;
      const filePath = path.join(this.UPLOAD_DIR, uniqueFilename);

      // Ensure upload directory exists
      await this.initializeUploadDir();

      // Write file to disk
      await fs.writeFile(filePath, file.buffer);

      return {
        success: true,
        filePath: filePath
      };
    } catch (error) {
      console.error('File upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Upload multiple files
   */
  static async uploadMultipleFiles(
    files: Express.Multer.File[],
    userId: number,
    serviceType: string
  ): Promise<MultipleFileUploadResult> {
    try {
      const results = await Promise.all(
        files.map(file => this.uploadSingleFile(file, userId, serviceType))
      );

      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      if (failed.length > 0) {
        return {
          success: false,
          errors: failed.map(f => f.error || 'Unknown error')
        };
      }

      return {
        success: true,
        filePaths: successful.map(r => r.filePath!).filter(Boolean)
      };
    } catch (error) {
      console.error('Multiple file upload error:', error);
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Upload failed']
      };
    }
  }

  /**
   * Delete a file
   */
  static async deleteFile(filePath: string): Promise<boolean> {
    try {
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      console.error('File deletion error:', error);
      return false;
    }
  }

  /**
   * Delete multiple files
   */
  static async deleteFiles(filePaths: string[]): Promise<boolean> {
    try {
      await Promise.all(filePaths.map(filePath => this.deleteFile(filePath)));
      return true;
    } catch (error) {
      console.error('Multiple file deletion error:', error);
      return false;
    }
  }

  /**
   * Clean up files for rejected services
   */
  static async cleanupRejectedServiceFiles(documentPaths: string[]): Promise<void> {
    if (documentPaths.length > 0) {
      const deleted = await this.deleteFiles(documentPaths);
      if (!deleted) {
        console.warn('Failed to clean up some rejected service files:', documentPaths);
      }
    }
  }

  /**
   * Move files from pending to approved status
   * (In a real implementation, you might move files to a different directory)
   */
  static async approveServiceFiles(documentPaths: string[]): Promise<string[]> {
    // For now, we'll just return the same paths
    // In a production system, you might want to move files to an "approved" directory
    return documentPaths;
  }

  /**
   * Get file info
   */
  static async getFileInfo(filePath: string): Promise<{ exists: boolean; size?: number }> {
    try {
      const stats = await fs.stat(filePath);
      return {
        exists: true,
        size: stats.size
      };
    } catch (error) {
      return { exists: false };
    }
  }

  /**
   * Simulate file upload for development/testing
   */
  static async simulateFileUpload(
    fileName: string,
    userId: number,
    serviceType: string
  ): Promise<FileUploadResult> {
    // Generate a simulated file path
    const timestamp = Date.now();
    const extension = path.extname(fileName);
    const simulatedPath = path.join(
      this.UPLOAD_DIR,
      `${userId}_${serviceType}_simulated_${timestamp}${extension}`
    );

    return {
      success: true,
      filePath: simulatedPath
    };
  }

  /**
   * Convert File objects to file paths (for form processing)
   */
  static async processFormFiles(
    files: { [key: string]: File | File[] | null },
    userId: number,
    serviceType: string
  ): Promise<{ [key: string]: string | string[] | null }> {
    const result: { [key: string]: string | string[] | null } = {};

    for (const [key, value] of Object.entries(files)) {
      if (!value) {
        result[key] = null;
        continue;
      }

      if (Array.isArray(value)) {
        // Multiple files
        const filePaths: string[] = [];
        for (const file of value) {
          const uploadResult = await this.simulateFileUpload(file.name, userId, serviceType);
          if (uploadResult.success && uploadResult.filePath) {
            filePaths.push(uploadResult.filePath);
          }
        }
        result[key] = filePaths;
      } else {
        // Single file
        const uploadResult = await this.simulateFileUpload(value.name, userId, serviceType);
        result[key] = uploadResult.success ? uploadResult.filePath || null : null;
      }
    }

    return result;
  }
}
