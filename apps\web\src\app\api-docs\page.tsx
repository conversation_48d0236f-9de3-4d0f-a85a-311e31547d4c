
"use client";

import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import "swagger-ui-react/swagger-ui.css";
import "@/app/swagger.css";
import dynamic from 'next/dynamic';
import { Skeleton } from "@/components/ui/skeleton";
import { AuthGuard } from "@/components/auth/auth-guard";

// Dynamically import SwaggerUI to prevent SSR issues
const SwaggerUI = dynamic(() => import('swagger-ui-react'), { 
  ssr: false,
  loading: () => (
    <div className="p-8 space-y-4">
      <Skeleton className="h-12 w-1/3" />
      <Skeleton className="h-8 w-1/4" />
      <div className="space-y-6 pt-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  )
});

export default function ApiDocsPage() {
  return (
    <AuthGuard>
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow bg-background">
          <SwaggerUI url="/api/proxy/swagger.json" />
        </main>
        <Footer />
      </div>
    </AuthGuard>
  );
}
