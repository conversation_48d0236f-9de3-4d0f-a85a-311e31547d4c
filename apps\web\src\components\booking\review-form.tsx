"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Star, Loader2, AlertTriangle, CheckCircle } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { cn } from "@/lib/utils";

interface ReviewFormProps {
  bookingId: number;
  providerName: string;
  serviceName: string;
  isOpen: boolean;
  onClose: () => void;
  onSubmitSuccess?: () => void;
}

const reviewTranslations = {
  leaveReview: { ro: "<PERSON><PERSON> o Recenzie", ru: "Оставить отзыв", en: "Leave a Review" },
  rateService: { ro: "Evaluează Serviciul", ru: "Оценить услугу", en: "Rate the Service" },
  selectRating: { ro: "Selectează o evaluare", ru: "Выберите оценку", en: "Select a rating" },
  writeReview: { ro: "Scrie o Recenzie", ru: "Написать отзыв", en: "Write a Review" },
  reviewPlaceholder: { ro: "Împărtășește experiența ta cu acest serviciu...", ru: "Поделитесь своим опытом с этой услугой...", en: "Share your experience with this service..." },
  submitReview: { ro: "Trimite Recenzia", ru: "Отправить отзыв", en: "Submit Review" },
  cancel: { ro: "Anulează", ru: "Отмена", en: "Cancel" },
  reviewSuccess: { ro: "Recenzia a fost trimisă!", ru: "Отзыв отправлен!", en: "Review submitted!" },
  reviewSuccessDesc: { ro: "Mulțumim pentru feedback-ul tău!", ru: "Спасибо за ваш отзыв!", en: "Thank you for your feedback!" },
  error: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  ratingRequired: { ro: "Te rugăm să selectezi o evaluare", ru: "Пожалуйста, выберите оценку", en: "Please select a rating" },
  excellent: { ro: "Excelent", ru: "Отлично", en: "Excellent" },
  good: { ro: "Bun", ru: "Хорошо", en: "Good" },
  average: { ro: "Mediu", ru: "Средне", en: "Average" },
  poor: { ro: "Slab", ru: "Плохо", en: "Poor" },
  terrible: { ro: "Groaznic", ru: "Ужасно", en: "Terrible" },
};

const ratingLabels = {
  5: 'excellent',
  4: 'good', 
  3: 'average',
  2: 'poor',
  1: 'terrible',
} as const;

export function ReviewForm({
  bookingId,
  providerName,
  serviceName,
  isOpen,
  onClose,
  onSubmitSuccess
}: ReviewFormProps) {
  const { translate } = useLanguage();
  const [rating, setRating] = useState<number>(0);
  const [hoveredRating, setHoveredRating] = useState<number>(0);
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async () => {
    if (rating === 0) {
      setError(translate(reviewTranslations, 'ratingRequired'));
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/proxy/reviews/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingId,
          rating,
          comment: comment.trim() || null,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setIsSuccess(true);
        setTimeout(() => {
          onClose();
          if (onSubmitSuccess) {
            onSubmitSuccess();
          }
          // Reset form
          setRating(0);
          setComment("");
          setIsSuccess(false);
        }, 2000);
      } else {
        setError(data.message || 'A apărut o eroare la trimiterea recenziei.');
      }
    } catch (error) {
      console.error('Review submission error:', error);
      setError('A apărut o eroare la trimiterea recenziei.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setRating(0);
      setComment("");
      setError(null);
      setIsSuccess(false);
      onClose();
    }
  };

  if (isSuccess) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center space-y-4 py-6">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold text-green-700">
                {translate(reviewTranslations, 'reviewSuccess')}
              </h3>
              <p className="text-muted-foreground mt-2">
                {translate(reviewTranslations, 'reviewSuccessDesc')}
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>{translate(reviewTranslations, 'leaveReview')}</DialogTitle>
          <DialogDescription>
            Evaluează serviciul "{serviceName}" de la {providerName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Service Details */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-1">{serviceName}</h4>
            <p className="text-sm text-muted-foreground">Furnizor: {providerName}</p>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>{translate(reviewTranslations, 'error')}</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Rating Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">
              {translate(reviewTranslations, 'rateService')}
            </Label>
            <div className="flex items-center gap-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="p-1 transition-colors hover:scale-110"
                  disabled={isSubmitting}
                >
                  <Star
                    className={cn(
                      "w-8 h-8 transition-colors",
                      (hoveredRating >= star || rating >= star)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-gray-300 hover:text-yellow-400"
                    )}
                  />
                </button>
              ))}
              {(rating > 0 || hoveredRating > 0) && (
                <span className="ml-2 text-sm text-muted-foreground">
                  {translate(reviewTranslations, ratingLabels[hoveredRating || rating as keyof typeof ratingLabels])}
                </span>
              )}
            </div>
            {rating === 0 && (
              <p className="text-xs text-muted-foreground">
                {translate(reviewTranslations, 'selectRating')}
              </p>
            )}
          </div>

          {/* Comment */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-base font-medium">
              {translate(reviewTranslations, 'writeReview')} (opțional)
            </Label>
            <Textarea
              id="comment"
              placeholder={translate(reviewTranslations, 'reviewPlaceholder')}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              disabled={isSubmitting}
              className="resize-none"
            />
          </div>

          {/* Actions */}
          <div className="flex gap-2 justify-end pt-4">
            <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
              {translate(reviewTranslations, 'cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={rating === 0 || isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Se trimite...
                </>
              ) : (
                translate(reviewTranslations, 'submitReview')
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
