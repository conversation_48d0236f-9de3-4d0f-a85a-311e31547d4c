
"use client";

import { useState, useMemo, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup } from '@/components/ui/select';
import { LocationCombobox } from '@/components/ui/location-combobox';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/language-context';
import { LocationService, type LocationEntry } from '@repo/services';
import { commonTranslations } from '@repo/translations';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

// Import specific filter components
import { NannyFilters } from './nanny-filters';
import { ElderCareFilters } from './eldercare-filters';
import { CleaningFilters } from './cleaning-filters';
import { TutoringFilters } from './tutoring-filters';
import { CookingFilters } from './cooking-filters';

const searchFiltersTranslations = {
  title: { ro: "Filtrează Rezultatele", ru: "Фильтровать результаты", en: "Filter Results" },
  locationLabel: { ro: "Locație", ru: "Местоположение", en: "Location" },
  priceRangeLabel: { ro: "Interval Preț (MDL)", ru: "Ценовой диапазон (MDL)", en: "Price Range (MDL)" },
  minRatingLabel: { ro: "Rating Minim", ru: "Минимальный рейтинг", en: "Minimum Rating" },
  minRatingPlaceholder: { ro: "Orice rating", ru: "Любой рейтинг", en: "Any rating" },
  ratingOption: { ro: "Peste {stars} stele", ru: "Более {stars} звезд", en: "Over {stars} stars" },
  availabilityLabel: { ro: "Disponibilitate", ru: "Доступность", en: "Availability" },
  applyButton: { ro: "Aplică Filtre", ru: "Применить фильтры", en: "Apply Filters" },
  clearAllButton: { ro: "Șterge Toate", ru: "Очистить все", en: "Clear All" },
};

interface SearchFiltersProps {
  serviceType: string;
}

export function SearchFilters({ serviceType }: SearchFiltersProps) {
  const { translate } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Initialize state from URL params
  const [locationValue, setLocationValue] = useState(searchParams.get('location') || "all");
  const [priceRange, setPriceRange] = useState<[number, number]>([
    Number(searchParams.get('minPrice') || 0),
    Number(searchParams.get('maxPrice') || 1000),
  ]);
  const [minRating, setMinRating] = useState(Number(searchParams.get('minRating') || 0));

  // Locations state
  const [locations, setLocations] = useState<LocationEntry[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(true);
  const [availability, setAvailability] = useState({
    weekdays: searchParams.get('weekdays') === 'true',
    weekends: searchParams.get('weekends') === 'true',
    evenings: searchParams.get('evenings') === 'true',
  });

  // Load locations on component mount
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setLocationsLoading(true);
        const locationData = await LocationService.getLocationsForSearch(translate.currentLanguage);
        setLocations(locationData);
      } catch (error) {
        console.error('Failed to load locations:', error);
      } finally {
        setLocationsLoading(false);
      }
    };

    loadLocations();
  }, [translate.currentLanguage]);



  const handleApplyFilters = useCallback(() => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('page', '1'); // Reset to first page on filter change

    // Location
    if (locationValue !== 'all') newParams.set('location', locationValue);
    else newParams.delete('location');

    // Price
    if (priceRange[0] > 0) newParams.set('minPrice', String(priceRange[0]));
    else newParams.delete('minPrice');
    if (priceRange[1] < 1000) newParams.set('maxPrice', String(priceRange[1]));
    else newParams.delete('maxPrice');

    // Rating
    if (minRating > 0) newParams.set('minRating', String(minRating));
    else newParams.delete('minRating');

    // Availability
    (Object.keys(availability) as Array<keyof typeof availability>).forEach(key => {
      if (availability[key]) newParams.set(key, 'true');
      else newParams.delete(key);
    });

    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  }, [locationValue, priceRange, minRating, availability, router, pathname, searchParams]);
  
  const ratingOptions = [0, 1, 2, 3, 4, 5].map(r => ({
    value: String(r),
    label: r === 0 ? translate(searchFiltersTranslations, 'minRatingPlaceholder') : translate(searchFiltersTranslations, 'ratingOption').replace('{stars}', String(r))
  }));

  // Calculate active filters count
  const getActiveFiltersCount = () => {
    let count = 0;
    if (locationValue !== "all") count++;
    if (priceRange[0] !== 0 || priceRange[1] !== 1000) count++;
    if (minRating > 0) count++;
    if (Object.values(availability).some(val => val)) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  const handleClearAllFilters = useCallback(() => {
    setLocationValue("all");
    setPriceRange([0, 1000]);
    setMinRating(0);
    setAvailability({ weekdays: false, weekends: false, evenings: false });
  }, []);

  const renderSpecificFilters = () => {
    const filterComponent = (() => {
      switch (serviceType) {
        case 'Nanny': return <NannyFilters />;
        case 'ElderCare': return <ElderCareFilters />;
        case 'Cleaning': return <CleaningFilters />;
        case 'Tutoring': return <TutoringFilters />;
        case 'Cooking': return <CookingFilters />;
        default: return null;
      }
    })();

    if (!filterComponent) return null;

    return (
      <div className="transition-all duration-300 ease-in-out">
        <div className="border-t border-border pt-4 mt-4">
          <h4 className="font-medium text-sm text-foreground mb-3">
            {translate(commonTranslations, `category${serviceType}` as keyof typeof commonTranslations)} Filters
          </h4>
          {filterComponent}
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-headline">{translate(searchFiltersTranslations, 'title')}</CardTitle>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeFiltersCount} active
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="location">{translate(searchFiltersTranslations, 'locationLabel')}</Label>
          <LocationCombobox
            locations={locations}
            value={locationValue}
            onValueChange={setLocationValue}
            placeholder={locationsLoading ? "Loading locations..." : translate(searchFiltersTranslations, 'locationLabel')}
          />
        </div>

        <div className="space-y-2">
          <Label>{translate(searchFiltersTranslations, 'priceRangeLabel')}</Label>
          <Slider
            value={priceRange}
            onValueChange={(value) => setPriceRange(value as [number, number])}
            max={1000} step={10} className="py-2"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{priceRange[0]} MDL</span>
            <span>{priceRange[1]} MDL</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label>{translate(searchFiltersTranslations, 'minRatingLabel')}</Label>
          <Select value={String(minRating)} onValueChange={(val) => setMinRating(Number(val))}>
            <SelectTrigger id="minRating"><SelectValue /></SelectTrigger>
            <SelectContent>
              {ratingOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>{translate(searchFiltersTranslations, 'availabilityLabel')}</Label>
          <div className="space-y-2">
            {(Object.keys(availability) as Array<keyof typeof availability>).map(key => (
              <div key={key} className="flex items-center space-x-2">
                <Checkbox
                  id={`avail-${key}`}
                  checked={availability[key]}
                  onCheckedChange={(checked) => setAvailability(prev => ({ ...prev, [key]: !!checked }))}
                />
                <Label htmlFor={`avail-${key}`} className="font-normal text-sm">
                  {translate(commonTranslations, `${key}` as keyof typeof commonTranslations)}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {renderSpecificFilters()}

        <div className="flex gap-2">
          <Button onClick={handleApplyFilters} className="flex-1">{translate(searchFiltersTranslations, 'applyButton')}</Button>
          {activeFiltersCount > 0 && (
            <Button
              variant="outline"
              onClick={handleClearAllFilters}
              className="px-3"
            >
              {translate(searchFiltersTranslations, 'clearAllButton')}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
