"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import type { NannyServiceDetails } from "@prisma/client";
import { useLanguage } from "@/contexts/language-context";
import { commonTranslations } from "@repo/translations";
import { BooleanDetailItem } from "../DetailItem";

interface NannyDetailsProps {
    details: NannyServiceDetails | null;
}

export function NannyDetailsView({ details }: NannyDetailsProps) {
    const { translate } = useLanguage();
    if (!details) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">
                    {translate(commonTranslations, 'childcareTitle')}
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'childcareAge0_2')} value={details.PreferredAge_0_2} />
                    <BooleanDetailItem label={translate(commonTranslations, 'childcareAge3_6')} value={details.PreferredAge_3_6} />
                    <BooleanDetailItem label={translate(commonTranslations, 'childcareAge7_plus')} value={details.PreferredAge_7_plus} />
                </div>
            </CardContent>
        </Card>
    );
}
