"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from "@/components/ui/drawer"
import { useIsMobile } from "@/hooks/use-mobile"

interface LocationEntry {
  id: number
  slug: string
  translationKey: string
  name: string
  displayName: string
  type: string
  parentId: number | null
  sortOrder: number
  isCapital: boolean
  specialType: string | null
}

interface LocationComboboxProps {
  locations: LocationEntry[]
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  className?: string
}

export function LocationCombobox({
  locations,
  value,
  onValueChange,
  placeholder = "Selectează locația...",
  className,
}: LocationComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  // Group locations for better organization
  const groupedLocations = React.useMemo(() => {
    const groups = {
      country: locations.filter(l => l.type === 'Country'),
      municipality: locations.filter(l => l.type === 'Municipality'),
      majorCities: locations.filter(l => l.specialType === 'major_city'),
      chisinauSectors: locations.filter(l => l.type === 'Sector'),
      chisinauSuburbs: locations.filter(l => l.type === 'Suburb'),
      otherTowns: locations.filter(l => l.type === 'Town' && !l.specialType)
    }
    return groups
  }, [locations])

  // Handle both slug and ID values for backward compatibility
  const selectedLocation = locations.find(location =>
    location.slug === value || String(location.id) === value
  )

  const LocationList = ({ setOpen }: { setOpen: (open: boolean) => void }) => (
    <Command>
      <CommandInput placeholder="Caută locația..." className="h-9" />
      <CommandList>
        <CommandEmpty>Nu s-a găsit nicio locație.</CommandEmpty>
        
        {/* Country & Municipality */}
        {(groupedLocations.country.length > 0 || groupedLocations.municipality.length > 0) && (
          <CommandGroup heading="Național">
            {groupedLocations.country.map(location => (
              <CommandItem
                key={location.slug}
                value={location.slug}
                onSelect={(currentValue) => {
                  onValueChange(currentValue === value ? "" : currentValue)
                  setOpen(false)
                }}
              >
                {location.displayName}
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === location.slug ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
            {groupedLocations.municipality.map(location => (
              <CommandItem
                key={location.slug}
                value={location.slug}
                onSelect={(currentValue) => {
                  onValueChange(currentValue === value ? "" : currentValue)
                  setOpen(false)
                }}
              >
                {location.displayName}
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === location.slug ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        )}

        {/* Major Cities */}
        {groupedLocations.majorCities.length > 0 && (
          <CommandGroup heading="Orașe Majore">
            {groupedLocations.majorCities.map(location => (
              <CommandItem
                key={location.slug}
                value={location.slug}
                onSelect={(currentValue) => {
                  onValueChange(currentValue === value ? "" : currentValue)
                  setOpen(false)
                }}
              >
                {location.displayName}
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === location.slug ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        )}

        {/* Chisinau Sectors */}
        {groupedLocations.chisinauSectors.length > 0 && (
          <CommandGroup heading="Chișinău - Sectoare">
            {groupedLocations.chisinauSectors.map(location => (
              <CommandItem
                key={location.slug}
                value={location.slug}
                onSelect={(currentValue) => {
                  onValueChange(currentValue === value ? "" : currentValue)
                  setOpen(false)
                }}
              >
                {location.displayName}
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === location.slug ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        )}

        {/* Chisinau Suburbs */}
        {groupedLocations.chisinauSuburbs.length > 0 && (
          <CommandGroup heading="Chișinău - Suburbii">
            {groupedLocations.chisinauSuburbs.map(location => (
              <CommandItem
                key={location.slug}
                value={location.slug}
                onSelect={(currentValue) => {
                  onValueChange(currentValue === value ? "" : currentValue)
                  setOpen(false)
                }}
              >
                {location.displayName}
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === location.slug ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        )}

        {/* Other Towns */}
        {groupedLocations.otherTowns.length > 0 && (
          <CommandGroup heading="Alte Orașe">
            {groupedLocations.otherTowns.map(location => (
              <CommandItem
                key={location.slug}
                value={location.slug}
                onSelect={(currentValue) => {
                  onValueChange(currentValue === value ? "" : currentValue)
                  setOpen(false)
                }}
              >
                {location.displayName}
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === location.slug ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        )}
      </CommandList>
    </Command>
  )

  if (isMobile) {
    return (
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between text-foreground", className)}
          >
            {selectedLocation ? selectedLocation.displayName : placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </DrawerTrigger>
        <DrawerContent>
          <div className="mt-4 border-t">
            <LocationList setOpen={setOpen} />
          </div>
        </DrawerContent>
      </Drawer>
    )
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between text-foreground", className)}
        >
          {selectedLocation ? selectedLocation.displayName : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <LocationList setOpen={setOpen} />
      </PopoverContent>
    </Popover>
  )
}
