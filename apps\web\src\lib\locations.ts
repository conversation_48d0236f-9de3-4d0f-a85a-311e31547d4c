
// DEPRECATED: This interface is kept for backward compatibility
// Use LocationEntry from @repo/services instead
export interface LocationEntry {
  key: string; // Unique key for React iteration, e.g., "chisinau_botanica"
  value: string; // Value for API query/URL param, e.g., "chisinau-botanica" or "chisinau"
  translationKey: string; // For useLanguage hook, e.g., "locChisinauBotanica"
  displayRo: string; // Full Romanian name for display/storage, e.g., "Chișinău, Botanica" or "Chișinău"
}

// DEPRECATED: Hardcoded arrays removed - use LocationService dynamic queries instead


// DEPRECATED: Hardcoded location array removed - use LocationService.getLocations() instead
// This array is kept empty for backward compatibility but should not be used
export const allMoldovaLocations: LocationEntry[] = [];

// DEPRECATED: This function is kept for backward compatibility
// Use LocationService.getLocationDisplayName() instead
// DEPRECATED: This function is deprecated - use LocationService.getLocationDisplayName() instead
export const getFullLocationPathDisplayHelper = (
  value: string,
  translateFn: (translations: Record<string, Record<string, string>>, key: string, fallback?: string) => string,
  commonTranslationsObj: Record<string, Record<string, string>>
): string => {
  // This function is deprecated and should not be used
  // Use LocationService.getLocationDisplayName() for database-driven location display names
  console.warn('getFullLocationPathDisplayHelper is deprecated. Use LocationService.getLocationDisplayName() instead.');
  return value;
};

// NEW: Modern helper function that works with LocationService
import { LocationService, type LocationEntry as ServiceLocationEntry } from '@repo/services';

/**
 * Modern replacement for getFullLocationPathDisplayHelper
 * Uses LocationService to get location display names
 */
export const getLocationDisplayName = async (
  value: string,
  lang: string = 'ro'
): Promise<string> => {
  try {
    const location = await LocationService.getLocationByValue(value, lang);
    return location.displayName;
  } catch (error) {
    console.warn(`Failed to get location display name for ${value}:`, error);
    return value;
  }
};

/**
 * Synchronous version that works with pre-loaded locations array
 */
export const getLocationDisplayNameSync = (
  value: string,
  locations: ServiceLocationEntry[]
): string => {
  return LocationService.getLocationDisplayName(value, locations);
};

