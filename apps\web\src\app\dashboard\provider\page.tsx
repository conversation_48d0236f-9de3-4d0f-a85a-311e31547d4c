
"use client";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Link from "next/link";
import {
  ListChecks,
  CalendarClock,
  Loader2,
  AlertTriangle,
  Briefcase,
  Calendar,
  MessageSquare,
  DollarSign,
  TrendingUp,
  Clock,
  Users,
  Star
} from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from "@repo/translations";
import { useSession } from "next-auth/react";
import { ProviderBookingManagement } from '@/components/booking/provider-booking-management';
import { ProviderServiceDelivery } from '@/components/booking/provider-service-delivery';

interface ProviderStats {
  activeServicesCount: number;
  pendingRequestsCount: number;
}

interface ProviderAppointment {
  id: string;
  serviceName: string;
  clientName: string;
  date: string;
  time: string;
  status: string;
}

const providerDashboardTranslations = {
  providerPanelTitle: { ro: "Panoul Furnizor", ru: "Панель поставщика", en: "Provider Dashboard" },
  providerPanelDescription: { ro: "Bun venit înapoi! Iată o privire de ansamblu asupra activității tale ca furnizor.", ru: "Добро пожаловать! Вот обзор вашей деятельности в качестве поставщика.", en: "Welcome back! Here's an overview of your provider activities." },
  errorFetchingStats: { ro: "Eroare la preluarea statisticilor.", ru: "Ошибка при загрузке статистики.", en: "Error fetching stats." },
  activeServices: { ro: "Servicii Active", ru: "Активные услуги", en: "Active Services" },
  pendingBookings: { ro: "Rezervări în Așteptare", ru: "Ожидающие бронирования", en: "Pending Bookings" },
  totalEarnings: { ro: "Câștiguri Totale", ru: "Общий доход", en: "Total Earnings" },
  monthlyBookings: { ro: "Rezervări Luna Aceasta", ru: "Бронирования в этом месяце", en: "Monthly Bookings" },
  averageRating: { ro: "Rating Mediu", ru: "Средний рейтинг", en: "Average Rating" },
  responseTime: { ro: "Timp de Răspuns", ru: "Время ответа", en: "Response Time" },
  recentBookings: { ro: "Rezervări Recente", ru: "Недавние бронирования", en: "Recent Bookings" },
  upcomingAppointments: { ro: "Programări Viitoare", ru: "Предстоящие встречи", en: "Upcoming Appointments" },
  quickActions: { ro: "Acțiuni Rapide", ru: "Быстрые действия", en: "Quick Actions" },
  manageServices: { ro: "Gestionează Servicii", ru: "Управление услугами", en: "Manage Services" },
  viewCalendar: { ro: "Vezi Calendarul", ru: "Посмотреть календарь", en: "View Calendar" },
  viewMessages: { ro: "Vezi Mesajele", ru: "Посмотреть сообщения", en: "View Messages" },
  noRecentBookings: { ro: "Nu există rezervări recente", ru: "Нет недавних бронирований", en: "No recent bookings" },
  noUpcomingAppointments: { ro: "Nu există programări viitoare", ru: "Нет предстоящих встреч", en: "No upcoming appointments" },
  viewAllBookings: { ro: "Vezi Toate Rezervările", ru: "Посмотреть все бронирования", en: "View All Bookings" },
  hours: { ro: "ore", ru: "часов", en: "hours" },
  fromLastMonth: { ro: "față de luna trecută", ru: "по сравнению с прошлым месяцем", en: "from last month" },
  thisMonth: { ro: "luna aceasta", ru: "в этом месяце", en: "this month" },
};

export default function ProviderDashboardPage() {
  const { translate } = useLanguage();
  const { data: session, status: sessionStatus } = useSession();
  const [stats, setStats] = useState<ProviderStats | null>(null);
  const [appointments, setAppointments] = useState<ProviderAppointment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingAppointments, setIsLoadingAppointments] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (sessionStatus === "authenticated" && (session?.user as any)?.isProvider) {
      const providerId = (session.user as any).id;
      if (!providerId) {
        setError("ID Furnizor nu a fost găsit în sesiune.");
        setIsLoading(false);
        return;
      }

      const fetchStats = async () => {
        setIsLoading(true);
        setError(null);
        try {
          const response = await fetch(`/api/proxy/dashboard/provider-stats?providerId=${providerId}`);
          if (!response.ok) {
            throw new Error(translate(providerDashboardTranslations, 'errorFetchingStats'));
          }
          const data = await response.json();
          setStats(data);
        } catch (err) {
          setError(err instanceof Error ? err.message : translate(providerDashboardTranslations, 'errorFetchingStats'));
        } finally {
          setIsLoading(false);
        }
      };

      const fetchAppointments = async () => {
        setIsLoadingAppointments(true);
        try {
          const response = await fetch(`/api/proxy/provider/appointments?providerId=${providerId}`);
          if (response.ok) {
            const data = await response.json();
            setAppointments(data.appointments || []);
          }
        } catch (err) {
          console.error('Error fetching appointments:', err);
        } finally {
          setIsLoadingAppointments(false);
        }
      };

      fetchStats();
      fetchAppointments();
    } else if (sessionStatus === "loading") {
      setIsLoading(true);
    } else {
      setIsLoading(false);
    }
  }, [sessionStatus, session, translate]);

  // Get recent and upcoming appointments
  const recentBookings = appointments.filter(apt =>
    apt.status === 'Completed' || apt.status === 'Confirmed'
  ).slice(0, 3);

  const upcomingAppointments = appointments.filter(apt =>
    apt.status === 'Pending' || apt.status === 'Confirmed'
  ).slice(0, 3);

  if (sessionStatus === "loading" || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading provider dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {translate(providerDashboardTranslations, 'providerPanelTitle')}
        </h1>
        <p className="text-muted-foreground">
          {translate(providerDashboardTranslations, 'providerPanelDescription')}
        </p>
      </div>

      {error ? (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {translate(providerDashboardTranslations, 'activeServices')}
                </CardTitle>
                <ListChecks className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.activeServicesCount || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Services currently offered
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {translate(providerDashboardTranslations, 'pendingBookings')}
                </CardTitle>
                <CalendarClock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.pendingRequestsCount || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Awaiting your response
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {translate(providerDashboardTranslations, 'monthlyBookings')}
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{appointments.length}</div>
                <p className="text-xs text-muted-foreground">
                  {translate(providerDashboardTranslations, 'thisMonth')}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {translate(providerDashboardTranslations, 'averageRating')}
                </CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.8</div>
                <p className="text-xs text-muted-foreground">
                  Based on client reviews
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {/* Pending Bookings Management */}
            <ProviderBookingManagement />

            <Card>
              <CardHeader>
                <CardTitle>{translate(providerDashboardTranslations, 'quickActions')}</CardTitle>
                <CardDescription>Common tasks and shortcuts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/dashboard/provider/services">
                    <ListChecks className="mr-2 h-4 w-4" />
                    {translate(providerDashboardTranslations, 'manageServices')}
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/dashboard/provider/calendar">
                    <Calendar className="mr-2 h-4 w-4" />
                    {translate(providerDashboardTranslations, 'viewCalendar')}
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/dashboard/chat">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    {translate(providerDashboardTranslations, 'viewMessages')}
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/dashboard/provider/reviews">
                    <Star className="mr-2 h-4 w-4" />
                    Vezi Recenzii
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Service Delivery Management */}
          <ProviderServiceDelivery />

          {/* Recent Bookings Section */}
          <Card>
            <CardHeader>
              <CardTitle>{translate(providerDashboardTranslations, 'recentBookings')}</CardTitle>
              <CardDescription>Your latest client bookings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoadingAppointments ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    <span className="text-sm text-muted-foreground">Loading bookings...</span>
                  </div>
                ) : recentBookings.length > 0 ? (
                  recentBookings.map((booking, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium">{booking.serviceName}</p>
                        <p className="text-xs text-muted-foreground">
                          {booking.clientName} • {booking.date}
                        </p>
                      </div>
                      <div className="text-sm font-medium">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          booking.status === 'Completed'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {booking.status}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">
                    {translate(providerDashboardTranslations, 'noRecentBookings')}
                  </p>
                )}
              </div>
              <div className="mt-4">
                <Button asChild variant="outline" className="w-full">
                  <Link href="/dashboard/provider/calendar">
                    {translate(providerDashboardTranslations, 'viewAllBookings')}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
