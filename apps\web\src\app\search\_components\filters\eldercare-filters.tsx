"use client";

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useLanguage } from '@/contexts/language-context';
import { BooleanFilterGroup, FilterBadges, getActiveFilters } from './filter-components';

const elderCareFilterTranslations = {
  clientTypes: { ro: "Tipuri Clienți", ru: "Типы клиентов", en: "Client Types" },
  mobile: { ro: "Mobil", ru: "Мобильный", en: "Mobile" },
  partiallyImmobilized: { ro: "Parțial imobilizat", ru: "Частично обездвиженный", en: "Partially Immobilized" },
  completelyImmobilized: { ro: "Complet imobilizat", ru: "Полностью обездвиженный", en: "Completely Immobilized" },
  
  medicalKnowledge: { ro: "Cunoștințe Medicale", ru: "Медицинские знания", en: "Medical Knowledge" },
  basicMedical: { ro: "Cunoștințe medicale de bază", ru: "Базовые медицинские знания", en: "Basic Medical Knowledge" },
  advancedMedical: { ro: "Cunoștințe medicale avansate", ru: "Продвинутые медицинские знания", en: "Advanced Medical Knowledge" },
  medicationAdmin: { ro: "Administrare medicamente", ru: "Введение лекарств", en: "Medication Administration" },
  
  additionalServices: { ro: "Servicii Adiționale", ru: "Дополнительные услуги", en: "Additional Services" },
  drivingLicense: { ro: "Permis de conducere", ru: "Водительские права", en: "Driving License" },
  cooking: { ro: "Gătit", ru: "Приготовление пищи", en: "Cooking" },
  lightCleaning: { ro: "Curățenie ușoară", ru: "Легкая уборка", en: "Light Cleaning" },
  companionship: { ro: "Companie", ru: "Общение", en: "Companionship" },
};

interface ElderCareFiltersState {
  // Client types
  TypeMobil: boolean;
  TypePartialImobilizat: boolean;
  TypeCompletImobilizat: boolean;
  
  // Medical knowledge
  MedicalKnowledgeBasic: boolean;
  MedicalKnowledgeAdvanced: boolean;
  MedicationAdmin: boolean;
  
  // Additional services
  DrivingLicense: boolean;
  ActivityCooking: boolean;
  ActivityCleaningLight: boolean;
  ActivityCompanionship: boolean;
}

export function ElderCareFilters() {
  const { translate } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<ElderCareFiltersState>({
    TypeMobil: searchParams.get('TypeMobil') === 'true',
    TypePartialImobilizat: searchParams.get('TypePartialImobilizat') === 'true',
    TypeCompletImobilizat: searchParams.get('TypeCompletImobilizat') === 'true',
    MedicalKnowledgeBasic: searchParams.get('MedicalKnowledgeBasic') === 'true',
    MedicalKnowledgeAdvanced: searchParams.get('MedicalKnowledgeAdvanced') === 'true',
    MedicationAdmin: searchParams.get('MedicationAdmin') === 'true',
    DrivingLicense: searchParams.get('DrivingLicense') === 'true',
    ActivityCooking: searchParams.get('ActivityCooking') === 'true',
    ActivityCleaningLight: searchParams.get('ActivityCleaningLight') === 'true',
    ActivityCompanionship: searchParams.get('ActivityCompanionship') === 'true',
  });

  // Update URL when filters change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams.toString());
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, 'true');
      } else {
        newParams.delete(key);
      }
    });

    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  }, [filters, router, pathname, searchParams]);

  const handleFilterChange = (key: string, checked: boolean) => {
    setFilters(prev => ({ ...prev, [key]: checked }));
  };

  const handleClearAll = () => {
    setFilters({
      TypeMobil: false,
      TypePartialImobilizat: false,
      TypeCompletImobilizat: false,
      MedicalKnowledgeBasic: false,
      MedicalKnowledgeAdvanced: false,
      MedicationAdmin: false,
      DrivingLicense: false,
      ActivityCooking: false,
      ActivityCleaningLight: false,
      ActivityCompanionship: false,
    });
  };

  const handleRemoveFilter = (key: string) => {
    setFilters(prev => ({ ...prev, [key]: false }));
  };

  // Create filter labels
  const filterLabels = {
    TypeMobil: translate(elderCareFilterTranslations, 'mobile'),
    TypePartialImobilizat: translate(elderCareFilterTranslations, 'partiallyImmobilized'),
    TypeCompletImobilizat: translate(elderCareFilterTranslations, 'completelyImmobilized'),
    MedicalKnowledgeBasic: translate(elderCareFilterTranslations, 'basicMedical'),
    MedicalKnowledgeAdvanced: translate(elderCareFilterTranslations, 'advancedMedical'),
    MedicationAdmin: translate(elderCareFilterTranslations, 'medicationAdmin'),
    DrivingLicense: translate(elderCareFilterTranslations, 'drivingLicense'),
    ActivityCooking: translate(elderCareFilterTranslations, 'cooking'),
    ActivityCleaningLight: translate(elderCareFilterTranslations, 'lightCleaning'),
    ActivityCompanionship: translate(elderCareFilterTranslations, 'companionship'),
  };

  // Get active filters for badges
  const activeFilters = getActiveFilters(filters, filterLabels, 'eldercare');

  return (
    <div className="space-y-4">
      <FilterBadges 
        activeFilters={activeFilters}
        onRemove={handleRemoveFilter}
        onClearAll={handleClearAll}
      />

      <BooleanFilterGroup
        title={translate(elderCareFilterTranslations, 'clientTypes')}
        options={[
          { key: 'TypeMobil', label: filterLabels.TypeMobil, checked: filters.TypeMobil },
          { key: 'TypePartialImobilizat', label: filterLabels.TypePartialImobilizat, checked: filters.TypePartialImobilizat },
          { key: 'TypeCompletImobilizat', label: filterLabels.TypeCompletImobilizat, checked: filters.TypeCompletImobilizat },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(elderCareFilterTranslations, 'medicalKnowledge')}
        options={[
          { key: 'MedicalKnowledgeBasic', label: filterLabels.MedicalKnowledgeBasic, checked: filters.MedicalKnowledgeBasic },
          { key: 'MedicalKnowledgeAdvanced', label: filterLabels.MedicalKnowledgeAdvanced, checked: filters.MedicalKnowledgeAdvanced },
          { key: 'MedicationAdmin', label: filterLabels.MedicationAdmin, checked: filters.MedicationAdmin },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(elderCareFilterTranslations, 'additionalServices')}
        options={[
          { key: 'DrivingLicense', label: filterLabels.DrivingLicense, checked: filters.DrivingLicense },
          { key: 'ActivityCooking', label: filterLabels.ActivityCooking, checked: filters.ActivityCooking },
          { key: 'ActivityCleaningLight', label: filterLabels.ActivityCleaningLight, checked: filters.ActivityCleaningLight },
          { key: 'ActivityCompanionship', label: filterLabels.ActivityCompanionship, checked: filters.ActivityCompanionship },
        ]}
        onChange={handleFilterChange}
      />
    </div>
  );
}
