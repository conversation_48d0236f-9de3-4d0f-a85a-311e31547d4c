
"use client";

import { useState, type FormEvent, Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import Link from "next/link";
import { Loader2, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from "@repo/translations";
import { useRouter, useSearchParams } from "next/navigation"; 
import { Separator } from "@/components/ui/separator";
import { signIn } from "next-auth/react";

const GoogleIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22.56 12.25C22.56 11.47 22.49 10.72 22.35 10H12V14.5H18.31C18.03 16.36 17.01 17.91 15.21 18.99V21.6H19.42C21.5 19.68 22.56 16.78 22.56 12.25Z" fill="#4285F4"/>
    <path d="M12 23C14.97 23 17.45 22.02 19.42 20.6L15.21 17.99C14.24 18.63 13.08 19 12 19C9.27 19 6.94 17.24 6.02 14.8H1.69V17.5C3.62 20.81 7.58 23 12 23Z" fill="#34A853"/>
    <path d="M6.02 14.8C5.79 14.18 5.66 13.49 5.66 12.79C5.66 12.09 5.79 11.4 6.02 10.78V8L1.69 8C0.63 10.03 0 12.44 0 15C0 17.56 0.63 19.97 1.69 22L6.02 19.22C5.79 18.51 5.66 17.82 5.66 17.12C5.66 16.41 5.79 15.72 6.02 14.8Z" fill="#FBBC05"/>
    <path d="M12 5.5C13.66 5.5 15.07 6.13 16.16 7.17L19.5 3.83C17.45 1.92 14.97 1 12 1C7.58 1 3.62 3.19 1.69 6.5L6.02 9.22C6.94 6.76 9.27 5 12 5V5.5Z" fill="#EA4335"/>
  </svg>
);

const FacebookIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18 2H15C12.69 2 12 3.05 12 5.42V8H9V12H12V22H16V12H19L20 8H16V5.91C16 4.81 16.63 4.09 17.56 4.09H19.5V2H18Z" fill="#1877F2"/>
  </svg>
);

const loginPageTranslations = {
  pageTitle: { ro: "Conectare", ru: "Вход", en: "Login" },
  description: { ro: "Introdu datele tale pentru a te conecta.", ru: "Введите свои данные для входа.", en: "Enter your details to log in." },
  emailLabel: { ro: "Email", ru: "Электронная почта", en: "Email" },
  emailPlaceholder: { ro: "<EMAIL>", ru: "<EMAIL>", en: "<EMAIL>" },
  passwordLabel: { ro: "Parolă", ru: "Пароль", en: "Password" },
  loginButton: { ro: "Conectează-te", ru: "Войти", en: "Log In" },
  loadingButton: { ro: "Se conectează...", ru: "Вход...", en: "Logging in..." },
  noAccount: { ro: "Nu ai cont?", ru: "Нет аккаунта?", en: "Don't have an account?" },
  registerLink: { ro: "Înregistrează-te", ru: "Зарегистрироваться", en: "Sign up" },
  errorAlertTitle: { ro: "Eroare", ru: "Ошибка", en: "Error" },
  toastSuccessTitle: { ro: "Succes!", ru: "Успех!", en: "Success!" },
  toastErrorTitle: { ro: "Eroare Autentificare", ru: "Ошибка входа", en: "Login Error" },
  failedToFetchError: { ro: "Nu s-a putut conecta la server. Verificați conexiunea la internet sau încercați mai târziu.", ru: "Не удалось подключиться к серверу. Проверьте интернет-соединение или попробуйте позже.", en: "Could not connect to the server. Check your internet connection or try again later." },
  unknownError: { ro: "Eroare necunoscută la autentificare. Verificați consola pentru detalii.", ru: "Неизвестная ошибка при входе. Проверьте консоль для деталей.", en: "Unknown login error. Check the console for details." },
  orContinueWith: { ro: "Sau continuă cu", ru: "Или продолжить с", en: "Or continue with" },
  continueWithGoogle: { ro: "Continuă cu Google", ru: "Продолжить с Google", en: "Continue with Google" },
  continueWithFacebook: { ro: "Continuă cu Facebook", ru: "Продолжить с Facebook", en: "Continue with Facebook" },
  oauthNotImplemented: { ro: "Autentificarea OAuth nu este încă implementată complet.", ru: "OAuth аутентификация еще не полностью реализована.", en: "OAuth authentication is not yet fully implemented."}
};

function RegisterComponent() {
  const { translate } = useLanguage();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [emailInput, setEmailInput] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const handleOAuthSignIn = async (provider: 'google' | 'facebook') => {
    setIsLoading(true);
    setError(null);
    const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';
    const result = await signIn(provider, { redirect: false, callbackUrl });
    
    if (result?.error) {
      const errorMessage = result.error === "OAuthAccountNotLinked" 
        ? "Acest cont este deja înregistrat cu alt provider. Încearcă să te loghezi cu providerul original."
        : result.error;
      setError(errorMessage);
      toast({ variant: "destructive", title: translate(loginPageTranslations, 'toastErrorTitle'), description: errorMessage });
      setIsLoading(false);
    } else if (result?.ok && result.url) {
      router.push(result.url);
    } else if (result?.ok) {
      router.push(callbackUrl);
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';

    const result = await signIn('credentials', {
      redirect: false,
      email: emailInput,
      password: password,
      callbackUrl: callbackUrl, 
    });

    if (result?.error) {
      let errorMessageToDisplay = result.error;
      if (result.error === 'CredentialsSignin') {
        errorMessageToDisplay = translate(commonTranslations, 'invalidCredentialsError');
      }
      setError(errorMessageToDisplay);
      toast({
        variant: "destructive",
        title: translate(loginPageTranslations, 'toastErrorTitle'),
        description: errorMessageToDisplay,
      });
      setIsLoading(false);
    } else if (result?.ok) {
      toast({
        title: translate(loginPageTranslations, 'toastSuccessTitle'),
        description: "Autentificare reușită! Redirecționare...",
      });
      if (result.url) {
        router.push(result.url);
      } else {
        router.push(callbackUrl);
      }
    } else {
        setError(translate(loginPageTranslations, 'unknownError'));
        toast({
            variant: "destructive",
            title: translate(loginPageTranslations, 'toastErrorTitle'),
            description: translate(loginPageTranslations, 'unknownError'),
        });
        setIsLoading(false);
    }
  };

  return (
    <main className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-background">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold font-headline">{translate(loginPageTranslations, 'pageTitle')}</CardTitle>
          <CardDescription>{translate(loginPageTranslations, 'description')}</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">{translate(loginPageTranslations, 'emailLabel')}</Label>
              <Input 
                id="email" 
                type="email" 
                placeholder={translate(loginPageTranslations, 'emailPlaceholder')}
                required 
                value={emailInput}
                onChange={(e) => setEmailInput(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">{translate(loginPageTranslations, 'passwordLabel')}</Label>
              <Input 
                id="password" 
                type="password" 
                required 
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            {error && (
              <Alert variant="destructive" className="mt-4">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 shrink-0" />
                  <div>
                    <AlertTitle>{translate(loginPageTranslations, 'errorAlertTitle')}</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </div>
                </div>
              </Alert>
            )}
             <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {isLoading ? translate(loginPageTranslations, 'loadingButton') : translate(loginPageTranslations, 'loginButton')}
            </Button>
          </CardContent>
        </form>
        
        <div className="px-6 pb-2">
          <div className="relative my-3">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                {translate(loginPageTranslations, 'orContinueWith')}
              </span>
            </div>
          </div>
          <div className="space-y-2">
            <Button variant="outline" className="w-full" onClick={() => handleOAuthSignIn('google')} disabled={isLoading}>
              {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <GoogleIcon />}
              <span className="ml-2">{translate(loginPageTranslations, 'continueWithGoogle')}</span>
            </Button>
            <Button variant="outline" className="w-full" onClick={() => handleOAuthSignIn('facebook')} disabled={isLoading}>
               {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <FacebookIcon />}
              <span className="ml-2">{translate(loginPageTranslations, 'continueWithFacebook')}</span>
            </Button>
          </div>
        </div>
        
        <CardFooter className="flex flex-col space-y-2 pt-4">
          <p className="text-sm text-center text-muted-foreground">
            {translate(loginPageTranslations, 'noAccount')}{" "}
            <Link href="/auth/register" className="font-medium text-primary hover:underline">
              {translate(loginPageTranslations, 'registerLink')}
            </Link>
          </p>
        </CardFooter>
      </Card>
    </main>
  );
}

export default function RegisterPage() {
    return (
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <Suspense fallback={<div className="flex-grow flex items-center justify-center"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
            <RegisterComponent />
          </Suspense>
          <Footer />
        </div>
      );
}
