
import { Router } from 'express';
import prisma from '../lib/db';
import bcrypt from 'bcryptjs';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { type AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// Configure multer for profile photo uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'profile-photos');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp and user ID
    const userId = (req as AuthenticatedRequest).user?.id;
    const ext = path.extname(file.originalname);
    const filename = `profile-${userId}-${Date.now()}${ext}`;
    cb(null, filename);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Fotografia trebuie să fie în format JPG, PNG sau WebP.'));
    }
  }
});

// Migrat din /api/provider-requests/my-status
router.get('/provider-request-status/:userId', async (req, res) => {
    try {
        const userId = parseInt(req.params.userId, 10);
        if (isNaN(userId)) {
            return res.status(400).json({ message: 'Invalid User ID' });
        }
        const request = await prisma.providerRegistrationRequest.findUnique({
            where: { UserId: userId },
        });
        res.json({ request });
    } catch (error) {
        console.error('[API /user/provider-request-status GET] Error:', error);
        res.status(500).json({ message: 'Failed to fetch status' });
    }
});

// Migrat din /api/provider-requests/submit
router.post('/provider-requests/submit', async (req, res) => {
    try {
        const { userId, userName, userEmail, requestedServices } = req.body;
        // ... logica de validare și creare ...
        const newRequest = await prisma.providerRegistrationRequest.create({
            data: {
                UserId: userId,
                UserName: userName,
                UserEmail: userEmail,
                RequestedServices: requestedServices, // as any
                Status: 'Pending',
                RequestDate: new Date(),
            }
        });
        res.status(201).json({ success: true, request: newRequest });
    } catch(error) {
         console.error('[API /user/provider-requests/submit POST] Error:', error);
        res.status(500).json({ message: 'Failed to submit request' });
    }
});

// Endpoint pentru schimbarea parolei inițiale, protejat de middleware-ul de autentificare
router.post('/change-password-initial', async (req: AuthenticatedRequest, res) => {
    // Middleware-ul 'authenticate' a verificat deja token-ul JWT.
    // Verificăm dacă ID-ul din token corespunde cu cel din corpul cererii.
    const authenticatedUserId = req.user?.id;
    const { userId, newPassword } = req.body;

    if (!authenticatedUserId || String(authenticatedUserId) !== String(userId)) {
        return res.status(403).json({ success: false, message: 'Forbidden: Nu aveți permisiunea de a schimba această parolă.' });
    }
    
    // Validarea parolei se face pe client, dar o putem dubla aici pentru siguranță.
    const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]).{8,}$/;
    if (!newPassword || !PASSWORD_REGEX.test(newPassword)) {
        return res.status(400).json({ success: false, message: 'Parola nu îndeplinește cerințele de complexitate.' });
    }

    try {
        const userToUpdate = await prisma.user.findUnique({
            where: { Id: userId },
        });
        if (!userToUpdate) {
            return res.status(404).json({ success: false, message: 'Utilizatorul nu a fost găsit.' });
        }

        const hashedPassword = bcrypt.hashSync(newPassword, 10);
        await prisma.user.update({
            where: { Id: userId },
            data: {
                Password: hashedPassword,
                MustChangePassword: false,
            },
        });

        res.json({ success: true, message: 'Parola a fost schimbată cu succes!' });
    } catch (error) {
        console.error(`[API /user/change-password-initial POST] Eroare:`, error);
        res.status(500).json({ success: false, message: 'Eroare internă la schimbarea parolei.' });
    }
});

// PATCH /users/:userId/profile - Update user profile information
router.patch('/:userId/profile', async (req: AuthenticatedRequest, res) => {
    try {
        const userId = parseInt(req.params.userId, 10);
        const authenticatedUserId = req.user?.id;

        // Verify user can only update their own profile
        if (!authenticatedUserId || String(authenticatedUserId) !== String(userId)) {
            return res.status(403).json({
                success: false,
                message: 'Nu aveți permisiunea de a actualiza acest profil.'
            });
        }

        if (isNaN(userId)) {
            return res.status(400).json({
                success: false,
                message: 'ID utilizator invalid.'
            });
        }

        const { phone, bio, spokenLanguages } = req.body;

        // Validate input data
        if (phone && (typeof phone !== 'string' || phone.length < 8 || phone.length > 15)) {
            return res.status(400).json({
                success: false,
                message: 'Numărul de telefon trebuie să aibă între 8 și 15 caractere.'
            });
        }

        if (bio && (typeof bio !== 'string' || bio.length > 500)) {
            return res.status(400).json({
                success: false,
                message: 'Biografia nu poate depăși 500 de caractere.'
            });
        }

        if (spokenLanguages && (!Array.isArray(spokenLanguages) || spokenLanguages.length === 0)) {
            return res.status(400).json({
                success: false,
                message: 'Selectează cel puțin o limbă.'
            });
        }

        // Prepare update data (only include provided fields)
        const updateData: any = {};
        if (phone !== undefined) updateData.Phone = phone;
        if (bio !== undefined) updateData.Bio = bio;
        if (spokenLanguages !== undefined) updateData.SpokenLanguages = spokenLanguages;

        // Update user profile
        const updatedUser = await prisma.user.update({
            where: { Id: userId },
            data: updateData,
            select: {
                Id: true,
                Phone: true,
                Bio: true,
                SpokenLanguages: true,
                AvatarUrl: true,
            }
        });

        console.log(`[API /users/${userId}/profile PATCH] Profile updated successfully`);

        res.json({
            success: true,
            message: 'Profilul a fost actualizat cu succes.',
            phone: updatedUser.Phone,
            bio: updatedUser.Bio,
            spokenLanguages: updatedUser.SpokenLanguages,
            avatarUrl: updatedUser.AvatarUrl,
        });

    } catch (error) {
        console.error(`[API /users/:userId/profile PATCH] Error:`, error);
        res.status(500).json({
            success: false,
            message: 'Eroare la actualizarea profilului.'
        });
    }
});

// POST /users/:userId/profile/photo - Upload profile photo
router.post('/:userId/profile/photo', upload.single('photo'), async (req: AuthenticatedRequest, res) => {
    try {
        const userId = parseInt(req.params.userId, 10);
        const authenticatedUserId = req.user?.id;

        // Verify user can only update their own profile
        if (!authenticatedUserId || String(authenticatedUserId) !== String(userId)) {
            return res.status(403).json({
                success: false,
                message: 'Nu aveți permisiunea de a actualiza acest profil.'
            });
        }

        if (isNaN(userId)) {
            return res.status(400).json({
                success: false,
                message: 'ID utilizator invalid.'
            });
        }

        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'Nicio fotografie nu a fost încărcată.'
            });
        }

        // Get current user to check for existing avatar
        const currentUser = await prisma.user.findUnique({
            where: { Id: userId },
            select: { AvatarUrl: true }
        });

        // Delete old profile photo if it exists
        if (currentUser?.AvatarUrl) {
            const oldPhotoPath = path.join(process.cwd(), 'uploads', 'profile-photos', path.basename(currentUser.AvatarUrl));
            if (fs.existsSync(oldPhotoPath)) {
                fs.unlinkSync(oldPhotoPath);
                console.log(`[API] Deleted old profile photo: ${oldPhotoPath}`);
            }
        }

        // Generate the URL for the uploaded photo
        const avatarUrl = `/uploads/profile-photos/${req.file.filename}`;

        // Update user's avatar URL in database
        const updatedUser = await prisma.user.update({
            where: { Id: userId },
            data: { AvatarUrl: avatarUrl },
            select: { AvatarUrl: true }
        });

        console.log(`[API /users/${userId}/profile/photo POST] Photo uploaded successfully`);

        res.json({
            success: true,
            message: 'Fotografia a fost încărcată cu succes.',
            avatarUrl: updatedUser.AvatarUrl,
        });

    } catch (error) {
        console.error(`[API /users/:userId/profile/photo POST] Error:`, error);

        // Clean up uploaded file if there was an error
        if (req.file) {
            const filePath = req.file.path;
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }

        res.status(500).json({
            success: false,
            message: 'Eroare la încărcarea fotografiei.'
        });
    }
});

// DELETE /users/:userId/profile/photo - Delete profile photo
router.delete('/:userId/profile/photo', async (req: AuthenticatedRequest, res) => {
    try {
        const userId = parseInt(req.params.userId, 10);
        const authenticatedUserId = req.user?.id;

        // Verify user can only update their own profile
        if (!authenticatedUserId || String(authenticatedUserId) !== String(userId)) {
            return res.status(403).json({
                success: false,
                message: 'Nu aveți permisiunea de a actualiza acest profil.'
            });
        }

        if (isNaN(userId)) {
            return res.status(400).json({
                success: false,
                message: 'ID utilizator invalid.'
            });
        }

        // Get current user to check for existing avatar
        const currentUser = await prisma.user.findUnique({
            where: { Id: userId },
            select: { AvatarUrl: true }
        });

        if (!currentUser?.AvatarUrl) {
            return res.status(404).json({
                success: false,
                message: 'Nu există nicio fotografie de profil de șters.'
            });
        }

        // Delete the physical file
        const photoPath = path.join(process.cwd(), 'uploads', 'profile-photos', path.basename(currentUser.AvatarUrl));
        if (fs.existsSync(photoPath)) {
            fs.unlinkSync(photoPath);
            console.log(`[API] Deleted profile photo: ${photoPath}`);
        }

        // Update user's avatar URL to null in database
        await prisma.user.update({
            where: { Id: userId },
            data: { AvatarUrl: null }
        });

        console.log(`[API /users/${userId}/profile/photo DELETE] Photo deleted successfully`);

        res.json({
            success: true,
            message: 'Fotografia a fost ștearsă cu succes.',
            avatarUrl: null,
        });

    } catch (error) {
        console.error(`[API /users/:userId/profile/photo DELETE] Error:`, error);
        res.status(500).json({
            success: false,
            message: 'Eroare la ștergerea fotografiei.'
        });
    }
});

export default router;
