"use client";

import { Loader2, User, Briefcase } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';

const loadingTranslations = {
  switchingRoles: { ro: "Comutare roluri...", ru: "Переключение ролей...", en: "Switching roles..." },
  loadingDashboard: { ro: "Se încarcă panoul...", ru: "Загрузка панели...", en: "Loading dashboard..." },
  loadingNavigation: { ro: "Se încarcă navigarea...", ru: "Загрузка навигации...", en: "Loading navigation..." },
  pleaseWait: { ro: "Te rugăm să aștepți", ru: "Пожалуйста, подождите", en: "Please wait" },
};

// Role switching loading overlay
export function RoleSwitchingLoader({ currentRole, targetRole }: { 
  currentRole: 'client' | 'provider'; 
  targetRole: 'client' | 'provider';
}) {
  const { translate } = useLanguage();
  
  const CurrentIcon = currentRole === 'client' ? User : Briefcase;
  const TargetIcon = targetRole === 'client' ? User : Briefcase;

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center">
      <Card className="w-full max-w-sm">
        <CardContent className="p-6">
          <div className="flex flex-col items-center space-y-4">
            {/* Role transition animation */}
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-muted rounded-full">
                <CurrentIcon className="w-6 h-6 text-muted-foreground" />
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <TargetIcon className="w-6 h-6 text-primary" />
              </div>
            </div>
            
            <div className="text-center">
              <p className="font-medium">{translate(loadingTranslations, 'switchingRoles')}</p>
              <p className="text-sm text-muted-foreground mt-1">
                {translate(loadingTranslations, 'pleaseWait')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Dashboard loading skeleton
export function DashboardSkeleton() {
  const { translate } = useLanguage();

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar skeleton */}
      <aside className="w-64 border-r bg-card">
        <div className="p-4 space-y-4">
          {/* User profile skeleton */}
          <div className="space-y-3">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          
          {/* Navigation skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-10 w-full" />
            ))}
          </div>
        </div>
      </aside>
      
      {/* Main content skeleton */}
      <main className="flex-1 p-4 space-y-4">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span className="text-sm text-muted-foreground">
            {translate(loadingTranslations, 'loadingDashboard')}
          </span>
        </div>
        
        <Skeleton className="h-8 w-48" />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-8 w-24 mt-4" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  );
}

// Navigation loading skeleton
export function NavigationSkeleton() {
  return (
    <div className="space-y-2">
      <Skeleton className="h-6 w-16" />
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-2">
          <Skeleton className="h-5 w-5" />
          <Skeleton className="h-4 w-24" />
        </div>
      ))}
    </div>
  );
}

// Role indicator loading skeleton
export function RoleIndicatorSkeleton() {
  return (
    <div className="flex items-center gap-2 px-3 py-2 rounded-lg border">
      <Skeleton className="w-4 h-4" />
      <Skeleton className="h-4 w-32" />
    </div>
  );
}

// Inline loading spinner
export function InlineLoader({ message, className }: { message?: string; className?: string }) {
  const { translate } = useLanguage();

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Loader2 className="w-4 h-4 animate-spin" />
      <span className="text-sm text-muted-foreground">
        {message || translate(loadingTranslations, 'pleaseWait')}
      </span>
    </div>
  );
}

// Page transition loader
export function PageTransitionLoader() {
  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <div className="h-1 bg-primary/20">
        <div className="h-full bg-primary animate-pulse" style={{
          animation: 'loading-bar 2s ease-in-out infinite'
        }} />
      </div>
      <style jsx>{`
        @keyframes loading-bar {
          0% { width: 0%; }
          50% { width: 70%; }
          100% { width: 100%; }
        }
      `}</style>
    </div>
  );
}

// Content loading placeholder
export function ContentPlaceholder({ 
  title, 
  description, 
  className 
}: { 
  title?: string; 
  description?: string; 
  className?: string;
}) {
  const { translate } = useLanguage();

  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      <div className="p-4 bg-muted rounded-full mb-4">
        <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium mb-2">
        {title || translate(loadingTranslations, 'loadingDashboard')}
      </h3>
      <p className="text-muted-foreground max-w-sm">
        {description || translate(loadingTranslations, 'pleaseWait')}
      </p>
    </div>
  );
}
