"use client";
import type { ReactNode } from 'react';
import { createContext, useContext, useState, useCallback } from 'react';

export interface Language {
  code: string;
  label: string;
}

interface LanguageContextType {
  currentLanguage: Language;
  setCurrentLanguage: (language: Language) => void;
  languages: Language[];
  translate: (translations: Record<string, Record<string, string>>, key: string, fallback?: string) => string;
}

const supportedLanguages: Language[] = [
  { code: 'ro', label: 'Română' },
  { code: 'ru', label: 'Русский' },
  { code: 'en', label: 'English' },
];

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [currentLanguage, setCurrentLanguageState] = useState<Language>(supportedLanguages[0]); // Default to Romanian

  const setCurrentLanguage = useCallback((language: Language) => {
    setCurrentLanguageState(language);
  }, []);

  const translate = useCallback((
    translations: Record<string, Record<string, string>>,
    key: string,
    fallback?: string
  ): string => {
    // Check if translations are in the format: { key: { lang: text } }
    const translationSet = translations[key];
    if (translationSet && typeof translationSet === 'object') {
      return translationSet[currentLanguage.code] || translationSet['ro'] || fallback || `Missing: ${key}`;
    }

    // Check if translations are in the format: { lang: { key: text } }
    const languageSet = translations[currentLanguage.code] || translations['ro'];
    if (languageSet && typeof languageSet === 'object' && languageSet[key]) {
      return languageSet[key];
    }

    return fallback || `Missing key: ${key}`;
  }, [currentLanguage.code]);

  return (
    <LanguageContext.Provider value={{ currentLanguage, setCurrentLanguage, languages: supportedLanguages, translate }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
