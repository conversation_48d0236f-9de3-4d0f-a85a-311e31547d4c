"use client";

import { User, Briefcase } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';

type Role = 'client' | 'provider';

interface RoleIndicatorProps {
  currentRole: Role;
  className?: string;
  variant?: 'default' | 'compact' | 'minimal';
}

const roleIndicatorTranslations = {
  currentlyViewing: { ro: "Vizualizezi în prezent:", ru: "В настоящее время просматриваете:", en: "Currently viewing:" },
  clientDashboard: { ro: "Panoul Client", ru: "Панель клиента", en: "Client Dashboard" },
  providerDashboard: { ro: "Panoul Prestator", ru: "Панель поставщика", en: "Provider Dashboard" },
  client: { ro: "Client", ru: "Клиент", en: "Client" },
  provider: { ro: "Prestator", ru: "Поставщик", en: "Provider" },
};

export function RoleIndicator({ currentRole, className, variant = 'default' }: RoleIndicatorProps) {
  const { translate } = useLanguage();

  const roleConfig = {
    client: {
      icon: User,
      label: translate(roleIndicatorTranslations, 'clientDashboard'),
      shortLabel: translate(roleIndicatorTranslations, 'client'),
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
      textColor: 'text-blue-700 dark:text-blue-300',
      iconColor: 'text-blue-600 dark:text-blue-400',
      borderColor: 'border-blue-200 dark:border-blue-800'
    },
    provider: {
      icon: Briefcase,
      label: translate(roleIndicatorTranslations, 'providerDashboard'),
      shortLabel: translate(roleIndicatorTranslations, 'provider'),
      bgColor: 'bg-green-50 dark:bg-green-950/20',
      textColor: 'text-green-700 dark:text-green-300',
      iconColor: 'text-green-600 dark:text-green-400',
      borderColor: 'border-green-200 dark:border-green-800'
    }
  };

  const config = roleConfig[currentRole];
  const Icon = config.icon;

  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <Icon className={cn("w-3 h-3", config.iconColor)} />
        <span className={cn("text-xs font-medium", config.textColor)}>
          {config.shortLabel}
        </span>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn(
        "flex items-center gap-2 px-2 py-1 rounded text-xs font-medium",
        config.bgColor,
        config.textColor,
        className
      )}>
        <Icon className={cn("w-3 h-3", config.iconColor)} />
        <span>{config.shortLabel}</span>
      </div>
    );
  }

  return (
    <div 
      className={cn(
        "flex items-center gap-2 px-3 py-2 rounded-lg border",
        config.bgColor,
        config.textColor,
        config.borderColor,
        className
      )}
      role="status"
      aria-live="polite"
      aria-label={`${translate(roleIndicatorTranslations, 'currentlyViewing')} ${config.label}`}
    >
      <Icon className={cn("w-4 h-4", config.iconColor)} aria-hidden="true" />
      <span className="text-sm font-medium">
        <span className="hidden sm:inline">
          {translate(roleIndicatorTranslations, 'currentlyViewing')}{' '}
        </span>
        {config.label}
      </span>
    </div>
  );
}

// Breadcrumb-style role indicator for navigation
export function RoleBreadcrumb({ currentRole, className }: { currentRole: Role; className?: string }) {
  const { translate } = useLanguage();
  
  const roleConfig = {
    client: {
      icon: User,
      label: translate(roleIndicatorTranslations, 'client'),
      color: 'text-blue-600 dark:text-blue-400'
    },
    provider: {
      icon: Briefcase,
      label: translate(roleIndicatorTranslations, 'provider'),
      color: 'text-green-600 dark:text-green-400'
    }
  };

  const config = roleConfig[currentRole];
  const Icon = config.icon;

  return (
    <div className={cn("flex items-center gap-1 text-sm", className)}>
      <span className="text-muted-foreground">Dashboard</span>
      <span className="text-muted-foreground">/</span>
      <div className="flex items-center gap-1">
        <Icon className={cn("w-3 h-3", config.color)} />
        <span className={cn("font-medium", config.color)}>
          {config.label}
        </span>
      </div>
    </div>
  );
}
