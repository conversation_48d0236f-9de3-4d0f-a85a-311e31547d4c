
import type {Metadata} from 'next';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { cn } from "@/lib/utils";
import { LanguageProvider } from '@/contexts/language-context';
import { NextAuthProvider } from './providers';
import { UserProvider } from '@/contexts/user-context'; // Import UserProvider

export const metadata: Metadata = {
  title: 'Bonami Moldova',
  description: 'Găsește îngrijire de calitate pentru familia ta în Moldova.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ro" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
      </head>
      <body className={cn("min-h-screen bg-background font-body antialiased", "font-body")}>
        <LanguageProvider>
          <NextAuthProvider>
            <UserProvider>
              {children}
              <Toaster />
            </UserProvider>
          </NextAuthProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
