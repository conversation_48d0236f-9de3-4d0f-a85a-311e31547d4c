
"use client";

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
// Remove direct Prisma type imports
// import type { ServiceCategorySlug, AdvertisedService as PrismaAdvertisedService, User as PrismaUser, NannyServiceDetails, ElderCareServiceDetails, CleaningServiceDetails, TutoringServiceDetails, CookingServiceDetails } from '@prisma/client';
import { getFullLocationPathDisplayHelper } from '@/lib/locations';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'; // Import CardDescription
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

// Define an interface for the data structure received from the API
interface ServiceDetailsFromApi {
  Id: number;
  ServiceName: string;
  Description: string | null;
  Status: string; // Assuming Status is returned as a string
  ServiceCategorySlug: string; // Using slug as a string
  Provider: { 
    FullName: string | null;
  } | null;
  // Specific details, simplified for frontend
  NannyServiceDetails: {
    ExperienceYears?: number | null;
    LocationValue?: string | null;
    PricePerHour?: number | null;
    PricePerDay?: number | null;
    PreferredAge_0_2?: boolean | null;
    PreferredAge_3_6?: boolean | null;
    PreferredAge_7_plus?: boolean | null;
  } | null;
  ElderCareServiceDetails: {
    ExperienceYears?: number | null;
    LocationValue?: string | null;
    PricePerHour?: number | null;
    PricePerDay?: number | null;
    TypeMobil?: boolean | null;
    TypePartialImobilizat?: boolean | null;
    TypeCompletImobilizat?: boolean | null;
  } | null;
  CleaningServiceDetails: {
     ExperienceYears?: number | null;
    LocationValue?: string | null;
    PricePerHour?: number | null;
    PricePerDay?: number | null;
    PropertyTypeApartments?: boolean | null;
    PropertyTypeHouses?: boolean | null;
    PropertyTypeOffices?: boolean | null;
  } | null;
   TutoringServiceDetails: {
     ExperienceYears?: number | null;
    LocationValue?: string | null;
    PricePerHour?: number | null;
    PricePerDay?: number | null;
    Grades_1_4?: boolean | null;
    Grades_5_8?: boolean | null;
    Grades_9_12?: boolean | null;
  } | null;
   CookingServiceDetails: {
     ExperienceYears?: number | null;
    LocationValue?: string | null;
    PricePerHour?: number | null;
    PricePerDay?: number | null;
    CuisineTypeTraditional?: boolean | null;
    CuisineTypeVegetarian?: boolean | null;
  } | null;
   Category: { // Add Category field as returned by API (simplified)
      NameKey: string;
      Slug: string;
   } | null;
}

// Helper component for displaying details in a consistent way
const DetailItem = ({ label, value }: { label: string; value: string | number | null | undefined }) => {
  if (value === null || value === undefined || value === '') return null;
  return (
    <div className="py-2 px-3 odd:bg-muted/30 rounded-md grid grid-cols-1 md:grid-cols-3 gap-1 items-center">
      <dt className="font-medium text-muted-foreground">{label}</dt>
      <dd className="md:col-span-2 text-foreground break-words">{String(value)}</dd>
    </div>
  );
};

// Helper component for boolean (switch-like) values
const BooleanDetailItem = ({ label, value }: { label: string; value: boolean | undefined | null }) => {
  return (
    <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm bg-background">
        <Label htmlFor={`switch-${label.replace(/s+/g, '-')}`} className="text-sm font-medium text-foreground cursor-default">
            {label}
        </Label>
        <Switch
            id={`switch-${label.replace(/s+/g, '-')}`}
            checked={!!value}
            disabled
            aria-readonly
        />
    </div>
  );
};

// Use the new interface for service details
interface ServiceDetailsDialogProps {
  serviceId: number | null;
  isOpen: boolean;
  onClose: () => void;
}

const getStatusBadgeClass = (status: string) => {
    switch(status) {
      case 'Activ': return 'bg-green-100 text-green-700 border-green-200';
      case 'PendingReview': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Rejected': return 'bg-red-100 text-red-700 border-red-200';
      case 'Inactiv': return 'bg-gray-100 text-gray-700 border-gray-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
  }
}

export function ServiceDetailsDialog({ serviceId, isOpen, onClose }: ServiceDetailsDialogProps) {
  const { translate } = useLanguage();
  // Use the new interface
  const [service, setService] = useState<ServiceDetailsFromApi | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && serviceId) {
        const fetchServiceDetails = async () => {
            setIsLoading(true);
            setError(null);
            setService(null);
            try {
                const response = await fetch(`/api/proxy/provider/services/${serviceId}`);
                if (!response.ok) {
                    const errData = await response.json().catch(() => ({}));
                    throw new Error(errData.message || "Nu s-au putut prelua detaliile serviciului.");
                }
                const data = await response.json();
                 // Assuming the API returns an object with a 'service' key
                setService(data.service as ServiceDetailsFromApi);
            } catch (err) {
                const msg = err instanceof Error ? err.message : "Eroare necunoscută.";
                setError(msg);
            } finally {
                setIsLoading(false);
            }
        };
        fetchServiceDetails();
    }
  }, [isOpen, serviceId]);


  const renderSpecificDetails = (service: ServiceDetailsFromApi) => {
    let specificDetailsContent: React.ReactNode = null;
    let details: any = null;

    switch (service.ServiceCategorySlug) {
        case 'Nanny': details = service.NannyServiceDetails; break;
        case 'ElderCare': details = service.ElderCareServiceDetails; break;
        case 'Cleaning': details = service.CleaningServiceDetails; break;
        case 'Tutoring': details = service.TutoringServiceDetails; break;
        case 'Cooking': details = service.CookingServiceDetails; break;
    }

    if (!details) {
        return (
            <Card>
                <CardHeader><CardTitle className="text-lg">Detalii Specifice Categoriei</CardTitle></CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">Nu sunt disponibile detalii specifice pentru acest serviciu.</p>
                </CardContent>
            </Card>
        );
    }
    
    // Nanny Details
    if (service.ServiceCategorySlug === 'Nanny') {
        specificDetailsContent = (
            <div className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'childcareAge0_2')} value={details.PreferredAge_0_2} />
                    <BooleanDetailItem label={translate(commonTranslations, 'childcareAge3_6')} value={details.PreferredAge_3_6} />
                    <BooleanDetailItem label={translate(commonTranslations, 'childcareAge7_plus')} value={details.PreferredAge_7_plus} />
                </div>
            </div>
        );
    } 
    // Elder Care Details
    else if (service.ServiceCategorySlug === 'ElderCare') {
        specificDetailsContent = (
             <div className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'elderCareTypeMobil')} value={details.TypeMobil} />
                    <BooleanDetailItem label={translate(commonTranslations, 'elderCareTypePartialImobilizat')} value={details.TypePartialImobilizat} />
                    <BooleanDetailItem label={translate(commonTranslations, 'elderCareTypeCompletImobilizat')} value={details.TypeCompletImobilizat} />
                </div>
            </div>
        );
    } 
    // Cleaning Details
    else if (service.ServiceCategorySlug === 'Cleaning') {
        specificDetailsContent = (
             <div className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'cleaningPropertyTypeApartments')} value={details.PropertyTypeApartments} />
                    <BooleanDetailItem label={translate(commonTranslations, 'cleaningPropertyTypeHouses')} value={details.PropertyTypeHouses} />
                    <BooleanDetailItem label={translate(commonTranslations, 'cleaningPropertyTypeOffices')} value={details.PropertyTypeOffices} />
                </div>
            </div>
        );
    } 
    // Tutoring Details
    else if (service.ServiceCategorySlug === 'Tutoring') {
        specificDetailsContent = (
             <div className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'tutoringGrades_1_4')} value={details.Grades_1_4} />
                    <BooleanDetailItem label={translate(commonTranslations, 'tutoringGrades_5_8')} value={details.Grades_5_8} />
                    <BooleanDetailItem label={translate(commonTranslations, 'tutoringGrades_9_12')} value={details.Grades_9_12} />
                </div>
            </div>
        );
    } 
    // Cooking Details
    else if (service.ServiceCategorySlug === 'Cooking') {
        specificDetailsContent = (
            <div className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'cookingCuisineTypeTraditional')} value={details.CuisineTypeTraditional} />
                    <BooleanDetailItem label={translate(commonTranslations, 'cookingCuisineTypeVegetarian')} value={details.CuisineTypeVegetarian} />
                </div>
            </div>
        );
    }
    
    return (
        <Card>
            <CardHeader><CardTitle className="text-lg">Detalii Specifice Categoriei</CardTitle></CardHeader>
            <CardContent>
                {specificDetailsContent || <p className="text-sm text-muted-foreground">Nu sunt disponibile detalii specifice pentru acest serviciu.</p>}
            </CardContent>
        </Card>
    );
  };
  
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-48">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col justify-center items-center h-48 text-destructive">
          <AlertTriangle className="w-8 h-8 mb-2" />
          <p>{error}</p>
        </div>
      );
    }
    
    if (service) {
      const details = service.NannyServiceDetails || service.ElderCareServiceDetails || service.CleaningServiceDetails || service.TutoringServiceDetails || service.CookingServiceDetails;
      const statusKey = `status${service.Status}` as keyof typeof commonTranslations;
      const translatedStatus = service.Status ? translate(commonTranslations, statusKey, service.Status) : 'N/A';
      // Use service.Category?.NameKey instead of service.Category?.NameKey
      const categoryName = service.Category?.NameKey ? translate(commonTranslations, service.Category.NameKey as keyof typeof commonTranslations) : 'N/A';

      return (
        <ScrollArea className="h-full pr-6 -mr-6">
          <div className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row justify-between items-start">
                  <div>
                      <CardTitle className="text-lg">{service.ServiceName}</CardTitle>
                      <CardDescription>
                          ID: {service.Id} | {translate(commonTranslations, 'adminTableProviderName')}: {service.Provider?.FullName || 'N/A'} | {translate(commonTranslations, 'serviceCategory')}: {categoryName} {/* Display Category Name */}
                      </CardDescription>
                  </div>
                  <Badge variant="outline" className={cn("text-xs font-semibold", getStatusBadgeClass(service.Status))}>
                      {translatedStatus}
                  </Badge>
              </CardHeader>
              <CardContent>
                <dl className="space-y-1 text-sm">
                  <div className="py-2 px-3 odd:bg-muted/30 rounded-md grid grid-cols-1 md:grid-cols-3 gap-1 items-start">
                      <dt className="font-medium text-muted-foreground">Descriere</dt>
                      <dd className="md:col-span-2 text-foreground break-words whitespace-pre-line">{service.Description || 'N/A'}</dd>
                  </div>
                   {details && (
                    <>
                      {details.ExperienceYears !== undefined && details.ExperienceYears !== null && <DetailItem label="Experiență (ani)" value={details.ExperienceYears} />}
                      {details.LocationValue !== undefined && details.LocationValue !== null && <DetailItem label="Locație" value={getFullLocationPathDisplayHelper(details.LocationValue, translate, commonTranslations)} />}
                      {details.PricePerHour !== undefined && details.PricePerHour !== null && <DetailItem label="Preț/oră (MDL)" value={details.PricePerHour} />}
                      {details.PricePerDay !== undefined && details.PricePerDay !== null && <DetailItem label="Preț/zi (MDL)" value={details.PricePerDay} />}
                    </>
                  )}
                </dl>
              </CardContent>
            </Card>
            {renderSpecificDetails(service)}
          </div>
        </ScrollArea>
      )
    }

    return (
        <div className="flex flex-col justify-center items-center h-48 text-muted-foreground">
          <AlertTriangle className="w-8 h-8 mb-2" />
          <p>Nu s-au putut încărca detaliile serviciului.</p>
        </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Detalii Serviciu</DialogTitle>
          <DialogDescription>Vizualizare completă a detaliilor serviciului.</DialogDescription>
        </DialogHeader>
        <div className="flex-grow overflow-hidden py-2">
            {renderContent()}
        </div>
        <DialogFooter className="mt-auto pt-4 border-t sm:justify-end">
          <DialogClose asChild>
            <Button variant="secondary">Închide</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
