
// This would be your API client.
// For now, we'll use a placeholder URL and logic.
// IMPORTANT: In a real app, use environment variables for the API URL.
const API_URL = 'http://localhost:9001/api';

export async function apiFetch(endpoint: string, options: RequestInit = {}) {
  try {
    const response = await fetch(`${API_URL}/${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      // The backend now sends a 'message' field in error responses.
      throw new Error(data.message || `API Error: ${response.status}`);
    }

    return data;
  } catch (error) {
    if (error instanceof TypeError && error.message === 'Network request failed') {
        throw new Error('Nu se poate conecta la server. Verificați conexiunea la internet sau adresa serverului.');
    }
    // Re-throw other errors (including the one from response.ok check)
    throw error;
  }
}
