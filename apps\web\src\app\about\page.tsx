
"use client";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";
import { useLanguage } from '@/contexts/language-context';

const aboutPageTranslations = {
  pageTitle: { ro: "Despre mesami", ru: "О mesami", en: "About mesami" },
  missionTitle: { ro: "Misiunea Noastră", ru: "Наша миссия", en: "Our Mission" },
  missionP1: {
    ro: "La mesami, misiunea noastră este de a conecta familiile din Moldova cu profesioniști de încredere în domeniul îngrijirii personale. Înțelegem cât de important este să găsești persoana potrivită pentru cei dragi, fie că este vorba de îngrijirea copiilor, a bă<PERSON><PERSON><PERSON>r, servicii de curățenie sau suport educațional.",
    ru: "В mesami наша миссия - связывать семьи в Молдове с надежными профессионалами в области личного ухода. Мы понимаем, насколько важно найти подходящего человека для ваших близких, будь то уход за детьми, пожилыми людьми, услуги по уборке или образовательная поддержка.",
    en: "At mesami, our mission is to connect families in Moldova with trusted professionals in personal care. We understand how important it is to find the right person for your loved ones, whether it's childcare, elderly care, cleaning services, or educational support."
  },
  missionP2: {
    ro: "Ne dedicăm să oferim o platformă sigură, ușor de utilizat și eficientă, care simplifică procesul de căutare și selecție, asigurând liniștea și satisfacția clienților noștri.",
    ru: "Мы стремимся предоставить безопасную, простую в использовании и эффективную платформу, которая упрощает процесс поиска и выбора, обеспечивая спокойствие и удовлетворение наших клиентов.",
    en: "We are dedicated to providing a safe, easy-to-use, and efficient platform that simplifies the search and selection process, ensuring the peace of mind and satisfaction of our customers."
  },
  valuesTitle: { ro: "Valorile Noastre", ru: "Наши ценности", en: "Our Values" },
  
  valueTrustLabel: { ro: "Încredere", ru: "Доверие", en: "Trust" },
  valueTrustText: { ro: "Verificăm cu atenție toți prestatorii pentru a asigura un mediu sigur.", ru: "Мы тщательно проверяем всех поставщиков, чтобы обеспечить безопасную среду.", en: "We carefully vet all providers to ensure a safe environment." },
  
  valueQualityLabel: { ro: "Calitate", ru: "Качество", en: "Quality" },
  valueQualityText: { ro: "Promovăm standarde înalte pentru toate serviciile listate.", ru: "Мы продвигаем высокие стандарты для всех перечисленных услуг.", en: "We promote high standards for all listed services." },
  
  valueCommunityLabel: { ro: "Comunitate", ru: "Сообщество", en: "Community" },
  valueCommunityText: { ro: "Construim o rețea de sprijin pentru familii și profesioniști.", ru: "Мы строим сеть поддержки для семей и профессионалов.", en: "We are building a support network for families and professionals." },
  
  valueAccessibilityLabel: { ro: "Accesibilitate", ru: "Доступность", en: "Accessibility" },
  valueAccessibilityText: { ro: "Facem serviciile de îngrijire mai ușor de găsit și de contractat.", ru: "Мы делаем услуги по уходу более доступными для поиска и найма.", en: "We make care services easier to find and contract." },
  
  valueInnovationLabel: { ro: "Inovație", ru: "Инновации", en: "Innovation" },
  valueInnovationText: { ro: "Utilizăm tehnologia pentru a îmbunătăți constant experiența utilizatorilor.", ru: "Мы используем технологии для постоянного улучшения пользовательского опыта.", en: "We use technology to constantly improve the user experience." },
  
  whyTitle: { ro: "De Ce mesami?", ru: "Почему mesami?", en: "Why mesami?" },
  whyP1: {
    ro: "Într-o lume agitată, găsirea unui ajutor de nădejde poate fi o provocare. mesami a fost creat pentru a rezolva această problemă, oferind o punte între nevoile familiilor și expertiza profesioniștilor. Cu un proces simplificat de căutare, recenzii transparente și un angajament față de siguranță, suntem partenerul tău de încredere în îngrijirea familiei.",
    ru: "В суетливом мире найти надежную помощь может быть непросто. mesami был создан для решения этой проблемы, предлагая мост между потребностями семей и опытом профессионалов. Благодаря упрощенному процессу поиска, прозрачным отзывам и приверженности безопасности, мы являемся вашим надежным партнером в уходе за семьей.",
    en: "In a hectic world, finding reliable help can be a challenge. mesami was created to solve this problem, offering a bridge between families' needs and professionals' expertise. With a simplified search process, transparent reviews, and a commitment to safety, we are your trusted partner in family care."
  },
  teamImageAlt: { ro: "Echipa mesami", ru: "Команда mesami", en: "mesami Team" },
};

export default function AboutPage() {
  const { translate } = useLanguage();

  const valueItems = [
    { labelKey: 'valueTrustLabel', textKey: 'valueTrustText' },
    { labelKey: 'valueQualityLabel', textKey: 'valueQualityText' },
    { labelKey: 'valueCommunityLabel', textKey: 'valueCommunityText' },
    { labelKey: 'valueAccessibilityLabel', textKey: 'valueAccessibilityText' },
    { labelKey: 'valueInnovationLabel', textKey: 'valueInnovationText' },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8 font-headline">{translate(aboutPageTranslations, 'pageTitle')}</h1>
          
          <Card className="mb-8 shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl font-headline">{translate(aboutPageTranslations, 'missionTitle')}</CardTitle>
            </CardHeader>
            <CardContent className="text-lg text-foreground/80 space-y-4">
              <p>{translate(aboutPageTranslations, 'missionP1')}</p>
              <p>{translate(aboutPageTranslations, 'missionP2')}</p>
            </CardContent>
          </Card>

          <div className="mb-8"> {/* Simplified layout, removing grid for the image */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="font-headline">{translate(aboutPageTranslations, 'valuesTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc list-inside space-y-2 text-foreground/80">
                  {valueItems.map(item => (
                    <li key={item.labelKey}>
                      <strong>{translate(aboutPageTranslations, item.labelKey)}:</strong>
                      {' '}{translate(aboutPageTranslations, item.textKey)}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
            {/* Image block removed */}
          </div>
          
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl font-headline text-center">{translate(aboutPageTranslations, 'whyTitle')}</CardTitle>
            </CardHeader>
            <CardContent className="text-lg text-foreground/80 space-y-4">
              <p>{translate(aboutPageTranslations, 'whyP1')}</p>
            </CardContent>
          </Card>

        </div>
      </main>
      <Footer />
    </div>
  );
}
