{"name": "api", "version": "1.0.0", "private": true, "main": "src/index.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@prisma/client": "^5.22.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "vaul": "^1.1.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^2.0.0", "@types/swagger-jsdoc": "^6.0.4", "prisma": "^5.22.0", "ts-node-dev": "^2.0.0", "tsx": "^4.16.2"}, "prisma": {"seed": "tsx prisma/seed.ts"}}