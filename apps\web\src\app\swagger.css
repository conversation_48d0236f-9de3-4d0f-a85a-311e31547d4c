
/* Custom styles for Swagger UI to better match the site's theme */

/* Main container */
.swagger-ui {
  font-family: var(--font-body), sans-serif;
  color: hsl(var(--foreground));
}

/* Hide the default top bar with the Swagger logo */
.swagger-ui .topbar {
  display: none;
}

/* Style the main background */
.swagger-ui .scheme-container {
  background-color: hsl(var(--card));
  padding: 1.5rem;
  box-shadow: none;
  border-radius: var(--radius);
  border: 1px solid hsl(var(--border));
}

/* General text and links */
.swagger-ui .info .title {
  color: hsl(var(--foreground));
  font-family: var(--font-headline), sans-serif;
}

.swagger-ui .info .description,
.swagger-ui .info .base-url,
.swagger-ui .info .contact,
.swagger-ui .info .license {
  color: hsl(var(--muted-foreground));
}

.swagger-ui a, .swagger-ui a:visited {
  color: hsl(var(--primary));
}

/* Operation blocks (the GET, POST, etc. sections) */
.swagger-ui .opblock, .swagger-ui .opblock .opblock-section-header {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  box-shadow: none;
}

.swagger-ui .opblock .opblock-summary {
  border-color: hsl(var(--border));
}

.swagger-ui .opblock .opblock-summary-method {
  border-radius: calc(var(--radius) - 2px);
}

.swagger-ui .opblock.opblock-get {
    background: hsl(var(--primary) / 0.05);
    border-color: hsl(var(--primary) / 0.3);
}
.swagger-ui .opblock.opblock-get .opblock-summary-method {
    background: hsl(var(--primary));
}
.swagger-ui .opblock.opblock-get .opblock-summary-path a,
.swagger-ui .opblock.opblock-get .opblock-summary-description {
    color: hsl(var(--primary));
}

.swagger-ui .opblock.opblock-post {
    background: hsl(var(--chart-4) / 0.05);
    border-color: hsl(var(--chart-4) / 0.3);
}
.swagger-ui .opblock.opblock-post .opblock-summary-method {
    background: hsl(var(--chart-4));
}

/* Parameters and Response sections */
.swagger-ui .opblock-body .opblock-section-header {
  background-color: hsl(var(--muted) / 0.5);
}
.swagger-ui .opblock-body .opblock-section-header h4 {
    color: hsl(var(--foreground));
}

.swagger-ui table thead tr th {
  background-color: hsl(var(--background));
  color: hsl(var(--muted-foreground));
  border-bottom-color: hsl(var(--border));
}

.swagger-ui .responses-table tr td {
  border-color: hsl(var(--border));
}

.swagger-ui .btn.try-out {
  border-color: hsl(var(--primary));
  color: hsl(var(--primary));
}

.swagger-ui .btn.try-out:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-color: hsl(var(--primary));
}

.swagger-ui .btn.execute {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

/* Models / Schemas */
.swagger-ui .model-title {
  color: hsl(var(--foreground));
}

.swagger-ui .model-toggle:after {
    border-color: hsl(var(--foreground));
}

.swagger-ui .model-container {
    background-color: hsl(var(--muted) / 0.3);
    border-color: hsl(var(--border));
    border-radius: var(--radius);
}

.swagger-ui .model, .swagger-ui .model .property.primitive {
    color: hsl(var(--foreground));
}

.swagger-ui .prop-type {
    color: hsl(var(--accent));
}

.swagger-ui .prop-format {
    color: hsl(var(--muted-foreground));
}
