
"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { MessageSquare, AlertTriangle, Loader2 } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNowStrict } from 'date-fns';
import { ro, ru, enUS as en } from 'date-fns/locale';
import { UserSilhouetteIcon } from "@/components/ui/user-silhouette-icon";

interface ChatRoomWithDetails {
  id: string;
  otherUserName: string;
  otherUserAvatar?: string | null;
  lastMessageContent?: string | null;
  lastMessageAt?: string | null;
  serviceName?: string;
}

const dateFnsLocalesMap: Record<string, Locale> = { ro, ru, en };

const chatInboxPageTranslations = {
  pageTitle: { ro: "Mesajele Mele", ru: "Мои Сообщения", en: "My Messages" },
  pageDescription: { ro: "Vezi și răspunde la conversațiile tale.", ru: "Просматривайте и отвечайте на ваши беседы.", en: "View and reply to your conversations." },
  noConversations: { ro: "Nu ai nicio conversație încă.", ru: "У вас пока нет бесед.", en: "You have no conversations yet." },
  startConversationHint: { ro: "Poți iniția o conversație de pe pagina unui furnizor.", ru: "Вы можете начать беседу со страницы поставщика.", en: "You can initiate a conversation from a provider's page." },
  viewConversation: { ro: "Vezi Conversația", ru: "Посмотреть беседу", en: "View Conversation" },
  errorFetchingConversations: { ro: "Eroare la preluarea conversațiilor.", ru: "Ошибка при загрузке бесед.", en: "Error fetching conversations." },
  lastMessagePrefix: { ro: "Ultimul mesaj:", ru: "Последнее сообщение:", en: "Last message:" },
  serviceContextPrefix: { ro: "Serviciu:", ru: "Услуга:", en: "Service:" },
  noLastMessage: { ro: "Niciun mesaj. Începe conversația!", ru: "Нет сообщений. Начните беседу!", en: "No messages. Start the conversation!"}
};

const getInitials = (name: string | null | undefined): string => name ? name.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() : 'U';

export default function ChatInboxPage() {
  const { translate, currentLanguage } = useLanguage();
  const selectedDateLocale = dateFnsLocalesMap[currentLanguage.code] || ro;
  const [chatRooms, setChatRooms] = useState<ChatRoomWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRooms = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/proxy/chat/rooms');
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || translate(chatInboxPageTranslations, 'errorFetchingConversations'));
        }
        const data = await response.json();
        setChatRooms(data.rooms || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : translate(chatInboxPageTranslations, 'errorFetchingConversations'));
      } finally {
        setIsLoading(false);
      }
    };
    fetchRooms();
  }, [translate]);

  return (
    <div className="flex flex-col h-full space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-headline flex items-center">
            <MessageSquare className="w-6 h-6 mr-2 text-primary"/>
            {translate(chatInboxPageTranslations, 'pageTitle')}
          </CardTitle>
          <CardDescription>{translate(chatInboxPageTranslations, 'pageDescription')}</CardDescription>
        </CardHeader>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
        </div>
      )}

      {error && (
        <Card>
          <CardContent className="py-10 text-center text-destructive">
            <AlertTriangle className="mx-auto h-8 w-8 mb-2" />
            <p>{error}</p>
          </CardContent>
        </Card>
      )}

      {!isLoading && !error && chatRooms.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <MessageSquare className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{translate(chatInboxPageTranslations, 'noConversations')}</p>
            <p className="text-sm text-muted-foreground mt-1">{translate(chatInboxPageTranslations, 'startConversationHint')}</p>
          </CardContent>
        </Card>
      )}

      {!isLoading && !error && chatRooms.length > 0 && (
        <div className="flex-1 overflow-y-auto pr-2 -mr-2">
            <div className="space-y-4">
            {chatRooms.map(room => (
                <Card key={room.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <div className="flex items-center space-x-4 flex-1">
                    <Avatar className="h-12 w-12">
                        <AvatarImage src={room.otherUserAvatar || undefined} />
                        <AvatarFallback><UserSilhouetteIcon className="w-6 h-6"/></AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                        <p className="font-semibold truncate">{room.otherUserName}</p>
                        {room.serviceName && <p className="text-xs text-muted-foreground">{translate(chatInboxPageTranslations, 'serviceContextPrefix')} {room.serviceName}</p>}
                        <p className="text-sm text-muted-foreground truncate max-w-xs mt-1">
                        {room.lastMessageContent ? room.lastMessageContent : translate(chatInboxPageTranslations, 'noLastMessage')}
                        </p>
                    </div>
                    </div>
                    <div className="text-right flex flex-col items-end shrink-0 self-stretch justify-between w-full sm:w-auto">
                    {room.lastMessageAt && (
                        <p className="text-xs text-muted-foreground mb-2 whitespace-nowrap">
                        {formatDistanceToNowStrict(new Date(room.lastMessageAt), { addSuffix: true, locale: selectedDateLocale })}
                        </p>
                    )}
                    <Button variant="outline" size="sm" asChild className="mt-auto">
                        <Link href={`/dashboard/chat/${room.id}`}>{translate(chatInboxPageTranslations, 'viewConversation')}</Link>
                    </Button>
                    </div>
                </CardContent>
                </Card>
            ))}
            </div>
        </div>
      )}
    </div>
  );
}
