import { Router, type Response, type Request } from 'express';
import prisma from '../lib/db';
import { commonTranslations } from '@repo/translations';
import { EnhancedLocationService } from '../services/enhanced-location-service';

const router = Router();

// Helper function for API translation (similar to services.ts)
function translateApi(translations: Record<string, Record<string, string>>, key: string, lang: string = 'ro'): string {
  const translationObj = translations[key];
  if (!translationObj) return key;
  return translationObj[lang] || translationObj['ro'] || key;
}

// Helper function to build location display name with hierarchy
function getLocationDisplayName(location: any, lang: string = 'ro'): string {
  // Use the stored Name field which already contains the proper hierarchy
  // or translate using the translation key if needed
  if (lang !== 'ro') {
    return translateApi(commonTranslations, location.TranslationKey, lang);
  }

  return location.Name;
}

/**
 * @swagger
 * /locations:
 *   get:
 *     summary: Get all locations
 *     tags: [Public]
 *     parameters:
 *       - in: query
 *         name: lang
 *         schema:
 *           type: string
 *           enum: [ro, ru, en]
 *           default: ro
 *         description: Language for location names
 *       - in: query
 *         name: includeAll
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include the "All Moldova" option
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by location type
 *     responses:
 *       200:
 *         description: List of locations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 locations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       slug:
 *                         type: string
 *                       translationKey:
 *                         type: string
 *                       name:
 *                         type: string
 *                       displayName:
 *                         type: string
 *                       type:
 *                         type: string

 *                       parentId:
 *                         type: integer
 *                       sortOrder:
 *                         type: integer
 *                       isCapital:
 *                         type: boolean
 *                       specialType:
 *                         type: string
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const { lang = 'ro', includeAll = 'false', type } = req.query;
    const shouldIncludeAll = includeAll === 'true';
    
    let whereClause: any = { IsActive: true };
    
    // Filter by type if specified
    if (type) {
      whereClause.Type = type;
    }
    
    // If not including "All", exclude the top-level "all" location
    if (!shouldIncludeAll) {
      whereClause.Slug = { not: 'all' };
    }
    
    const locations = await prisma.location.findMany({
      where: whereClause,
      include: {
        Parent: true,
      },
      orderBy: { SortOrder: 'asc' },
    });
    
    // Transform locations to include display names
    const transformedLocations = locations.map(location => ({
      id: location.Id,
      slug: location.Slug,
      translationKey: location.TranslationKey,
      name: location.Name,
      displayName: getLocationDisplayName(location, lang as string),
      type: location.Type,
      parentId: location.ParentId,
      sortOrder: location.SortOrder,
      isCapital: location.IsCapital,
      specialType: location.SpecialType,
    }));
    
    res.json({ success: true, locations: transformedLocations });
  } catch (error) {
    console.error('[API /locations GET] Error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch locations' });
  }
});

/**
 * @swagger
 * /locations/{id}:
 *   get:
 *     summary: Get a specific location by ID
 *     tags: [Public]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID
 *       - in: query
 *         name: lang
 *         schema:
 *           type: string
 *           enum: [ro, ru, en]
 *           default: ro
 *         description: Language for location name
 *     responses:
 *       200:
 *         description: Location details
 *       404:
 *         description: Location not found
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { lang = 'ro' } = req.query;
    
    const locationId = parseInt(id, 10);
    if (isNaN(locationId)) {
      return res.status(400).json({ success: false, message: 'Invalid location ID' });
    }
    
    const location = await prisma.location.findUnique({
      where: { Id: locationId, IsActive: true },
      include: {
        Parent: true,
        Children: {
          where: { IsActive: true },
          orderBy: { SortOrder: 'asc' },
        },
      },
    });
    
    if (!location) {
      return res.status(404).json({ success: false, message: 'Location not found' });
    }
    
    const transformedLocation = {
      id: location.Id,
      slug: location.Slug,
      translationKey: location.TranslationKey,
      name: location.Name,
      displayName: getLocationDisplayName(location, lang as string),
      type: location.Type,
      parentId: location.ParentId,
      sortOrder: location.SortOrder,
      isCapital: location.IsCapital,
      specialType: location.SpecialType,
      parent: location.Parent ? {
        id: location.Parent.Id,
        slug: location.Parent.Slug,
        name: location.Parent.Name,
        displayName: getLocationDisplayName(location.Parent, lang as string),
      } : null,
      children: location.Children.map(child => ({
        id: child.Id,
        slug: child.Slug,
        name: child.Name,
        displayName: getLocationDisplayName(child, lang as string),
      })),
    };
    
    res.json({ success: true, location: transformedLocation });
  } catch (error) {
    console.error('[API /locations/:id GET] Error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch location' });
  }
});

/**
 * @swagger
 * /locations/by-value/{value}:
 *   get:
 *     summary: Get a specific location by value
 *     tags: [Public]
 *     parameters:
 *       - in: path
 *         name: value
 *         required: true
 *         schema:
 *           type: string
 *         description: Location value (e.g., "chisinau-botanica")
 *       - in: query
 *         name: lang
 *         schema:
 *           type: string
 *           enum: [ro, ru, en]
 *           default: ro
 *         description: Language for location name
 *     responses:
 *       200:
 *         description: Location details
 *       404:
 *         description: Location not found
 */
router.get('/by-value/:value', async (req: Request, res: Response) => {
  try {
    const { value } = req.params;
    const { lang = 'ro' } = req.query;
    
    const location = await prisma.location.findUnique({
      where: { Slug: value, IsActive: true },
      include: {
        Parent: true,
      },
    });
    
    if (!location) {
      return res.status(404).json({ success: false, message: 'Location not found' });
    }
    
    const transformedLocation = {
      id: location.Id,
      slug: location.Slug,
      translationKey: location.TranslationKey,
      name: location.Name,
      displayName: getLocationDisplayName(location, lang as string),
      type: location.Type,
      parentId: location.ParentId,
      sortOrder: location.SortOrder,
      isCapital: location.IsCapital,
      specialType: location.SpecialType,
    };
    
    res.json({ success: true, location: transformedLocation });
  } catch (error) {
    console.error('[API /locations/by-value/:value GET] Error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch location' });
  }
});

// Get location hierarchy for a specific location
router.get('/hierarchy/:id', async (req: Request, res: Response) => {
  try {
    const locationId = parseInt(req.params.id);
    if (isNaN(locationId)) {
      return res.status(400).json({ error: 'Invalid location ID' });
    }

    const hierarchy = await EnhancedLocationService.getLocationHierarchy(locationId);
    res.json({ success: true, hierarchy });
  } catch (error) {
    console.error('Error fetching location hierarchy:', error);
    res.status(500).json({ error: 'Failed to fetch location hierarchy' });
  }
});

// Migrate addresses to use foreign keys (admin endpoint)
router.post('/migrate-addresses', async (req: Request, res: Response) => {
  try {
    const result = await EnhancedLocationService.batchMigrateAddresses();
    res.json({
      success: true,
      message: `Migration completed. Updated: ${result.updated}, Errors: ${result.errors}`,
      result
    });
  } catch (error) {
    console.error('Error migrating addresses:', error);
    res.status(500).json({ error: 'Failed to migrate addresses' });
  }
});

export default router;
