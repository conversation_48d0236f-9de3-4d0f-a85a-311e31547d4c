
"use client";

import React, { useState, useEffect, type FormEvent, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Loader2 } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LocationCombobox } from '@/components/ui/location-combobox';
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from '@/hooks/use-toast';
import { useParams, useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { cn } from '@/lib/utils';
import type { AdvertisedService as PrismaAdvertisedService, NannyServiceDetails } from '@prisma/client';
import { useSession } from "next-auth/react";
import { ServicesService } from '@repo/services';
import type { AdvertisedServicePayload, NannyServiceDetailsPayload } from '@repo/types';
import { LocationService, type LocationEntry } from '@repo/services';

const StepInput = React.memo(({ id, value, onChange, label, placeholder, type = "text", className, fieldError }: { id: string; value: string | number; onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; label: string; placeholder?: string; type?: string; className?: string; fieldError?: boolean; }) => (
  <div className="space-y-1.5"><Label htmlFor={id}>{label}</Label><Input id={id} type={type} value={value} onChange={onChange} placeholder={placeholder} className={cn("text-sm", className, fieldError && "border-destructive focus-visible:ring-destructive")} /></div>
));
StepInput.displayName = 'StepInput';

const StepTextarea = React.memo(({ id, value, onChange, label, placeholder, rows = 3, className, fieldError }: { id: string; value: string; onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void; label: string; placeholder?: string; rows?: number; className?: string; fieldError?: boolean; }) => (
  <div className="space-y-1.5"><Label htmlFor={id}>{label}</Label><Textarea id={id} value={value} onChange={onChange} placeholder={placeholder} rows={rows} className={cn("text-sm", className, fieldError && "border-destructive focus-visible:ring-destructive")} /></div>
));
StepTextarea.displayName = 'StepTextarea';

const StepSwitchField = React.memo(({ id, label, checked, onCheckedChange }: { id: string; label: string; checked: boolean | undefined; onCheckedChange: (checked: boolean) => void; }) => (
  <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm bg-card"><Label htmlFor={id} className="cursor-pointer text-sm">{label}</Label><Switch id={id} checked={!!checked} onCheckedChange={onCheckedChange} /></div>
));
StepSwitchField.displayName = 'StepSwitchField';


const NannyFields = React.memo(({ details, handleDetailChange, translate }: { details: any; handleDetailChange: (field: string, value: any) => void; translate: (t: any, k: string) => string; }) => (
  <div className="space-y-4 p-4 border rounded-md bg-muted/20 mt-4">
    <h4 className="font-medium text-md mb-3">{translate(commonTranslations, 'childcareTitle')}</h4>
    <div className="space-y-2"><Label>{translate(commonTranslations, 'childcarePreferredAgeLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id="PreferredAge_0_2" label={translate(commonTranslations, 'childcareAge0_2')} checked={details.PreferredAge_0_2} onCheckedChange={(c)=>handleDetailChange('PreferredAge_0_2',c)} /><StepSwitchField id="PreferredAge_3_6" label={translate(commonTranslations, 'childcareAge3_6')} checked={details.PreferredAge_3_6} onCheckedChange={(c)=>handleDetailChange('PreferredAge_3_6',c)} /><StepSwitchField id="PreferredAge_7_plus" label={translate(commonTranslations, 'childcareAge7_plus')} checked={details.PreferredAge_7_plus} onCheckedChange={(c)=>handleDetailChange('PreferredAge_7_plus',c)} /></div></div>
  </div>
));
NannyFields.displayName = "NannyFields";

export default function EditNannyServicePage() {
  const { translate } = useLanguage();
  const params = useParams();
  const router = useRouter();
  const serviceId = params.id as string;
  const { data: session, status: sessionStatus } = useSession();
  const { toast } = useToast();

  const [formData, setFormData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});

  // Locations state
  const [locations, setLocations] = useState<LocationEntry[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(true);

  useEffect(() => {
    if (sessionStatus === "authenticated" && serviceId) {
      setIsLoading(true);
      ServicesService.getServiceById(parseInt(serviceId, 10))
        .then(data => {
          if (!data || !data.service) {
            throw new Error("Serviciul nu a fost găsit sau nu aveți permisiunea de a-l edita.");
          }
          const service = data.service as PrismaAdvertisedService & { nannyServiceDetails?: NannyServiceDetails | null };
          
          const rawDetails = service.nannyServiceDetails;
          const detailsForFormState = {
            experienceYears: String(rawDetails?.ExperienceYears ?? ''),
            description: rawDetails?.Description ?? '',
            LocationId: rawDetails?.LocationId ?? null,
            PricePerHour: rawDetails?.PricePerHour ? String(rawDetails.PricePerHour) : '',
            PricePerDay: rawDetails?.PricePerDay ? String(rawDetails.PricePerDay) : '',
            PreferredAge_0_2: rawDetails?.PreferredAge_0_2 ?? false,
            PreferredAge_3_6: rawDetails?.PreferredAge_3_6 ?? false,
            PreferredAge_7_plus: rawDetails?.PreferredAge_7_plus ?? false,
          };

          setFormData({
            ServiceName: service.ServiceName,
            Description: service.Description,
            Status: service.Status as 'Activ' | 'Inactiv',
            Details: detailsForFormState,
          });
        })
        .catch(err => {
          setError(err.message);
          toast({ variant: "destructive", title: "Eroare", description: err.message });
        })
        .finally(() => setIsLoading(false));
    } else if (sessionStatus === "unauthenticated") {
      router.push('/login');
    }
  }, [serviceId, sessionStatus, router, toast]);

  // Load locations on component mount
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setLocationsLoading(true);
        const locationData = await LocationService.getLocationsForForms(translate.currentLanguage);
        setLocations(locationData);
      } catch (error) {
        console.error('Failed to load locations:', error);
      } finally {
        setLocationsLoading(false);
      }
    };

    loadLocations();
  }, [translate.currentLanguage]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (!formData) return;
    setFormData((prev: any) => ({ ...prev, [name]: value }));
    if (fieldErrors[name]) setFieldErrors(p => ({...p, [name]: false}));
  };
  
  const handleDetailChange = useCallback((fieldPath: string, value: any) => {
     setFormData((prev: any) => ({ ...prev, Details: { ...prev.Details, [fieldPath]: value } }));
     if (fieldErrors[fieldPath]) setFieldErrors(p => ({...p, [fieldPath]: false}));
  }, [fieldErrors]);

  const validateForm = () => {
    if (!formData) return false;
    const errors: Record<string, boolean> = {};
    if (!formData.ServiceName.trim()) errors.ServiceName = true;
    if (!formData.Description.trim()) errors.Description = true;
    if (!formData.Details?.LocationValue) errors.LocationValue = true;
    if (!formData.Details?.experienceYears?.trim() || isNaN(parseInt(formData.Details.experienceYears, 10)) || parseInt(formData.Details.experienceYears, 10) < 0) {
        errors.experienceYears = true;
    }
    setFieldErrors(errors);
    if (Object.keys(errors).length > 0) {
        toast({ variant: "destructive", title: "Eroare Validare", description: "Vă rugăm completați câmpurile obligatorii." });
        return false;
    }
    return true;
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!validateForm() || !formData) return;
    
    setIsSubmitting(true);
    const providerId = (session?.user as any)?.id;
    if (!providerId) {
        toast({ variant: "destructive", title: "Eroare", description: "Sesiune invalidă." });
        setIsSubmitting(false);
        return;
    }
    
    const detailsPayload: NannyServiceDetailsPayload = {
      ...formData.Details,
      ExperienceYears: parseInt(formData.Details.experienceYears, 10) || 0,
      PricePerHour: formData.Details.PricePerHour ? parseFloat(formData.Details.PricePerHour) : null,
      PricePerDay: formData.Details.PricePerDay ? parseFloat(formData.Details.PricePerDay) : null,
    };

    const payload: Partial<AdvertisedServicePayload> = {
      ProviderId: parseInt(providerId, 10),
      ServiceName: formData.ServiceName,
      Description: formData.Description,
      ServiceCategorySlug: 'Nanny',
      Status: formData.Status,
      NannyDetails: detailsPayload,
    };

    try {
      await ServicesService.updateService(parseInt(serviceId, 10), payload);
      toast({ title: "Succes!", description: `Serviciul de Bonă a fost actualizat.` });
      router.push('/dashboard/provider/services');
    } catch (err) {
      toast({ variant: "destructive", title: "Eroare", description: err instanceof Error ? err.message : "A apărut o eroare." });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) return <div className="flex justify-center items-center h-full"><Loader2 className="w-8 h-8 animate-spin" /></div>;
  if (error) return <div className="text-destructive text-center p-8 bg-destructive/10 rounded-md">{error}</div>;
  if (!formData) return <div className="text-center">Nu s-au putut încărca datele serviciului.</div>;

  return (
    <div className="space-y-6 pb-12">
      <Button variant="outline" asChild>
        <Link href="/dashboard/provider/services">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Înapoi la Serviciile Mele
        </Link>
      </Button>
      <Card>
        <CardHeader>
          <CardTitle>Editează Serviciul: Bonă</CardTitle>
          <CardDescription>Actualizează detaliile pentru serviciul tău de bonă.</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="ServiceName">Nume Serviciu</Label>
                <Input id="ServiceName" name="ServiceName" value={formData.ServiceName} onChange={handleInputChange} className={cn(fieldErrors.ServiceName && "border-destructive")}/>
            </div>
            <div className="space-y-2">
                <Label htmlFor="Description">Descriere</Label>
                <Textarea id="Description" name="Description" value={formData.Description} onChange={handleInputChange} rows={4} className={cn(fieldErrors.Description && "border-destructive")}/>
            </div>
            <div className="space-y-2">
                <Label htmlFor="status">Status Serviciu</Label>
                <Select name="Status" value={formData.Status} onValueChange={(val) => setFormData((p: any) => p ? {...p, Status: val as 'Activ' | 'Inactiv'} : null)}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                        <SelectItem value="Activ">{translate(commonTranslations, 'statusActive')}</SelectItem>
                        <SelectItem value="Inactiv">{translate(commonTranslations, 'statusInactive')}</SelectItem>
                    </SelectContent>
                </Select>
            </div>
            
            <StepInput id="experienceYears" value={formData.Details.experienceYears} onChange={(e) => handleDetailChange("experienceYears", e.target.value)} label={translate(commonTranslations, 'experienceYearsLabel')} placeholder={translate(commonTranslations, 'experiencePlaceholder')} type="number" fieldError={fieldErrors.experienceYears} />
            <div className="space-y-2">
                <Label htmlFor="LocationId">{translate(commonTranslations, 'locationLabel')}</Label>
                <LocationCombobox
                  locations={locations}
                  value={formData.Details.LocationId ? (locations.find(loc => loc.id === formData.Details.LocationId)?.slug || "") : ""}
                  onValueChange={(val) => {
                    // Convert slug back to ID for storage
                    const location = locations.find(loc => loc.slug === val);
                    handleDetailChange('LocationId', location ? location.id : null);
                  }}
                  placeholder={locationsLoading ? "Loading locations..." : translate(commonTranslations, 'locationPlaceholder')}
                  className={cn("text-sm", fieldErrors.LocationId && "border-destructive focus-visible:ring-destructive")}
                />
            </div>
            <StepInput id="PricePerHour" value={formData.Details.PricePerHour} onChange={(e) => handleDetailChange("PricePerHour", e.target.value)} label={translate(commonTranslations, 'pricePerHourLabel')} type="number" />
            <StepInput id="PricePerDay" value={formData.Details.PricePerDay} onChange={(e) => handleDetailChange("PricePerDay", e.target.value)} label={translate(commonTranslations, 'pricePerDayLabel')} type="number" />

            <NannyFields details={formData.Details} handleDetailChange={handleDetailChange} translate={translate} />

          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Salvează Modificările
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
