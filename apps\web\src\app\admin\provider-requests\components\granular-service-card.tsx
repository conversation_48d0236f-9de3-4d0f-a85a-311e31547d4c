"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  ChevronDown,
  ChevronUp,
  Loader2,
  Star,
  Shield,
  Eye,
  Download
} from "lucide-react";
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { ComprehensiveFieldRenderer } from './comprehensive-field-renderer';

export interface PendingService {
  Id: number;
  ServiceName: string;
  Description: string;
  ServiceCategorySlug: string;
  ExperienceYears: number;
  Status: 'PendingReview' | 'Approved' | 'Rejected' | 'RequiresChanges';
  AdminNotes?: string | null;
  DocumentPaths: string[];
  CreatedAt: string;
  UpdatedAt: string;
  Category: {
    Id: number;
    NameKey: string;
    Slug: string;
  };
  // Service-specific details
  NannyServiceDetails?: any;
  ElderCareServiceDetails?: any;
  CleaningServiceDetails?: any;
  TutoringServiceDetails?: any;
  CookingServiceDetails?: any;
}

interface GranularServiceCardProps {
  service: PendingService;
  onStatusUpdate: (serviceId: number, status: string, adminNotes?: string) => Promise<void>;
  isUpdating?: boolean;
}

const statusConfig = {
  PendingReview: {
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: Clock,
    label: 'Pending Review'
  },
  Approved: {
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
    label: 'Approved'
  },
  Rejected: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: XCircle,
    label: 'Rejected'
  },
  RequiresChanges: {
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: AlertTriangle,
    label: 'Requires Changes'
  }
};

export function GranularServiceCard({ service, onStatusUpdate, isUpdating = false }: GranularServiceCardProps) {
  const { translate } = useLanguage();
  const { toast } = useToast();
  const [adminNotes, setAdminNotes] = useState(service.AdminNotes || '');
  const [isActionLoading, setIsActionLoading] = useState(false);

  const statusInfo = statusConfig[service.Status];
  const StatusIcon = statusInfo.icon;

  // Calculate comprehensive service quality indicators
  const getServiceQuality = () => {
    const details = service.NannyServiceDetails ||
                   service.ElderCareServiceDetails ||
                   service.CleaningServiceDetails ||
                   service.TutoringServiceDetails ||
                   service.CookingServiceDetails;

    let score = 0;
    let maxScore = 0;

    // Basic information completeness (weight: 3)
    if (service.Description && service.Description.length > 50) score += 2;
    maxScore += 2;

    if (service.ExperienceYears > 0) score += 1;
    maxScore += 1;

    // Documents availability (weight: 3)
    if (service.DocumentPaths && service.DocumentPaths.length > 0) score += 2;
    maxScore += 2;

    if (details?.DocBuletinFileName) score += 1;
    maxScore += 1;

    // Service-specific details completeness (weight: 4)
    if (details) {
      // Location and pricing
      if (details.LocationValue) score += 1;
      maxScore += 1;

      if (details.PricePerHour || details.PricePerDay || details.PriceSubscriptionAmount) score += 1;
      maxScore += 1;

      // Availability information
      if (details.AvailabilityWeekdays !== undefined ||
          details.AvailabilityWeekends !== undefined ||
          details.AvailabilityEvenings !== undefined) score += 1;
      maxScore += 1;

      // Service-specific specializations
      if (service.NannyServiceDetails) {
        const nannyDetails = service.NannyServiceDetails;
        if (nannyDetails.PreferredAge_0_2 || nannyDetails.PreferredAge_3_6 || nannyDetails.PreferredAge_7_plus) score += 1;
        maxScore += 1;
      } else if (service.ElderCareServiceDetails) {
        const elderDetails = service.ElderCareServiceDetails;
        if (elderDetails.TypeMobil || elderDetails.TypePartialImobilizat || elderDetails.TypeCompletImobilizat) score += 1;
        maxScore += 1;
      } else if (service.CleaningServiceDetails) {
        const cleaningDetails = service.CleaningServiceDetails;
        if (cleaningDetails.PropertyTypeApartments || cleaningDetails.PropertyTypeHouses || cleaningDetails.PropertyTypeOffices) score += 1;
        maxScore += 1;
      } else if (service.TutoringServiceDetails) {
        const tutoringDetails = service.TutoringServiceDetails;
        if (tutoringDetails.Grades_1_4 || tutoringDetails.Grades_5_8 || tutoringDetails.Grades_9_12) score += 1;
        maxScore += 1;
      } else if (service.CookingServiceDetails) {
        const cookingDetails = service.CookingServiceDetails;
        if (cookingDetails.CuisineTypeTraditional || cookingDetails.CuisineTypeVegetarian || cookingDetails.CuisineTypeKids) score += 1;
        maxScore += 1;
      }
    }

    const percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;
    return {
      score,
      maxScore,
      percentage,
      level: percentage >= 85 ? 'high' : percentage >= 65 ? 'medium' : 'low'
    };
  };

  const quality = getServiceQuality();

  const handleStatusUpdate = async (newStatus: string) => {
    if (isActionLoading) return;
    
    setIsActionLoading(true);
    try {
      await onStatusUpdate(service.Id, newStatus, adminNotes);
      toast({
        title: translate(commonTranslations, 'adminSuccess'),
        description: translate(commonTranslations, 'adminServiceUpdateSuccess'),
      });
    } catch (error) {
      toast({
        title: translate(commonTranslations, 'adminError'),
        description: translate(commonTranslations, 'adminServiceUpdateError'),
        variant: "destructive",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  const renderServiceDetails = () => {
    const details = service.NannyServiceDetails ||
                   service.ElderCareServiceDetails ||
                   service.CleaningServiceDetails ||
                   service.TutoringServiceDetails ||
                   service.CookingServiceDetails;

    if (!details) {
      return null;
    }

    // Determine service type
    let serviceType: 'nanny' | 'eldercare' | 'cleaning' | 'tutoring' | 'cooking';
    if (service.NannyServiceDetails) serviceType = 'nanny';
    else if (service.ElderCareServiceDetails) serviceType = 'eldercare';
    else if (service.CleaningServiceDetails) serviceType = 'cleaning';
    else if (service.TutoringServiceDetails) serviceType = 'tutoring';
    else if (service.CookingServiceDetails) serviceType = 'cooking';
    else return null;

    return (
      <ComprehensiveFieldRenderer
        serviceType={serviceType}
        details={details}
      />
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <CardTitle className="text-lg">{service.ServiceName}</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {service.Category.NameKey}
              </Badge>
              <Badge className={`text-xs ${statusInfo.color}`}>
                <StatusIcon className="w-3 h-3 mr-1" />
                {statusInfo.label}
              </Badge>
            </div>
          </div>

          {/* Quality and Experience Indicators */}
          <div className="flex items-center gap-2 flex-wrap">
            <Badge
              variant="outline"
              className={`text-xs ${
                quality.level === 'high' ? 'bg-green-50 text-green-700 border-green-200' :
                quality.level === 'medium' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                'bg-red-50 text-red-700 border-red-200'
              }`}
            >
              <Star className="w-3 h-3 mr-1" />
              {Math.round(quality.percentage)}% Complete
            </Badge>

            {/* Document Indicator */}
            {service.DocumentPaths.length > 0 && (
              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                <Shield className="w-3 h-3 mr-1" />
                {service.DocumentPaths.length} {translate(commonTranslations, 'adminDocs')}{service.DocumentPaths.length !== 1 ? 's' : ''}
              </Badge>
            )}

            {/* Experience Badge for ALL Services */}
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
              <Star className="w-3 h-3 mr-1" />
              {service.ExperienceYears} {service.ExperienceYears === 1 ? translate(commonTranslations, 'yearSingular') : translate(commonTranslations, 'yearsSuffix')}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Basic Service Information */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Category</h4>
              <p className="text-sm">{service.ServiceCategorySlug}</p>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Description</h4>
            <p className="text-sm leading-relaxed bg-muted/30 p-3 rounded-md">{service.Description}</p>
          </div>
        </div>

        {service.DocumentPaths.length > 0 ? (
          <div className="space-y-2">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Documents ({service.DocumentPaths.length})
            </h4>
            <div className="grid grid-cols-1 gap-2">
              {service.DocumentPaths.map((path, index) => {
                const fileName = path.split('/').pop() || `Document ${index + 1}`;
                const fileExtension = fileName.split('.').pop()?.toLowerCase();

                return (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted/30 rounded-md">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <FileText className="w-4 h-4 text-muted-foreground shrink-0" />
                      <span className="text-sm truncate" title={fileName}>
                        {fileName}
                      </span>
                      {fileExtension && (
                        <Badge variant="outline" className="text-xs shrink-0">
                          {fileExtension.toUpperCase()}
                        </Badge>
                      )}
                    </div>
                    <div className="flex gap-1 shrink-0">
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <Download className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Documents
            </h4>
            <div className="p-3 bg-orange-50 border border-orange-200 rounded-md">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-orange-600" />
                <span className="text-sm text-orange-700">No documents provided</span>
              </div>
            </div>
          </div>
        )}

        {/* Always show service details */}
        {renderServiceDetails()}

        {service.Status === 'PendingReview' && (
          <div className="space-y-3 pt-3 border-t">
            <div className="space-y-2">
              <Label htmlFor={`notes-${service.Id}`} className="text-sm font-medium">
                Admin Notes
              </Label>
              <Textarea
                id={`notes-${service.Id}`}
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder="Add notes for this service..."
                className="min-h-[80px]"
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => handleStatusUpdate('Approved')}
                disabled={isActionLoading || isUpdating}
                className="bg-green-600 hover:bg-green-700"
                size="sm"
              >
                {isActionLoading ? <Loader2 className="w-4 h-4 mr-1 animate-spin" /> : <CheckCircle className="w-4 h-4 mr-1" />}
                {translate(commonTranslations, 'adminApprove')}
              </Button>
              <Button
                onClick={() => handleStatusUpdate('Rejected')}
                disabled={isActionLoading || isUpdating}
                variant="destructive"
                size="sm"
              >
                {isActionLoading ? <Loader2 className="w-4 h-4 mr-1 animate-spin" /> : <XCircle className="w-4 h-4 mr-1" />}
                {translate(commonTranslations, 'adminReject')}
              </Button>
              <Button
                onClick={() => handleStatusUpdate('RequiresChanges')}
                disabled={isActionLoading || isUpdating}
                variant="outline"
                size="sm"
              >
                {isActionLoading ? <Loader2 className="w-4 h-4 mr-1 animate-spin" /> : <AlertTriangle className="w-4 h-4 mr-1" />}
                {translate(commonTranslations, 'adminNeedsChanges')}
              </Button>
            </div>
          </div>
        )}

        {service.AdminNotes && service.Status !== 'PendingReview' && (
          <div className="space-y-2 pt-3 border-t">
            <Label className="text-sm font-medium">{translate(commonTranslations, 'adminAdminNotes')}</Label>
            <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
              {service.AdminNotes}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
