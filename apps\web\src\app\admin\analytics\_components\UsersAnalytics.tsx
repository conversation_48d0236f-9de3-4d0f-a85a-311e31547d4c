"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Users, UserPlus, UserCheck, Clock, Activity } from "lucide-react";
import { useLanguage } from '@/contexts/language-context';
import { UserRegistrationChart } from './UserRegistrationChart';
import { ActiveUsersStats } from './ActiveUsersStats';

const usersTranslations = {
  usersAnalytics: { ro: "Analiză utilizatori", ru: "Аналитика пользователей", en: "Users Analytics" },
  userRegistrations: { ro: "Înregistrări utilizatori", ru: "Регистрации пользователей", en: "User Registrations" },
  activeUsers: { ro: "Utilizatori activi", ru: "Активные пользователи", en: "Active Users" },
  userGrowth: { ro: "Creșterea utilizatorilor", ru: "Рост пользователей", en: "User Growth" },
  userEngagement: { ro: "Implicarea utilizatorilor", ru: "Вовлеченность пользователей", en: "User Engagement" },
  userRetention: { ro: "Retenția utilizatorilor", ru: "Удержание пользователей", en: "User Retention" },
  userDemographics: { ro: "Demografia utilizatorilor", ru: "Демография пользователей", en: "User Demographics" },
  newUsersToday: { ro: "Utilizatori noi astăzi", ru: "Новые пользователи сегодня", en: "New Users Today" },
  totalActiveUsers: { ro: "Total utilizatori activi", ru: "Всего активных пользователей", en: "Total Active Users" },
  averageSessionTime: { ro: "Timpul mediu de sesiune", ru: "Среднее время сессии", en: "Average Session Time" },
  comingSoon: { ro: "În curând...", ru: "Скоро...", en: "Coming soon..." },
};

interface UsersAnalyticsProps {
  timePeriod: string;
  refreshTrigger: Date;
}

interface UserStats {
  totalUsers: number;
  newUsersToday: number;
  totalActiveUsers: number;
  averageSessionTime: string;
}

export function UsersAnalytics({ timePeriod, refreshTrigger }: UsersAnalyticsProps) {
  const { translate } = useLanguage();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const [statsRes, activeUsersRes] = await Promise.all([
          fetch('/api/proxy/admin/stats'),
          fetch('/api/proxy/admin/analytics/active-users')
        ]);

        if (!statsRes.ok || !activeUsersRes.ok) {
          throw new Error('Failed to fetch user analytics');
        }

        const statsData = await statsRes.json();
        const activeUsersData = await activeUsersRes.json();

        setStats({
          totalUsers: statsData.totalUsers,
          newUsersToday: activeUsersData.today || 0,
          totalActiveUsers: activeUsersData.thisMonth || 0,
          averageSessionTime: "12m 34s" // Would need session tracking
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error fetching user analytics:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger]);

  if (error) {
    return (
      <div className="text-center text-red-600 py-8">
        <p>Error loading user analytics: {error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-2">
        <Users className="w-6 h-6 text-primary" />
        <h2 className="text-2xl font-bold">
          {translate(usersTranslations, 'usersAnalytics')}
        </h2>
      </div>

      {/* Active Users Stats */}
      <ActiveUsersStats />

      {/* User Registration Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <UserRegistrationChart period={timePeriod as '7d' | '30d' | '90d' | '1y'} refreshTrigger={refreshTrigger} />
        </div>
        
        {/* Quick Stats */}
        <div className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {translate(usersTranslations, 'newUsersToday')}
              </CardTitle>
              <UserPlus className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mb-2" />
              ) : (
                <div className="text-2xl font-bold">{stats?.newUsersToday || 0}</div>
              )}
              <p className="text-xs text-muted-foreground">
                New registrations today
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {translate(usersTranslations, 'totalActiveUsers')}
              </CardTitle>
              <Activity className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-8 w-20 mb-2" />
              ) : (
                <div className="text-2xl font-bold">{stats?.totalActiveUsers || 0}</div>
              )}
              <p className="text-xs text-muted-foreground">
                Active this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {translate(usersTranslations, 'averageSessionTime')}
              </CardTitle>
              <Clock className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-8 w-20 mb-2" />
              ) : (
                <div className="text-2xl font-bold">{stats?.averageSessionTime || "0m"}</div>
              )}
              <p className="text-xs text-muted-foreground">
                Average session duration
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              {translate(usersTranslations, 'userEngagement')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-muted-foreground py-8">
              <p>{translate(usersTranslations, 'comingSoon')}</p>
              <p className="text-sm mt-2">User engagement metrics and charts</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="w-5 h-5" />
              {translate(usersTranslations, 'userRetention')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-muted-foreground py-8">
              <p>{translate(usersTranslations, 'comingSoon')}</p>
              <p className="text-sm mt-2">User retention analysis and cohort data</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Demographics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {translate(usersTranslations, 'userDemographics')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>{translate(usersTranslations, 'comingSoon')}</p>
            <p className="text-sm mt-2">User demographics breakdown by location, age, and preferences</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
