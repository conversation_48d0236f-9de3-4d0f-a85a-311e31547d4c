import { Router } from 'express';
import prisma from '../lib/db';
import bcrypt from 'bcryptjs';
import { UserRole } from "@prisma/client";

const router = Router();

// Endpoint for user registration
router.post('/register', async (req, res) => {
    const { fullName, email, password } = req.body;
    
    // --- Validation ---
    const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]).{8,}$/;
    if (!fullName || !email || !password) {
        return res.status(400).json({ message: 'Numele complet, email-ul și parola sunt obligatorii.' });
    }
    if (!PASSWORD_REGEX.test(password)) {
        return res.status(400).json({ message: 'Parola nu îndeplinește cerințele de complexitate.' });
    }

    try {
        const existingUser = await prisma.user.findUnique({ where: { Email: email } });
        if (existingUser) {
            return res.status(409).json({ message: 'Un utilizator cu acest email există deja.' });
        }

        const clientRole = await prisma.role.findUnique({ where: { Name: UserRole.Client } });
        if (!clientRole) {
            console.error("[API Register] Critical: 'Client' role not found in database.");
            return res.status(500).json({ message: 'Eroare de configurare server: Rolul client lipsește.' });
        }
        
        const hashedPassword = bcrypt.hashSync(password, 10);
        const newUser = await prisma.user.create({
            data: {
                FullName: fullName,
                Email: email,
                Password: hashedPassword,
                UserRoles: {
                    create: { RoleId: clientRole.Id }
                }
            },
            include: {
                UserRoles: {
                    include: { Role: true }
                }
            }
        });

        // Exclude password from the returned object
        const { Password, ...userToReturn } = newUser;
        
        return res.status(201).json({ success: true, message: "Înregistrare reușită!", user: userToReturn });

    } catch (error) {
        console.error('[API /auth/register POST] Error:', error);
        return res.status(500).json({ message: 'A apărut o eroare la înregistrare.' });
    }
});


// Endpoint to find a user by email
router.post('/user-by-email', async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }
        const user = await prisma.user.findUnique({
            where: { Email: email },
            include: {
                UserRoles: {
                    include: { Role: true }
                },
                Accounts: true
            }
        });

        if (user) {
            // Transform the user data to match the expected format for NextAuth
            const transformedUser = {
                ...user,
                Roles: user.UserRoles.map(ur => ({ Name: ur.Role.Name }))
            };
            return res.json(transformedUser);
        }
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.json(user);
    } catch (error) {
        console.error('[API /auth/user-by-email POST] Error:', error);
        res.status(500).json({ message: 'Failed to fetch user' });
    }
});

// Endpoint to find a user by ID
router.post('/user-by-id', async (req, res) => {
    try {
        const { id } = req.body;
         if (!id) {
            return res.status(400).json({ message: 'ID is required' });
        }
        const userId = parseInt(id, 10);
         if (isNaN(userId)) {
            return res.status(400).json({ message: 'Invalid User ID' });
        }
        const user = await prisma.user.findUnique({
            where: { Id: userId },
            include: {
                UserRoles: {
                    include: { Role: true }
                },
                Accounts: true
            }
        });
         if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.json(user);
    } catch (error) {
        console.error('[API /auth/user-by-id POST] Error:', error);
        res.status(500).json({ message: 'Failed to fetch user' });
    }
});

// Endpoint to create a new account (for OAuth)
router.post('/create-account', async (req, res) => {
    try {
        const accountData = req.body;
        const newAccount = await prisma.account.create({
            data: accountData
        });
        res.status(201).json(newAccount);
    } catch (error) {
        console.error('[API /auth/create-account POST] Error:', error);
        res.status(500).json({ message: 'Failed to create account' });
    }
});

// Endpoint to update user data
router.post('/update-user', async (req, res) => {
    try {
        const { id, data } = req.body;
        if (!id || !data) {
            return res.status(400).json({ message: 'ID and data are required' });
        }
         const userId = parseInt(id, 10);
         if (isNaN(userId)) {
            return res.status(400).json({ message: 'Invalid User ID' });
        }
        const updatedUser = await prisma.user.update({
            where: { Id: userId },
            data: data
        });
        res.json(updatedUser);
    } catch (error) {
        console.error('[API /auth/update-user POST] Error:', error);
        res.status(500).json({ message: 'Failed to update user' });
    }
});

// Endpoint to get the 'Client' role
router.get('/role/client', async (req, res) => {
    try {
        const clientRole = await prisma.role.findUnique({ where: { Name: UserRole.Client } });
        if (!clientRole) {
            return res.status(404).json({ message: 'Client role not found' });
        }
        res.json(clientRole);
    } catch (error) {
        console.error('[API /auth/role/client GET] Error:', error);
        res.status(500).json({ message: 'Failed to fetch client role' });
    }
});

// Endpoint to create a new user (for OAuth)
router.post('/create-user', async (req, res) => {
    try {
        const userData = req.body;
         // Exclude Accounts and Roles from the initial create data
        const { Accounts, Roles, ...createData } = userData;

        const newUser = await prisma.user.create({
            data: createData
        });

        // Connect roles if provided
        if (Roles && Roles.connect) {
            for (const roleConnection of Roles.connect) {
                await prisma.userRoleJunction.create({
                    data: {
                        UserId: newUser.Id,
                        RoleId: roleConnection.Id
                    }
                });
            }
        }

        // Create accounts if provided
        if (Accounts && Accounts.create) {
             for (const account of Accounts.create) {
                await prisma.account.create({
                    data: {
                        UserId: newUser.Id,
                        ...account
                    }
                });
            }
        }

        // Fetch the user again with roles and accounts to return the complete object
        const userWithRelations = await prisma.user.findUnique({
            where: { Id: newUser.Id },
            include: { Roles: true, Accounts: true }
        });

        res.status(201).json(userWithRelations);
    } catch (error) {
        console.error('[API /auth/create-user POST] Error:', error);
        res.status(500).json({ message: 'Failed to create user' });
    }
});

export default router;
