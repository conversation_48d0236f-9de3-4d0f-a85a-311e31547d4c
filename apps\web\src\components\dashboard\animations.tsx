"use client";

import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Smooth fade in animation for dashboard content
export function FadeIn({ 
  children, 
  delay = 0, 
  duration = 0.3, 
  className 
}: { 
  children: React.ReactNode; 
  delay?: number; 
  duration?: number; 
  className?: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration, delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Slide in from left animation for navigation
export function SlideInLeft({ 
  children, 
  delay = 0, 
  className 
}: { 
  children: React.ReactNode; 
  delay?: number; 
  className?: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Scale animation for role toggle
export function ScaleIn({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Stagger animation for navigation items
export function StaggerContainer({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string;
}) {
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

export function StaggerItem({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string;
}) {
  return (
    <motion.div
      variants={{
        hidden: { opacity: 0, x: -10 },
        visible: { opacity: 1, x: 0 }
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Role transition animation
export function RoleTransition({ 
  children, 
  roleKey, 
  className 
}: { 
  children: React.ReactNode; 
  roleKey: string; 
  className?: string;
}) {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={roleKey}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.2 }}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}

// Smooth height animation for collapsible content
export function AnimatedHeight({ 
  children, 
  isOpen, 
  className 
}: { 
  children: React.ReactNode; 
  isOpen: boolean; 
  className?: string;
}) {
  return (
    <motion.div
      initial={false}
      animate={{ height: isOpen ? 'auto' : 0 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={cn("overflow-hidden", className)}
    >
      <div>{children}</div>
    </motion.div>
  );
}

// Bounce animation for notifications
export function BounceIn({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.3 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{
        type: "spring",
        stiffness: 260,
        damping: 20
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Hover animation wrapper
export function HoverScale({ 
  children, 
  scale = 1.02, 
  className 
}: { 
  children: React.ReactNode; 
  scale?: number; 
  className?: string;
}) {
  return (
    <motion.div
      whileHover={{ scale }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Page transition wrapper
export function PageTransition({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Loading pulse animation
export function PulseLoader({ 
  className 
}: { 
  className?: string;
}) {
  return (
    <motion.div
      animate={{
        opacity: [0.5, 1, 0.5],
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      className={cn("bg-muted rounded", className)}
    />
  );
}

// Smooth slide up animation for modals/tooltips
export function SlideUp({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 50 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Typewriter effect for text
export function TypeWriter({ 
  text, 
  delay = 0, 
  className 
}: { 
  text: string; 
  delay?: number; 
  className?: string;
}) {
  return (
    <motion.div
      initial={{ width: 0 }}
      animate={{ width: 'auto' }}
      transition={{ duration: text.length * 0.05, delay }}
      className={cn("overflow-hidden whitespace-nowrap", className)}
    >
      {text}
    </motion.div>
  );
}

// Smooth accordion animation
export function AccordionContent({ 
  children, 
  isOpen, 
  className 
}: { 
  children: React.ReactNode; 
  isOpen: boolean; 
  className?: string;
}) {
  return (
    <AnimatePresence initial={false}>
      {isOpen && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className={cn("overflow-hidden", className)}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
