/*
  Warnings:

  - You are about to drop the column `DisplayRo` on the `Locations` table. All the data in the column will be lost.
  - You are about to drop the column `Key` on the `Locations` table. All the data in the column will be lost.
  - You are about to drop the column `Value` on the `Locations` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[Slug]` on the table `Locations` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `Level` to the `Locations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `Name` to the `Locations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `Slug` to the `Locations` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "LocationLevel" AS ENUM ('Level0', 'Level1', 'Level2', 'Level3', 'Level4');

-- DropIndex
DROP INDEX "Locations_Key_key";

-- DropIndex
DROP INDEX "Locations_Value_key";

-- AlterTable
ALTER TABLE "Locations" DROP COLUMN "DisplayRo",
DROP COLUMN "Key",
DROP COLUMN "Value",
ADD COLUMN     "IsCapital" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "Level" "LocationLevel" NOT NULL,
ADD COLUMN     "Name" TEXT NOT NULL,
ADD COLUMN     "Slug" TEXT NOT NULL,
ADD COLUMN     "SpecialType" TEXT,
ALTER COLUMN "Id" DROP DEFAULT;
DROP SEQUENCE "Locations_Id_seq";

-- CreateIndex
CREATE UNIQUE INDEX "Locations_Slug_key" ON "Locations"("Slug");
