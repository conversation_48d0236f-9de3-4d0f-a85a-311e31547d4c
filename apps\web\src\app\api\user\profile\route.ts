import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions, type ExtendedSession } from '@repo/auth';
import jwt from 'jsonwebtoken';
import apiFetch from '@/lib/api-client';

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: 'Nu ești autentificat.' },
        { status: 401 }
      );
    }

    // Create JWT token for backend authentication
    const internalApiJwt = jwt.sign(
      {
        id: session.user.id,
        roles: session.user.roles,
        isAdmin: session.user.isAdmin,
      },
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '5m' }
    );

    // Call the backend API to get fresh user data
    const apiResponse = await apiFetch(`auth/user-by-id`, {
      method: 'POST',
      body: JSON.stringify({ id: session.user.id }),
      jwt: internalApiJwt,
    });

    if (!apiResponse.ok) {
      const errorData = await apiResponse.json();
      console.error('[User Profile API] Backend error:', errorData);
      return NextResponse.json(
        { success: false, message: errorData.message || 'Eroare la preluarea datelor profilului.' },
        { status: apiResponse.status }
      );
    }

    const userData = await apiResponse.json();

    console.log(`[User Profile API] Fresh profile data fetched successfully for user ${session.user.id}`);
    
    // Transform the data to match the expected format
    const response = {
      success: true,
      user: {
        id: userData.Id,
        fullName: userData.FullName,
        email: userData.Email,
        phone: userData.Phone,
        bio: userData.Bio,
        image: userData.AvatarUrl || userData.Image,
        avatarUrl: userData.AvatarUrl,
        SpokenLanguages: userData.SpokenLanguages || [],
        roles: userData.UserRoles?.map((ur: any) => ur.Role.Name) || [],
        isProvider: userData.UserRoles?.some((ur: any) => ur.Role.Name === 'Provider') || false,
        isAdmin: userData.UserRoles?.some((ur: any) => ur.Role.Name === 'Admin') || false,
        provider: userData.Accounts?.[0]?.Provider || 'credentials',
        createdAt: userData.CreatedAt,
        EmailVerified: userData.EmailVerified,
        MustChangePassword: userData.MustChangePassword,
      },
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('[User Profile API] Unexpected error:', error);
    return NextResponse.json(
      { success: false, message: 'A apărut o eroare neașteptată.' },
      { status: 500 }
    );
  }
}
