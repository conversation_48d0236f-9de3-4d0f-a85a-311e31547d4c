
"use client";
import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { Button } from '@/components/ui/button-mobile';
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';
import type { CaregiverSearchResult } from '@repo/types';
import { MaterialIcons } from '@expo/vector-icons';

interface CaregiverListItemProps {
  caregiver: CaregiverSearchResult;
}

export function CaregiverListItem({ caregiver }: CaregiverListItemProps) {
  const { translate } = useLanguage();
  const canViewProfile = caregiver.serviceIdForLink !== -1;

  return (
    <View style={styles.card}>
      <Image source={{ uri: caregiver.imageUrl || 'https://placehold.co/100x100.png' }} style={styles.image} />
      <View style={styles.content}>
        <Text style={styles.name}>{caregiver.name}</Text>
        <Text style={styles.serviceType}>{caregiver.serviceType}</Text>
        <View style={styles.locationContainer}>
          <MaterialIcons name="location-on" size={12} color="gray" />
          <Text style={styles.location}>{caregiver.location}</Text>
        </View>
        <View style={styles.ratingContainer}>
          <MaterialIcons name="star" size={12} color="gold" />
          <Text style={styles.rating}>{caregiver.rating.toFixed(1)} ({caregiver.reviewsCount} {translate(commonTranslations, 'reviewsSuffix')})</Text>
        </View>
        <Text style={styles.description}>{caregiver.description}</Text>
      </View>
      <View style={styles.action}>
        <Button title={translate(commonTranslations, 'viewProfileButton')} disabled={!canViewProfile} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
    card: {
        flexDirection: 'row',
        margin: 8,
        backgroundColor: 'white',
        borderRadius: 8,
        overflow: 'hidden',
        alignItems: 'center',
    },
    image: {
        width: 100,
        height: 100,
    },
    content: {
        flex: 1,
        padding: 8,
    },
    name: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    serviceType: {
        fontSize: 12,
        color: 'gray',
    },
    locationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    location: {
        fontSize: 12,
        color: 'gray',
        marginLeft: 4,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rating: {
        fontSize: 12,
        marginLeft: 4,
    },
    description: {
        fontSize: 12,
        marginVertical: 8,
    },
    action: {
        padding: 8,
    }
})
