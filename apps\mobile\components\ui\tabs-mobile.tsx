
"use client"

import * as React from "react"
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { cn } from "@/lib/utils-mobile";

const TabsContext = React.createContext({
    value: '',
    onValueChange: (value: string) => {},
});

const Tabs = ({ value, onValueChange, children }: { value: string, onValueChange: (value: string) => void, children: React.ReactNode }) => {
    return (
        <TabsContext.Provider value={{ value, onValueChange }}>
            <View>{children}</View>
        </TabsContext.Provider>
    );
};

const TabsList = ({ children }: { children: React.ReactNode }) => {
    return (
        <View style={styles.tabsListContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabsList}>
                {children}
            </ScrollView>
        </View>
    );
};

const TabsTrigger = ({ value, label, children }: { value: string, label: string, children?: React.ReactNode }) => {
    const { value: activeValue, onValueChange } = React.useContext(TabsContext);
    const isActive = activeValue === value;

    return (
        <TouchableOpacity onPress={() => onValueChange(value)} style={[styles.trigger, isActive && styles.activeTrigger]}>
            <Text style={[styles.triggerText, isActive && styles.activeTriggerText]}>{label}</Text>
        </TouchableOpacity>
    );
};

const TabsContent = ({ value, children }: { value: string, children: React.ReactNode }) => {
    const { value: activeValue } = React.useContext(TabsContext);
    return activeValue === value ? <View>{children}</View> : null;
};

const styles = StyleSheet.create({
    tabsListContainer: {
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    tabsList: {
        flexDirection: 'row',
        paddingHorizontal: 8,
    },
    trigger: {
        paddingVertical: 12,
        paddingHorizontal: 16,
    },
    activeTrigger: {
        borderBottomWidth: 2,
        borderBottomColor: '#3F51B5',
    },
    triggerText: {
        color: '#6B7280',
    },
    activeTriggerText: {
        color: '#3F51B5',
        fontWeight: 'bold',
    },
});

export { Tabs, TabsList, TabsTrigger, TabsContent };
