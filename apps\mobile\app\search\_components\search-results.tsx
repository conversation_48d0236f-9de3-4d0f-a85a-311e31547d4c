
"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useLanguage } from '@/contexts/language-context-mobile';
import { commonTranslations } from '@repo/translations';
import { SearchFilters } from './filters';
import { CaregiverCard } from './caregiver-card';
import { CaregiverListItem } from './caregiver-list-item';
import { Button } from '@/components/ui/button-mobile';
import { View, Text, FlatList, ActivityIndicator, StyleSheet, TouchableOpacity } from "react-native";
import { MaterialIcons } from '@expo/vector-icons';
import { cn } from "@/lib/utils-mobile";
import type { CaregiverSearchResult } from '@repo/types';

interface SearchResultsProps {
  serviceType: string;
}

export function SearchResults({ serviceType }: SearchResultsProps) {
  const { translate } = useLanguage();
  const { data: session } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [caregivers, setCaregivers] = useState<CaregiverSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [apiError, setApiError] = useState<string | null>(null);

  const currentPage = useMemo(() => Number(searchParams.get('page') || '1'), [searchParams]);
  const currentUserId = (session?.user as any)?.id;

  const fetchCaregivers = useCallback(async () => {
    setIsLoading(true);
    setApiError(null);
    const params = new URLSearchParams(searchParams.toString());
    params.set('serviceType', serviceType);
    if (currentUserId) {
        params.append('excludeProviderId', String(currentUserId));
    }

    try {
      // Assuming api.ts is set up for mobile to proxy requests
      const response = await fetch(`/api/proxy/services?${params.toString()}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch results.');
      }
      const data = await response.json();
      setCaregivers(data.caregivers || []);
      setTotalPages(data.totalPages || 0);
      setTotalItems(data.totalItems || 0);
    } catch (error) {
      setApiError(error instanceof Error ? error.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  }, [serviceType, searchParams, currentUserId]);

  useEffect(() => {
    fetchCaregivers();
  }, [fetchCaregivers]);

  const renderItem = ({ item }: { item: CaregiverSearchResult }) => (
    viewMode === 'grid' ? <CaregiverCard caregiver={item} /> : <CaregiverListItem caregiver={item} />
  );

  return (
    <View style={styles.container}>
      <SearchFilters serviceType={serviceType} />
      
      <View style={styles.header}>
        <Text style={styles.resultsText}>
          {isLoading ? 'Se încarcă...' : `Afișare ${caregivers.length} din ${totalItems} rezultate`}
        </Text>
        <View style={styles.viewButtons}>
          <TouchableOpacity onPress={() => setViewMode('grid')} style={styles.viewButton}>
            <MaterialIcons name="grid-view" color={viewMode === 'grid' ? 'blue' : 'black'} size={24} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setViewMode('list')} style={styles.viewButton}>
            <MaterialIcons name="list" color={viewMode === 'list' ? 'blue' : 'black'} size={24} />
          </TouchableOpacity>
        </View>
      </View>

      {isLoading ? <ActivityIndicator size="large" /> : apiError ? (
        <View><Text>{apiError}</Text></View>
      ) : caregivers.length > 0 ? (
        <FlatList
          data={caregivers}
          renderItem={renderItem}
          keyExtractor={(item) => item.id.toString()}
          key={viewMode} // Change key to re-render on view mode change
          numColumns={viewMode === 'grid' ? 2 : 1}
        />
      ) : (
        <View><Text>Niciun rezultat</Text></View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 8,
    },
    resultsText: {
        fontSize: 12,
        color: 'gray',
    },
    viewButtons: {
        flexDirection: 'row',
        gap: 8,
    },
    viewButton: {
        padding: 8,
        borderRadius: 4,
        backgroundColor: '#f0f0f0',
    },
})
