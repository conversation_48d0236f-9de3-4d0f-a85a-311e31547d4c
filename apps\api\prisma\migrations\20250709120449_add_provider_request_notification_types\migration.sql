/*
  Warnings:

  - The values [RequestApproved,RequestRejected] on the enum `NotificationType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "NotificationType_new" AS ENUM ('NewMessage', 'NewBookingRequest', 'BookingStatusChanged', 'ServiceStatusChanged', 'ServiceForReview', 'NewProviderRequest', 'ProviderRequestApproved', 'ProviderRequestRejected', 'General');
ALTER TABLE "Notification" ALTER COLUMN "Type" DROP DEFAULT;
ALTER TABLE "Notification" ALTER COLUMN "Type" TYPE "NotificationType_new" USING ("Type"::text::"NotificationType_new");
ALTER TYPE "NotificationType" RENAME TO "NotificationType_old";
ALTER TYPE "NotificationType_new" RENAME TO "NotificationType";
DROP TYPE "NotificationType_old";
ALTER TABLE "Notification" ALTER COLUMN "Type" SET DEFAULT 'General';
COMMIT;
