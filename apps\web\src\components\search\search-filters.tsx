
"use client";

import { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel } from '@/components/ui/select';
import { LocationCombobox } from '@/components/ui/location-combobox';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/language-context';
import { LocationService, type LocationEntry } from '@repo/services';

import { commonTranslations } from '@repo/translations';

const searchFiltersTranslations = {
  title: { ro: "Filtrează Rezultatele", ru: "Фильтровать результаты", en: "Filter Results" },
  
  locationLabel: { ro: "Loca<PERSON>ie", ru: "Местоположение", en: "Location" },
  locationPlaceholder: { ro: "Selectează locație", ru: "Выберите местоположение", en: "Select location" },
  priceRangeLabel: { ro: "Interval Preț (MDL)", ru: "Ценовой диапазон (MDL)", en: "Price Range (MDL)" },
  minRatingLabel: { ro: "Rating Minim", ru: "Минимальный рейтинг", en: "Minimum Rating" },
  minRatingPlaceholder: { ro: "Orice rating", ru: "Любой рейтинг", en: "Any rating" },
  ratingOption: { ro: "Peste {stars} stele", ru: "Более {stars} звезд", en: "Over {stars} stars" },
  availabilityLabel: { ro: "Disponibilitate", ru: "Доступность", en: "Availability" },
  availabilityWeekdays: { ro: "Zile lucrătoare", ru: "Будние дни", en: "Weekdays" },
  availabilityWeekends: { ro: "Weekenduri", ru: "Выходные", en: "Weekends" },
  availabilityEvenings: { ro: "Seri", ru: "Вечера", en: "Evenings" },
  applyButton: { ro: "Aplică Filtre", ru: "Применить фильтры", en: "Apply Filters" },
};

interface SearchFiltersProps {
  onFilterChange: (filters: { location: string; priceRange: number[]; minRating: number; availability: any }) => void;
  initialFilters?: {
    location?: string;
  };
  currentServiceType?: string; // Prop to know the current service, might be used later for specific filters
}

export function SearchFilters({ onFilterChange, initialFilters, currentServiceType }: SearchFiltersProps) {
  const { translate } = useLanguage();

  const [locationValue, setLocationValue] = useState(initialFilters?.location || "all");
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [minRating, setMinRating] = useState(0);
  const [availability, setAvailability] = useState({
    weekdays: false,
    weekends: false,
    evenings: false,
  });

  // Locations state
  const [locations, setLocations] = useState<LocationEntry[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(true);

  // Load locations on component mount
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setLocationsLoading(true);
        const locationData = await LocationService.getLocationsForSearch(translate.currentLanguage);
        setLocations(locationData);
      } catch (error) {
        console.error('Failed to load locations:', error);
      } finally {
        setLocationsLoading(false);
      }
    };

    loadLocations();
  }, [translate.currentLanguage]);

  useEffect(() => {
    if (initialFilters?.location) {
      setLocationValue(initialFilters.location);
    }
  }, [initialFilters?.location]);



  const handleApplyFilters = () => {
    onFilterChange({
      location: locationValue,
      priceRange,
      minRating,
      availability,
    });
  };
  
  const ratingOptions = [0,1,2,3,4,5].map(r => ({
    value: String(r),
    label: r === 0 ? translate(searchFiltersTranslations, 'minRatingPlaceholder') : translate(searchFiltersTranslations, 'ratingOption').replace('{stars}', String(r))
  }));

  const availabilityCheckboxes = [
    { key: 'weekdays', labelKey: 'availabilityWeekdays' },
    { key: 'weekends', labelKey: 'availabilityWeekends' },
    { key: 'evenings', labelKey: 'availabilityEvenings' },
  ];
  


  // TODO: Add logic to change displayed filters based on currentServiceType if needed in the future.
  // For now, all filters are common.

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-headline">{translate(searchFiltersTranslations, 'title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="location">{translate(searchFiltersTranslations, 'locationLabel')}</Label>
          <LocationCombobox
            locations={locations}
            value={locationValue}
            onValueChange={setLocationValue}
            placeholder={locationsLoading ? "Loading locations..." : translate(searchFiltersTranslations, 'locationPlaceholder')}
          />
        </div>

        <div className="space-y-2">
          <Label>{translate(searchFiltersTranslations, 'priceRangeLabel')}</Label> 
          <Slider
            defaultValue={[0, 1000]} 
            max={1000}
            step={10}
            value={priceRange}
            onValueChange={setPriceRange}
            className="py-2"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{priceRange[0]} MDL</span>
            <span>{priceRange[1]} MDL</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label>{translate(searchFiltersTranslations, 'minRatingLabel')}</Label>
           <Select value={String(minRating)} onValueChange={(val) => setMinRating(Number(val))}>
            <SelectTrigger id="minRating">
              <SelectValue placeholder={translate(searchFiltersTranslations, 'minRatingPlaceholder')} />
            </SelectTrigger>
            <SelectContent>
              {ratingOptions.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>{translate(searchFiltersTranslations, 'availabilityLabel')}</Label>
          <div className="space-y-2">
            {availabilityCheckboxes.map((item) => (
              <div key={item.key} className="flex items-center space-x-2">
                <Checkbox
                  id={`avail-${item.key}`}
                  checked={availability[item.key as keyof typeof availability]}
                  onCheckedChange={(checked) =>
                    setAvailability(prev => ({ ...prev, [item.key]: checked }))
                  }
                />
                <Label htmlFor={`avail-${item.key}`} className="font-normal text-sm">{translate(searchFiltersTranslations, item.labelKey as keyof typeof searchFiltersTranslations)}</Label>
              </div>
            ))}
          </div>
        </div>
        
        <Button onClick={handleApplyFilters} className="w-full">{translate(searchFiltersTranslations, 'applyButton')}</Button>
      </CardContent>
    </Card>
  );
}
    
