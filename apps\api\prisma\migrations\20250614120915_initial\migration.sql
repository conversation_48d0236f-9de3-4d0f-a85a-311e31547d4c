-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('Client', 'Provider', 'Admin');

-- CreateEnum
CREATE TYPE "ServiceStatus" AS ENUM ('Activ', 'Inactiv');

-- CreateEnum
CREATE TYPE "ServiceCategorySlug" AS ENUM ('Nanny', 'ElderCare', 'Cleaning', 'Tutoring', 'Cooking');

-- CreateEnum
CREATE TYPE "BookingStatus" AS ENUM ('Pending', 'Confirmed', 'Cancelled', 'Completed');

-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('NewMessage', 'RequestApproved', 'RequestRejected', 'NewProviderRequest', 'General');

-- CreateEnum
CREATE TYPE "ChatMessageStatus" AS ENUM ('Sent', 'Delivered', 'Read');

-- CreateEnum
CREATE TYPE "ProviderRegistrationRequestStatus" AS ENUM ('Pending', 'Approved', 'Rejected');

-- CreateTable
CREATE TABLE "User" (
    "Id" SERIAL NOT NULL,
    "Email" TEXT,
    "FullName" TEXT,
    "Password" TEXT,
    "AvatarUrl" TEXT,
    "Bio" TEXT,
    "Phone" TEXT,
    "MustChangePassword" BOOLEAN NOT NULL DEFAULT false,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "accounts" (
    "id" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "Id" SERIAL NOT NULL,
    "Name" "UserRole" NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "AdvertisedService" (
    "Id" SERIAL NOT NULL,
    "ProviderId" INTEGER NOT NULL,
    "CategoryId" INTEGER NOT NULL,
    "ServiceCategorySlug" "ServiceCategorySlug" NOT NULL,
    "ServiceName" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" "ServiceStatus" NOT NULL,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,
    "NannyServiceDetailsId" INTEGER,
    "ElderCareServiceDetailsId" INTEGER,
    "CleaningServiceDetailsId" INTEGER,
    "TutoringServiceDetailsId" INTEGER,
    "CookingServiceDetailsId" INTEGER,

    CONSTRAINT "AdvertisedService_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "NannyServiceDetails" (
    "Id" SERIAL NOT NULL,
    "AdvertisedServiceId" INTEGER,
    "LocationValue" TEXT,
    "ExperienceYears" INTEGER,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "PricePerHour" DECIMAL(10,2),
    "PricePerDay" DECIMAL(10,2),
    "PriceSubscriptionAmount" DECIMAL(10,2),
    "PriceSubscriptionUnit" TEXT,
    "PriceSubscriptionText" TEXT,
    "SubscriptionDetails" TEXT,
    "DocBuletinFileName" TEXT,
    "DocDiplomeFileNames" JSONB,
    "DocRecomandariFileNames" JSONB,
    "PreferredAge_0_2" BOOLEAN DEFAULT false,
    "PreferredAge_3_6" BOOLEAN DEFAULT false,
    "PreferredAge_7_plus" BOOLEAN DEFAULT false,
    "AvailabilityFullTime" BOOLEAN DEFAULT false,
    "AvailabilityPartTime" BOOLEAN DEFAULT false,
    "FirstAid" BOOLEAN DEFAULT false,
    "SchoolPickup" BOOLEAN DEFAULT false,
    "ActivityWalks" BOOLEAN DEFAULT false,
    "ActivityGames" BOOLEAN DEFAULT false,
    "ActivityFeeding" BOOLEAN DEFAULT false,
    "ActivitySleep" BOOLEAN DEFAULT false,

    CONSTRAINT "NannyServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ElderCareServiceDetails" (
    "Id" SERIAL NOT NULL,
    "AdvertisedServiceId" INTEGER,
    "LocationValue" TEXT,
    "ExperienceYears" INTEGER,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "PricePerHour" DECIMAL(10,2),
    "PricePerDay" DECIMAL(10,2),
    "PriceSubscriptionAmount" DECIMAL(10,2),
    "PriceSubscriptionUnit" TEXT,
    "PriceSubscriptionText" TEXT,
    "SubscriptionDetails" TEXT,
    "DocBuletinFileName" TEXT,
    "DocDiplomeFileNames" JSONB,
    "DocRecomandariFileNames" JSONB,
    "TypeMobil" BOOLEAN DEFAULT false,
    "TypePartialImobilizat" BOOLEAN DEFAULT false,
    "TypeCompletImobilizat" BOOLEAN DEFAULT false,
    "MedicalKnowledgeBasic" BOOLEAN DEFAULT false,
    "MedicalKnowledgeAdvanced" BOOLEAN DEFAULT false,
    "MedicationAdmin" BOOLEAN DEFAULT false,
    "DrivingLicense" BOOLEAN DEFAULT false,
    "ActivityCooking" BOOLEAN DEFAULT false,
    "ActivityCleaningLight" BOOLEAN DEFAULT false,
    "ActivityCompanionship" BOOLEAN DEFAULT false,

    CONSTRAINT "ElderCareServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "CleaningServiceDetails" (
    "Id" SERIAL NOT NULL,
    "AdvertisedServiceId" INTEGER,
    "LocationValue" TEXT,
    "ExperienceYears" INTEGER,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "PricePerHour" DECIMAL(10,2),
    "PricePerDay" DECIMAL(10,2),
    "PriceSubscriptionAmount" DECIMAL(10,2),
    "PriceSubscriptionUnit" TEXT,
    "PriceSubscriptionText" TEXT,
    "SubscriptionDetails" TEXT,
    "DocBuletinFileName" TEXT,
    "DocDiplomeFileNames" JSONB,
    "DocRecomandariFileNames" JSONB,
    "PropertyTypeApartments" BOOLEAN DEFAULT false,
    "PropertyTypeHouses" BOOLEAN DEFAULT false,
    "PropertyTypeOffices" BOOLEAN DEFAULT false,
    "OwnProducts" BOOLEAN DEFAULT false,
    "TypeGeneral" BOOLEAN DEFAULT false,
    "TypePostRenovation" BOOLEAN DEFAULT false,
    "TypeOccasional" BOOLEAN DEFAULT false,
    "TypeRegular" BOOLEAN DEFAULT false,
    "ExtraIroning" BOOLEAN DEFAULT false,
    "ExtraWindows" BOOLEAN DEFAULT false,
    "ExtraDisinfection" BOOLEAN DEFAULT false,

    CONSTRAINT "CleaningServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "TutoringServiceDetails" (
    "Id" SERIAL NOT NULL,
    "AdvertisedServiceId" INTEGER,
    "LocationValue" TEXT,
    "ExperienceYears" INTEGER,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "PricePerHour" DECIMAL(10,2),
    "PricePerDay" DECIMAL(10,2),
    "PriceSubscriptionAmount" DECIMAL(10,2),
    "PriceSubscriptionUnit" TEXT,
    "PriceSubscriptionText" TEXT,
    "SubscriptionDetails" TEXT,
    "DocBuletinFileName" TEXT,
    "DocDiplomeFileNames" JSONB,
    "DocRecomandariFileNames" JSONB,
    "ServiceAfterSchool" BOOLEAN DEFAULT false,
    "ServiceHomeworkHelp" BOOLEAN DEFAULT false,
    "ServiceIndividualLessons" BOOLEAN DEFAULT false,
    "Grades_1_4" BOOLEAN DEFAULT false,
    "Grades_5_8" BOOLEAN DEFAULT false,
    "Grades_9_12" BOOLEAN DEFAULT false,
    "SubjectRomanian" BOOLEAN DEFAULT false,
    "SubjectMath" BOOLEAN DEFAULT false,
    "SubjectEnglish" BOOLEAN DEFAULT false,
    "SubjectOther" TEXT,
    "FormatOnline" BOOLEAN DEFAULT false,
    "FormatOwnHome" BOOLEAN DEFAULT false,
    "FormatChildHome" BOOLEAN DEFAULT false,
    "ExtraGames" BOOLEAN DEFAULT false,
    "ExtraSnack" BOOLEAN DEFAULT false,
    "ExtraTransport" BOOLEAN DEFAULT false,
    "ExtraSupervisedHomework" BOOLEAN DEFAULT false,

    CONSTRAINT "TutoringServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "CookingServiceDetails" (
    "Id" SERIAL NOT NULL,
    "AdvertisedServiceId" INTEGER,
    "LocationValue" TEXT,
    "ExperienceYears" INTEGER,
    "AvailabilityWeekdays" BOOLEAN DEFAULT false,
    "AvailabilityWeekends" BOOLEAN DEFAULT false,
    "AvailabilityEvenings" BOOLEAN DEFAULT false,
    "PricePerHour" DECIMAL(10,2),
    "PricePerDay" DECIMAL(10,2),
    "PriceSubscriptionAmount" DECIMAL(10,2),
    "PriceSubscriptionUnit" TEXT,
    "PriceSubscriptionText" TEXT,
    "SubscriptionDetails" TEXT,
    "DocBuletinFileName" TEXT,
    "DocDiplomeFileNames" JSONB,
    "DocRecomandariFileNames" JSONB,
    "CuisineTypeTraditional" BOOLEAN DEFAULT false,
    "CuisineTypeVegetarian" BOOLEAN DEFAULT false,
    "CuisineTypeKids" BOOLEAN DEFAULT false,
    "CuisineTypeDiet" BOOLEAN DEFAULT false,
    "OffersDelivery" BOOLEAN DEFAULT false,
    "AtClientHome" BOOLEAN DEFAULT false,
    "AtOwnHome" BOOLEAN DEFAULT false,
    "OwnProducts" BOOLEAN DEFAULT false,
    "MinPortions" INTEGER,
    "WeeklySubscription" BOOLEAN DEFAULT false,
    "PricePerMeal" DECIMAL(10,2),
    "MealDetails" TEXT,

    CONSTRAINT "CookingServiceDetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ServiceCategory" (
    "Id" SERIAL NOT NULL,
    "NameKey" TEXT NOT NULL,
    "Slug" "ServiceCategorySlug" NOT NULL,
    "AiExpectedValue" TEXT NOT NULL,
    "DefaultImageHint" TEXT NOT NULL,

    CONSTRAINT "ServiceCategory_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Booking" (
    "Id" SERIAL NOT NULL,
    "ClientId" INTEGER NOT NULL,
    "ProviderId" INTEGER NOT NULL,
    "AdvertisedServiceId" INTEGER NOT NULL,
    "EventStartDateTime" TIMESTAMP(3),
    "EventEndDateTime" TIMESTAMP(3),
    "Status" "BookingStatus" NOT NULL DEFAULT 'Pending',
    "ClientNotes" TEXT,
    "ProviderNotes" TEXT,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Booking_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Review" (
    "Id" SERIAL NOT NULL,
    "BookingId" INTEGER NOT NULL,
    "ReviewerId" INTEGER NOT NULL,
    "ProviderId" INTEGER NOT NULL,
    "Rating" INTEGER NOT NULL,
    "Comment" TEXT,
    "ReviewDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ProviderRespondedAt" TIMESTAMP(3),
    "ProviderResponse" TEXT,

    CONSTRAINT "Review_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ChatRoom" (
    "Id" TEXT NOT NULL,
    "ClientId" INTEGER NOT NULL,
    "ProviderId" INTEGER NOT NULL,
    "AdvertisedServiceId" INTEGER NOT NULL,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChatRoom_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ChatMessage" (
    "Id" TEXT NOT NULL,
    "ChatRoomId" TEXT NOT NULL,
    "SenderId" INTEGER NOT NULL,
    "RecipientId" INTEGER NOT NULL,
    "Content" TEXT NOT NULL,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "Status" "ChatMessageStatus" NOT NULL DEFAULT 'Sent',

    CONSTRAINT "ChatMessage_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ProviderRegistrationRequest" (
    "Id" TEXT NOT NULL,
    "UserId" INTEGER NOT NULL,
    "UserName" TEXT NOT NULL,
    "UserEmail" TEXT NOT NULL,
    "RequestedServices" JSONB NOT NULL,
    "Status" "ProviderRegistrationRequestStatus" NOT NULL DEFAULT 'Pending',
    "AdminNotes" TEXT,
    "RequestDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProviderRegistrationRequest_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "Id" SERIAL NOT NULL,
    "UserId" INTEGER NOT NULL,
    "Message" TEXT NOT NULL,
    "Link" TEXT,
    "IsRead" BOOLEAN NOT NULL DEFAULT false,
    "Type" "NotificationType" NOT NULL DEFAULT 'General',
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "_UserRoles" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "User_Email_key" ON "User"("Email");

-- CreateIndex
CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON "accounts"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_sessionToken_key" ON "sessions"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "Role_Name_key" ON "Role"("Name");

-- CreateIndex
CREATE UNIQUE INDEX "AdvertisedService_NannyServiceDetailsId_key" ON "AdvertisedService"("NannyServiceDetailsId");

-- CreateIndex
CREATE UNIQUE INDEX "AdvertisedService_ElderCareServiceDetailsId_key" ON "AdvertisedService"("ElderCareServiceDetailsId");

-- CreateIndex
CREATE UNIQUE INDEX "AdvertisedService_CleaningServiceDetailsId_key" ON "AdvertisedService"("CleaningServiceDetailsId");

-- CreateIndex
CREATE UNIQUE INDEX "AdvertisedService_TutoringServiceDetailsId_key" ON "AdvertisedService"("TutoringServiceDetailsId");

-- CreateIndex
CREATE UNIQUE INDEX "AdvertisedService_CookingServiceDetailsId_key" ON "AdvertisedService"("CookingServiceDetailsId");

-- CreateIndex
CREATE UNIQUE INDEX "NannyServiceDetails_AdvertisedServiceId_key" ON "NannyServiceDetails"("AdvertisedServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "ElderCareServiceDetails_AdvertisedServiceId_key" ON "ElderCareServiceDetails"("AdvertisedServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "CleaningServiceDetails_AdvertisedServiceId_key" ON "CleaningServiceDetails"("AdvertisedServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "TutoringServiceDetails_AdvertisedServiceId_key" ON "TutoringServiceDetails"("AdvertisedServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "CookingServiceDetails_AdvertisedServiceId_key" ON "CookingServiceDetails"("AdvertisedServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "ServiceCategory_NameKey_key" ON "ServiceCategory"("NameKey");

-- CreateIndex
CREATE UNIQUE INDEX "ServiceCategory_Slug_key" ON "ServiceCategory"("Slug");

-- CreateIndex
CREATE UNIQUE INDEX "Review_BookingId_key" ON "Review"("BookingId");

-- CreateIndex
CREATE UNIQUE INDEX "ChatRoom_ClientId_ProviderId_AdvertisedServiceId_key" ON "ChatRoom"("ClientId", "ProviderId", "AdvertisedServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "ProviderRegistrationRequest_UserId_key" ON "ProviderRegistrationRequest"("UserId");

-- CreateIndex
CREATE UNIQUE INDEX "_UserRoles_AB_unique" ON "_UserRoles"("A", "B");

-- CreateIndex
CREATE INDEX "_UserRoles_B_index" ON "_UserRoles"("B");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AdvertisedService" ADD CONSTRAINT "AdvertisedService_ProviderId_fkey" FOREIGN KEY ("ProviderId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AdvertisedService" ADD CONSTRAINT "AdvertisedService_CategoryId_fkey" FOREIGN KEY ("CategoryId") REFERENCES "ServiceCategory"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NannyServiceDetails" ADD CONSTRAINT "NannyServiceDetails_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ElderCareServiceDetails" ADD CONSTRAINT "ElderCareServiceDetails_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CleaningServiceDetails" ADD CONSTRAINT "CleaningServiceDetails_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TutoringServiceDetails" ADD CONSTRAINT "TutoringServiceDetails_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CookingServiceDetails" ADD CONSTRAINT "CookingServiceDetails_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Booking" ADD CONSTRAINT "Booking_ClientId_fkey" FOREIGN KEY ("ClientId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Booking" ADD CONSTRAINT "Booking_ProviderId_fkey" FOREIGN KEY ("ProviderId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Booking" ADD CONSTRAINT "Booking_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Review" ADD CONSTRAINT "Review_BookingId_fkey" FOREIGN KEY ("BookingId") REFERENCES "Booking"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Review" ADD CONSTRAINT "Review_ReviewerId_fkey" FOREIGN KEY ("ReviewerId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Review" ADD CONSTRAINT "Review_ProviderId_fkey" FOREIGN KEY ("ProviderId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoom" ADD CONSTRAINT "ChatRoom_ClientId_fkey" FOREIGN KEY ("ClientId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoom" ADD CONSTRAINT "ChatRoom_ProviderId_fkey" FOREIGN KEY ("ProviderId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoom" ADD CONSTRAINT "ChatRoom_AdvertisedServiceId_fkey" FOREIGN KEY ("AdvertisedServiceId") REFERENCES "AdvertisedService"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_ChatRoomId_fkey" FOREIGN KEY ("ChatRoomId") REFERENCES "ChatRoom"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_SenderId_fkey" FOREIGN KEY ("SenderId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_RecipientId_fkey" FOREIGN KEY ("RecipientId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProviderRegistrationRequest" ADD CONSTRAINT "ProviderRegistrationRequest_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserRoles" ADD CONSTRAINT "_UserRoles_A_fkey" FOREIGN KEY ("A") REFERENCES "Role"("Id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserRoles" ADD CONSTRAINT "_UserRoles_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("Id") ON DELETE CASCADE ON UPDATE CASCADE;
