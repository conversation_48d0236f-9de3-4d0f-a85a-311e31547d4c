"use client";

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { User, Briefcase } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { useRouter, usePathname } from 'next/navigation';
import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { useRoleToggleAnalytics } from '@/lib/analytics/role-toggle-analytics';

type Role = 'client' | 'provider';

interface RoleToggleProps {
  currentRole: Role;
  availableRoles: Role[];
  onRoleChange: (role: Role) => void;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

const roleToggleTranslations = {
  client: { ro: "Client", ru: "Клиент", en: "Client" },
  provider: { ro: "Prestator", ru: "Поставщик", en: "Provider" },
  switchToClient: { ro: "Comută la vizualizarea client", ru: "Переключиться на клиентский вид", en: "Switch to client view" },
  switchToProvider: { ro: "Comută la vizualizarea prestator", ru: "Переключиться на вид поставщика", en: "Switch to provider view" },
};

export function RoleToggle({ currentRole, availableRoles, onRoleChange, className, isLoading = false, disabled = false }: RoleToggleProps) {
  const { translate } = useLanguage();
  const isProviderAvailable = availableRoles.includes('provider');
  
  // If user is not a provider, show a simple badge
  if (!isProviderAvailable) {
    return (
      <Badge variant="secondary" className={cn("flex items-center gap-2", className)}>
        <User className="w-4 h-4" />
        <span className="hidden sm:inline">{translate(roleToggleTranslations, 'client')}</span>
      </Badge>
    );
  }

  const handleKeyDown = (event: React.KeyboardEvent, role: Role) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onRoleChange(role);
    } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      event.preventDefault();
      // Switch to the other role with arrow keys
      const otherRole = role === 'client' ? 'provider' : 'client';
      if (availableRoles.includes(otherRole)) {
        onRoleChange(otherRole);
      }
    }
  };

  return (
    <div 
      className={cn("flex items-center bg-muted rounded-lg p-1", className)}
      role="tablist"
      aria-label="User role selection"
    >
      <Button
        variant={currentRole === 'client' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onRoleChange('client')}
        onKeyDown={(e) => handleKeyDown(e, 'client')}
        disabled={disabled || isLoading}
        className={cn(
          "flex items-center gap-2 transition-all duration-200 min-w-0",
          currentRole === 'client'
            ? "bg-primary text-primary-foreground shadow-sm"
            : "text-muted-foreground hover:text-foreground hover:bg-muted-foreground/10"
        )}
        role="tab"
        aria-selected={currentRole === 'client'}
        aria-controls="dashboard-content"
        aria-label={translate(roleToggleTranslations, 'switchToClient')}
        tabIndex={currentRole === 'client' ? 0 : -1}
      >
        {isLoading && currentRole !== 'client' ? (
          <Loader2 className="w-4 h-4 flex-shrink-0 animate-spin" />
        ) : (
          <User className="w-4 h-4 flex-shrink-0" />
        )}
        <span className="hidden sm:inline truncate">
          {translate(roleToggleTranslations, 'client')}
        </span>
      </Button>
      
      <Button
        variant={currentRole === 'provider' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onRoleChange('provider')}
        onKeyDown={(e) => handleKeyDown(e, 'provider')}
        disabled={disabled || isLoading}
        className={cn(
          "flex items-center gap-2 transition-all duration-200 min-w-0",
          currentRole === 'provider'
            ? "bg-primary text-primary-foreground shadow-sm"
            : "text-muted-foreground hover:text-foreground hover:bg-muted-foreground/10"
        )}
        role="tab"
        aria-selected={currentRole === 'provider'}
        aria-controls="dashboard-content"
        aria-label={translate(roleToggleTranslations, 'switchToProvider')}
        tabIndex={currentRole === 'provider' ? 0 : -1}
      >
        {isLoading && currentRole !== 'provider' ? (
          <Loader2 className="w-4 h-4 flex-shrink-0 animate-spin" />
        ) : (
          <Briefcase className="w-4 h-4 flex-shrink-0" />
        )}
        <span className="hidden sm:inline truncate">
          {translate(roleToggleTranslations, 'provider')}
        </span>
      </Button>
    </div>
  );
}

// Hook for managing role toggle state and navigation
export function useRoleToggle() {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { trackRoleSwitch, trackError } = useRoleToggleAnalytics();

  const getCurrentRole = (): Role => {
    if (pathname.includes('/dashboard/provider')) return 'provider';
    return 'client';
  };

  const switchRole = async (newRole: Role) => {
    const currentRole = getCurrentRole();
    if (currentRole === newRole) return;

    const startTime = Date.now();
    setIsLoading(true);
    setError(null);

    try {
      // Extract the current page path after the role
      const pathParts = pathname.split('/');
      const dashboardIndex = pathParts.indexOf('dashboard');

      let newPath: string;
      if (dashboardIndex !== -1 && pathParts.length > dashboardIndex + 2) {
        // If there's a sub-path after the role, preserve it
        const remainingPath = pathParts.slice(dashboardIndex + 2).join('/');
        newPath = `/dashboard/${newRole}/${remainingPath}`;
      } else {
        // Default to role dashboard (no sub-path)
        newPath = `/dashboard/${newRole}`;
      }

      console.log(`[RoleToggle] Switching from ${currentRole} to ${newRole}: ${pathname} -> ${newPath}`);

      // Navigate to new role
      router.push(newPath);

      // Track successful role switch
      trackRoleSwitch({
        from_role: currentRole,
        to_role: newRole,
        method: 'header_toggle',
        page_path: pathname,
        switch_duration_ms: Date.now() - startTime,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to switch roles';
      console.error('Error switching roles:', err);
      setError(errorMessage);

      // Track role switch error
      trackError({
        error_type: 'navigation_failed',
        error_message: errorMessage,
        current_role: currentRole,
        target_role: newRole,
      });
    } finally {
      // Reset loading state after a short delay to show the transition
      setTimeout(() => setIsLoading(false), 500);
    }

    // Announce role change to screen readers
    if (typeof window !== 'undefined') {
      const announcement = `Switched to ${newRole} view`;
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', 'polite');
      announcer.setAttribute('aria-atomic', 'true');
      announcer.className = 'sr-only';
      announcer.textContent = announcement;
      document.body.appendChild(announcer);

      // Remove the announcer after a short delay
      setTimeout(() => {
        document.body.removeChild(announcer);
      }, 1000);
    }

    // Analytics tracking
    if (typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.track('role_switch', {
        from_role: currentRole,
        to_role: newRole,
        method: 'header_toggle',
        timestamp: Date.now()
      });
    }
  };

  const retryLastSwitch = () => {
    setError(null);
    // You could store the last attempted role and retry it here
  };

  return {
    currentRole: getCurrentRole(),
    switchRole,
    isLoading,
    error,
    retryLastSwitch
  };
}
