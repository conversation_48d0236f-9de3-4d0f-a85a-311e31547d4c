{"name": "mobile", "version": "1.0.0", "private": true, "main": "expo-router/entry", "scripts": {"start": "npx expo start", "start:clear": "npx expo start --clear", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "dev": "npx expo start"}, "dependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-react": "^7.27.1", "@expo/ngrok": "^4.1.3", "@expo/server": "^0.6.3", "@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@repo/base": "*", "@repo/translations": "*", "bcryptjs": "^2.4.3", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@expo/cli": "^0.24.20", "@types/bcryptjs": "^2.4.6", "@types/react": "~19.0.10", "babel-preset-expo": "~13.0.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "expo": "^53.0.20", "typescript": "~5.8.3"}}