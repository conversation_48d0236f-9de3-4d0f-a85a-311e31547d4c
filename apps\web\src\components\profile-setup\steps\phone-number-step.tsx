"use client";

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Phone, CheckCircle, AlertCircle } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { PROFILE_SETUP_VALIDATION } from '@/types/profile-setup';

interface PhoneNumberStepProps {
  value: string;
  onChange: (phone: string) => void;
}

const phoneStepTranslations = {
  en: {
    title: "Add Your Phone Number",
    description: "Your phone number is required for reservations and communication with service providers.",
    phoneLabel: "Phone Number",
    phonePlaceholder: "e.g., +373 69 123 456",
    whyNeeded: "Why do we need your phone number?",
    reason1: "Service providers can contact you directly",
    reason2: "Receive SMS notifications about your reservations",
    reason3: "Emergency contact for service appointments",
    reason4: "Verify your identity for safety",
    validPhone: "Phone number looks good!",
    invalidPhone: "Please enter a valid phone number",
    formatHint: "Use international format with country code (e.g., +373 for Moldova)",
    privacyNote: "Your phone number will only be shared with service providers you book with."
  },
  ro: {
    title: "Adaugă Numărul de Telefon",
    description: "Numărul de telefon este necesar pentru rezervări și comunicarea cu furnizorii de servicii.",
    phoneLabel: "Numărul de Telefon",
    phonePlaceholder: "ex., +373 69 123 456",
    whyNeeded: "De ce avem nevoie de numărul tău de telefon?",
    reason1: "Furnizorii de servicii te pot contacta direct",
    reason2: "Primești notificări SMS despre rezervările tale",
    reason3: "Contact de urgență pentru programările de servicii",
    reason4: "Verificarea identității pentru siguranță",
    validPhone: "Numărul de telefon arată bine!",
    invalidPhone: "Te rugăm să introduci un număr de telefon valid",
    formatHint: "Folosește formatul internațional cu codul țării (ex., +373 pentru Moldova)",
    privacyNote: "Numărul tău de telefon va fi partajat doar cu furnizorii de servicii cu care faci rezervări."
  },
  ru: {
    title: "Добавьте Номер Телефона",
    description: "Ваш номер телефона необходим для бронирования и общения с поставщиками услуг.",
    phoneLabel: "Номер Телефона",
    phonePlaceholder: "напр., +373 69 123 456",
    whyNeeded: "Зачем нам нужен ваш номер телефона?",
    reason1: "Поставщики услуг могут связаться с вами напрямую",
    reason2: "Получайте SMS-уведомления о ваших бронированиях",
    reason3: "Экстренный контакт для записи на услуги",
    reason4: "Подтверждение личности для безопасности",
    validPhone: "Номер телефона выглядит хорошо!",
    invalidPhone: "Пожалуйста, введите действительный номер телефона",
    formatHint: "Используйте международный формат с кодом страны (напр., +373 для Молдовы)",
    privacyNote: "Ваш номер телефона будет передан только поставщикам услуг, у которых вы бронируете."
  }
};

export function PhoneNumberStep({ value, onChange }: PhoneNumberStepProps) {
  const { translate } = useLanguage();
  const [touched, setTouched] = useState(false);

  const isValid = value.length >= PROFILE_SETUP_VALIDATION.phone.minLength && 
                  value.length <= PROFILE_SETUP_VALIDATION.phone.maxLength &&
                  PROFILE_SETUP_VALIDATION.phone.pattern.test(value);

  const showValidation = touched && value.length > 0;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleBlur = () => {
    setTouched(true);
  };

  const reasons = [
    translate(phoneStepTranslations, 'reason1'),
    translate(phoneStepTranslations, 'reason2'),
    translate(phoneStepTranslations, 'reason3'),
    translate(phoneStepTranslations, 'reason4'),
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <Phone className="w-6 h-6 text-primary" />
          </div>
        </div>
        <h3 className="text-xl font-semibold mb-2">
          {translate(phoneStepTranslations, 'title')}
        </h3>
        <p className="text-muted-foreground">
          {translate(phoneStepTranslations, 'description')}
        </p>
      </div>

      {/* Phone Input */}
      <div className="space-y-2">
        <Label htmlFor="phone" className="text-sm font-medium">
          {translate(phoneStepTranslations, 'phoneLabel')} *
        </Label>
        <div className="relative">
          <Input
            id="phone"
            type="tel"
            value={value}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder={translate(phoneStepTranslations, 'phonePlaceholder')}
            className={`pl-4 pr-10 ${
              showValidation 
                ? isValid 
                  ? 'border-green-500 focus:border-green-500' 
                  : 'border-red-500 focus:border-red-500'
                : ''
            }`}
          />
          {showValidation && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {isValid ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-500" />
              )}
            </div>
          )}
        </div>
        
        {/* Validation Message */}
        {showValidation && (
          <p className={`text-sm ${isValid ? 'text-green-600' : 'text-red-600'}`}>
            {isValid 
              ? translate(phoneStepTranslations, 'validPhone')
              : translate(phoneStepTranslations, 'invalidPhone')
            }
          </p>
        )}
        
        {/* Format Hint */}
        <p className="text-xs text-muted-foreground">
          {translate(phoneStepTranslations, 'formatHint')}
        </p>
      </div>

      {/* Why We Need This */}
      <Card className="bg-muted/30">
        <CardContent className="p-4">
          <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
            <Phone className="w-4 h-4 text-primary" />
            {translate(phoneStepTranslations, 'whyNeeded')}
          </h4>
          <ul className="space-y-2">
            {reasons.map((reason, index) => (
              <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>{reason}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Privacy Note */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
          🔒 {translate(phoneStepTranslations, 'privacyNote')}
        </p>
      </div>
    </div>
  );
}
