
import { apiFetch } from './api';
import bcrypt from 'bcryptjs';

type RegisterFunction = (fullName: string, email: string, password: string) => Promise<any>;
type LoginFunction = (email: string, password: string) => Promise<any>;

interface AuthService {
  register: RegisterFunction;
  login: LoginFunction;
}

export const authService: AuthService = {
  register: async (fullName, email, password) => {
    return apiFetch('auth/register', {
      method: 'POST',
      body: JSON.stringify({ fullName, email, password }),
    });
  },
  login: async (email, password) => {
    try {
        const user = await apiFetch('auth/user-by-email', {
            method: 'POST',
            body: JSON.stringify({ email }),
        });

        if (user && user.Password) {
            // IMPORTANT: bcryptjs needs a polyfill for random values in some RN environments.
            // If you face issues, ensure `react-native-get-random-values` is installed and imported at the top of your entry file (e.g., index.js or App.js).
            // For now, we assume the environment is set up correctly.
            const isPasswordValid = bcrypt.compareSync(password, user.Password);
            if (isPasswordValid) {
                // Do not send password back to the app
                const { Password, ...userToReturn } = user;
                return { success: true, user: userToReturn };
            }
        }
        // Throw an error for both user not found and password mismatch for security
        throw new Error("Email sau parolă incorectă.");

    } catch (error) {
       // Re-throw the error to be caught by the component
       throw error;
    }
  }
};
