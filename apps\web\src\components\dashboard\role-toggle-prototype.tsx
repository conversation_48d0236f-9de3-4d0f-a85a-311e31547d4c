"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { User, Briefcase } from 'lucide-react';
import { cn } from '@/lib/utils';

type Role = 'client' | 'provider';

interface RoleToggleProps {
  currentRole: Role;
  availableRoles: Role[];
  onRoleChange: (role: Role) => void;
  className?: string;
}

export function RoleToggle({ currentRole, availableRoles, onRoleChange, className }: RoleToggleProps) {
  const isProviderAvailable = availableRoles.includes('provider');
  
  if (!isProviderAvailable) {
    return (
      <Badge variant="secondary" className={cn("flex items-center gap-2", className)}>
        <User className="w-4 h-4" />
        Client
      </Badge>
    );
  }

  return (
    <div className={cn("flex items-center bg-muted rounded-lg p-1", className)}>
      <Button
        variant={currentRole === 'client' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onRoleChange('client')}
        className={cn(
          "flex items-center gap-2 transition-all duration-200",
          currentRole === 'client' 
            ? "bg-primary text-primary-foreground shadow-sm" 
            : "text-muted-foreground hover:text-foreground"
        )}
        aria-pressed={currentRole === 'client'}
        aria-label="Switch to client view"
      >
        <User className="w-4 h-4" />
        <span className="hidden sm:inline">Client</span>
      </Button>
      
      <Button
        variant={currentRole === 'provider' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onRoleChange('provider')}
        className={cn(
          "flex items-center gap-2 transition-all duration-200",
          currentRole === 'provider' 
            ? "bg-primary text-primary-foreground shadow-sm" 
            : "text-muted-foreground hover:text-foreground"
        )}
        aria-pressed={currentRole === 'provider'}
        aria-label="Switch to provider view"
      >
        <Briefcase className="w-4 h-4" />
        <span className="hidden sm:inline">Provider</span>
      </Button>
    </div>
  );
}

interface RoleIndicatorProps {
  currentRole: Role;
  className?: string;
}

export function RoleIndicator({ currentRole, className }: RoleIndicatorProps) {
  const roleConfig = {
    client: {
      icon: User,
      label: 'Client Dashboard',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      iconColor: 'text-blue-600'
    },
    provider: {
      icon: Briefcase,
      label: 'Provider Dashboard',
      bgColor: 'bg-green-50',
      textColor: 'text-green-700',
      iconColor: 'text-green-600'
    }
  };

  const config = roleConfig[currentRole];
  const Icon = config.icon;

  return (
    <div className={cn(
      "flex items-center gap-2 px-3 py-2 rounded-lg border",
      config.bgColor,
      config.textColor,
      className
    )}>
      <Icon className={cn("w-4 h-4", config.iconColor)} />
      <span className="text-sm font-medium">
        Currently viewing: {config.label}
      </span>
    </div>
  );
}

// Demo component to showcase the role toggle functionality
export function RoleToggleDemo() {
  const [currentRole, setCurrentRole] = useState<Role>('client');
  const [isProvider, setIsProvider] = useState(true);

  const handleRoleChange = (role: Role) => {
    setCurrentRole(role);
    // In real implementation, this would update the URL and trigger navigation
    console.log(`Role changed to: ${role}`);
  };

  const availableRoles: Role[] = isProvider ? ['client', 'provider'] : ['client'];

  return (
    <div className="space-y-6 p-6 max-w-2xl mx-auto">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Role Toggle Prototype</h2>
        <p className="text-muted-foreground">
          This demonstrates the recommended header role toggle solution for dual-role users.
        </p>
      </div>

      {/* Simulated Header */}
      <div className="flex items-center justify-between p-4 bg-background border rounded-lg">
        <div className="flex items-center gap-4">
          <div className="font-bold text-lg">BONAMI</div>
          <div className="hidden md:block text-sm text-muted-foreground">Dashboard</div>
        </div>
        
        <div className="flex items-center gap-4">
          <RoleToggle
            currentRole={currentRole}
            availableRoles={availableRoles}
            onRoleChange={handleRoleChange}
          />
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-semibold">
            JD
          </div>
        </div>
      </div>

      {/* Role Indicator */}
      <RoleIndicator currentRole={currentRole} />

      {/* Simulated Navigation */}
      <div className="grid md:grid-cols-4 gap-4">
        <div className="md:col-span-1">
          <div className="space-y-2 p-4 bg-card border rounded-lg">
            <h3 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
              Navigation
            </h3>
            {currentRole === 'client' ? (
              <div className="space-y-1">
                <div className="flex items-center gap-2 p-2 bg-primary/10 text-primary rounded text-sm font-medium">
                  <User className="w-4 h-4" />
                  Dashboard
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  📅 My Bookings
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  💬 Messages
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  ⚙️ Settings
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  📍 Addresses
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                <div className="flex items-center gap-2 p-2 bg-primary/10 text-primary rounded text-sm font-medium">
                  <Briefcase className="w-4 h-4" />
                  Dashboard
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  📅 Calendar
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  🛠️ Services
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  💬 Messages
                </div>
                <div className="flex items-center gap-2 p-2 text-muted-foreground hover:text-foreground rounded text-sm">
                  ⚙️ Settings
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Simulated Content */}
        <div className="md:col-span-3">
          <div className="p-6 bg-card border rounded-lg">
            <h3 className="text-xl font-semibold mb-4">
              {currentRole === 'client' ? 'Client Dashboard' : 'Provider Dashboard'}
            </h3>
            <div className="space-y-4">
              {currentRole === 'client' ? (
                <>
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900">Welcome back!</h4>
                    <p className="text-blue-700 text-sm">You have 2 upcoming bookings this week.</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h5 className="font-medium">Recent Bookings</h5>
                      <p className="text-sm text-muted-foreground">View your booking history</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h5 className="font-medium">Find Services</h5>
                      <p className="text-sm text-muted-foreground">Search for new providers</p>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900">Provider Overview</h4>
                    <p className="text-green-700 text-sm">You have 5 new booking requests to review.</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h5 className="font-medium">Service Performance</h5>
                      <p className="text-sm text-muted-foreground">View your service metrics</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h5 className="font-medium">Manage Services</h5>
                      <p className="text-sm text-muted-foreground">Edit your service offerings</p>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Demo Controls */}
      <div className="p-4 bg-muted rounded-lg">
        <h4 className="font-medium mb-2">Demo Controls</h4>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={isProvider}
              onChange={(e) => {
                setIsProvider(e.target.checked);
                if (!e.target.checked) setCurrentRole('client');
              }}
            />
            <span className="text-sm">User is a provider</span>
          </label>
        </div>
        <p className="text-xs text-muted-foreground mt-2">
          Toggle this to see how the interface adapts for client-only vs dual-role users.
        </p>
      </div>
    </div>
  );
}
