
"use client";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { useLanguage } from '@/contexts/language-context';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const gdprPageTranslations = {
  pageTitle: { ro: "Informații GDPR", ru: "Информация GDPR", en: "GDPR Information" },
  lastUpdated: { ro: "Ultima actualizare: 5 Iunie 2024", ru: "Последнее обновление: 5 июня 2024 г.", en: "Last updated: June 5, 2024" },
  
  introTitle: { ro: "Introducere", ru: "Введение", en: "Introduction" },
  introContent: { 
    ro: "bonami („noi”, „nouă”, „nostru”) se angajează să protejeze și să respecte confidențialitatea dvs. Această politică explică modul în care colectăm, utilizăm și protejăm datele dvs. personale atunci când utilizați platforma noastră.",
    ru: "bonami («мы», «нас», «наш») обязуется защищать и уважать вашу конфиденциальность. Эта политика объясняет, как мы собираем, используем и защищаем ваши личные данные при использовании нашей платформы.",
    en: "bonami ('we', 'us', 'our') is committed to protecting and respecting your privacy. This policy explains how we collect, use, and protect your personal data when you use our platform."
  },

  controllerTitle: { ro: "Operatorul de Date", ru: "Оператор данных", en: "Data Controller" },
  controllerContent: {
    ro: "bonami este operatorul de date pentru datele personale pe care le colectăm de la dvs. sau pe care ni le furnizați.",
    ru: "bonami является оператором данных для личных данных, которые мы собираем от вас или которые вы нам предоставляете.",
    en: "bonami is the data controller for the personal data we collect from you or that you provide to us."
  },

  dataCollectedTitle: { ro: "Ce Date Colectăm", ru: "Какие данные мы собираем", en: "What Data We Collect" },
  dataCollectedContent: {
    ro: "Putem colecta și prelucra următoarele date despre dvs.: informații pe care le furnizați prin completarea formularelor de pe site-ul nostru (de exemplu, la înregistrare), detalii despre tranzacțiile efectuate prin intermediul site-ului nostru, și date despre vizitele dvs. pe site-ul nostru, inclusiv, dar fără a se limita la, date de trafic, date de localizare, și alte date de comunicare.",
    ru: "Мы можем собирать и обрабатывать следующие данные о вас: информация, которую вы предоставляете, заполняя формы на нашем сайте (например, при регистрации), детали транзакций, совершенных через наш сайт, и данные о ваших посещениях нашего сайта, включая, но не ограничиваясь, данные о трафике, данные о местоположении и другие коммуникационные данные.",
    en: "We may collect and process the following data about you: information you provide by filling in forms on our site (e.g., when registering), details of transactions you carry out through our site, and details of your visits to our site including, but not limited to, traffic data, location data, and other communication data."
  },

  howWeUseDataTitle: { ro: "Cum Utilizăm Datele Dvs.", ru: "Как мы используем ваши данные", en: "How We Use Your Data" },
  howWeUseDataContent: {
    ro: "Utilizăm informațiile pe care le deținem despre dvs. pentru a vă oferi serviciile noastre, pentru a ne îndeplini obligațiile contractuale, pentru a vă notifica despre modificările aduse serviciilor noastre și pentru a asigura că conținutul de pe site-ul nostru este prezentat în cel mai eficient mod pentru dvs.",
    ru: "Мы используем имеющуюся у нас информацию о вас для предоставления наших услуг, выполнения наших договорных обязательств, уведомления вас об изменениях в наших услугах и обеспечения того, чтобы контент на нашем сайте представлялся наиболее эффективным для вас образом.",
    en: "We use information held about you to provide you with our services, to carry out our contractual obligations, to notify you about changes to our service, and to ensure that content from our site is presented in the most effective manner for you."
  },

  yourRightsTitle: { ro: "Drepturile Dvs.", ru: "Ваши права", en: "Your Rights" },
  yourRightsContent: {
    ro: "Aveți dreptul de a solicita accesul la datele dvs. personale, rectificarea sau ștergerea acestora, restricționarea prelucrării, de a vă opune prelucrării, precum și dreptul la portabilitatea datelor. De asemenea, aveți dreptul de a depune o plângere la autoritatea de supraveghere.",
    ru: "Вы имеете право запрашивать доступ к вашим личным данным, их исправление или удаление, ограничение обработки, возражать против обработки, а также право на переносимость данных. Вы также имеете право подать жалобу в надзорный орган.",
    en: "You have the right to request access to, rectification or erasure of your personal data, restriction of processing, to object to processing, as well as the right to data portability. You also have the right to lodge a complaint with a supervisory authority."
  },
  
  contactTitle: { ro: "Contact", ru: "Контакт", en: "Contact" },
  contactContent: {
    ro: "Întrebările, comentariile și solicitările referitoare la această politică de confidențialitate sunt binevenite și trebuie adresate la [adresa de email pentru GDPR/contact].",
    ru: "Вопросы, комментарии и запросы относительно этой политики конфиденциальности приветствуются и должны быть адресованы по адресу [адрес электронной почты для GDPR/контактов].",
    en: "Questions, comments and requests regarding this privacy policy are welcomed and should be addressed to [email address for GDPR/contact]."
  }
};

export default function GdprPage() {
  const { translate } = useLanguage();

  const sections = [
    { titleKey: 'introTitle', contentKey: 'introContent' },
    { titleKey: 'controllerTitle', contentKey: 'controllerContent' },
    { titleKey: 'dataCollectedTitle', contentKey: 'dataCollectedContent' },
    { titleKey: 'howWeUseDataTitle', contentKey: 'howWeUseDataContent' },
    { titleKey: 'yourRightsTitle', contentKey: 'yourRightsContent' },
    { titleKey: 'contactTitle', contentKey: 'contactContent' },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <Card className="max-w-3xl mx-auto mt-10 shadow-lg">
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center font-headline">
              {translate(gdprPageTranslations, 'pageTitle')}
            </CardTitle>
            <p className="text-sm text-muted-foreground text-center">{translate(gdprPageTranslations, 'lastUpdated')}</p>
          </CardHeader>
          <CardContent className="space-y-6 text-foreground/80">
            {sections.map(section => (
              <div key={section.titleKey}>
                <h2 className="text-xl font-semibold mb-2 font-headline text-foreground">
                  {translate(gdprPageTranslations, section.titleKey)}
                </h2>
                <p className="whitespace-pre-line">
                  {translate(gdprPageTranslations, section.contentKey)}
                </p>
              </div>
            ))}
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
