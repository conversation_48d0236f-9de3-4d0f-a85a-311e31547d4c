
"use client";
import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, <PERSON>r<PERSON><PERSON><PERSON>, CheckCircle, XCircle, FileText, MessageSquareWarning, Search, ChevronLeft, ChevronRight } from "lucide-react";
import type { ProviderRegistrationRequest, ServiceCategory, ServiceCategorySlug } from "@prisma/client";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { getFullLocationPathDisplayHelper } from '@/lib/locations';
import { cn } from '@/lib/utils';
import React from 'react';
import { Switch } from '@/components/ui/switch';
import { revalidateTag } from 'next/cache';
import { GranularServiceCard } from './components/granular-service-card';

// Updated interface for granular provider registration
export interface ProviderRegistrationRequestWithServices extends ProviderRegistrationRequest {
  PendingServices: PendingService[];
}

export interface PendingService {
  Id: number;
  ServiceName: string;
  Description: string;
  ServiceCategorySlug: ServiceCategorySlug;
  ExperienceYears: number;
  Status: 'PendingReview' | 'Approved' | 'Rejected' | 'RequiresChanges';
  AdminNotes?: string | null;
  DocumentPaths: string[];
  CreatedAt: string;
  UpdatedAt: string;
  Category: {
    Id: number;
    NameKey: string;
    Slug: string;
  };
  // Service-specific details
  NannyServiceDetails?: any;
  ElderCareServiceDetails?: any;
  CleaningServiceDetails?: any;
  TutoringServiceDetails?: any;
  CookingServiceDetails?: any;
}

// Legacy interface for backward compatibility
export interface ServiceRequestDetailForAdmin {
  categoryId: number;
  serviceCategorySlug: ServiceCategorySlug;
  experienceYears: number | string;
  description: string;
  availabilityWeekdays?: boolean;
  availabilityWeekends?: boolean;
  availabilityEvenings?: boolean;
  LocationValue?: string;
  PricePerHour?: string;
  PricePerDay?: string;
  PriceSubscriptionAmount?: string;
  PriceSubscriptionUnit?: string;
  PriceSubscriptionText?: string;
  SubscriptionDetails?: string;
  DocBuletinFileName?: string | null;
  DocDiplomeFileNames?: string[] | null;
  DocRecomandariFileNames?: string[] | null;
  [key: string]: any; // Allow other properties
}

// --- Helper Components for Displaying Details ---

const DetailItem = ({ label, value }: { label: string, value?: string | React.ReactNode | null }) => (
  value ? <div className="grid grid-cols-1 md:grid-cols-3 gap-1 py-2 border-b last:border-b-0 items-center"><dt className="font-semibold text-muted-foreground">{label}</dt><dd className="md:col-span-2 text-foreground">{value}</dd></div> : null
);

const BooleanDetailItem = ({ label, value }: { label: string, value?: boolean | null }) => (
    <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm bg-background">
        <Label htmlFor={`switch-${label.replace(/\s+/g, '-')}`} className="text-sm font-medium text-foreground cursor-default">
            {label}
        </Label>
        <Switch
            id={`switch-${label.replace(/\s+/g, '-')}`}
            checked={!!value}
            disabled
            aria-readonly
        />
    </div>
);

const DocumentItem = ({ fileName, label }: { fileName?: string | null, label: string }) => {
    return (
        <div className="flex items-center gap-2 text-sm">
            {fileName ? (
                <>
                    <CheckCircle className="w-4 h-4 text-green-600 shrink-0" />
                    <span className="font-medium text-foreground">{label}:</span>
                    <Button variant="link" size="sm" className="p-0 h-auto text-primary hover:underline flex items-center gap-1.5 text-left break-all -ml-1">
                        <FileText className="w-4 h-4 shrink-0" /> {fileName}
                    </Button>
                </>
            ) : (
                <>
                    <XCircle className="w-4 h-4 text-muted-foreground shrink-0" />
                    <span className="text-muted-foreground">{label} (neatașat)</span>
                </>
            )}
        </div>
    );
};

// --- Main Page Component ---

export default function AdminProviderRequestsPage() {
  const { translate } = useLanguage();
  const { toast } = useToast();
  const router = useRouter();
  const [requests, setRequests] = useState<ProviderRegistrationRequestWithServices[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingRequestId, setUpdatingRequestId] = useState<string | null>(null);
  const [updatingServiceId, setUpdatingServiceId] = useState<number | null>(null);
  const [currentAction, setCurrentAction] = useState<'approve' | 'reject' | null>(null);
  const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([]);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<ProviderRegistrationRequestWithServices | null>(null);
  const [rejectionReason, setRejectionReason] = useState("");

  const [serviceStatusFilter, setServiceStatusFilter] = useState('all');

  // New state for filters and pagination
  const [currentStatus, setCurrentStatus] = useState<'All' | 'Pending' | 'Approved' | 'Rejected'>('Pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10,
    hasNext: false,
    hasPrev: false
  });

  const fetchRequests = async (page = currentPage, status = currentStatus, search = searchTerm) => {
    console.log('[Admin Provider Requests] fetchRequests called with:', { page, status, search });
    setIsLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        status,
        page: page.toString(),
        limit: '10',
        search
      });

      console.log('[Admin Provider Requests] Making API calls to:', {
        providerRequests: `/api/proxy/admin/provider-requests?${params}`,
        serviceCategories: `/api/proxy/service-categories`
      });

      const [requestsResponse, categoriesResponse] = await Promise.all([
        fetch(`/api/proxy/admin/provider-requests?${params}`),
        fetch(`/api/proxy/service-categories`)
      ]);

      console.log('[Admin Provider Requests] API responses:', {
        requestsStatus: requestsResponse.status,
        categoriesStatus: categoriesResponse.status
      });

      if (!requestsResponse.ok) {
        throw new Error(translate(commonTranslations, 'adminErrorFetchingRequests'));
      }
      const requestsData = await requestsResponse.json();
      setRequests(requestsData.requests || []);
      setPagination(requestsData.pagination || {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        limit: 10,
        hasNext: false,
        hasPrev: false
      });
      
      if (!categoriesResponse.ok) {
        throw new Error('Failed to fetch service categories');
      }
      const categoriesData = await categoriesResponse.json();
      setServiceCategories(categoriesData || []);

    } catch (err) {
      setError(err instanceof Error ? err.message : translate(commonTranslations, 'adminErrorUnknown'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log('[Admin Provider Requests] useEffect triggered with:', { currentPage, currentStatus, searchTerm });
    fetchRequests(currentPage, currentStatus, searchTerm);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [translate, currentPage, currentStatus, searchTerm]);

  const getCategoryNameBySlug = (slug: ServiceCategorySlug): string => {
    const category = serviceCategories.find(cat => cat.Slug === slug);
    return category ? translate(commonTranslations, category.NameKey as keyof typeof commonTranslations) : slug;
  };

  // Helper functions for status and pagination
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Pending': return 'secondary'; // Will be styled as yellow/orange
      case 'Approved': return 'default'; // Will be styled as green
      case 'Rejected': return 'destructive'; // Will be styled as red
      default: return 'secondary';
    }
  };

  const getStatusBadgeClassName = (status: string) => {
    switch (status) {
      case 'Pending': return 'bg-yellow-100 text-yellow-800 border-yellow-300 hover:bg-yellow-200';
      case 'Approved': return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';
      case 'Rejected': return 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200';
    }
  };

  const handleStatusChange = (status: 'All' | 'Pending' | 'Approved' | 'Rejected') => {
    setCurrentStatus(status);
    setCurrentPage(1);
  };

  const handleSearchChange = (search: string) => {
    setSearchTerm(search);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const getCategoryNameById = (categoryId: number): string => {
    const category = serviceCategories.find(cat => cat.Id === categoryId);
    return category ? translate(commonTranslations, category.NameKey as keyof typeof commonTranslations) : `ID:${categoryId}`;
  };

  const navigateToDetails = (request: ProviderRegistrationRequestWithServices) => {
    router.push(`/admin/provider-requests/${request.Id}`);
  };
  
  const openRejectDialog = () => {
    if (!selectedRequest) return;
    setRejectionReason(""); 
    setIsRejectDialogOpen(true);
  };

  const handleUpdateRequest = async (action: 'approve' | 'reject', adminNotes?: string) => {
    if (!selectedRequest) return;
    const requestId = selectedRequest.Id;

    setUpdatingRequestId(requestId);
    setCurrentAction(action);
    try {
        const response = await fetch(`/api/proxy/admin/provider-requests/update`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ requestId, action, adminNotes: action === 'reject' ? adminNotes : undefined }),
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.message || translate(commonTranslations, 'adminRequestUpdateError'));
        }

        // If a user became a provider, trigger a global session refresh event
        if (action === 'approve' && data.userBecameProvider) {
            console.log(`[Admin] User ${data.userId} became a provider, triggering session refresh event`);
            // Dispatch a custom event that can be listened to by the user context or session management
            if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('userBecameProvider', {
                    detail: { userId: data.userId }
                }));
            }
        }

        toast({
            title: translate(commonTranslations, 'toastSuccessTitle'),
            description: action === 'approve' ? translate(commonTranslations, 'adminRequestApprovedSuccess') : translate(commonTranslations, 'adminRequestRejectedSuccess'),
        });
        await fetchRequests(currentPage, currentStatus, searchTerm); // Refetch requests to update list

    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : translate(commonTranslations, 'adminRequestUpdateError');
        toast({ variant: "destructive", title: translate(commonTranslations, 'toastErrorTitle'), description: errorMessage });
    } finally {
        setUpdatingRequestId(null);
        setCurrentAction(null);
        setIsRejectDialogOpen(false);
        setSelectedRequest(null);
        setRejectionReason("");
    }
  };

  // New function for handling individual service status updates
  const handleServiceStatusUpdate = async (serviceId: number, status: string, adminNotes?: string) => {
    setUpdatingServiceId(serviceId);
    try {
      const response = await fetch(`/api/proxy/admin/provider-services/update`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ serviceId, status, adminNotes }),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || 'Failed to update service status');
      }

      // Update the local state to reflect the change
      setRequests(prevRequests =>
        prevRequests.map(request => ({
          ...request,
          PendingServices: request.PendingServices.map(service =>
            service.Id === serviceId
              ? { ...service, Status: status as any, AdminNotes: adminNotes, UpdatedAt: new Date().toISOString() }
              : service
          )
        }))
      );

      // Also update selectedRequest if it's open
      if (selectedRequest) {
        setSelectedRequest(prev => prev ? ({
          ...prev,
          PendingServices: prev.PendingServices.map(service =>
            service.Id === serviceId
              ? { ...service, Status: status as any, AdminNotes: adminNotes, UpdatedAt: new Date().toISOString() }
              : service
          )
        }) : null);
      }

    } catch (error) {
      throw error; // Re-throw to be handled by the component
    } finally {
      setUpdatingServiceId(null);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-headline flex items-center">
            <UserCheck className="w-6 h-6 mr-2 text-primary"/>
            {translate(commonTranslations, 'adminProviderRequestsTitle')}
          </CardTitle>
          <CardDescription>{translate(commonTranslations, 'adminProviderRequestsDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder={translate(commonTranslations, 'searchRequestsPlaceholder')}
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={currentStatus} onValueChange={(value) => handleStatusChange(value as any)}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder={translate(commonTranslations, 'filterByStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">{translate(commonTranslations, 'allStatusesFilter')}</SelectItem>
                <SelectItem value="Pending">{translate(commonTranslations, 'statusPendingFilter')}</SelectItem>
                <SelectItem value="Approved">{translate(commonTranslations, 'statusApprovedFilter')}</SelectItem>
                <SelectItem value="Rejected">{translate(commonTranslations, 'statusRejectedFilter')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
        </div>
      )}

      {error && (
         <Card>
          <CardContent className="py-10 text-center text-destructive">
            <p>{translate(commonTranslations, 'adminErrorLoadingData')}: {error}</p>
          </CardContent>
        </Card>
      )}

      {!isLoading && !error && (
        <Card>
          <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{translate(commonTranslations, 'adminRequestsTableUserName')}</TableHead>
                      <TableHead className="hidden sm:table-cell">{translate(commonTranslations, 'adminRequestsTableUserEmail')}</TableHead>
                      <TableHead>{translate(commonTranslations, 'adminRequestsTableRequestedServices')}</TableHead>
                      <TableHead className="hidden md:table-cell">{translate(commonTranslations, 'adminRequestsTableRequestDate')}</TableHead>
                      <TableHead className="hidden lg:table-cell">{translate(commonTranslations, 'adminTableStatus')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {requests.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="h-24 text-center">
                          {translate(commonTranslations, 'adminNoRequestsFound')}
                        </TableCell>
                      </TableRow>
                    )}
                    {requests.map((request) => (
                  <TableRow
                    key={request.Id}
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => navigateToDetails(request)}
                  >
                    <TableCell className="font-medium">{request.UserName}</TableCell>
                    <TableCell className="text-xs text-muted-foreground hidden sm:table-cell">{request.UserEmail}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {request.PendingServices?.map(service => (
                          <Badge key={service.Id} variant="outline" className="text-xs">
                            {getCategoryNameBySlug(service.ServiceCategorySlug)}
                            <span className="ml-1 text-xs">
                              ({service.Status === 'PendingReview' ? '⏳' :
                                service.Status === 'Approved' ? '✅' :
                                service.Status === 'Rejected' ? '❌' : '⚠️'})
                            </span>
                          </Badge>
                        )) || []}
                      </div>
                    </TableCell>
                    <TableCell className="text-xs align-top hidden md:table-cell">{new Date(request.RequestDate).toLocaleDateString()}</TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <Badge className={cn("text-xs border", getStatusBadgeClassName(request.Status))}>
                        {request.Status === 'Pending' ? translate(commonTranslations, 'statusPendingFilter') :
                         request.Status === 'Approved' ? translate(commonTranslations, 'statusApprovedFilter') :
                         request.Status === 'Rejected' ? translate(commonTranslations, 'statusRejectedFilter') : request.Status}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

          </CardContent>

          {/* Pagination with total count */}
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-muted-foreground">
              {pagination.totalCount > 0 ? (
                <>
                  {translate(commonTranslations, 'showingResults')
                    .replace('{start}', String(((pagination.currentPage - 1) * pagination.limit) + 1))
                    .replace('{end}', String(Math.min(pagination.currentPage * pagination.limit, pagination.totalCount)))
                    .replace('{total}', String(pagination.totalCount))
                  } {translate(commonTranslations, 'requestsText')}
                </>
              ) : (
                translate(commonTranslations, 'adminNoRequestsFound')
              )}
            </div>
            {pagination.totalPages > 1 && (
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      aria-disabled={!pagination.hasPrev}
                      className={!pagination.hasPrev ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                  {[...Array(pagination.totalPages).keys()].map(i => (
                    <PaginationItem key={i}>
                      <PaginationLink
                        onClick={() => handlePageChange(i + 1)}
                        isActive={pagination.currentPage === i + 1}
                      >
                        {i + 1}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      aria-disabled={!pagination.hasNext}
                      className={!pagination.hasNext ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        </Card>
      )}

      {/* Reject Reason Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <MessageSquareWarning className="w-5 h-5 mr-2 text-destructive"/>
              {translate(commonTranslations, 'adminRejectReasonDialogTitle')}
            </DialogTitle>
            <DialogDescription className="pt-2">
              {translate(commonTranslations, 'adminRejectReasonDialogDescription')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-2">
            <Label htmlFor="rejectionReason" className="sr-only">
              {translate(commonTranslations, 'adminRejectReasonLabel')}
            </Label>
            <Textarea
              id="rejectionReason"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder={translate(commonTranslations, 'adminRejectReasonPlaceholder')}
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>{translate(commonTranslations, 'cancelButton')}</Button>
            <Button
              variant="destructive"
              onClick={() => handleUpdateRequest('reject', rejectionReason)}
              disabled={isLoading || !rejectionReason.trim()}
            >
              {isLoading && currentAction === 'reject' ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
              {translate(commonTranslations, 'adminConfirmRejectButton')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
