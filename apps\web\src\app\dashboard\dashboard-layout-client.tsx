"use client";

import Link from 'next/link';
import { Navbar } from '@/components/layout/navbar';
import { Separator } from '@/components/ui/separator';
import { User, Settings, LayoutDashboard, ListChecks, CalendarCheck, CalendarClock, Briefcase, MessageSquare, Loader2, MapPin, ShieldAlert } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import type { ExtendedSession } from '@repo/auth';
import { RoleIndicator } from '@/components/dashboard/role-indicator';
import { useNavigationConfig, getRoleFromPath, isNavigationItemActive, trackNavigationClick, type NavigationItem } from '@/components/dashboard/navigation-config';
import { OnboardingTooltips, NewProviderWelcomeBanner, useOnboarding } from '@/components/dashboard/onboarding-tooltips';

const dashboardLayoutTranslations = {
  userPanelTitle: { ro: "Panou Utilizator", ru: "Панель пользователя", en: "User Panel" },
  loadingUserData: { ro: "Se încarcă datele utilizatorului...", ru: "Загрузка данных пользователя...", en: "Loading user data..." },
  clientSectionTitle: { ro: "Client", ru: "Клиент", en: "Client" },
  providerSectionTitle: { ro: "Prestator", ru: "Поставщик", en: "Provider" },
  adminAccessRestrictedTitle: { ro: "Acces Restricționat", ru: "Доступ ограничен", en: "Access Restricted" },
  adminAccessRestrictedDesc: { ro: "Administratorii nu au acces la panoul de client. Te rugăm să folosești panoul de administrare.", ru: "Администраторы не имеют доступа к панели клиента. Пожалуйста, используйте панель администратора.", en: "Administrators do not have access to the client panel. Please use the admin panel." },
  goToAdminPanel: { ro: "Mergi la Panoul de Admin", ru: "Перейти в панель администратора", en: "Go to Admin Panel" },
};

interface DashboardLayoutClientProps {
  children: React.ReactNode;
  session: ExtendedSession;
}

export function DashboardLayoutClient({ children, session }: DashboardLayoutClientProps) {
  const { translate } = useLanguage();
  const pathname = usePathname();
  const user = session.user;

  // Get current role from URL
  const currentRole = getRoleFromPath(pathname);
  const navigationConfig = useNavigationConfig();
  const navigationItems = navigationConfig[currentRole];

  // Onboarding system
  const { shouldShowOnboarding, isNewProvider, completeOnboarding } = useOnboarding();

  // Helper function to render navigation item
  const renderNavigationItem = (item: NavigationItem) => {
    const Icon = item.icon;
    const isActive = isNavigationItemActive(item.href, pathname);

    return (
      <Link
        key={item.href}
        href={item.href}
        onClick={() => trackNavigationClick(item, currentRole)}
        className={cn(
          "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
          isActive
            ? "bg-primary text-primary-foreground"
            : "text-muted-foreground hover:text-foreground hover:bg-muted"
        )}
      >
        <Icon className="w-5 h-5 mr-2" />
        {translate(commonTranslations, item.labelKey)}
      </Link>
    );
  };

  const userNameDisplay = user?.name || translate(dashboardLayoutTranslations, 'userPanelTitle');
  const userEmailDisplay = user?.email || "<EMAIL>";



  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <div className="flex-1 flex overflow-hidden">
        <aside className="w-full md:w-80 lg:w-96 md:shrink-0 p-4 pl-8 pr-4">
            <div className="p-4 rounded-lg shadow-md bg-card h-full overflow-y-auto">
                <div className="flex items-center space-x-3 mb-4">
                <div className="bg-primary text-primary-foreground rounded-full w-12 h-12 flex items-center justify-center text-xl font-semibold">
                    {userNameDisplay.substring(0,1).toUpperCase()}
                </div>
                <div>
                    <p className="font-semibold text-card-foreground">{userNameDisplay}</p>
                    <p className="text-xs text-muted-foreground">{userEmailDisplay}</p>
                </div>
                </div>
                <Separator />
                <nav className="mt-4 space-y-1">
                  {/* Role indicator section */}
                  <div className="px-3 py-2 text-xs font-semibold text-muted-foreground/80 tracking-wider uppercase flex items-center">
                    {currentRole === 'client' ? (
                      <>
                        <User className="w-4 h-4 mr-2 text-primary"/>
                        {translate(dashboardLayoutTranslations, 'clientSectionTitle')}
                      </>
                    ) : (
                      <>
                        <Briefcase className="w-4 h-4 mr-2 text-primary"/>
                        {translate(dashboardLayoutTranslations, 'providerSectionTitle')}
                      </>
                    )}
                  </div>

                  {/* Contextual navigation items */}
                  <div className="space-y-1">
                    {navigationItems.map(renderNavigationItem)}
                  </div>
                </nav>
            </div>
        </aside>
        <main className="flex-1 flex flex-col p-4 pr-8 overflow-y-auto" id="dashboard-content">
            {/* New provider welcome banner */}
            {isNewProvider && currentRole === 'provider' && (
              <NewProviderWelcomeBanner />
            )}

            {/* Role indicator for main content */}
            <div className="mb-4">
              <RoleIndicator currentRole={currentRole} variant="compact" />
            </div>
            {children}

            {/* Onboarding tooltips */}
            <OnboardingTooltips
              isNewProvider={shouldShowOnboarding}
              onComplete={completeOnboarding}
            />
        </main>
      </div>
    </div>
  );
}
