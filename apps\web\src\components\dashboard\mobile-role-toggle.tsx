"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet';
import { User, Briefcase, Menu, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { motion } from 'framer-motion';

type Role = 'client' | 'provider';

interface MobileRoleToggleProps {
  currentRole: Role;
  availableRoles: Role[];
  onRoleChange: (role: Role) => void;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

const mobileRoleTranslations = {
  switchRole: { ro: "Comută Rolul", ru: "Переключить роль", en: "Switch Role" },
  currentRole: { ro: "Rolul Curent", ru: "Текущая роль", en: "Current Role" },
  client: { ro: "Client", ru: "Клиент", en: "Client" },
  provider: { ro: "Prestator", ru: "Поставщик", en: "Provider" },
  clientDescription: { ro: "Caută și rezervă servicii", ru: "Ищите и бронируйте услуги", en: "Search and book services" },
  providerDescription: { ro: "Gestionează serviciile tale", ru: "Управляйте своими услугами", en: "Manage your services" },
  switchTo: { ro: "Comută la", ru: "Переключиться на", en: "Switch to" },
};

export function MobileRoleToggle({ 
  currentRole, 
  availableRoles, 
  onRoleChange, 
  isLoading = false, 
  disabled = false,
  className 
}: MobileRoleToggleProps) {
  const { translate } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const isProviderAvailable = availableRoles.includes('provider');

  if (!isProviderAvailable) {
    return (
      <Button variant="ghost" size="sm" className={cn("sm:hidden", className)}>
        <User className="w-4 h-4 mr-2" />
        <span className="text-sm">{translate(mobileRoleTranslations, 'client')}</span>
      </Button>
    );
  }

  const handleRoleChange = (role: Role) => {
    onRoleChange(role);
    setIsOpen(false);
  };

  const roleConfig = {
    client: {
      icon: User,
      label: translate(mobileRoleTranslations, 'client'),
      description: translate(mobileRoleTranslations, 'clientDescription'),
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20'
    },
    provider: {
      icon: Briefcase,
      label: translate(mobileRoleTranslations, 'provider'),
      description: translate(mobileRoleTranslations, 'providerDescription'),
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-950/20'
    }
  };

  const CurrentIcon = roleConfig[currentRole].icon;

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className={cn("sm:hidden flex items-center gap-2", className)}
          disabled={disabled || isLoading}
        >
          <CurrentIcon className="w-4 h-4" />
          <span className="text-sm">{roleConfig[currentRole].label}</span>
        </Button>
      </SheetTrigger>
      
      <SheetContent side="bottom" className="h-auto">
        <SheetHeader className="text-left">
          <SheetTitle>{translate(mobileRoleTranslations, 'switchRole')}</SheetTitle>
        </SheetHeader>
        
        <div className="mt-6 space-y-3">
          {availableRoles.map((role) => {
            const config = roleConfig[role];
            const Icon = config.icon;
            const isActive = currentRole === role;
            
            return (
              <motion.button
                key={role}
                onClick={() => handleRoleChange(role)}
                disabled={disabled || isLoading}
                className={cn(
                  "w-full p-4 rounded-lg border-2 transition-all duration-200 text-left",
                  isActive 
                    ? "border-primary bg-primary/5" 
                    : "border-muted hover:border-primary/50 hover:bg-muted/50"
                )}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "p-2 rounded-full",
                      isActive ? config.bgColor : "bg-muted"
                    )}>
                      <Icon className={cn(
                        "w-5 h-5",
                        isActive ? config.color : "text-muted-foreground"
                      )} />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className={cn(
                          "font-medium",
                          isActive ? "text-primary" : "text-foreground"
                        )}>
                          {config.label}
                        </h3>
                        {isActive && (
                          <Check className="w-4 h-4 text-primary" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {config.description}
                      </p>
                    </div>
                  </div>
                </div>
                
                {isActive && (
                  <div className="mt-2 text-xs text-primary font-medium">
                    {translate(mobileRoleTranslations, 'currentRole')}
                  </div>
                )}
              </motion.button>
            );
          })}
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <p className="text-xs text-muted-foreground text-center">
            {translate(mobileRoleTranslations, 'switchTo')} {' '}
            {availableRoles.filter(role => role !== currentRole).map(role => 
              roleConfig[role].label
            ).join(', ')}
          </p>
        </div>
      </SheetContent>
    </Sheet>
  );
}

// Compact mobile role indicator
export function MobileRoleIndicator({ 
  currentRole, 
  className 
}: { 
  currentRole: Role; 
  className?: string;
}) {
  const { translate } = useLanguage();
  
  const roleConfig = {
    client: {
      icon: User,
      label: translate(mobileRoleTranslations, 'client'),
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20'
    },
    provider: {
      icon: Briefcase,
      label: translate(mobileRoleTranslations, 'provider'),
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-950/20'
    }
  };

  const config = roleConfig[currentRole];
  const Icon = config.icon;

  return (
    <div className={cn(
      "sm:hidden flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium",
      config.bgColor,
      config.color,
      className
    )}>
      <Icon className="w-3 h-3" />
      <span>{config.label}</span>
    </div>
  );
}

// Mobile navigation drawer with role-aware content
export function MobileNavigationDrawer({ 
  children, 
  currentRole, 
  onRoleChange,
  availableRoles,
  className 
}: {
  children: React.ReactNode;
  currentRole: Role;
  onRoleChange: (role: Role) => void;
  availableRoles: Role[];
  className?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className={cn("sm:hidden", className)}>
          <Menu className="w-5 h-5" />
        </Button>
      </SheetTrigger>
      
      <SheetContent side="left" className="w-80">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle>Navigation</SheetTitle>
            <MobileRoleToggle
              currentRole={currentRole}
              availableRoles={availableRoles}
              onRoleChange={onRoleChange}
            />
          </div>
        </SheetHeader>
        
        <div className="mt-6">
          <MobileRoleIndicator currentRole={currentRole} className="mb-4" />
          {children}
        </div>
      </SheetContent>
    </Sheet>
  );
}
