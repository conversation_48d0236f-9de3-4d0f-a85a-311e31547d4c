
"use client";

import * as React from 'react'; // Adăugat importul React
import { useState, type FormEvent, useEffect, useMemo, type ChangeEvent, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { LocationCombobox } from "@/components/ui/location-combobox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Link from "next/link";
import { Loader2, AlertTriangle, CheckCircle, ArrowRight, ArrowLeft, Hourglass, ShieldAlert, FileText } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import type { ServiceCategorySlug, ProviderRegistrationRequestStatus } from '@prisma/client';
import type {
  ServiceCategory as PrismaServiceCategory,
  ProviderRegistrationRequest,
  NannyServiceDetails,
  ElderCareServiceDetails,
  CleaningServiceDetails,
  TutoringServiceDetails,
  CookingServiceDetails
} from '@prisma/client';
import { commonTranslations } from '@repo/translations';
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { useSession } from "next-auth/react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ProviderStatusDashboard, ProviderRegistrationWithServices } from './components/provider-status-dashboard';
import { LocationService, type LocationEntry } from '@repo/services';
import { StepperProgress } from "@/components/ui/stepper-progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FileUpload } from "@/components/ui/file-upload";
import { convertFilesToFileNames, simulateFileUpload, simulateMultipleFileUpload } from "@/lib/file-upload";

// --- Helper Components (Moved Outside RegisterProviderPage) ---
interface StepInputProps {
  id: string; value: string | number; onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string; placeholder?: string; type?: string; className?: string; fieldError?: boolean;
}
const StepInput = React.memo(({ id, value, onChange, label, placeholder, type = "text", className, fieldError }: StepInputProps) => (
  <div className="space-y-1.5">
    <Label htmlFor={id}>{label}</Label>
    <Input id={id} type={type} value={value} onChange={onChange} placeholder={placeholder} className={cn("text-sm", className, fieldError && "border-destructive focus-visible:ring-destructive")} />
  </div>
));
StepInput.displayName = 'StepInput';


interface StepTextareaProps {
  id: string; value: string; onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  label: string; placeholder?: string; rows?: number; className?: string; fieldError?: boolean;
}
const StepTextarea = React.memo(({ id, value, onChange, label, placeholder, rows = 3, className, fieldError }: StepTextareaProps) => (
  <div className="space-y-1.5">
    <Label htmlFor={id}>{label}</Label>
    <Textarea id={id} value={value} onChange={onChange} placeholder={placeholder} rows={rows} className={cn("text-sm", className, fieldError && "border-destructive focus-visible:ring-destructive")} />
  </div>
));
StepTextarea.displayName = 'StepTextarea';

interface StepSwitchFieldProps {
  id: string; label: string; checked: boolean | undefined; onCheckedChange: (checked: boolean) => void;
}
const StepSwitchField = React.memo(({ id, label, checked, onCheckedChange }: StepSwitchFieldProps) => {
  const handleLabelClick = React.useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onCheckedChange(!checked);
  }, [checked, onCheckedChange]);

  const handleSwitchChange = React.useCallback((newChecked: boolean) => {
    onCheckedChange(newChecked);
  }, [onCheckedChange]);

  return (
    <div className={cn(
      "flex items-center justify-between rounded-lg border p-4 shadow-sm transition-all duration-200 hover:shadow-md",
      checked ? "bg-blue-50 border-blue-200 ring-1 ring-blue-200" : "bg-white border-gray-200 hover:border-gray-300"
    )}>
      <Label
        htmlFor={id}
        className="cursor-pointer text-sm font-medium flex-1 pr-3"
        onClick={handleLabelClick}
      >
        {label}
      </Label>
      <Switch
        id={id}
        checked={!!checked}
        onCheckedChange={handleSwitchChange}
        className="shrink-0"
      />
    </div>
  );
});
StepSwitchField.displayName = 'StepSwitchField';

interface StepSpecificServiceFormData {
  experienceYears: string;
  description: string;
  availabilityWeekdays?: boolean;
  availabilityWeekends?: boolean;
  availabilityEvenings?: boolean;
  LocationId?: number;
  PricePerHour?: string;
  PricePerDay?: string;
  PriceSubscriptionAmount?: string;
  PriceSubscriptionUnit?: string;
  PriceSubscriptionText?: string;
  SubscriptionDetails?: string;
  DocBuletinFileName?: string | null;
  DocDiplomeFileNames?: string[] | null;
  DocRecomandariFileNames?: string[] | null;
  // File objects for upload
  DocBuletinFile?: File | null;
  DocDiplomeFiles?: File[] | null;
  DocRecomandariFiles?: File[] | null;
  PreferredAge_0_2?: boolean;
  PreferredAge_3_6?: boolean;
  PreferredAge_7_plus?: boolean;
  AvailabilityFullTime?: boolean;
  AvailabilityPartTime?: boolean;
  FirstAid?: boolean;
  SchoolPickup?: boolean;
  ActivityWalks?: boolean;
  ActivityGames?: boolean;
  ActivityFeeding?: boolean;
  ActivitySleep?: boolean;
  TypeMobil?: boolean;
  TypePartialImobilizat?: boolean;
  TypeCompletImobilizat?: boolean;
  MedicalKnowledgeBasic?: boolean;
  MedicalKnowledgeAdvanced?: boolean;
  MedicationAdmin?: boolean;
  DrivingLicense?: boolean;
  ActivityCooking?: boolean;
  ActivityCleaningLight?: boolean;
  ActivityCompanionship?: boolean;
  PropertyTypeApartments?: boolean;
  PropertyTypeHouses?: boolean;
  PropertyTypeOffices?: boolean;
  OwnProducts?: boolean;
  TypeGeneral?: boolean;
  TypePostRenovation?: boolean;
  TypeOccasional?: boolean;
  TypeRegular?: boolean;
  ExtraIroning?: boolean;
  ExtraWindows?: boolean;
  ExtraDisinfection?: boolean;
  CuisineTypeTraditional?: boolean;
  CuisineTypeVegetarian?: boolean;
  CuisineTypeKids?: boolean;
  CuisineTypeDiet?: boolean;
  OffersDelivery?: boolean;
  AtClientHome?: boolean;
  AtOwnHome?: boolean;
  cookingOwnProducts?: boolean;
  MinPortions?: string;
  WeeklySubscription?: boolean;
  PricePerMeal?: string;
  MealDetails?: string;
  ServiceAfterSchool?: boolean;
  ServiceHomeworkHelp?: boolean;
  ServiceIndividualLessons?: boolean;
  Grades_1_4?: boolean;
  Grades_5_8?: boolean;
  Grades_9_12?: boolean;
  SubjectRomanian?: boolean;
  SubjectMath?: boolean;
  SubjectEnglish?: boolean;
  SubjectOther?: string;
  FormatOnline?: boolean;
  FormatOwnHome?: boolean;
  FormatChildHome?: boolean;
  ExtraGames?: boolean;
  ExtraSnack?: boolean;
  ExtraTransport?: boolean;
  ExtraSupervisedHomework?: boolean;
}

const initialStepSpecificFormData: StepSpecificServiceFormData = {
  experienceYears: "", description: "", availabilityWeekdays: false, availabilityWeekends: false, availabilityEvenings: false,
  LocationId: null, PricePerHour: '', PricePerDay: '', PriceSubscriptionAmount: '', PriceSubscriptionUnit: '', PriceSubscriptionText: '', SubscriptionDetails: '',
  DocBuletinFileName: null, DocDiplomeFileNames: [], DocRecomandariFileNames: [],
  DocBuletinFile: null, DocDiplomeFiles: [], DocRecomandariFiles: [],
  PreferredAge_0_2: false, PreferredAge_3_6: false, PreferredAge_7_plus: false, AvailabilityFullTime: false, AvailabilityPartTime: false, FirstAid: false, SchoolPickup: false, ActivityWalks: false, ActivityGames: false, ActivityFeeding: false, ActivitySleep: false,
  TypeMobil: false, TypePartialImobilizat: false, TypeCompletImobilizat: false, MedicalKnowledgeBasic: false, MedicalKnowledgeAdvanced: false, MedicationAdmin: false, DrivingLicense: false, ActivityCooking: false, ActivityCleaningLight: false, ActivityCompanionship: false,
  PropertyTypeApartments: false, PropertyTypeHouses: false, PropertyTypeOffices: false, OwnProducts: false, TypeGeneral: false, TypePostRenovation: false, TypeOccasional: false, TypeRegular: false, ExtraIroning: false, ExtraWindows: false, ExtraDisinfection: false,
  CuisineTypeTraditional: false, CuisineTypeVegetarian: false, CuisineTypeKids: false, CuisineTypeDiet: false, OffersDelivery: false, AtClientHome: false, AtOwnHome: false, cookingOwnProducts: false, MinPortions: '', WeeklySubscription: false, PricePerMeal: '', MealDetails: '',
  ServiceAfterSchool: false, ServiceHomeworkHelp: false, ServiceIndividualLessons: false, Grades_1_4: false, Grades_5_8: false, Grades_9_12: false, SubjectRomanian: false, SubjectMath: false, SubjectEnglish: false, SubjectOther: '', FormatOnline: false, FormatOwnHome: false, FormatChildHome: false, ExtraGames: false, ExtraSnack: false, ExtraTransport: false, ExtraSupervisedHomework: false,
};

interface BaseDetailsProps {
  categoryId: number;
  details: StepSpecificServiceFormData;
  handleDetailInputChange: (categoryId: number, fieldPath: string, value: any) => void;
  fieldErrors: Record<string, boolean>;
  translate: (translations: any, key: string, fallback?: string) => string;
}

interface CommonDetailsProps extends BaseDetailsProps {
  locations: LocationEntry[];
  locationsLoading: boolean;
}

const CommonDetailsSection = React.memo(({ categoryId, details, handleDetailInputChange, fieldErrors, translate, locations, locationsLoading }: CommonDetailsProps) => {
  return (
    <>
      <StepInput
        id={`${categoryId}_experienceYears`} value={details.experienceYears || ''}
        onChange={(e) => handleDetailInputChange(categoryId, "experienceYears", e.target.value)}
        label={translate(commonTranslations, 'experienceYearsLabel')}
        placeholder={translate(commonTranslations, 'experiencePlaceholder')} type="number"
        fieldError={fieldErrors[`${categoryId}_experienceYears`]}
      />
      <StepTextarea
        id={`${categoryId}_description`} value={details.description || ''}
        onChange={(e) => handleDetailInputChange(categoryId, "description", e.target.value)}
        label={translate(commonTranslations, 'descriptionLabel')}
        placeholder={translate(commonTranslations, 'descriptionPlaceholder')}
        fieldError={fieldErrors[`${categoryId}_description`]}
      />
      <div className="space-y-2">
        <Label className="text-sm font-medium">
          {translate(commonTranslations, 'availabilityLabel')}
        </Label>
        <div className={cn("grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 p-3 border rounded-md", fieldErrors[`${categoryId}_availability`] && "border-destructive ring-1 ring-destructive")}>
          {['availabilityWeekdays', 'availabilityWeekends', 'availabilityEvenings'].map(availKey => (
            <div key={availKey} className="flex items-center space-x-2">
              <Checkbox id={`${availKey}_${categoryId}`} checked={!!details[availKey as keyof StepSpecificServiceFormData]} onCheckedChange={(checked) => handleDetailInputChange(categoryId, availKey, !!checked)} />
              <Label htmlFor={`${availKey}_${categoryId}`} className="font-normal text-sm">{translate(commonTranslations, availKey as keyof typeof commonTranslations)}</Label>
            </div>
          ))}
        </div>
      </div>
       <div className="space-y-2">
            <Label htmlFor={`${categoryId}_LocationId`}>{translate(commonTranslations, 'locationLabel')}</Label>
            <LocationCombobox
              locations={locations}
              value={details.LocationId ? (locations.find(loc => loc.id === details.LocationId)?.slug || "") : ""}
              onValueChange={(val) => {
                // Convert slug back to ID for storage
                const location = locations.find(loc => loc.slug === val);
                handleDetailInputChange(categoryId, 'LocationId', location ? location.id : null);
              }}
              placeholder={locationsLoading ? "Loading locations..." : translate(commonTranslations, 'locationPlaceholder')}
              className={cn("text-sm", fieldErrors[`${categoryId}_LocationId`] && "border-destructive focus-visible:ring-destructive")}
            />
        </div>
        <StepInput id={`${categoryId}_PricePerHour`} value={details.PricePerHour || ''} onChange={(e) => handleDetailInputChange(categoryId, "PricePerHour", e.target.value)} label={translate(commonTranslations, 'pricePerHourLabel')} type="number" />
        <StepInput id={`${categoryId}_PricePerDay`} value={details.PricePerDay || ''} onChange={(e) => handleDetailInputChange(categoryId, "PricePerDay", e.target.value)} label={translate(commonTranslations, 'pricePerDayLabel')} type="number" />
        
        <div className="space-y-3 pt-3 border-t mt-4">
            <h5 className="font-medium text-md">{translate(commonTranslations, 'regProvPriceSubscriptionSectionTitle')}</h5>
            <StepInput id={`${categoryId}_PriceSubscriptionAmount`} value={details.PriceSubscriptionAmount||''} onChange={(e) => handleDetailInputChange(categoryId, "PriceSubscriptionAmount", e.target.value)} label={translate(commonTranslations, 'regProvPriceSubscriptionAmountLabel')} type="number" />
            <StepInput id={`${categoryId}_PriceSubscriptionUnit`} value={details.PriceSubscriptionUnit||''} onChange={(e) => handleDetailInputChange(categoryId, "PriceSubscriptionUnit", e.target.value)} label={translate(commonTranslations, 'regProvPriceSubscriptionUnitLabel')} placeholder={translate(commonTranslations, 'regProvPriceSubscriptionUnitPlaceholder')} />
            <StepInput id={`${categoryId}_PriceSubscriptionText`} value={details.PriceSubscriptionText||''} onChange={(e) => handleDetailInputChange(categoryId, "PriceSubscriptionText", e.target.value)} label={translate(commonTranslations, 'regProvPriceSubscriptionTextLabel')} placeholder={translate(commonTranslations, 'regProvPriceSubscriptionTextPlaceholder')} />
            <div className="space-y-1.5">
                <Label htmlFor={`${categoryId}_SubscriptionDetails`}>{translate(commonTranslations, 'regProvSubscriptionDetailsLabel')}</Label>
                <Textarea id={`${categoryId}_SubscriptionDetails`} value={details.SubscriptionDetails || ''} onChange={(e) => handleDetailInputChange(categoryId, "SubscriptionDetails", e.target.value)} placeholder={translate(commonTranslations, 'regProvSubscriptionDetailsPlaceholder')} rows={2} className="text-sm"/>
            </div>
        </div>

        <div className="space-y-4 pt-4 border-t mt-6">
            <div className="space-y-2">
                <h5 className="font-medium text-lg text-gray-800">{translate(commonTranslations, 'documentsTitle')}</h5>
                <p className="text-sm text-muted-foreground">{translate(commonTranslations, 'fileUploadNote')}</p>
            </div>

            <div className="space-y-4">
                <FileUpload
                    id={`${categoryId}_DocBuletinFile`}
                    label={translate(commonTranslations, 'docBuletinLabel')}
                    multiple={false}
                    value={details.DocBuletinFile || null}
                    onChange={(file) => handleDetailInputChange(categoryId, 'DocBuletinFile', file)}
                    accept=".pdf,.jpg,.jpeg,.png"
                    maxSize={5 * 1024 * 1024}
                    className="w-full"
                />

                <FileUpload
                    id={`${categoryId}_DocDiplomeFiles`}
                    label={translate(commonTranslations, 'docDiplomeLabel')}
                    multiple={true}
                    value={details.DocDiplomeFiles || null}
                    onChange={(files) => handleDetailInputChange(categoryId, 'DocDiplomeFiles', files)}
                    accept=".pdf,.jpg,.jpeg,.png"
                    maxSize={5 * 1024 * 1024}
                    className="w-full"
                />

                <FileUpload
                    id={`${categoryId}_DocRecomandariFiles`}
                    label={translate(commonTranslations, 'docRecomandariLabel')}
                    multiple={true}
                    value={details.DocRecomandariFiles || null}
                    onChange={(files) => handleDetailInputChange(categoryId, 'DocRecomandariFiles', files)}
                    accept=".pdf,.jpg,.jpeg,.png"
                    maxSize={5 * 1024 * 1024}
                    className="w-full"
                />
            </div>
        </div>
    </>
  );
});
CommonDetailsSection.displayName = "CommonDetailsSection";


const NannyFields = React.memo(({ categoryId, details, handleDetailInputChange, translate, fieldErrors }: BaseDetailsProps) => (
  <div className="space-y-8 p-8 border-2 rounded-xl bg-gradient-to-br from-blue-50/70 to-indigo-50/70 mt-6 shadow-sm">
    <div className="flex items-center gap-3 mb-6">
      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center shadow-sm">
        <span className="text-blue-600 text-lg font-semibold">👶</span>
      </div>
      <h4 className="font-bold text-xl text-blue-900">{translate(commonTranslations, 'childcareTitle')}</h4>
    </div>

    {/* Age Groups Section */}
    <div className="space-y-4 p-4 bg-white/60 rounded-lg border border-blue-100">
      <div className="flex items-center gap-3">
        <Label className="text-lg font-semibold text-gray-800">{translate(commonTranslations, 'childcarePreferredAgeLabel')}</Label>
        <div className="text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-full border border-blue-200">{translate(commonTranslations, 'regProvSelectAgeHint')}</div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <StepSwitchField id={`${categoryId}_PreferredAge_0_2`} label={translate(commonTranslations, 'childcareAge0_2')} checked={details.PreferredAge_0_2} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'PreferredAge_0_2',c)} />
        <StepSwitchField id={`${categoryId}_PreferredAge_3_6`} label={translate(commonTranslations, 'childcareAge3_6')} checked={details.PreferredAge_3_6} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'PreferredAge_3_6',c)} />
        <StepSwitchField id={`${categoryId}_PreferredAge_7_plus`} label={translate(commonTranslations, 'childcareAge7_plus')} checked={details.PreferredAge_7_plus} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'PreferredAge_7_plus',c)} />
      </div>
    </div>

    {/* Availability Type Section */}
    <div className="space-y-4 p-4 bg-white/60 rounded-lg border border-blue-100">
      <div className="flex items-center gap-3">
        <Label className="text-lg font-semibold text-gray-800">{translate(commonTranslations, 'childcareAvailabilityTypeLabel')}</Label>
        <div className="text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-full border border-blue-200">{translate(commonTranslations, 'regProvAvailabilityTypeHint')}</div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <StepSwitchField id={`${categoryId}_AvailabilityFullTime`} label={translate(commonTranslations, 'childcareAvailabilityFullTime')} checked={details.AvailabilityFullTime} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'AvailabilityFullTime',c)} />
        <StepSwitchField id={`${categoryId}_AvailabilityPartTime`} label={translate(commonTranslations, 'childcareAvailabilityPartTime')} checked={details.AvailabilityPartTime} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'AvailabilityPartTime',c)} />
      </div>
    </div>

    {/* Qualifications Section */}
    <div className="space-y-4 p-4 bg-white/60 rounded-lg border border-blue-100">
      <Label className="text-lg font-semibold text-gray-800">{translate(commonTranslations, 'regProvQualificationsTitle')}</Label>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <StepSwitchField id={`${categoryId}_FirstAid`} label={translate(commonTranslations, 'childcareFirstAidLabel')} checked={details.FirstAid} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'FirstAid',c)} />
        <StepSwitchField id={`${categoryId}_SchoolPickup`} label={translate(commonTranslations, 'childcareSchoolPickupLabel')} checked={details.SchoolPickup} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'SchoolPickup',c)} />
      </div>
    </div>

    {/* Activities Section */}
    <div className="space-y-4 p-4 bg-white/60 rounded-lg border border-blue-100">
      <div className="flex items-center gap-3">
        <Label className="text-lg font-semibold text-gray-800">{translate(commonTranslations, 'childcareActivitiesOfferedLabel')}</Label>
        <div className="text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-full border border-blue-200">{translate(commonTranslations, 'regProvActivitiesHint')}</div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <StepSwitchField id={`${categoryId}_ActivityWalks`} label={translate(commonTranslations, 'childcareActivityWalks')} checked={details.ActivityWalks} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ActivityWalks',c)} />
        <StepSwitchField id={`${categoryId}_ActivityGames`} label={translate(commonTranslations, 'childcareActivityGames')} checked={details.ActivityGames} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ActivityGames',c)} />
        <StepSwitchField id={`${categoryId}_ActivityFeeding`} label={translate(commonTranslations, 'childcareActivityFeeding')} checked={details.ActivityFeeding} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ActivityFeeding',c)} />
        <StepSwitchField id={`${categoryId}_ActivitySleep`} label={translate(commonTranslations, 'childcareActivitySleep')} checked={details.ActivitySleep} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ActivitySleep',c)} />
      </div>
    </div>
  </div>
));
NannyFields.displayName = "NannyFields";

const ElderCareFields = React.memo(({ categoryId, details, handleDetailInputChange, translate, fieldErrors }: BaseDetailsProps) => (
    <div className="space-y-4 p-4 border rounded-md bg-muted/20 mt-4">
        <h4 className="font-medium text-md mb-3">{translate(commonTranslations, 'elderCareTitle')}</h4>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'elderCareTypeLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id={`${categoryId}_TypeMobil`} label={translate(commonTranslations, 'elderCareTypeMobil')} checked={details.TypeMobil} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'TypeMobil',c)} /><StepSwitchField id={`${categoryId}_TypePartialImobilizat`} label={translate(commonTranslations, 'elderCareTypePartialImobilizat')} checked={details.TypePartialImobilizat} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'TypePartialImobilizat',c)} /><StepSwitchField id={`${categoryId}_TypeCompletImobilizat`} label={translate(commonTranslations, 'elderCareTypeCompletImobilizat')} checked={details.TypeCompletImobilizat} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'TypeCompletImobilizat',c)} /></div></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'elderCareMedicalKnowledgeLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 gap-3"><StepSwitchField id={`${categoryId}_MedicalKnowledgeBasic`} label={translate(commonTranslations, 'elderCareMedicalKnowledgeBasicLabel')} checked={details.MedicalKnowledgeBasic} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'MedicalKnowledgeBasic',c)} /><StepSwitchField id={`${categoryId}_MedicalKnowledgeAdvanced`} label={translate(commonTranslations, 'elderCareMedicalKnowledgeAdvancedLabel')} checked={details.MedicalKnowledgeAdvanced} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'MedicalKnowledgeAdvanced',c)} /></div></div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4"><StepSwitchField id={`${categoryId}_MedicationAdmin`} label={translate(commonTranslations, 'elderCareMedicationAdminLabel')} checked={details.MedicationAdmin} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'MedicationAdmin',c)} /><StepSwitchField id={`${categoryId}_DrivingLicense`} label={translate(commonTranslations, 'elderCareDrivingLicenseLabel')} checked={details.DrivingLicense} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'DrivingLicense',c)} /></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'elderCareActivitiesLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id={`${categoryId}_ActivityCooking`} label={translate(commonTranslations, 'elderCareActivityCooking')} checked={details.ActivityCooking} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ActivityCooking',c)} /><StepSwitchField id={`${categoryId}_ActivityCleaningLight`} label={translate(commonTranslations, 'elderCareActivityCleaningLight')} checked={details.ActivityCleaningLight} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ActivityCleaningLight',c)} /><StepSwitchField id={`${categoryId}_ActivityCompanionship`} label={translate(commonTranslations, 'elderCareActivityCompanionship')} checked={details.ActivityCompanionship} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ActivityCompanionship',c)} /></div></div>
    </div>
));
ElderCareFields.displayName = "ElderCareFields";

const CleaningFields = React.memo(({ categoryId, details, handleDetailInputChange, translate, fieldErrors }: BaseDetailsProps) => (
    <div className="space-y-4 p-4 border rounded-md bg-muted/20 mt-4">
        <h4 className="font-medium text-md mb-3">{translate(commonTranslations, 'cleaningTitle')}</h4>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'cleaningPropertyTypeLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id={`${categoryId}_PropertyTypeApartments`} label={translate(commonTranslations, 'cleaningPropertyTypeApartments')} checked={details.PropertyTypeApartments} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'PropertyTypeApartments',c)} /><StepSwitchField id={`${categoryId}_PropertyTypeHouses`} label={translate(commonTranslations, 'cleaningPropertyTypeHouses')} checked={details.PropertyTypeHouses} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'PropertyTypeHouses',c)} /><StepSwitchField id={`${categoryId}_PropertyTypeOffices`} label={translate(commonTranslations, 'cleaningPropertyTypeOffices')} checked={details.PropertyTypeOffices} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'PropertyTypeOffices',c)} /></div></div>
        <StepSwitchField id={`${categoryId}_OwnProducts`} label={translate(commonTranslations, 'cleaningOwnProductsLabel')} checked={details.OwnProducts} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'OwnProducts',c)} />
        <div className="space-y-2"><Label>{translate(commonTranslations, 'cleaningTypesOfferedLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 gap-3"><StepSwitchField id={`${categoryId}_TypeGeneral`} label={translate(commonTranslations, 'cleaningTypeGeneral')} checked={details.TypeGeneral} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'TypeGeneral',c)} /><StepSwitchField id={`${categoryId}_TypePostRenovation`} label={translate(commonTranslations, 'cleaningTypePostRenovation')} checked={details.TypePostRenovation} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'TypePostRenovation',c)} /><StepSwitchField id={`${categoryId}_TypeOccasional`} label={translate(commonTranslations, 'cleaningTypeOccasional')} checked={details.TypeOccasional} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'TypeOccasional',c)} /><StepSwitchField id={`${categoryId}_TypeRegular`} label={translate(commonTranslations, 'cleaningTypeRegular')} checked={details.TypeRegular} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'TypeRegular',c)} /></div></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'cleaningExtraServicesLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id={`${categoryId}_ExtraIroning`} label={translate(commonTranslations, 'cleaningExtraIroning')} checked={details.ExtraIroning} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ExtraIroning',c)} /><StepSwitchField id={`${categoryId}_ExtraWindows`} label={translate(commonTranslations, 'cleaningExtraWindows')} checked={details.ExtraWindows} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ExtraWindows',c)} /><StepSwitchField id={`${categoryId}_ExtraDisinfection`} label={translate(commonTranslations, 'cleaningExtraDisinfection')} checked={details.ExtraDisinfection} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ExtraDisinfection',c)} /></div></div>
    </div>
));
CleaningFields.displayName = "CleaningFields";

const CookingFields = React.memo(({ categoryId, details, handleDetailInputChange, translate, fieldErrors }: BaseDetailsProps) => (
    <div className="space-y-4 p-4 border rounded-md bg-muted/20 mt-4">
        <h4 className="font-medium text-md mb-3">{translate(commonTranslations, 'cookingTitle')}</h4>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'cookingCuisineTypeLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 gap-3"><StepSwitchField id={`${categoryId}_CuisineTypeTraditional`} label={translate(commonTranslations, 'cookingCuisineTypeTraditional')} checked={details.CuisineTypeTraditional} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'CuisineTypeTraditional',c)} /><StepSwitchField id={`${categoryId}_CuisineTypeVegetarian`} label={translate(commonTranslations, 'cookingCuisineTypeVegetarian')} checked={details.CuisineTypeVegetarian} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'CuisineTypeVegetarian',c)} /><StepSwitchField id={`${categoryId}_CuisineTypeKids`} label={translate(commonTranslations, 'cookingCuisineTypeKids')} checked={details.CuisineTypeKids} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'CuisineTypeKids',c)} /><StepSwitchField id={`${categoryId}_CuisineTypeDiet`} label={translate(commonTranslations, 'cookingCuisineTypeDiet')} checked={details.CuisineTypeDiet} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'CuisineTypeDiet',c)} /></div></div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3"><StepSwitchField id={`${categoryId}_OffersDelivery`} label={translate(commonTranslations, 'cookingOffersDeliveryLabel')} checked={details.OffersDelivery} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'OffersDelivery',c)} /><StepSwitchField id={`${categoryId}_cookingOwnProducts`} label={translate(commonTranslations, 'cookingOwnProductsLabel')} checked={details.cookingOwnProducts} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'cookingOwnProducts',c)} /></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'cookingLocationLabelDetails')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 gap-3"><StepSwitchField id={`${categoryId}_AtClientHome`} label={translate(commonTranslations, 'cookingLocationClient')} checked={details.AtClientHome} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'AtClientHome',c)} /><StepSwitchField id={`${categoryId}_AtOwnHome`} label={translate(commonTranslations, 'cookingLocationOwn')} checked={details.AtOwnHome} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'AtOwnHome',c)} /></div></div>
        <StepInput id={`${categoryId}_MinPortions`} value={details.MinPortions||''} onChange={(e)=>handleDetailInputChange(categoryId,'MinPortions',e.target.value)} label={translate(commonTranslations, 'cookingMinPortionsLabel')} placeholder={translate(commonTranslations, 'cookingMinPortionsPlaceholder')} type="number" />
        <StepSwitchField id={`${categoryId}_WeeklySubscription`} label={translate(commonTranslations, 'cookingWeeklySubscriptionLabel')} checked={details.WeeklySubscription} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'WeeklySubscription',c)} />
        <div className="space-y-2 pt-2">
            <StepInput id={`${categoryId}_PricePerMeal`} value={details.PricePerMeal||''} onChange={(e)=>handleDetailInputChange(categoryId,'PricePerMeal',e.target.value)} label={translate(commonTranslations, 'cookingPricePerMealLabel')} placeholder={translate(commonTranslations, 'cookingPricePerMealPlaceholder')} type="number" />
            <div className="pt-2"><StepTextarea id={`${categoryId}_MealDetails`} value={details.MealDetails||''} onChange={(e)=>handleDetailInputChange(categoryId,'MealDetails',e.target.value)} label={translate(commonTranslations, 'cookingMealDetailsLabel')} placeholder={translate(commonTranslations, 'cookingMealDetailsPlaceholder')} /></div>
        </div>
    </div>
));
CookingFields.displayName = "CookingFields";

const TutoringFields = React.memo(({ categoryId, details, handleDetailInputChange, translate, fieldErrors }: BaseDetailsProps) => (
    <div className="space-y-4 p-4 border rounded-md bg-muted/20 mt-4">
        <h4 className="font-medium text-md mb-3">{translate(commonTranslations, 'tutoringTitle')}</h4>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'tutoringServicesOfferedLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id={`${categoryId}_ServiceAfterSchool`} label={translate(commonTranslations, 'tutoringServiceAfterSchool')} checked={details.ServiceAfterSchool} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ServiceAfterSchool',c)} /><StepSwitchField id={`${categoryId}_ServiceHomeworkHelp`} label={translate(commonTranslations, 'tutoringServiceHomeworkHelp')} checked={details.ServiceHomeworkHelp} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ServiceHomeworkHelp',c)} /><StepSwitchField id={`${categoryId}_ServiceIndividualLessons`} label={translate(commonTranslations, 'tutoringServiceIndividualLessons')} checked={details.ServiceIndividualLessons} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ServiceIndividualLessons',c)} /></div></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'tutoringGradesCoveredLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id={`${categoryId}_Grades_1_4`} label={translate(commonTranslations, 'tutoringGrades_1_4')} checked={details.Grades_1_4} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'Grades_1_4',c)} /><StepSwitchField id={`${categoryId}_Grades_5_8`} label={translate(commonTranslations, 'tutoringGrades_5_8')} checked={details.Grades_5_8} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'Grades_5_8',c)} /><StepSwitchField id={`${categoryId}_Grades_9_12`} label={translate(commonTranslations, 'tutoringGrades_9_12')} checked={details.Grades_9_12} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'Grades_9_12',c)} /></div></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'tutoringSubjectsTaughtLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mb-2"><StepSwitchField id={`${categoryId}_SubjectRomanian`} label={translate(commonTranslations, 'tutoringSubjectRomanian')} checked={details.SubjectRomanian} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'SubjectRomanian',c)} /><StepSwitchField id={`${categoryId}_SubjectMath`} label={translate(commonTranslations, 'tutoringSubjectMath')} checked={details.SubjectMath} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'SubjectMath',c)} /><StepSwitchField id={`${categoryId}_SubjectEnglish`} label={translate(commonTranslations, 'tutoringSubjectEnglish')} checked={details.SubjectEnglish} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'SubjectEnglish',c)} /></div><StepInput id={`${categoryId}_SubjectOther`} value={details.SubjectOther||''} onChange={(e)=>handleDetailInputChange(categoryId,'SubjectOther',e.target.value)} label={translate(commonTranslations, 'tutoringSubjectOtherLabel')} placeholder={translate(commonTranslations, 'tutoringSubjectOtherPlaceholder')} /></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'tutoringFormatLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"><StepSwitchField id={`${categoryId}_FormatOnline`} label={translate(commonTranslations, 'tutoringFormatOnline')} checked={details.FormatOnline} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'FormatOnline',c)} /><StepSwitchField id={`${categoryId}_FormatOwnHome`} label={translate(commonTranslations, 'tutoringFormatOwnHome')} checked={details.FormatOwnHome} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'FormatOwnHome',c)} /><StepSwitchField id={`${categoryId}_FormatChildHome`} label={translate(commonTranslations, 'tutoringFormatChildHome')} checked={details.FormatChildHome} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'FormatChildHome',c)} /></div></div>
        <div className="space-y-2"><Label>{translate(commonTranslations, 'tutoringExtraActivitiesLabel')}</Label><div className="grid grid-cols-1 sm:grid-cols-2 gap-3"><StepSwitchField id={`${categoryId}_ExtraGames`} label={translate(commonTranslations, 'tutoringExtraGames')} checked={details.ExtraGames} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ExtraGames',c)} /><StepSwitchField id={`${categoryId}_ExtraSnack`} label={translate(commonTranslations, 'tutoringExtraSnack')} checked={details.ExtraSnack} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ExtraSnack',c)} /><StepSwitchField id={`${categoryId}_ExtraTransport`} label={translate(commonTranslations, 'tutoringExtraTransport')} checked={details.ExtraTransport} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ExtraTransport',c)} /><StepSwitchField id={`${categoryId}_ExtraSupervisedHomework`} label={translate(commonTranslations, 'tutoringExtraSupervisedHomework')} checked={details.ExtraSupervisedHomework} onCheckedChange={(c)=>handleDetailInputChange(categoryId,'ExtraSupervisedHomework',c)} /></div></div>
    </div>
));
TutoringFields.displayName = "TutoringFields";


// --- Main Component ---
export default function RegisterProviderPage() {
  const { translate, currentLanguage } = useLanguage();
  const { data: session, status: sessionStatus } = useSession();
  const { toast } = useToast();
  const router = useRouter();

  // Extract current user ID from session
  const currentUserId = session?.user ? (session.user as any).id : null;
  const currentUserName = session?.user?.name || null;
  const currentUserEmail = session?.user?.email || null;

  // Debug log for development (remove in production)
  console.log('Provider Registration - User ID:', currentUserId, 'Session Status:', sessionStatus);

  const [apiServiceCategories, setApiServiceCategories] = useState<PrismaServiceCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);
  const [categoriesError, setCategoriesError] = useState<string | null>(null);

  const [currentStep, setCurrentStep] = useState(0);
  const [selectedCategoriesData, setSelectedCategoriesData] = useState<PrismaServiceCategory[]>([]);
  const [serviceDetailsForms, setServiceDetailsForms] = useState<Record<number, StepSpecificServiceFormData>>({});
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [providerRequestStatus, setProviderRequestStatus] = useState<ProviderRegistrationRequest | 'loading' | 'none' | 'error' >('loading');
  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});
  const [isDraftSaved, setIsDraftSaved] = useState(false);
  const [lastSavedTime, setLastSavedTime] = useState<Date | null>(null);
  const isProcessingCategoryChange = React.useRef(false);

  // Locations state
  const [locations, setLocations] = useState<LocationEntry[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(true);


  useEffect(() => {
    async function fetchCategories() {
      setIsLoadingCategories(true);
      setCategoriesError(null);
      try {
        const response = await fetch(`/api/proxy/service-categories`);
        if (!response.ok) {
          throw new Error(translate(commonTranslations, 'regProvErrorFetchingCategories'));
        }
        const data = await response.json();
        setApiServiceCategories(data || []);
      } catch (err) {
        setCategoriesError(err instanceof Error ? err.message : translate(commonTranslations, 'regProvErrorFetchingCategories'));
        setApiServiceCategories([]);
      } finally {
        setIsLoadingCategories(false);
      }
    }
    fetchCategories();
  }, [translate]);

  // Load locations on component mount
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setLocationsLoading(true);
        const locationData = await LocationService.getLocationsForForms(currentLanguage.code);
        setLocations(locationData);
      } catch (error) {
        console.error('Failed to load locations:', error);
      } finally {
        setLocationsLoading(false);
      }
    };

    loadLocations();
  }, [currentLanguage.code]);

  // Load saved draft from localStorage (user-specific)
  useEffect(() => {
    if (sessionStatus !== "authenticated" || !currentUserId || typeof window === 'undefined') return; // Wait for user ID to be available and ensure we're on client side

    const draftKey = `providerRegistrationDraft_${currentUserId}`;
    const savedDraft = localStorage.getItem(draftKey);
    if (savedDraft) {
      try {
        const draft = JSON.parse(savedDraft);
        // Verify the draft belongs to the current user
        if (draft.userId === currentUserId) {
          setCurrentStep(draft.currentStep || 0);
          setSelectedCategoriesData(draft.selectedCategoriesData || []);
          setServiceDetailsForms(draft.serviceDetailsForms || {});
          setTermsAccepted(draft.termsAccepted || false);
          setLastSavedTime(draft.lastSavedTime ? new Date(draft.lastSavedTime) : null);
          setIsDraftSaved(true);
        } else {
          // Draft belongs to different user, remove it
          localStorage.removeItem(draftKey);
        }
      } catch (error) {
        console.error('Error loading draft:', error);
        localStorage.removeItem(draftKey);
      }
    }
  }, [currentUserId, sessionStatus]);

  // Auto-save draft with debounce (user-specific)
  useEffect(() => {
    // Only save if there's actual data to save and user ID is available
    if (sessionStatus !== "authenticated" || !currentUserId || typeof window === 'undefined' || (currentStep === 0 && selectedCategoriesData.length === 0 && Object.keys(serviceDetailsForms).length === 0)) {
      return;
    }

    const saveDraft = () => {
      try {
        // Create a copy of serviceDetailsForms without File objects (they can't be serialized)
        const serializableServiceDetailsForms: Record<number, StepSpecificServiceFormData> = {};

        Object.keys(serviceDetailsForms).forEach(categoryIdStr => {
          const categoryId = parseInt(categoryIdStr, 10);
          const form = serviceDetailsForms[categoryId];

          // Create a copy without File objects
          const serializableForm = { ...form };
          delete serializableForm.DocBuletinFile;
          delete serializableForm.DocDiplomeFiles;
          delete serializableForm.DocRecomandariFiles;

          serializableServiceDetailsForms[categoryId] = serializableForm;
        });

        const draft = {
          userId: currentUserId, // Include user ID for verification
          currentStep,
          selectedCategoriesData,
          serviceDetailsForms: serializableServiceDetailsForms,
          termsAccepted,
          lastSavedTime: new Date().toISOString()
        };

        const draftKey = `providerRegistrationDraft_${currentUserId}`;
        localStorage.setItem(draftKey, JSON.stringify(draft));
        setLastSavedTime(new Date());
        setIsDraftSaved(true);
      } catch (error) {
        console.error('Error saving draft:', error);
      }
    };

    // Debounce the save operation
    const timeoutId = setTimeout(saveDraft, 2000); // Save after 2 seconds of inactivity
    return () => clearTimeout(timeoutId);
  }, [currentStep, selectedCategoriesData, serviceDetailsForms, termsAccepted, currentUserId, sessionStatus]);

  useEffect(() => {
    if (sessionStatus === "loading") {
      setProviderRequestStatus('loading');
      return;
    }
    if (sessionStatus === "unauthenticated") {
      return; // AuthGuard will handle the redirect
    }

    if (sessionStatus === "authenticated" && session?.user) {
      if ((session.user as any).isProvider) {
          return; // AuthGuard will handle redirecting existing providers away
      }
      const fetchStatus = async () => {
        setProviderRequestStatus('loading');
        try {
          const res = await fetch(`/api/proxy/provider-requests/my-status`);
          if (!res.ok) {
            setProviderRequestStatus('none');
          } else {
            const data = await res.json();
            setProviderRequestStatus(data.request || 'none');
          }
        } catch (err) {
          console.error("Error fetching provider request status:", err);
          setProviderRequestStatus('error');
        }
      };
      fetchStatus();
    }
  }, [session, sessionStatus, router]);

  const handleCategorySelectionChange = React.useCallback((categoryId: number, categorySlug: ServiceCategorySlug, checked: boolean) => {
    // Prevent double-processing
    if (isProcessingCategoryChange.current) {
      return;
    }

    isProcessingCategoryChange.current = true;

    try {
      const category = apiServiceCategories.find(c => c.Id === categoryId);
      if (!category) return;

      setSelectedCategoriesData(prev =>
        checked
          ? [...prev, category]
          : prev.filter(c => c.Id !== categoryId)
      );

      if (checked && !serviceDetailsForms[categoryId]) {
        setServiceDetailsForms(prev => ({
          ...prev,
          [categoryId]: {
              ...initialStepSpecificFormData,
          }
        }));
      }
    } finally {
      // Reset the flag after a short delay to allow for proper state updates
      setTimeout(() => {
        isProcessingCategoryChange.current = false;
      }, 100);
    }
  }, [apiServiceCategories, serviceDetailsForms]);

  const handleDetailInputChangeCallback = useCallback((categoryId: number, fieldPath: string, value: string | number | boolean | File | File[] | null) => {
    setServiceDetailsForms(prev => {
      const categoryForm = prev[categoryId] || { ...initialStepSpecificFormData };

      const keys = fieldPath.split('.');
      let currentLevel: any = categoryForm;

      keys.forEach((key, index) => {
        if (index === keys.length - 1) {
          if (['PricePerHour', 'PricePerDay', 'ExperienceYears', 'PricePerMeal', 'MinPortions', 'PriceSubscriptionAmount'].includes(key)) {
            currentLevel[key] = value === '' ? '' : String(value);
          } else {
            currentLevel[key] = value;
          }
        } else {
          currentLevel[key] = currentLevel[key] || {};
          currentLevel = currentLevel[key];
        }
      });
      return { ...prev, [categoryId]: { ...categoryForm } };
    });
    if (fieldErrors[`${categoryId}_${fieldPath}`]) {
        setFieldErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[`${categoryId}_${fieldPath}`];
            return newErrors;
        });
    }
  }, [fieldErrors]);

  const validateCurrentStep = () => {
    let currentErrors: string[] = [];
    let currentFieldErrorsLocal: Record<string, boolean> = {};

    if (currentStep === 0) {
      if (selectedCategoriesData.length === 0) {
        currentErrors.push(translate(commonTranslations, 'regProvErrorNoCategorySelected'));
      }
    } else if (currentStep > 0 && currentStep <= selectedCategoriesData.length) {
      const currentCategoryData = selectedCategoriesData[currentStep - 1];
      const details = serviceDetailsForms[currentCategoryData.Id];
      const categoryName = translate(commonTranslations, currentCategoryData.NameKey as keyof typeof commonTranslations);
      const catId = currentCategoryData.Id;

      if (!details?.experienceYears?.trim() || isNaN(parseInt(details.experienceYears, 10)) || parseInt(details.experienceYears, 10) < 0) {
        currentErrors.push(translate(commonTranslations, 'regProvErrorCategoryDetailsMissing').replace('{categoryName}', categoryName) + " (experiența invalidă)");
        currentFieldErrorsLocal[`${catId}_experienceYears`] = true;
      }
      if (!details?.description?.trim()) {
        currentErrors.push(translate(commonTranslations, 'regProvErrorCategoryDetailsMissing').replace('{categoryName}', categoryName) + " (descrierea lipsește)");
        currentFieldErrorsLocal[`${catId}_description`] = true;
      }
      if (!details?.availabilityWeekdays && !details?.availabilityWeekends && !details?.availabilityEvenings) {
        currentErrors.push(translate(commonTranslations, 'regProvErrorCategoryAvailabilityMissing').replace('{categoryName}', categoryName));
        currentFieldErrorsLocal[`${catId}_availability`] = true;
      }
      if (!details?.LocationId) {
        currentErrors.push(translate(commonTranslations, 'regProvErrorLocationRequired').replace('{categoryName}', categoryName));
        currentFieldErrorsLocal[`${catId}_LocationId`] = true;
      }
    } else {
      if (!termsAccepted) {
        currentErrors.push(translate(commonTranslations, 'regProvErrorTermsNotAccepted'));
        currentFieldErrorsLocal['termsAccepted'] = true;
      }
    }
    setErrors(currentErrors);
    setFieldErrors(currentFieldErrorsLocal);

    if (currentErrors.length > 0) {
      toast({
        variant: "destructive",
        title: translate(commonTranslations, 'regProvErrorAlertTitle'),
        description: (<ul className="list-disc list-inside">{currentErrors.map((err, i) => <li key={i}>{err}</li>)}</ul>)
      });
      return false;
    }
    return true;
  };

  const handleNextStep = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  const clearDraft = () => {
    if (typeof window !== 'undefined') {
      if (currentUserId) {
        const draftKey = `providerRegistrationDraft_${currentUserId}`;
        localStorage.removeItem(draftKey);
      }
      // Also remove old non-user-specific draft for cleanup
      localStorage.removeItem('providerRegistrationDraft');
    }

    setCurrentStep(0);
    setSelectedCategoriesData([]);
    setServiceDetailsForms({});
    setTermsAccepted(false);
    setLastSavedTime(null);
    setIsDraftSaved(false);
    setErrors([]);
    setFieldErrors({});
  };

  // Helper function to process file uploads for a service category
  const processFileUploads = async (detailsForm: StepSpecificServiceFormData) => {
    const processedForm = { ...detailsForm };

    try {
      // Handle single file upload (DocBuletinFile)
      if (detailsForm.DocBuletinFile) {
        const result = await simulateFileUpload(detailsForm.DocBuletinFile);
        if (result.success) {
          processedForm.DocBuletinFileName = result.fileName || null;
        } else {
          throw new Error(`ID Card upload failed: ${result.error}`);
        }
      }

      // Handle multiple file uploads (DocDiplomeFiles)
      if (detailsForm.DocDiplomeFiles && detailsForm.DocDiplomeFiles.length > 0) {
        const result = await simulateMultipleFileUpload(detailsForm.DocDiplomeFiles);
        if (result.success) {
          processedForm.DocDiplomeFileNames = result.fileNames || [];
        } else {
          throw new Error(`Diplomas upload failed: ${result.errors?.join(', ')}`);
        }
      }

      // Handle multiple file uploads (DocRecomandariFiles)
      if (detailsForm.DocRecomandariFiles && detailsForm.DocRecomandariFiles.length > 0) {
        const result = await simulateMultipleFileUpload(detailsForm.DocRecomandariFiles);
        if (result.success) {
          processedForm.DocRecomandariFileNames = result.fileNames || [];
        } else {
          throw new Error(`Recommendations upload failed: ${result.errors?.join(', ')}`);
        }
      }

      // Remove file objects from the payload (keep only file names)
      delete processedForm.DocBuletinFile;
      delete processedForm.DocDiplomeFiles;
      delete processedForm.DocRecomandariFiles;

      return processedForm;
    } catch (error) {
      throw error;
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!validateCurrentStep()) return;

    setIsLoading(true);
    setErrors([]);

    if (sessionStatus !== "authenticated" || !session?.user || !currentUserId || !currentUserName || !currentUserEmail) {
      toast({ variant: "destructive", title: translate(commonTranslations, 'regProvToastErrorTitle'), description: translate(commonTranslations, 'regProvErrorLoadingUserData') });
      setIsLoading(false);
      return;
    }

    try {
      // Process file uploads for each service category
      const requestedServicesPayload = await Promise.all(
        selectedCategoriesData.map(async (catData) => {
          const detailsForm = serviceDetailsForms[catData.Id];
          const processedForm = await processFileUploads(detailsForm);

          return {
            categoryId: catData.Id,
            serviceCategorySlug: catData.Slug,
            ...processedForm
          };
        })
      );

      // Submit the form with processed file data
      const response = await fetch('/api/proxy/provider-requests/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: parseInt(currentUserId, 10),
          userName: currentUserName,
          userEmail: currentUserEmail,
          requestedServices: requestedServicesPayload,
        }),
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.message || "A apărut o eroare la trimiterea cererii.");

      // Proactively set local state to true, so AuthGuard can detect the change after approval
      if (typeof window !== 'undefined') {
        localStorage.setItem('isUserProvider', 'true');
        window.dispatchEvent(new Event('authChange'));
      }

      toast({ title: translate(commonTranslations, 'regProvToastSuccessTitle'), description: translate(commonTranslations, 'regProvToastSuccessDescription')});
      setProviderRequestStatus(data.request);

      // Clear saved draft after successful submission
      if (typeof window !== 'undefined') {
        if (currentUserId) {
          const draftKey = `providerRegistrationDraft_${currentUserId}`;
          localStorage.removeItem(draftKey);
        }
        // Also remove old non-user-specific draft for cleanup
        localStorage.removeItem('providerRegistrationDraft');
      }
      setLastSavedTime(null);
      setIsDraftSaved(false);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Eroare necunoscută.";
      setErrors([errorMessage]);
      toast({ variant: "destructive", title: translate(commonTranslations, 'regProvToastErrorTitle'), description: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const stepperSteps = useMemo(() => {
    const labels = [{ label: translate(commonTranslations, 'regProvStep1TitleShort') }];
    selectedCategoriesData.forEach(cat => {
        labels.push({ label: translate(commonTranslations, cat.NameKey as keyof typeof commonTranslations) });
    });
    if (selectedCategoriesData.length > 0) {
        labels.push({ label: translate(commonTranslations, 'regProvTermsStepTitleShort') });
    }
    return labels;
  }, [selectedCategoriesData, translate]);

  // Create handlers outside of render to avoid hooks order violations
  const createCategoryHandlers = React.useCallback((categoryId: number, categorySlug: ServiceCategorySlug, isSelected: boolean) => {
    const handleCardClick = (event: React.MouseEvent) => {
      // Don't trigger if clicking on checkbox or its label
      const target = event.target as HTMLElement;
      if (target.closest('[role="checkbox"]') || target.closest('label[for*="category-"]')) {
        return;
      }
      event.preventDefault();
      event.stopPropagation();
      handleCategorySelectionChange(categoryId, categorySlug, !isSelected);
    };

    const handleCheckboxChange = (checked: boolean) => {
      handleCategorySelectionChange(categoryId, categorySlug, checked);
    };

    const handleLabelClick = (e: React.MouseEvent) => {
      e.preventDefault();
      handleCategorySelectionChange(categoryId, categorySlug, !isSelected);
    };

    return { handleCardClick, handleCheckboxChange, handleLabelClick };
  }, [handleCategorySelectionChange]);

  const renderStepContent = () => {
    if (currentStep === 0) {
      return (
        <div className="space-y-8 p-2">
          <div className="text-center">
            <Label className="text-xl font-semibold text-gray-800">{translate(commonTranslations, 'regProvStep1Description')}</Label>
            <p className="text-base text-gray-600 mt-3">Selectează toate serviciile pe care dorești să le oferi. Poți alege mai multe opțiuni.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-h-[450px] overflow-y-auto px-2">
            {apiServiceCategories.map(category => {
              const isSelected = selectedCategoriesData.some(c => c.Id === category.Id);
              const { handleCardClick, handleCheckboxChange, handleLabelClick } = createCategoryHandlers(
                category.Id,
                category.Slug as ServiceCategorySlug,
                isSelected
              );

              return (
                <div
                  key={category.Id}
                  className={cn(
                    "flex items-center space-x-5 p-6 rounded-xl border-2 transition-all duration-200 cursor-pointer hover:shadow-lg hover:scale-[1.02]",
                    isSelected
                      ? "border-blue-300 bg-gradient-to-br from-blue-50 to-blue-100 shadow-md ring-1 ring-blue-200"
                      : "border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50",
                    fieldErrors[`category-${category.Id}`] && "border-red-300 bg-red-50 ring-1 ring-red-200"
                  )}
                  onClick={handleCardClick}
                >
                  <Checkbox
                    id={`category-${category.Id}`}
                    checked={isSelected}
                    onCheckedChange={handleCheckboxChange}
                    className="shrink-0 w-5 h-5"
                  />
                  <div className="flex-1 space-y-2">
                    <Label
                      htmlFor={`category-${category.Id}`}
                      className="font-semibold text-lg cursor-pointer text-gray-800 block leading-tight"
                      onClick={handleLabelClick}
                    >
                      {translate(commonTranslations, category.NameKey as keyof typeof commonTranslations)}
                    </Label>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      {category.Slug === 'Nanny' && translate(commonTranslations, 'regProvServiceDescNanny')}
                      {category.Slug === 'ElderCare' && translate(commonTranslations, 'regProvServiceDescElderCare')}
                      {category.Slug === 'Cleaning' && translate(commonTranslations, 'regProvServiceDescCleaning')}
                      {category.Slug === 'Cooking' && translate(commonTranslations, 'regProvServiceDescCooking')}
                      {category.Slug === 'Tutoring' && translate(commonTranslations, 'regProvServiceDescTutoring')}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>

          {selectedCategoriesData.length > 0 && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 shadow-sm">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold text-lg">✓</span>
                </div>
                <div>
                  <p className="text-green-800 font-semibold text-lg">
                    {translate(commonTranslations, 'regProvSelectedServices')
                      .replace('{count}', selectedCategoriesData.length.toString())
                      .replace('{plural}', selectedCategoriesData.length > 1 ? 'ri' : '')}
                  </p>
                  <p className="text-green-700 text-base mt-1">
                    Vei completa detaliile pentru fiecare serviciu în pașii următori.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      );
    }

    if (currentStep > 0 && currentStep <= selectedCategoriesData.length) {
      const currentCategory = selectedCategoriesData[currentStep - 1];
      const categoryId = currentCategory.Id;
      const details = serviceDetailsForms[categoryId] || { ...initialStepSpecificFormData };

      return (
        <div className="space-y-4">
            <CommonDetailsSection
                categoryId={categoryId}
                details={details}
                handleDetailInputChange={handleDetailInputChangeCallback}
                fieldErrors={fieldErrors}
                translate={translate}
                locations={locations}
                locationsLoading={locationsLoading}
            />
            {currentCategory.Slug === 'Nanny' && ( <NannyFields categoryId={categoryId} details={details} handleDetailInputChange={handleDetailInputChangeCallback} fieldErrors={fieldErrors} translate={translate} />)}
            {currentCategory.Slug === 'ElderCare' && ( <ElderCareFields categoryId={categoryId} details={details} handleDetailInputChange={handleDetailInputChangeCallback} fieldErrors={fieldErrors} translate={translate} /> )}
            {currentCategory.Slug === 'Cleaning' && ( <CleaningFields categoryId={categoryId} details={details} handleDetailInputChange={handleDetailInputChangeCallback} fieldErrors={fieldErrors} translate={translate} /> )}
            {currentCategory.Slug === 'Cooking' && ( <CookingFields categoryId={categoryId} details={details} handleDetailInputChange={handleDetailInputChangeCallback} fieldErrors={fieldErrors} translate={translate} /> )}
            {currentCategory.Slug === 'Tutoring' && ( <TutoringFields categoryId={categoryId} details={details} handleDetailInputChange={handleDetailInputChangeCallback} fieldErrors={fieldErrors} translate={translate} /> )}
        </div>
      );
    }

    if (currentStep === selectedCategoriesData.length + 1 && selectedCategoriesData.length > 0) {
      return (
        <div className="space-y-4">
          <Label className="text-base font-medium">{translate(commonTranslations, 'regProvTermsStepTitle')}</Label>
          <ScrollArea className="h-60 w-full rounded-md border p-4 text-sm">
            <p className="whitespace-pre-line text-muted-foreground">
              {translate(commonTranslations, 'regProvFullTermsContent')}
            </p>
          </ScrollArea>
          <div className={cn("flex items-center space-x-2 p-3 border rounded-md", fieldErrors.termsAccepted && "border-destructive ring-1 ring-destructive")}>
            <Checkbox
              id="termsProvider"
              checked={termsAccepted}
              onCheckedChange={(checked) => setTermsAccepted(checked as boolean)}
            />
            <Label htmlFor="termsProvider" className="text-sm text-muted-foreground cursor-pointer">
              {translate(commonTranslations, 'regProvTermsAgreementText')}{" "}
              <Link href="/terms" className="underline hover:text-primary" target="_blank" rel="noopener noreferrer">
                {translate(commonTranslations, 'regProvTermsLink')}
              </Link>
            </Label>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderPageContentBasedOnStatus = () => {
    if (sessionStatus === "loading" || providerRequestStatus === 'loading' || isLoadingCategories) {
      return (
        <div className="flex flex-col items-center justify-center min-h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="ml-3 mt-2">
            {isLoadingCategories ? translate(commonTranslations, 'regProvLoadingCategories') :
            (providerRequestStatus === 'loading' ? translate(commonTranslations, 'regProvLoadingRequestStatus') :
            translate(commonTranslations, 'regProvLoadingUserData'))}
          </p>
        </div>
      );
    }

    if (sessionStatus === "unauthenticated" || providerRequestStatus === 'error' || categoriesError) {
      return (
        <Card className="w-full max-w-md mx-auto mt-10">
          <CardHeader className="text-center">
            <AlertTriangle className="mx-auto h-10 w-10 text-destructive" />
            <CardTitle className="mt-2">{translate(commonTranslations, 'regProvToastErrorTitle')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">{categoriesError || translate(commonTranslations, 'regProvErrorLoadingUserData')}</p>
          </CardContent>
          <CardFooter>
            <Button className="w-full" asChild><Link href={sessionStatus === "unauthenticated" ? "/login" : "/dashboard"}>
              {sessionStatus === "unauthenticated" ? translate(commonTranslations, 'regProvGoToAuth') : translate(commonTranslations, 'regProvGoToPanel')}
            </Link></Button>
          </CardFooter>
        </Card>
      );
    }

    if (providerRequestStatus && typeof providerRequestStatus === 'object') {
        const request = providerRequestStatus as ProviderRegistrationRequest & { PendingServices?: any[] };

        // If the request has PendingServices, use the new granular dashboard
        if (request.PendingServices && request.PendingServices.length > 0) {
          const handleRefreshStatus = async () => {
            try {
              const res = await fetch(`/api/proxy/provider-requests/my-status`);
              if (res.ok) {
                const data = await res.json();
                setProviderRequestStatus(data.request || 'none');
              }
            } catch (err) {
              console.error("Error refreshing status:", err);
            }
          };

          const handleResubmitService = async (serviceId: number) => {
            // This would redirect to a service resubmission form
            // For now, we'll show a toast
            toast({
              title: translate(commonTranslations, 'serviceResubmissionTitle'),
              description: translate(commonTranslations, 'serviceResubmissionComingSoon'),
            });
          };

          return (
            <ProviderStatusDashboard
              request={request as ProviderRegistrationWithServices}
              onRefresh={handleRefreshStatus}
              onResubmitService={handleResubmitService}
            />
          );
        }

        // Legacy status display for requests without PendingServices
        if (request.Status === 'Pending') {
          return ( <Card className="w-full max-w-md mx-auto mt-10 text-center"><CardHeader><Hourglass className="mx-auto h-12 w-12 text-primary" /><CardTitle className="mt-4">{translate(commonTranslations, 'regProvRequestStatusPendingTitle')}</CardTitle></CardHeader><CardContent><p className="text-muted-foreground">{translate(commonTranslations, 'regProvRequestStatusPendingDescription')}</p></CardContent><CardFooter><Button className="w-full" asChild><Link href="/dashboard">{translate(commonTranslations, 'regProvGoToDashboardButton')}</Link></Button></CardFooter></Card>);
        }
        if (request.Status === 'Approved') {
          return ( <Card className="w-full max-w-md mx-auto mt-10 text-center"><CardHeader><CheckCircle className="mx-auto h-12 w-12 text-green-600" /><CardTitle className="mt-4">{translate(commonTranslations, 'regProvRequestStatusApprovedTitle')}</CardTitle></CardHeader><CardContent><p className="text-muted-foreground">{translate(commonTranslations, 'regProvRequestStatusApprovedDescription')}</p></CardContent><CardFooter><Button className="w-full" asChild><Link href="/dashboard">{translate(commonTranslations, 'regProvGoToDashboardButton')}</Link></Button></CardFooter></Card>);
        }
        if (request.Status === 'Rejected') {
            return ( <Card className="w-full max-w-md mx-auto mt-10 text-center"><CardHeader><ShieldAlert className="mx-auto h-12 w-12 text-destructive" /><CardTitle className="mt-4">{translate(commonTranslations, 'regProvRequestStatusRejectedTitle')}</CardTitle></CardHeader><CardContent><p className="text-muted-foreground">{translate(commonTranslations, 'regProvRequestStatusRejectedDescription').replace('{adminNotes}', request.AdminNotes || translate(commonTranslations, 'regProvNoAdminNotes'))}</p></CardContent><CardFooter className="flex-col space-y-2"><Button className="w-full" onClick={() => { setProviderRequestStatus('none'); setCurrentStep(0); setSelectedCategoriesData([]); setServiceDetailsForms({}); }}>{translate(commonTranslations, 'regProvReapplyButton')}</Button><Button variant="outline" className="w-full" asChild><Link href="/dashboard">{translate(commonTranslations, 'regProvGoToDashboardButton')}</Link></Button></CardFooter></Card>);
        }
    }

    if (providerRequestStatus === 'none') {
        const { title, description } = renderStepTitleAndDescription();
        const isLastDetailsStep = currentStep === selectedCategoriesData.length && selectedCategoriesData.length > 0;
        const isTermsStep = currentStep === selectedCategoriesData.length + 1 && selectedCategoriesData.length > 0;

        return (
            <div className="w-full max-w-4xl mx-auto">
              <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-gray-50">
                <CardHeader className="text-center relative pb-6 pt-8 px-8">
                  <CardTitle className="text-3xl font-bold font-headline text-gray-800 mb-3">{title}</CardTitle>
                  <CardDescription className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">{description}</CardDescription>
                </CardHeader>

                {/* Draft Status Indicator - Moved outside header to avoid overlap */}
                {lastSavedTime && (
                  <div className="px-8 pb-4">
                    <div className="flex items-center justify-end gap-3">
                      <div className="flex items-center gap-2 text-xs text-gray-500 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-full shadow-sm border">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        {translate(commonTranslations, 'regProvAutoSaved')} {lastSavedTime.toLocaleTimeString('ro-RO', { hour: '2-digit', minute: '2-digit' })}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearDraft}
                        className="text-xs text-gray-500 hover:text-red-600 hover:bg-red-50 px-3 py-2 h-auto rounded-full border border-transparent hover:border-red-200"
                      >
                        Șterge progresul
                      </Button>
                    </div>
                  </div>
                )}

                {stepperSteps.length > 1 && (
                  <div className="px-8 py-4 border-b border-gray-100">
                    <StepperProgress currentStep={currentStep} steps={stepperSteps} className=""/>
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                    <CardContent className="space-y-8 p-8 md:p-10">
                        <div key={currentStep} className="animate-in fade-in-50 duration-500">
                            {renderStepContent()}
                        </div>
                        {errors.length > 0 && (
                        <Alert variant="destructive" className="mt-8 border-red-200 bg-gradient-to-r from-red-50 to-pink-50 shadow-sm">
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                            <AlertTitle className="text-red-800 font-semibold text-lg">{translate(commonTranslations, 'regProvErrorAlertTitle')}</AlertTitle>
                            <AlertDescription className="text-red-700 mt-2">
                              <ul className="list-disc list-inside space-y-2 mt-3">
                                {errors.map((err, i) => <li key={i} className="text-base leading-relaxed">{err}</li>)}
                              </ul>
                            </AlertDescription>
                        </Alert>
                        )}
                    </CardContent>
                    <CardFooter className="flex flex-col sm:flex-row justify-between pt-4 pb-4 px-8 gap-4 border-t border-gray-100 bg-gray-50/50">
                        {currentStep > 0 && (
                            <Button
                              type="button"
                              variant="outline"
                              onClick={handlePrevStep}
                              disabled={isLoading}
                              className="w-full sm:w-auto px-6 py-3 text-base font-medium border-2 hover:bg-gray-50 transition-all duration-200"
                            >
                                <ArrowLeft className="mr-2 h-5 w-5" />
                                {translate(commonTranslations, 'regProvPrevButton')}
                            </Button>
                        )}
                        <div className="sm:ml-auto">
                        {currentStep === 0 || (currentStep <= selectedCategoriesData.length && !isLastDetailsStep) ? (
                            <Button
                              type="button"
                              onClick={handleNextStep}
                              disabled={isLoading || selectedCategoriesData.length === 0 && currentStep === 0}
                              className="w-full sm:w-auto px-6 py-3 text-base font-medium bg-blue-600 hover:bg-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
                            >
                                {translate(commonTranslations, 'regProvNextButton')}
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Button>
                        ) : null}
                        {(isLastDetailsStep) && (
                             <Button
                               type="button"
                               onClick={handleNextStep}
                               disabled={isLoading}
                               className="w-full sm:w-auto px-6 py-3 text-base font-medium bg-blue-600 hover:bg-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
                             >
                                {translate(commonTranslations, 'regProvNextButton')}
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Button>
                         )}
                         {isTermsStep && (
                            <Button
                              type="submit"
                              disabled={isLoading || !termsAccepted}
                              className="w-full sm:w-auto px-6 py-3 text-base font-medium bg-green-600 hover:bg-green-700 disabled:bg-gray-400 transition-all duration-200 shadow-md hover:shadow-lg"
                            >
                                {isLoading ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : null}
                                {isLoading ? translate(commonTranslations, 'regProvSubmittingRequestButton') : translate(commonTranslations, 'regProvSubmitRequestButton')}
                            </Button>
                         )}
                         </div>
                    </CardFooter>
                </form>
              </Card>
            </div>
        );
    }
    return null;
  };

  const renderStepTitleAndDescription = () => {
    if (currentStep === 0) {
        return { title: translate(commonTranslations, 'regProvStep1Title'), description: translate(commonTranslations, 'regProvStep1Description') };
    }
    if (currentStep > 0 && currentStep <= selectedCategoriesData.length) {
        const category = selectedCategoriesData[currentStep - 1];
        const categoryName = translate(commonTranslations, category.NameKey as keyof typeof commonTranslations);
        return {
            title: translate(commonTranslations, 'regProvStep2Title')
                        .replace('{currentSubStep}', String(currentStep))
                        .replace('{totalSubSteps}', String(selectedCategoriesData.length))
                        .replace('{categoryName}', categoryName),
            description: translate(commonTranslations, 'regProvStep2Description').replace('{categoryName}', categoryName)
        };
    }
     if (currentStep === selectedCategoriesData.length + 1 && selectedCategoriesData.length > 0) {
      return { title: translate(commonTranslations, 'regProvTermsStepTitle'), description: translate(commonTranslations, 'regProvTermsStepDescription') };
    }
    return { title: translate(commonTranslations, 'regProvPageTitle'), description: translate(commonTranslations, 'regProvPageDescription') };
  };

  return (
    <>
        {renderPageContentBasedOnStatus()}
    </>
  );
}

