
import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import type { ReactNode } from 'react';

interface ServiceCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  link: string;
  linkText?: string;
}

export function ServiceCard({ icon, title, description, link, linkText = "Vezi furnizori" }: ServiceCardProps) {
  return (
    <Card className="bg-card hover:shadow-xl transition-shadow duration-300 ease-in-out hover:transform hover:-translate-y-1 flex flex-col text-center">
      <CardHeader className="items-center">
        <div className="bg-primary/10 w-20 h-20 rounded-full flex items-center justify-center mb-6 text-primary">
          {/* Icon-ul va fi redat aici, de exemplu <Child className="w-10 h-10" /> */}
          {icon}
        </div>
        <CardTitle className="font-headline text-xl">{title}</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow">
        <CardDescription className="text-foreground/80 mb-4">{description}</CardDescription>
      </CardContent>
      <div className="p-6 pt-0 mt-auto">
        <Button variant="link" asChild className="p-0 text-primary hover:text-accent group">
          <Link href={link}>
            {linkText}
            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Link>
        </Button>
      </div>
    </Card>
  );
}
