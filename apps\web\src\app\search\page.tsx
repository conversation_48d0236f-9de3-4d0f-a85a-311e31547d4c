
"use client";

import { useState, useEffect, Suspense } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import type { ServiceCategory } from '@prisma/client';

// Lazy load service-specific search components
const NannySearchPage = dynamic(() => import('./service-pages/nanny/nanny-search').then(mod => mod.NannySearchPage), {
  loading: () => <SearchPageSkeleton />,
});
const ElderCareSearchPage = dynamic(() => import('./service-pages/elder-care/elder-care-search').then(mod => mod.ElderCareSearchPage), {
  loading: () => <SearchPageSkeleton />,
});
const CleaningSearchPage = dynamic(() => import('./service-pages/cleaning/cleaning-search').then(mod => mod.CleaningSearchPage), {
  loading: () => <SearchPageSkeleton />,
});
const TutoringSearchPage = dynamic(() => import('./service-pages/tutoring/tutoring-search').then(mod => mod.TutoringSearchPage), {
    loading: () => <SearchPageSkeleton />,
});
const CookingSearchPage = dynamic(() => import('./service-pages/cooking/cooking-search').then(mod => mod.CookingSearchPage), {
    loading: () => <SearchPageSkeleton />,
});

const serviceComponentMap: { [key: string]: React.ComponentType } = {
  Nanny: NannySearchPage,
  ElderCare: ElderCareSearchPage,
  Cleaning: CleaningSearchPage,
  Tutoring: TutoringSearchPage,
  Cooking: CookingSearchPage,
};

function SearchPageSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-6">
      <div className="md:col-span-1">
        <Skeleton className="h-[400px] w-full" />
      </div>
      <div className="md:col-span-3 space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-40 w-full" />
        <Skeleton className="h-40 w-full" />
        <Skeleton className="h-40 w-full" />
      </div>
    </div>
  );
}

function SearchPageComponent() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { translate } = useLanguage();
  
  const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const activeTab = searchParams.get('service') || serviceCategories[0]?.Slug || '';

  useEffect(() => {
    async function fetchCategories() {
      setIsLoading(true);
      try {
        const response = await fetch('/api/proxy/service-categories');
        if (!response.ok) throw new Error('Failed to fetch categories');
        const data = await response.json();
        setServiceCategories(data || []);
        
        // If no service is in URL, or it's invalid, set the first available one
        const currentService = searchParams.get('service');
        const isValidService = data.some((cat: ServiceCategory) => cat.Slug === currentService);
        if ((!currentService || !isValidService) && data.length > 0) {
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.set('service', data[0].Slug);
            router.replace(`${pathname}?${newParams.toString()}`, { scroll: false });
        }
      } catch (error) {
        console.error("Error fetching service categories:", error);
      } finally {
        setIsLoading(false);
      }
    }
    fetchCategories();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleTabChange = (value: string) => {
    const newParams = new URLSearchParams(); // Start fresh
    newParams.set('service', value);
    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  };
  
  const ActiveServiceComponent = serviceComponentMap[activeTab];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Navbar />
      <main className="flex-grow container mx-auto py-8 px-4">
        {isLoading ? (
          <Skeleton className="h-12 w-full max-w-2xl mx-auto" />
        ) : (
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5">
              {serviceCategories.map((category) => (
                <TabsTrigger key={category.Slug} value={category.Slug}>
                  {translate(commonTranslations, category.NameKey as keyof typeof commonTranslations)}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {/* The content is rendered outside the Tabs.Content for simpler dynamic rendering */}
          </Tabs>
        )}
        
        <div className="mt-4">
            {ActiveServiceComponent ? <ActiveServiceComponent /> : <SearchPageSkeleton />}
        </div>
      </main>
      <Footer />
    </div>
  );
}

export default function SearchPage() {
    return (
        <Suspense fallback={<div className="flex items-center justify-center h-screen w-full">Loading...</div>}>
            <SearchPageComponent />
        </Suspense>
    )
}
