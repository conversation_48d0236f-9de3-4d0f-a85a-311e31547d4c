"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Globe, CheckCircle, Plus, X } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { AVAILABLE_LANGUAGES, type LanguageOption } from '@/types/profile-setup';

interface LanguagesStepProps {
  value: string[];
  onChange: (languages: string[]) => void;
}

const languagesStepTranslations = {
  en: {
    title: "Select Your Spoken Languages",
    description: "Help service providers know which languages you can communicate in. Select at least one language.",
    selectedLanguages: "Selected Languages",
    availableLanguages: "Available Languages",
    whyNeeded: "Why do we need this information?",
    reason1: "Service providers can communicate with you in your preferred language",
    reason2: "Better matching with providers who speak your languages",
    reason3: "Avoid communication barriers during service delivery",
    reason4: "Ensure clear understanding of service requirements",
    selectAtLeast: "Please select at least one language",
    addLanguage: "Add Language",
    removeLanguage: "Remove {language}",
    commonLanguages: "Most Common Languages",
    otherLanguages: "Other Languages"
  },
  ro: {
    title: "Selectează Limbile Vorbite",
    description: "Ajută furnizorii de servicii să știe în ce limbă pot comunica cu tine. Selectează cel puțin o limbă.",
    selectedLanguages: "Limbile Selectate",
    availableLanguages: "Limbile Disponibile",
    whyNeeded: "De ce avem nevoie de această informație?",
    reason1: "Furnizorii de servicii pot comunica cu tine în limba preferată",
    reason2: "Potrivire mai bună cu furnizorii care vorbesc limbile tale",
    reason3: "Evită barierele de comunicare în timpul prestării serviciilor",
    reason4: "Asigură înțelegerea clară a cerințelor serviciului",
    selectAtLeast: "Te rugăm să selectezi cel puțin o limbă",
    addLanguage: "Adaugă Limba",
    removeLanguage: "Elimină {language}",
    commonLanguages: "Limbile Cele Mai Comune",
    otherLanguages: "Alte Limbi"
  },
  ru: {
    title: "Выберите Ваши Разговорные Языки",
    description: "Помогите поставщикам услуг знать, на каких языках они могут с вами общаться. Выберите хотя бы один язык.",
    selectedLanguages: "Выбранные Языки",
    availableLanguages: "Доступные Языки",
    whyNeeded: "Зачем нам нужна эта информация?",
    reason1: "Поставщики услуг могут общаться с вами на предпочитаемом языке",
    reason2: "Лучшее соответствие с поставщиками, говорящими на ваших языках",
    reason3: "Избежание языковых барьеров при оказании услуг",
    reason4: "Обеспечение четкого понимания требований к услугам",
    selectAtLeast: "Пожалуйста, выберите хотя бы один язык",
    addLanguage: "Добавить Язык",
    removeLanguage: "Удалить {language}",
    commonLanguages: "Наиболее Распространенные Языки",
    otherLanguages: "Другие Языки"
  }
};

export function LanguagesStep({ value, onChange }: LanguagesStepProps) {
  const { translate } = useLanguage();

  const selectedLanguages = value || [];
  const availableLanguages = AVAILABLE_LANGUAGES.filter(
    lang => !selectedLanguages.includes(lang.code)
  );

  // Split languages into common and other
  const commonLanguageCodes = ['ro', 'ru', 'en'];
  const commonLanguages = availableLanguages.filter(lang => 
    commonLanguageCodes.includes(lang.code)
  );
  const otherLanguages = availableLanguages.filter(lang => 
    !commonLanguageCodes.includes(lang.code)
  );

  const handleAddLanguage = (languageCode: string) => {
    if (!selectedLanguages.includes(languageCode)) {
      onChange([...selectedLanguages, languageCode]);
    }
  };

  const handleRemoveLanguage = (languageCode: string) => {
    onChange(selectedLanguages.filter(code => code !== languageCode));
  };

  const getLanguageLabel = (code: string): LanguageOption | undefined => {
    return AVAILABLE_LANGUAGES.find(lang => lang.code === code);
  };

  const reasons = [
    translate(languagesStepTranslations, 'reason1'),
    translate(languagesStepTranslations, 'reason2'),
    translate(languagesStepTranslations, 'reason3'),
    translate(languagesStepTranslations, 'reason4'),
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <Globe className="w-6 h-6 text-primary" />
          </div>
        </div>
        <h3 className="text-xl font-semibold mb-2">
          {translate(languagesStepTranslations, 'title')}
        </h3>
        <p className="text-muted-foreground">
          {translate(languagesStepTranslations, 'description')}
        </p>
      </div>

      {/* Selected Languages */}
      {selectedLanguages.length > 0 && (
        <div>
          <h4 className="font-medium text-sm mb-3">
            {translate(languagesStepTranslations, 'selectedLanguages')} ({selectedLanguages.length})
          </h4>
          <div className="flex flex-wrap gap-2">
            {selectedLanguages.map(code => {
              const language = getLanguageLabel(code);
              return language ? (
                <Badge
                  key={code}
                  variant="default"
                  className="px-3 py-1 text-sm flex items-center gap-2"
                >
                  <span>{language.nativeLabel}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 hover:bg-transparent"
                    onClick={() => handleRemoveLanguage(code)}
                    aria-label={translate(languagesStepTranslations, 'removeLanguage').replace('{language}', language.nativeLabel)}
                  >
                    <X className="w-3 h-3 hover:text-destructive" />
                  </Button>
                </Badge>
              ) : null;
            })}
          </div>
        </div>
      )}

      {/* Validation Message */}
      {selectedLanguages.length === 0 && (
        <div className="text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            {translate(languagesStepTranslations, 'selectAtLeast')}
          </p>
        </div>
      )}

      {/* Common Languages */}
      {commonLanguages.length > 0 && (
        <div>
          <h4 className="font-medium text-sm mb-3">
            {translate(languagesStepTranslations, 'commonLanguages')}
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {commonLanguages.map(language => (
              <Button
                key={language.code}
                variant="outline"
                className="justify-start h-auto p-3 text-left"
                onClick={() => handleAddLanguage(language.code)}
              >
                <Plus className="w-4 h-4 mr-2 text-primary" />
                <div>
                  <div className="font-medium">{language.nativeLabel}</div>
                  <div className="text-xs text-muted-foreground">{language.label}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Other Languages */}
      {otherLanguages.length > 0 && (
        <div>
          <h4 className="font-medium text-sm mb-3">
            {translate(languagesStepTranslations, 'otherLanguages')}
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {otherLanguages.map(language => (
              <Button
                key={language.code}
                variant="outline"
                className="justify-start h-auto p-3 text-left"
                onClick={() => handleAddLanguage(language.code)}
              >
                <Plus className="w-4 h-4 mr-2 text-primary" />
                <div>
                  <div className="font-medium">{language.nativeLabel}</div>
                  <div className="text-xs text-muted-foreground">{language.label}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Why We Need This */}
      <Card className="bg-muted/30">
        <CardContent className="p-4">
          <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
            <Globe className="w-4 h-4 text-primary" />
            {translate(languagesStepTranslations, 'whyNeeded')}
          </h4>
          <ul className="space-y-2">
            {reasons.map((reason, index) => (
              <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>{reason}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
