import { PrismaClient, ServiceCategorySlug, PendingServiceStatus } from '@prisma/client';
import { FileUploadService } from './file-upload-service';

const prisma = new PrismaClient();

export interface ServiceSubmissionData {
  categoryId: number;
  serviceCategorySlug: ServiceCategorySlug;
  serviceName?: string;
  description: string;
  experienceYears: number;
  
  // File uploads
  DocBuletinFile?: File | null;
  DocDiplomeFiles?: File[] | null;
  DocRecomandariFiles?: File[] | null;
  
  // Service-specific details (will be distributed to appropriate detail models)
  [key: string]: any;
}

export interface ProviderRegistrationData {
  userId: number;
  userName: string;
  userEmail: string;
  services: ServiceSubmissionData[];
}

export interface ServiceCreationResult {
  success: boolean;
  serviceId?: number;
  error?: string;
}

export interface RegistrationResult {
  success: boolean;
  requestId?: string;
  servicesCreated?: number;
  errors?: string[];
}

/**
 * Service for handling granular provider registration
 */
export class ProviderRegistrationService {
  
  /**
   * Submit a new provider registration with multiple services
   */
  static async submitRegistration(data: ProviderRegistrationData): Promise<RegistrationResult> {
    const transaction = await prisma.$transaction(async (tx) => {
      try {
        // 1. Check for existing request
        const existingRequest = await tx.providerRegistrationRequest.findUnique({
          where: { UserId: data.userId }
        });

        let request;
        
        if (existingRequest) {
          if (existingRequest.Status === 'Pending' || existingRequest.Status === 'Approved') {
            throw new Error(`Ai deja o cerere cu statusul: ${existingRequest.Status}`);
          }
          
          // Update existing rejected request
          request = await tx.providerRegistrationRequest.update({
            where: { Id: existingRequest.Id },
            data: {
              UserName: data.userName,
              UserEmail: data.userEmail,
              Status: 'Pending',
              RequestDate: new Date(),
              AdminNotes: null,
            }
          });
          
          // Delete old pending services for resubmission
          await tx.pendingService.deleteMany({
            where: { RequestId: existingRequest.Id }
          });
        } else {
          // Create new request
          request = await tx.providerRegistrationRequest.create({
            data: {
              UserId: data.userId,
              UserName: data.userName,
              UserEmail: data.userEmail,
              Status: 'Pending',
              RequestDate: new Date(),
            }
          });
        }

        // 2. Create pending services
        const serviceResults: ServiceCreationResult[] = [];
        
        for (const serviceData of data.services) {
          try {
            const result = await this.createPendingService(tx, request.Id, serviceData);
            serviceResults.push(result);
          } catch (error) {
            serviceResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        const successfulServices = serviceResults.filter(r => r.success);
        const failedServices = serviceResults.filter(r => !r.success);

        if (failedServices.length > 0) {
          throw new Error(`Failed to create ${failedServices.length} services: ${failedServices.map(f => f.error).join(', ')}`);
        }

        return {
          success: true,
          requestId: request.Id,
          servicesCreated: successfulServices.length,
        };

      } catch (error) {
        throw error;
      }
    });

    return transaction;
  }

  /**
   * Create a single pending service with its details
   */
  private static async createPendingService(
    tx: any,
    requestId: string,
    serviceData: ServiceSubmissionData
  ): Promise<ServiceCreationResult> {
    try {
      // 1. Process file uploads
      const documentPaths: string[] = [];
      
      // Simulate file upload processing (in real implementation, files would be uploaded here)
      if (serviceData.DocBuletinFile) {
        const result = await FileUploadService.simulateFileUpload(
          serviceData.DocBuletinFile.name,
          parseInt(requestId.slice(-6), 16), // Use part of requestId as userId simulation
          serviceData.serviceCategorySlug
        );
        if (result.success && result.filePath) {
          documentPaths.push(result.filePath);
        }
      }

      if (serviceData.DocDiplomeFiles && serviceData.DocDiplomeFiles.length > 0) {
        for (const file of serviceData.DocDiplomeFiles) {
          const result = await FileUploadService.simulateFileUpload(
            file.name,
            parseInt(requestId.slice(-6), 16),
            serviceData.serviceCategorySlug
          );
          if (result.success && result.filePath) {
            documentPaths.push(result.filePath);
          }
        }
      }

      if (serviceData.DocRecomandariFiles && serviceData.DocRecomandariFiles.length > 0) {
        for (const file of serviceData.DocRecomandariFiles) {
          const result = await FileUploadService.simulateFileUpload(
            file.name,
            parseInt(requestId.slice(-6), 16),
            serviceData.serviceCategorySlug
          );
          if (result.success && result.filePath) {
            documentPaths.push(result.filePath);
          }
        }
      }

      // 2. Create the pending service
      const serviceName = serviceData.serviceName || `Serviciu de ${serviceData.serviceCategorySlug}`;
      
      const pendingService = await tx.pendingService.create({
        data: {
          RequestId: requestId,
          CategoryId: serviceData.categoryId,
          ServiceCategorySlug: serviceData.serviceCategorySlug,
          ServiceName: serviceName,
          Description: serviceData.description,
          ExperienceYears: serviceData.experienceYears,
          Status: 'PendingReview',
          DocumentPaths: documentPaths,
        }
      });

      // 3. Create service-specific details
      await this.createServiceDetails(tx, pendingService.Id, serviceData);

      return {
        success: true,
        serviceId: pendingService.Id,
      };

    } catch (error) {
      console.error('Error creating pending service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create service-specific detail records
   */
  private static async createServiceDetails(
    tx: any,
    pendingServiceId: number,
    serviceData: ServiceSubmissionData
  ): Promise<void> {
    const slug = serviceData.serviceCategorySlug;

    switch (slug) {
      case 'Nanny':
        await tx.pendingNannyServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            PreferredAge_0_2: serviceData.PreferredAge_0_2 || false,
            PreferredAge_3_6: serviceData.PreferredAge_3_6 || false,
            PreferredAge_7_plus: serviceData.PreferredAge_7_plus || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServiceBabysitting: serviceData.ServiceBabysitting || false,
            ServicePlaytime: serviceData.ServicePlaytime || false,
            ServiceMeals: serviceData.ServiceMeals || false,
            ServiceBedtime: serviceData.ServiceBedtime || false,
            ServiceEducational: serviceData.ServiceEducational || false,
            ServiceOutdoor: serviceData.ServiceOutdoor || false,
            ServiceTransport: serviceData.ServiceTransport || false,
            ServiceHousework: serviceData.ServiceHousework || false,
            ExtraFirstAid: serviceData.ExtraFirstAid || false,
            ExtraOwnTransport: serviceData.ExtraOwnTransport || false,
            ExtraCooking: serviceData.ExtraCooking || false,
            ExtraLanguages: serviceData.ExtraLanguages || null,
            ExtraSpecialNeeds: serviceData.ExtraSpecialNeeds || false,
            ExtraOvernightCare: serviceData.ExtraOvernightCare || false,
          }
        });
        break;

      case 'Cleaning':
        await tx.pendingCleaningServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServiceRegularCleaning: serviceData.ServiceRegularCleaning || false,
            ServiceDeepCleaning: serviceData.ServiceDeepCleaning || false,
            ServiceWindowCleaning: serviceData.ServiceWindowCleaning || false,
            ServiceCarpetCleaning: serviceData.ServiceCarpetCleaning || false,
            ServiceLaundry: serviceData.ServiceLaundry || false,
            ServiceIroning: serviceData.ServiceIroning || false,
            ServiceOrganizing: serviceData.ServiceOrganizing || false,
            ServicePostConstruction: serviceData.ServicePostConstruction || false,
            ExtraOwnSupplies: serviceData.ExtraOwnSupplies || false,
            ExtraEcoFriendly: serviceData.ExtraEcoFriendly || false,
            ExtraOwnTransport: serviceData.ExtraOwnTransport || false,
            ExtraInsured: serviceData.ExtraInsured || false,
            ExtraWeekendAvailable: serviceData.ExtraWeekendAvailable || false,
            ExtraEmergencyService: serviceData.ExtraEmergencyService || false,
          }
        });
        break;

      case 'ElderCare':
        await tx.pendingElderCareServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServicePersonalCare: serviceData.ServicePersonalCare || false,
            ServiceMedicalCare: serviceData.ServiceMedicalCare || false,
            ServiceMobility: serviceData.ServiceMobility || false,
            ServiceCompanionship: serviceData.ServiceCompanionship || false,
            ServiceMeals: serviceData.ServiceMeals || false,
            ServiceHousework: serviceData.ServiceHousework || false,
            ServiceShopping: serviceData.ServiceShopping || false,
            ServiceTransport: serviceData.ServiceTransport || false,
            ExtraFirstAid: serviceData.ExtraFirstAid || false,
            ExtraOwnTransport: serviceData.ExtraOwnTransport || false,
            ExtraCooking: serviceData.ExtraCooking || false,
            ExtraLanguages: serviceData.ExtraLanguages || null,
            ExtraSpecialNeeds: serviceData.ExtraSpecialNeeds || false,
            ExtraOvernightCare: serviceData.ExtraOvernightCare || false,
          }
        });
        break;

      case 'Tutoring':
        await tx.pendingTutoringServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            SubjectMath: serviceData.SubjectMath || false,
            SubjectScience: serviceData.SubjectScience || false,
            SubjectLanguages: serviceData.SubjectLanguages || false,
            SubjectHistory: serviceData.SubjectHistory || false,
            SubjectArts: serviceData.SubjectArts || false,
            SubjectMusic: serviceData.SubjectMusic || false,
            SubjectSports: serviceData.SubjectSports || false,
            SubjectComputer: serviceData.SubjectComputer || false,
            LevelPreschool: serviceData.LevelPreschool || false,
            LevelPrimary: serviceData.LevelPrimary || false,
            LevelSecondary: serviceData.LevelSecondary || false,
            LevelHighSchool: serviceData.LevelHighSchool || false,
            LevelUniversity: serviceData.LevelUniversity || false,
            ExtraOnlineTeaching: serviceData.ExtraOnlineTeaching || false,
            ExtraGroupLessons: serviceData.ExtraGroupLessons || false,
            ExtraHomeworkHelp: serviceData.ExtraHomeworkHelp || false,
            ExtraExamPrep: serviceData.ExtraExamPrep || false,
            ExtraSpecialNeeds: serviceData.ExtraSpecialNeeds || false,
          }
        });
        break;

      case 'Cooking':
        await tx.pendingCookingServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServiceMealPrep: serviceData.ServiceMealPrep || false,
            ServiceCooking: serviceData.ServiceCooking || false,
            ServiceBaking: serviceData.ServiceBaking || false,
            ServiceSpecialDiet: serviceData.ServiceSpecialDiet || false,
            ServiceEventCatering: serviceData.ServiceEventCatering || false,
            ServiceMealPlanning: serviceData.ServiceMealPlanning || false,
            ServiceGroceryShopping: serviceData.ServiceGroceryShopping || false,
            ServiceKitchenCleaning: serviceData.ServiceKitchenCleaning || false,
            CuisineRomanian: serviceData.CuisineRomanian || false,
            CuisineItalian: serviceData.CuisineItalian || false,
            CuisineAsian: serviceData.CuisineAsian || false,
            CuisineMediterranean: serviceData.CuisineMediterranean || false,
            CuisineVegetarian: serviceData.CuisineVegetarian || false,
            CuisineVegan: serviceData.CuisineVegan || false,
            ExtraOwnIngredients: serviceData.ExtraOwnIngredients || false,
            ExtraSpecialDiet: serviceData.ExtraSpecialDiet || false,
            ExtraLargeGroups: serviceData.ExtraLargeGroups || false,
            ExtraWeekendService: serviceData.ExtraWeekendService || false,
          }
        });
        break;

      default:
        console.warn(`Service details creation not implemented for: ${slug}`);
    }
  }

  /**
   * Get pending services for a request
   */
  static async getPendingServices(requestId: string) {
    return await prisma.pendingService.findMany({
      where: { RequestId: requestId },
      include: {
        Category: true,
        NannyServiceDetails: true,
        ElderCareServiceDetails: true,
        CleaningServiceDetails: true,
        TutoringServiceDetails: true,
        CookingServiceDetails: true,
      }
    });
  }

  /**
   * Update service status (for admin actions)
   */
  static async updateServiceStatus(
    serviceId: number,
    status: PendingServiceStatus,
    adminNotes?: string
  ) {
    return await prisma.pendingService.update({
      where: { Id: serviceId },
      data: {
        Status: status,
        AdminNotes: adminNotes,
        UpdatedAt: new Date(),
      }
    });
  }
}
