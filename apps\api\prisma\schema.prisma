// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// --- ENUMS ---
enum UserRole {
  Client
  Provider
  Admin
}

enum ServiceStatus {
  Activ
  Inactiv
  PendingReview
  Rejected
}

enum BookingStatus {
  Pending
  Confirmed
  InProgress
  Cancelled
  Completed
}

enum NotificationType {
  NewBookingRequest
  BookingStatusChanged
  NewMessage
  NewReview
  ProviderRequestApproved
  ProviderRequestRejected
  ServiceStatusChanged
  NewProviderRequest
  ServiceForReview
}

enum ChatMessageStatus {
  Sent
  Delivered
  Read
}

enum ServiceCategorySlug {
  Nanny
  ElderCare
  Cleaning
  Tutoring
  Cooking
}

enum ProviderRegistrationRequestStatus{
	Pending
	Approved
	Rejected
}

enum PendingServiceStatus {
  PendingReview
  Approved
  Rejected
  RequiresChanges
}

// --- MODELS ---

model User {
  Id                 Int                           @id @default(autoincrement())
  FullName           String?
  Email              String                        @unique
  EmailVerified      DateTime?
  Password           String?
  Image              String? // For NextAuth compatibility
  AvatarUrl          String?
  Bio                String?
  Phone              String?
  MustChangePassword Boolean                       @default(false)
  SpokenLanguages    String[]                      @default(["ro"])
  CreatedAt          DateTime                      @default(now())
  UpdatedAt          DateTime                      @updatedAt

  // Explicit many-to-many relationship with Role
  UserRoles UserRoleJunction[]

  // For NextAuth compatibility
  Accounts Account[]

  // Relations for User as a Client
  BookingsAsClient       Booking[]                     @relation("ClientBookings")
  ReviewsAsClient        Review[]                      @relation("ClientReviews")
  ClientChatRooms        ChatRoom[]                    @relation("ClientChatRooms")

  // Relations for User as a Provider
  AdvertisedServices     AdvertisedService[]           @relation("ProviderServices")
  BookingsAsProvider     Booking[]                     @relation("ProviderBookings")
  ReviewsAsProvider      Review[]                      @relation("ProviderReviews")
  ProviderChatRooms      ChatRoom[]                    @relation("ProviderChatRooms")

  // Generic User Relations
  Notifications                Notification[]
  SentMessages                 ChatMessage[]                 @relation("SentMessages")
  ReceivedMessages             ChatMessage[]                 @relation("ReceivedMessages")
  ProviderRegistrationRequest ProviderRegistrationRequest?
  Addresses Address[]

  @@map("Users")
}

model Role {
  Id    Int      @id @default(autoincrement())
  Name  UserRole @unique

  // Explicit many-to-many relationship with User
  UserRoleJunctions UserRoleJunction[]

  @@map("Roles")
}

// Explicit junction table for User-Role relationship
model UserRoleJunction {
  Id     Int @id @default(autoincrement())
  UserId Int
  RoleId Int

  User User @relation(fields: [UserId], references: [Id], onDelete: Cascade)
  Role Role @relation(fields: [RoleId], references: [Id], onDelete: Cascade)

  @@unique([UserId, RoleId])
  @@map("UserRoles")
}

// For NextAuth compatibility
model Account {
  Id                Int       @id @default(autoincrement())
  UserId            Int
  Type              String
  Provider          String
  ProviderAccountId String
  RefreshToken      String?   @db.Text
  AccessToken       String?   @db.Text
  ExpiresAt         Int?
  TokenType         String?
  Scope             String?
  IdToken           String?   @db.Text
  SessionState      String?

  User User @relation(fields: [UserId], references: [Id], onDelete: Cascade)

  @@unique([Provider, ProviderAccountId])
}

model ServiceCategory {
  Id        Int                 @id @default(autoincrement())
  NameKey   String              @unique // e.g., "category_nanny", for i18n
  Slug      ServiceCategorySlug @unique
  AiExpectedValue String        @unique // e.g., "Nanny", for AI matching
  DefaultImageHint String?      // e.g., "child playing"

  AdvertisedServices AdvertisedService[]
  PendingServices    PendingService[]
}

model AdvertisedService {
  Id                  Int                 @id @default(autoincrement())
  ProviderId          Int
  CategoryId          Int
  ServiceCategorySlug ServiceCategorySlug
  ServiceName         String
  Description         String
  Status              ServiceStatus       @default(PendingReview)
  CreatedAt           DateTime            @default(now())
  UpdatedAt           DateTime            @updatedAt

  Provider   User           @relation("ProviderServices", fields: [ProviderId], references: [Id], onDelete: Cascade)
  Category   ServiceCategory @relation(fields: [CategoryId], references: [Id])
  Bookings   Booking[]
  ChatRooms  ChatRoom[]
  
  NannyServiceDetails NannyServiceDetails?
  ElderCareServiceDetails ElderCareServiceDetails?
  CleaningServiceDetails CleaningServiceDetails?
  TutoringServiceDetails TutoringServiceDetails?
  CookingServiceDetails CookingServiceDetails?
}

model Booking {
  Id                  Int           @id @default(autoincrement())
  ClientId            Int
  ProviderId          Int
  AdvertisedServiceId Int
  EventStartDateTime  DateTime?
  EventEndDateTime    DateTime?
  Status              BookingStatus
  ClientNotes         String?
  ProviderNotes       String?
  CreatedAt           DateTime      @default(now())
  UpdatedAt           DateTime      @updatedAt

  Client            User              @relation("ClientBookings", fields: [ClientId], references: [Id])
  Provider          User              @relation("ProviderBookings", fields: [ProviderId], references: [Id])
  AdvertisedService AdvertisedService @relation(fields: [AdvertisedServiceId], references: [Id])
  Review            Review?
}

model Review {
  Id           Int      @id @default(autoincrement())
  BookingId    Int      @unique
  ClientId     Int
  ProviderId   Int
  Rating       Int
  Comment      String?
  CreatedAt    DateTime @default(now())

  Booking  Booking @relation(fields: [BookingId], references: [Id], onDelete: Cascade)
  Client   User    @relation("ClientReviews", fields: [ClientId], references: [Id])
  Provider User    @relation("ProviderReviews", fields: [ProviderId], references: [Id])
}

model Notification {
  Id        Int              @id @default(autoincrement())
  UserId    Int
  Message   String
  Link      String?
  IsRead    Boolean          @default(false)
  CreatedAt DateTime         @default(now())
  Type      NotificationType

  User User @relation(fields: [UserId], references: [Id], onDelete: Cascade)
}

model ChatRoom {
  Id                  String   @id @default(cuid())
  ClientId            Int
  ProviderId          Int
  AdvertisedServiceId Int
  CreatedAt           DateTime @default(now())
  UpdatedAt           DateTime @updatedAt
  
  Client            User              @relation("ClientChatRooms", fields: [ClientId], references: [Id])
  Provider          User              @relation("ProviderChatRooms", fields: [ProviderId], references: [Id])
  AdvertisedService AdvertisedService @relation(fields: [AdvertisedServiceId], references: [Id])
  Messages          ChatMessage[]
}

model ChatMessage {
  Id          String            @id @default(cuid())
  ChatRoomId  String
  SenderId    Int
  RecipientId Int
  Content     String            @db.Text
  CreatedAt   DateTime          @default(now())
  Status      ChatMessageStatus @default(Sent)
  
  ChatRoom ChatRoom @relation(fields: [ChatRoomId], references: [Id], onDelete: Cascade)
  Sender   User     @relation("SentMessages", fields: [SenderId], references: [Id])
  Recipient User    @relation("ReceivedMessages", fields: [RecipientId], references: [Id])
}

model ProviderRegistrationRequest {
  Id              String                            @id @default(cuid())
  UserId          Int                               @unique
  UserName        String
  UserEmail       String
  RequestDate     DateTime                          @default(now())
  Status          ProviderRegistrationRequestStatus @default(Pending)
  AdminNotes      String?

  // Replace RequestedServices JSON with proper relations
  PendingServices PendingService[]
  User            User @relation(fields: [UserId], references: [Id], onDelete: Cascade)
}

model PendingService {
  Id                  Int                  @id @default(autoincrement())
  RequestId           String
  CategoryId          Int
  ServiceCategorySlug ServiceCategorySlug
  ServiceName         String
  Description         String
  ExperienceYears     Int
  Status              PendingServiceStatus @default(PendingReview)
  AdminNotes          String?
  CreatedAt           DateTime             @default(now())
  UpdatedAt           DateTime             @updatedAt

  // File storage (replace JSON filename arrays)
  DocumentPaths       String[]             // Actual file paths

  Request             ProviderRegistrationRequest @relation(fields: [RequestId], references: [Id], onDelete: Cascade)
  Category            ServiceCategory @relation(fields: [CategoryId], references: [Id])

  // Service-specific details (replace JSON with proper relations)
  NannyServiceDetails     PendingNannyServiceDetails?
  ElderCareServiceDetails PendingElderCareServiceDetails?
  CleaningServiceDetails  PendingCleaningServiceDetails?
  TutoringServiceDetails  PendingTutoringServiceDetails?
  CookingServiceDetails   PendingCookingServiceDetails?
}

model PendingNannyServiceDetails {
  Id                      Int    @id @default(autoincrement())
  PendingServiceId        Int    @unique
  LocationId              Int?
  PricePerHour            String?
  PricePerDay             String?
  AvailabilityWeekdays    Boolean? @default(false)
  AvailabilityWeekends    Boolean? @default(false)
  AvailabilityEvenings    Boolean? @default(false)
  PreferredAge_0_2        Boolean? @default(false)
  PreferredAge_3_6        Boolean? @default(false)
  PreferredAge_7_plus     Boolean? @default(false)
  AvailabilityFullTime    Boolean? @default(false)
  AvailabilityPartTime    Boolean? @default(false)
  AvailabilityOccasional  Boolean? @default(false)
  ServiceBabysitting      Boolean? @default(false)
  ServicePlaytime         Boolean? @default(false)
  ServiceMeals            Boolean? @default(false)
  ServiceBedtime          Boolean? @default(false)
  ServiceEducational      Boolean? @default(false)
  ServiceOutdoor          Boolean? @default(false)
  ServiceTransport        Boolean? @default(false)
  ServiceHousework        Boolean? @default(false)
  ExtraFirstAid           Boolean? @default(false)
  ExtraOwnTransport       Boolean? @default(false)
  ExtraCooking            Boolean? @default(false)
  ExtraLanguages          String?
  ExtraSpecialNeeds       Boolean? @default(false)
  ExtraOvernightCare      Boolean? @default(false)

  PendingService PendingService @relation(fields: [PendingServiceId], references: [Id], onDelete: Cascade)
  Location       Location?      @relation("PendingNannyLocation", fields: [LocationId], references: [Id])
}

model PendingElderCareServiceDetails {
  Id                      Int    @id @default(autoincrement())
  PendingServiceId        Int    @unique
  LocationId              Int?
  PricePerHour            String?
  PricePerDay             String?
  AvailabilityWeekdays    Boolean? @default(false)
  AvailabilityWeekends    Boolean? @default(false)
  AvailabilityEvenings    Boolean? @default(false)
  AvailabilityFullTime    Boolean? @default(false)
  AvailabilityPartTime    Boolean? @default(false)
  AvailabilityOccasional  Boolean? @default(false)
  ServicePersonalCare     Boolean? @default(false)
  ServiceMedicalSupport   Boolean? @default(false)
  ServiceCompanionship    Boolean? @default(false)
  ServiceHousekeeping     Boolean? @default(false)
  ServiceMeals            Boolean? @default(false)
  ServiceTransport        Boolean? @default(false)
  ServiceShopping         Boolean? @default(false)
  ServiceMobility         Boolean? @default(false)
  ExtraFirstAid           Boolean? @default(false)
  ExtraMedicalTraining    Boolean? @default(false)
  ExtraOwnTransport       Boolean? @default(false)
  ExtraLanguages          String?
  ExtraSpecialNeeds       Boolean? @default(false)
  ExtraOvernightCare      Boolean? @default(false)

  PendingService PendingService @relation(fields: [PendingServiceId], references: [Id], onDelete: Cascade)
  Location       Location?      @relation("PendingElderCareLocation", fields: [LocationId], references: [Id])
}

model PendingCleaningServiceDetails {
  Id                      Int    @id @default(autoincrement())
  PendingServiceId        Int    @unique
  LocationId              Int?
  PricePerHour            String?
  PricePerDay             String?
  AvailabilityWeekdays    Boolean? @default(false)
  AvailabilityWeekends    Boolean? @default(false)
  AvailabilityEvenings    Boolean? @default(false)
  AvailabilityFullTime    Boolean? @default(false)
  AvailabilityPartTime    Boolean? @default(false)
  AvailabilityOccasional  Boolean? @default(false)
  ServiceRegularCleaning  Boolean? @default(false)
  ServiceDeepCleaning     Boolean? @default(false)
  ServiceWindowCleaning   Boolean? @default(false)
  ServiceCarpetCleaning   Boolean? @default(false)
  ServiceLaundry          Boolean? @default(false)
  ServiceIroning          Boolean? @default(false)
  ServiceOrganizing       Boolean? @default(false)
  ServicePostConstruction Boolean? @default(false)
  ExtraOwnSupplies        Boolean? @default(false)
  ExtraEcoFriendly        Boolean? @default(false)
  ExtraOwnTransport       Boolean? @default(false)
  ExtraInsured            Boolean? @default(false)
  ExtraWeekendAvailable   Boolean? @default(false)
  ExtraEmergencyService   Boolean? @default(false)

  PendingService PendingService @relation(fields: [PendingServiceId], references: [Id], onDelete: Cascade)
  Location       Location?      @relation("PendingCleaningLocation", fields: [LocationId], references: [Id])
}

model PendingTutoringServiceDetails {
  Id                      Int    @id @default(autoincrement())
  PendingServiceId        Int    @unique
  LocationId              Int?
  PricePerHour            String?
  PricePerDay             String?
  AvailabilityWeekdays    Boolean? @default(false)
  AvailabilityWeekends    Boolean? @default(false)
  AvailabilityEvenings    Boolean? @default(false)
  ServiceAfterSchool      Boolean? @default(false)
  ServiceHomeworkHelp     Boolean? @default(false)
  ServiceIndividualLessons Boolean? @default(false)
  Grades_1_4              Boolean? @default(false)
  Grades_5_8              Boolean? @default(false)
  Grades_9_12             Boolean? @default(false)
  SubjectRomanian         Boolean? @default(false)
  SubjectMath             Boolean? @default(false)
  SubjectEnglish          Boolean? @default(false)
  SubjectOther            String?
  FormatOnline            Boolean? @default(false)
  FormatOwnHome           Boolean? @default(false)
  FormatChildHome         Boolean? @default(false)
  ExtraGames              Boolean? @default(false)
  ExtraSnack              Boolean? @default(false)
  ExtraTransport          Boolean? @default(false)
  ExtraSupervisedHomework Boolean? @default(false)

  PendingService PendingService @relation(fields: [PendingServiceId], references: [Id], onDelete: Cascade)
  Location       Location?      @relation("PendingTutoringLocation", fields: [LocationId], references: [Id])
}

model PendingCookingServiceDetails {
  Id                      Int    @id @default(autoincrement())
  PendingServiceId        Int    @unique
  LocationId              Int?
  PricePerHour            String?
  PricePerDay             String?
  PricePerMeal            String?
  MinPortions             String?
  PriceSubscriptionAmount String?
  PriceSubscriptionUnit   String?
  PriceSubscriptionText   String?
  SubscriptionDetails     String?
  AvailabilityWeekdays    Boolean? @default(false)
  AvailabilityWeekends    Boolean? @default(false)
  AvailabilityEvenings    Boolean? @default(false)
  ServiceMealPrep         Boolean? @default(false)
  ServiceCatering         Boolean? @default(false)
  ServiceSpecialDiet      Boolean? @default(false)
  ServiceBaking           Boolean? @default(false)
  ServiceGroceryShopping  Boolean? @default(false)
  ServiceKitchenCleanup   Boolean? @default(false)
  CuisineRomanian         Boolean? @default(false)
  CuisineItalian          Boolean? @default(false)
  CuisineFrench           Boolean? @default(false)
  CuisineAsian            Boolean? @default(false)
  CuisineVegetarian       Boolean? @default(false)
  CuisineVegan            Boolean? @default(false)
  CuisineOther            String?
  ExtraOwnIngredients     Boolean? @default(false)
  ExtraOwnTransport       Boolean? @default(false)
  ExtraWeekendAvailable   Boolean? @default(false)

  PendingService PendingService @relation(fields: [PendingServiceId], references: [Id], onDelete: Cascade)
  Location       Location?      @relation("PendingCookingLocation", fields: [LocationId], references: [Id])
}

model Address {
  Id Int @id @default(autoincrement())
  UserId Int
  Label String
  Street String

  // Enhanced location architecture with foreign key relationships
  CountryId       Int?     // Reference to Country location
  RegionId        Int?     // Reference to Region/Municipality location
  CityId          Int?     // Reference to City location
  SectorId        Int?     // Reference to Sector/Suburb location (most specific)

  // Backward compatibility fields (deprecated, will be removed in future)
  City String?
  Region String?
  Country String? @default("Moldova")

  PostalCode String?
  IsDefault Boolean @default(false)
  CreatedAt DateTime @default(now())
  UpdatedAt DateTime @updatedAt

  // Relationships
  User User @relation(fields: [UserId], references: [Id], onDelete: Cascade)
  CountryLocation Location? @relation("AddressCountry", fields: [CountryId], references: [Id])
  RegionLocation  Location? @relation("AddressRegion", fields: [RegionId], references: [Id])
  CityLocation    Location? @relation("AddressCity", fields: [CityId], references: [Id])
  SectorLocation  Location? @relation("AddressSector", fields: [SectorId], references: [Id])
}

// --- SERVICE DETAIL MODELS ---

model NannyServiceDetails {
  Id Int @id @default(autoincrement())
  AdvertisedServiceId Int @unique
  AdvertisedService AdvertisedService @relation(fields: [AdvertisedServiceId], references: [Id], onDelete: Cascade)

  // Common Details
  ExperienceYears Int?
  Description String? @db.Text
  AvailabilityWeekdays Boolean? @default(false)
  AvailabilityWeekends Boolean? @default(false)
  AvailabilityEvenings Boolean? @default(false)
  LocationId Int?
  PricePerHour Decimal? @db.Decimal(10, 2)
  PricePerDay Decimal? @db.Decimal(10, 2)
  PriceSubscriptionAmount Decimal? @db.Decimal(10, 2)
  PriceSubscriptionUnit String?
  PriceSubscriptionText String?
  SubscriptionDetails String?

  // Specific Nanny Details
  PreferredAge_0_2 Boolean? @default(false)
  PreferredAge_3_6 Boolean? @default(false)
  PreferredAge_7_plus Boolean? @default(false)
  AvailabilityFullTime Boolean? @default(false)
  AvailabilityPartTime Boolean? @default(false)
  FirstAid Boolean? @default(false)
  SchoolPickup Boolean? @default(false)
  ActivityWalks Boolean? @default(false)
  ActivityGames Boolean? @default(false)
  ActivityFeeding Boolean? @default(false)
  ActivitySleep Boolean? @default(false)

  // Documents (as file names)
  DocBuletinFileName String?
  DocDiplomeFileNames String[]
  DocRecomandariFileNames String[]

  Location Location? @relation("NannyLocation", fields: [LocationId], references: [Id])

  @@map("NannyServiceDetails")
}

model ElderCareServiceDetails {
  Id Int @id @default(autoincrement())
  AdvertisedServiceId Int @unique
  AdvertisedService AdvertisedService @relation(fields: [AdvertisedServiceId], references: [Id], onDelete: Cascade)

  ExperienceYears Int?
  Description String? @db.Text
  AvailabilityWeekdays Boolean? @default(false)
  AvailabilityWeekends Boolean? @default(false)
  AvailabilityEvenings Boolean? @default(false)
  LocationId Int?
  PricePerHour Decimal? @db.Decimal(10, 2)
  PricePerDay Decimal? @db.Decimal(10, 2)
  PriceSubscriptionAmount Decimal? @db.Decimal(10, 2)
  PriceSubscriptionUnit String?
  PriceSubscriptionText String?
  SubscriptionDetails String?

  TypeMobil Boolean? @default(false)
  TypePartialImobilizat Boolean? @default(false)
  TypeCompletImobilizat Boolean? @default(false)
  MedicalKnowledgeBasic Boolean? @default(false)
  MedicalKnowledgeAdvanced Boolean? @default(false)
  MedicationAdmin Boolean? @default(false)
  DrivingLicense Boolean? @default(false)
  ActivityCooking Boolean? @default(false)
  ActivityCleaningLight Boolean? @default(false)
  ActivityCompanionship Boolean? @default(false)

  DocBuletinFileName String?
  DocDiplomeFileNames String[]
  DocRecomandariFileNames String[]

  Location Location? @relation("ElderCareLocation", fields: [LocationId], references: [Id])

  @@map("ElderCareServiceDetails")
}

model CleaningServiceDetails {
  Id Int @id @default(autoincrement())
  AdvertisedServiceId Int @unique
  AdvertisedService AdvertisedService @relation(fields: [AdvertisedServiceId], references: [Id], onDelete: Cascade)

  ExperienceYears Int?
  Description String? @db.Text
  AvailabilityWeekdays Boolean? @default(false)
  AvailabilityWeekends Boolean? @default(false)
  AvailabilityEvenings Boolean? @default(false)
  LocationId Int?
  PricePerHour Decimal? @db.Decimal(10, 2)
  PricePerDay Decimal? @db.Decimal(10, 2)
  PriceSubscriptionAmount Decimal? @db.Decimal(10, 2)
  PriceSubscriptionUnit String?
  PriceSubscriptionText String?
  SubscriptionDetails String?

  PropertyTypeApartments Boolean? @default(false)
  PropertyTypeHouses Boolean? @default(false)
  PropertyTypeOffices Boolean? @default(false)
  OwnProducts Boolean? @default(false)
  TypeGeneral Boolean? @default(false)
  TypePostRenovation Boolean? @default(false)
  TypeOccasional Boolean? @default(false)
  TypeRegular Boolean? @default(false)
  ExtraIroning Boolean? @default(false)
  ExtraWindows Boolean? @default(false)
  ExtraDisinfection Boolean? @default(false)

  DocBuletinFileName String?
  DocDiplomeFileNames String[]
  DocRecomandariFileNames String[]

  Location Location? @relation("CleaningLocation", fields: [LocationId], references: [Id])

  @@map("CleaningServiceDetails")
}

model CookingServiceDetails {
  Id Int @id @default(autoincrement())
  AdvertisedServiceId Int @unique
  AdvertisedService AdvertisedService @relation(fields: [AdvertisedServiceId], references: [Id], onDelete: Cascade)

  ExperienceYears Int?
  Description String? @db.Text
  AvailabilityWeekdays Boolean? @default(false)
  AvailabilityWeekends Boolean? @default(false)
  AvailabilityEvenings Boolean? @default(false)
  LocationId Int?
  PricePerHour Decimal? @db.Decimal(10, 2)
  PricePerDay Decimal? @db.Decimal(10, 2)
  PriceSubscriptionAmount Decimal? @db.Decimal(10, 2)
  PriceSubscriptionUnit String?
  PriceSubscriptionText String?
  SubscriptionDetails String?

  CuisineTypeTraditional Boolean? @default(false)
  CuisineTypeVegetarian Boolean? @default(false)
  CuisineTypeKids Boolean? @default(false)
  CuisineTypeDiet Boolean? @default(false)
  OffersDelivery Boolean? @default(false)
  AtClientHome Boolean? @default(false)
  AtOwnHome Boolean? @default(false)
  cookingOwnProducts Boolean? @default(false)
  MinPortions Int?
  WeeklySubscription Boolean? @default(false)
  PricePerMeal Decimal? @db.Decimal(10, 2)
  MealDetails String?

  DocBuletinFileName String?
  DocDiplomeFileNames String[]
  DocRecomandariFileNames String[]

  Location Location? @relation("CookingLocation", fields: [LocationId], references: [Id])

  @@map("CookingServiceDetails")
}

model TutoringServiceDetails {
  Id Int @id @default(autoincrement())
  AdvertisedServiceId Int @unique
  AdvertisedService AdvertisedService @relation(fields: [AdvertisedServiceId], references: [Id], onDelete: Cascade)

  ExperienceYears Int?
  Description String? @db.Text
  AvailabilityWeekdays Boolean? @default(false)
  AvailabilityWeekends Boolean? @default(false)
  AvailabilityEvenings Boolean? @default(false)
  LocationId Int?
  PricePerHour Decimal? @db.Decimal(10, 2)
  PricePerDay Decimal? @db.Decimal(10, 2)
  PriceSubscriptionAmount Decimal? @db.Decimal(10, 2)
  PriceSubscriptionUnit String?
  PriceSubscriptionText String?
  SubscriptionDetails String?

  ServiceAfterSchool Boolean? @default(false)
  ServiceHomeworkHelp Boolean? @default(false)
  ServiceIndividualLessons Boolean? @default(false)
  Grades_1_4 Boolean? @default(false)
  Grades_5_8 Boolean? @default(false)
  Grades_9_12 Boolean? @default(false)
  SubjectRomanian Boolean? @default(false)
  SubjectMath Boolean? @default(false)
  SubjectEnglish Boolean? @default(false)
  SubjectOther String?
  FormatOnline Boolean? @default(false)
  FormatOwnHome Boolean? @default(false)
  FormatChildHome Boolean? @default(false)
  ExtraGames Boolean? @default(false)
  ExtraSnack Boolean? @default(false)
  ExtraTransport Boolean? @default(false)
  ExtraSupervisedHomework Boolean? @default(false)

  DocBuletinFileName String?
  DocDiplomeFileNames String[]
  DocRecomandariFileNames String[]

  Location Location? @relation("TutoringLocation", fields: [LocationId], references: [Id])

  @@map("TutoringServiceDetails")
}

// --- LOCATION MODELS ---

enum LocationType {
  Country
  Municipality
  City
  Sector
  Suburb
  Town
  Village
}

model Location {
  Id              Int           @id // Explicit ID, no auto-increment
  Slug            String        @unique // URL-friendly identifier, e.g., "chisinau-botanica"
  TranslationKey  String        // For useLanguage hook, e.g., "locChisinauBotanica"
  Name            String        // Display name in native language, e.g., "Chișinău, Botanica"
  Type            LocationType  // Type of location (Country, Municipality, City, etc.)
  ParentId        Int?          // Reference to parent location
  SortOrder       Int           @default(0) // For ordering locations in dropdowns
  IsActive        Boolean       @default(true) // For enabling/disabling locations
  IsCapital       Boolean       @default(false) // Is this a capital city/main location
  SpecialType     String?       // Special designation (e.g., "capital", "major_city", "border_town")
  CreatedAt       DateTime      @default(now())
  UpdatedAt       DateTime      @updatedAt

  // Self-referential relationship for hierarchy
  Parent          Location?     @relation("LocationHierarchy", fields: [ParentId], references: [Id])
  Children        Location[]    @relation("LocationHierarchy")

  // Service detail relations
  NannyServices           NannyServiceDetails[]           @relation("NannyLocation")
  ElderCareServices       ElderCareServiceDetails[]       @relation("ElderCareLocation")
  CleaningServices        CleaningServiceDetails[]        @relation("CleaningLocation")
  TutoringServices        TutoringServiceDetails[]        @relation("TutoringLocation")
  CookingServices         CookingServiceDetails[]         @relation("CookingLocation")

  // Pending service detail relations
  PendingNannyServices    PendingNannyServiceDetails[]    @relation("PendingNannyLocation")
  PendingElderCareServices PendingElderCareServiceDetails[] @relation("PendingElderCareLocation")
  PendingCleaningServices PendingCleaningServiceDetails[] @relation("PendingCleaningLocation")
  PendingTutoringServices PendingTutoringServiceDetails[] @relation("PendingTutoringLocation")
  PendingCookingServices  PendingCookingServiceDetails[]  @relation("PendingCookingLocation")

  // Address relations for enhanced location architecture
  AddressesAsCountry Address[] @relation("AddressCountry")
  AddressesAsRegion  Address[] @relation("AddressRegion")
  AddressesAsCity    Address[] @relation("AddressCity")
  AddressesAsSector  Address[] @relation("AddressSector")

  @@map("Locations")
}

// Placeholder table for a generic items example in the app
model items_table_placeholder {
  id    Int     @id @default(autoincrement())
  name  String?
  value String?
}
