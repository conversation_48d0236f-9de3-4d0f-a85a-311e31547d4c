
"use client";

import { useState, useMemo, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, XCircle, Clock, ChevronLeft, ChevronRight, CalendarIcon, Loader2 } from 'lucide-react';
import { 
  format, 
  addMonths, 
  subMonths, 
  startOfWeek, 
  endOfWeek, 
  eachDayOfInterval, 
  isSameDay, 
  isSameMonth,
  addDays,
  subDays,
  addWeeks,
  subWeeks,
  startOfMonth,
  parseISO
} from 'date-fns';
import { ro, ru, enUS as en } from 'date-fns/locale';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { type BookingStatus } from '@prisma/client'; // Import just the enum
import { useSession } from "next-auth/react"; // Use NextAuth session
import { cn } from '@/lib/utils';

const calendarPageTranslations = {
  pageTitle: { ro: "Calendarul Serviciilor Mele", ru: "Календарь моих услуг", en: "My Services Calendar" },
  pageDescription: { ro: "Gestionează programările și disponibilitatea.", ru: "Управляйте записями и доступностью.", en: "Manage your appointments and availability." },
  tabMonth: { ro: "Lună", ru: "Месяц", en: "Month" },
  tabWeek: { ro: "Săptămână", ru: "Неделя", en: "Week" },
  tabDay: { ro: "Zi", ru: "День", en: "Day" },
  buttonToday: { ro: "Astăzi", ru: "Сегодня", en: "Today" },
  prevPeriod: { ro: "Perioada precedentă", ru: "Предыдущий период", en: "Previous period" },
  nextPeriod: { ro: "Perioada următoare", ru: "Следующий период", en: "Next period" },
  appointmentsFor: { ro: "Programări pentru", ru: "Записи на", en: "Appointments for" },
  selectedDate: { ro: "data selectată", ru: "выбранную дату", en: "selected date" },
  noAppointments: { ro: "Nicio programare pentru această dată.", ru: "Нет записей на эту дату.", en: "No appointments for this date." },
  statusConfirmed: { ro: "Confirmată", ru: "Подтверждена", en: "Confirmed" },
  statusPending: { ro: "În Așteptare", ru: "В ожидании", en: "Pending" },
  statusCompleted: { ro: "Finalizată", ru: "Завершено", en: "Completed" },
  statusCancelled: { ro: "Anulată", ru: "Отменено", en: "Cancelled" },
  freeLabel: { ro: "Liber", ru: "Свободно", en: "Free" },
  clientLabel: { ro: "Client", ru: "Клиент", en: "Client" },
  timeLabel: { ro: "Ora", ru: "Время", en: "Time" },
  dateLabel: { ro: "Data", ru: "Дата", en: "Date" },
  confirmButton: { ro: "Confirmă", ru: "Подтвердить", en: "Confirm" },
  rejectButton: { ro: "Refuză", ru: "Отклонить", en: "Reject" },
  footerNote: { ro: "Notă: Funcționalitatea completă de gestionare a programărilor este în curs de dezvoltare.", ru: "Примечание: Полная функциональность управления записями находится в разработке.", en: "Note: Full appointment management functionality is under development." },
  errorFetchingAppointments: { ro: "Eroare la preluarea programărilor.", ru: "Ошибка при загрузке записей.", en: "Error fetching appointments." },
  errorNoProviderId: { ro: "ID Prestator nu este disponibil. Asigură-te că ești autentificat ca prestator.", ru: "ID Поставщика недоступен. Убедитесь, что вы вошли как поставщик.", en: "Provider ID not available. Ensure you are logged in as a provider." }
};

// Interface that matches the API response
interface AppointmentEvent {
  id: string;
  serviceName: string;
  clientName: string;
  date: Date;
  time: string;
  status: BookingStatus;
}

const dateFnsLocalesMap = {
  ro: ro,
  ru: ru,
  en: en,
};

export default function ServiceCalendarPage() {
  const { translate, currentLanguage } = useLanguage();
  const { data: session, status: sessionStatus } = useSession();
  const selectedDateFnsLocale = dateFnsLocalesMap[currentLanguage.code as keyof typeof dateFnsLocalesMap] || ro;
  
  const [currentViewDate, setCurrentViewDate] = useState(startOfMonth(new Date())); 
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [activeView, setActiveView] = useState<CalendarView>("month");
  const [appointments, setAppointments] = useState<AppointmentEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (sessionStatus === "authenticated" && session?.user) {
      const providerIdFromSession = (session.user as any).id; // Assuming id is string from NextAuth
      if (providerIdFromSession) {
        async function fetchAppointments() {
          setIsLoading(true);
          setError(null);
          try {
            const response = await fetch(`/api/proxy/provider/appointments?providerId=${providerIdFromSession}`);
            if (!response.ok) {
              throw new Error(translate(calendarPageTranslations, 'errorFetchingAppointments'));
            }
            const data = await response.json();
            const formattedAppointments = (data.appointments || []).map((app: any) => ({
              ...app,
              date: parseISO(app.date), 
            }));
            setAppointments(formattedAppointments);
          } catch (err) {
            setError(err instanceof Error ? err.message : translate(calendarPageTranslations, 'errorFetchingAppointments'));
          } finally {
            setIsLoading(false);
          }
        }
        fetchAppointments();
      } else {
        setError(translate(calendarPageTranslations, 'errorNoProviderId'));
        setIsLoading(false);
      }
    } else if (sessionStatus === "loading") {
      setIsLoading(true);
    } else { // unauthenticated
      setError(translate(calendarPageTranslations, 'errorNoProviderId'));
      setIsLoading(false);
    }
  }, [session, sessionStatus, translate]);

  const getStatusBadge = (status: BookingStatus) => {
    switch (status) {
      case "Confirmed":
        return <Badge variant="default" className="text-xs whitespace-nowrap bg-green-500 hover:bg-green-600"><CheckCircle className="w-3 h-3 mr-1"/>{translate(calendarPageTranslations, 'statusConfirmed')}</Badge>;
      case "Pending":
        return <Badge variant="secondary" className="text-xs whitespace-nowrap bg-yellow-400 hover:bg-yellow-500 text-black"><Clock className="w-3 h-3 mr-1"/>{translate(calendarPageTranslations, 'statusPending')}</Badge>;
      case "Cancelled":
        return <Badge variant="destructive" className="text-xs whitespace-nowrap"><XCircle className="w-3 h-3 mr-1"/>{translate(calendarPageTranslations, 'statusCancelled')}</Badge>;
      case "Completed":
        return <Badge variant="default" className="text-xs whitespace-nowrap bg-blue-500 hover:bg-blue-600"><CheckCircle className="w-3 h-3 mr-1"/>{translate(calendarPageTranslations, 'statusCompleted')}</Badge>;
      default:
        return <Badge className="text-xs whitespace-nowrap">{status}</Badge>;
    }
  };
  
  type CalendarView = "month" | "week" | "day";

  const handlePrev = () => {
    if (activeView === "month") setCurrentViewDate(prev => startOfMonth(subMonths(prev, 1)));
    if (activeView === "week") setCurrentViewDate(prev => startOfWeek(subWeeks(prev, 1), { weekStartsOn: 1, locale: selectedDateFnsLocale }));
    if (activeView === "day") setCurrentViewDate(prev => subDays(prev, 1));
  };

  const handleNext = () => {
    if (activeView === "month") setCurrentViewDate(prev => startOfMonth(addMonths(prev, 1)));
    if (activeView === "week") setCurrentViewDate(prev => startOfWeek(addWeeks(prev, 1), { weekStartsOn: 1, locale: selectedDateFnsLocale }));
    if (activeView === "day") setCurrentViewDate(prev => addDays(prev, 1));
  };

  const handleToday = () => {
    const today = new Date();
    setCurrentViewDate(startOfMonth(today));
    setSelectedDate(today);
    if (activeView === "week") setCurrentViewDate(startOfWeek(today, { weekStartsOn: 1, locale: selectedDateFnsLocale }));
    if (activeView === "day") setCurrentViewDate(today);
  };
  
  const weekStartsOn = 1; 

  const daysInWeek = useMemo(() => {
    const start = startOfWeek(currentViewDate, { weekStartsOn, locale: selectedDateFnsLocale });
    const end = endOfWeek(currentViewDate, { weekStartsOn, locale: selectedDateFnsLocale });
    return eachDayOfInterval({ start, end });
  }, [currentViewDate, weekStartsOn, selectedDateFnsLocale]);

  const eventsForDate = (date: Date | undefined): AppointmentEvent[] => {
    if (!date) return [];
    return appointments.filter(req => isSameDay(req.date, date));
  };

  const eventsForDayInWeek = (date: Date): AppointmentEvent[] => {
    return appointments.filter(req => isSameDay(req.date, date));
  };

  const confirmedDates = useMemo(() => 
    appointments
        .filter(app => app.status === 'Confirmed' || app.status === 'Completed')
        .map(app => app.date), 
  [appointments]);

  const pendingDates = useMemo(() => 
    appointments
        .filter(app => app.status === 'Pending')
        .map(app => app.date), 
  [appointments]);
  
  const calendarTitle = useMemo(() => {
    if (activeView === "month") return format(currentViewDate, "LLLL yyyy", { locale: selectedDateFnsLocale });
    if (activeView === "week") {
      const start = startOfWeek(currentViewDate, { weekStartsOn, locale: selectedDateFnsLocale });
      const end = endOfWeek(currentViewDate, { weekStartsOn, locale: selectedDateFnsLocale });
      if (isSameMonth(start, end)) {
        return `${format(start, "d")} - ${format(end, "d MMMM yyyy", { locale: selectedDateFnsLocale })}`;
      }
      return `${format(start, "d MMMM", { locale: selectedDateFnsLocale })} - ${format(end, "d MMMM yyyy", { locale: selectedDateFnsLocale })}`;
    }
    if (activeView === "day") return format(currentViewDate, "eeee, d MMMM yyyy", { locale: selectedDateFnsLocale });
    return "";
  }, [currentViewDate, activeView, weekStartsOn, selectedDateFnsLocale]);

  const handleDateSelectForMonthView = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date && !isSameMonth(date, currentViewDate)) {
      setCurrentViewDate(startOfMonth(date));
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-10 text-center text-destructive">
          <p>{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="month" value={activeView} onValueChange={(value) => setActiveView(value as CalendarView)}>
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <CardTitle className="font-headline text-xl">{translate(calendarPageTranslations, 'pageTitle')}</CardTitle>
                <CardDescription>{translate(calendarPageTranslations, 'pageDescription')}</CardDescription>
              </div>
              <TabsList className="grid w-full grid-cols-3 sm:w-auto">
                <TabsTrigger value="month">{translate(calendarPageTranslations, 'tabMonth')}</TabsTrigger>
                <TabsTrigger value="week">{translate(calendarPageTranslations, 'tabWeek')}</TabsTrigger>
                <TabsTrigger value="day">{translate(calendarPageTranslations, 'tabDay')}</TabsTrigger>
              </TabsList>
            </div>
            <div className="mt-4 flex items-center justify-between border-t pt-4">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={handlePrev} aria-label={translate(calendarPageTranslations, 'prevPeriod')}>
                  <ChevronLeft className="h-5 w-5" />
                </Button>
                <Button variant="outline" size="icon" onClick={handleNext} aria-label={translate(calendarPageTranslations, 'nextPeriod')}>
                  <ChevronRight className="h-5 w-5" />
                </Button>
                <Button variant="outline" onClick={handleToday} className="hidden sm:flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4"/> {translate(calendarPageTranslations, 'buttonToday')}
                </Button>
              </div>
              <h2 className="text-lg font-semibold text-center capitalize">{calendarTitle}</h2>
              <Button variant="outline" onClick={handleToday} className="sm:hidden">{translate(calendarPageTranslations, 'buttonToday')}</Button>
            </div>
          </CardHeader>
          
          <TabsContent value="month">
            <CardContent className="flex flex-col lg:flex-row gap-6">
              <div className="p-1 border rounded-md flex justify-center lg:justify-start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={handleDateSelectForMonthView}
                  month={currentViewDate}
                  onMonthChange={(month) => setCurrentViewDate(startOfMonth(month))}
                  className="rounded-md"
                  locale={selectedDateFnsLocale}
                  weekStartsOn={weekStartsOn}
                  modifiers={{ 
                    pending: pendingDates,
                    confirmed: confirmedDates,
                  }}
                  modifiersClassNames={{
                    pending: 'day-event day-pending',
                    confirmed: 'day-event day-confirmed',
                  }}
                  showOutsideDays={true}
                />
              </div>
              <div className="flex-1 lg:min-w-[300px]">
                <h3 className="text-lg font-semibold mb-3 capitalize">
                  {translate(calendarPageTranslations, 'appointmentsFor')} {selectedDate ? format(selectedDate, "eeee, d MMMM yyyy", { locale: selectedDateFnsLocale }) : translate(calendarPageTranslations, 'selectedDate')}
                </h3>
                {eventsForDate(selectedDate).length > 0 ? (
                    <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
                        {eventsForDate(selectedDate).map(req => (
                            <EventCard key={req.id} event={req} getStatusBadge={getStatusBadge} translate={translate} locale={selectedDateFnsLocale} />
                        ))}
                    </div>
                ): (
                    <p className="text-sm text-muted-foreground">{translate(calendarPageTranslations, 'noAppointments')}</p>
                )}
              </div>
            </CardContent>
          </TabsContent>

          <TabsContent value="week">
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-7 gap-px bg-border border rounded-md overflow-hidden">
                {daysInWeek.map(day => (
                  <div key={day.toString()} className="bg-card p-3 min-h-[150px]">
                    <p className={`text-sm font-medium text-center mb-2 ${isSameDay(day, new Date()) ? 'text-primary font-bold' : 'text-muted-foreground'}`}>
                      {format(day, "E d", { locale: selectedDateFnsLocale })}
                    </p>
                    <div className="space-y-2">
                      {eventsForDayInWeek(day).length > 0 ? (
                        eventsForDayInWeek(day).map(req => (
                          <EventCardSmall key={req.id} event={req} getStatusBadge={getStatusBadge} />
                        ))
                      ) : (
                        <p className="text-xs text-muted-foreground text-center py-2">{translate(calendarPageTranslations, 'freeLabel')}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </TabsContent>

          <TabsContent value="day">
              <CardContent>
                  <h3 className="text-lg font-semibold mb-4 capitalize">
                    {translate(calendarPageTranslations, 'appointmentsFor')} {format(currentViewDate, "eeee, d MMMM yyyy", { locale: selectedDateFnsLocale })}
                  </h3>
                  {eventsForDate(currentViewDate).length > 0 ? (
                      <div className="space-y-3">
                          {eventsForDate(currentViewDate).map(req => (
                              <EventCard key={req.id} event={req} detailed getStatusBadge={getStatusBadge} translate={translate} locale={selectedDateFnsLocale} />
                          ))}
                      </div>
                  ): (
                      <p className="text-sm text-muted-foreground">{translate(calendarPageTranslations, 'noAppointments')}</p>
                  )}
              </CardContent>
          </TabsContent>

          <CardFooter>
              <p className="text-xs text-muted-foreground">{translate(calendarPageTranslations, 'footerNote')}</p>
         </CardFooter>
        </Card>
      </Tabs>
    </div>
  );
}

function EventCard({ event, detailed = false, getStatusBadge, translate, locale }: { event: AppointmentEvent, detailed?: boolean, getStatusBadge: Function, translate: Function, locale: Locale }) {
  return (
    <Card className="p-3 bg-muted/40 shadow-sm">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
        <div>
          <p className="font-medium text-sm sm:text-base">{event.serviceName}</p>
          <p className="text-xs text-muted-foreground">{translate(calendarPageTranslations, 'clientLabel')}: {event.clientName}</p>
          <p className="text-xs text-muted-foreground">{translate(calendarPageTranslations, 'timeLabel')}: {event.time}</p>
          {detailed && <p className="text-xs text-muted-foreground">{translate(calendarPageTranslations, 'dateLabel')}: {format(event.date, "d MMM yyyy", { locale })}</p>}
        </div>
        <div className="mt-2 sm:mt-0 self-start sm:self-center">
          {getStatusBadge(event.status)}
        </div>
      </div>
      {event.status === "Pending" && (
        <div className="mt-2 pt-2 border-t border-border flex space-x-2">
            <Button size="xs" variant="outline" className="text-xs border-green-600 text-green-700 hover:bg-green-50 hover:text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              {translate(calendarPageTranslations, 'confirmButton')}
            </Button>
            <Button size="xs" variant="outline" className="text-xs border-red-600 text-red-700 hover:bg-red-50 hover:text-red-800">
               <XCircle className="w-3 h-3 mr-1" />
              {translate(calendarPageTranslations, 'rejectButton')}
            </Button>
        </div>
      )}
    </Card>
  );
}

function EventCardSmall({ event, getStatusBadge }: { event: AppointmentEvent, getStatusBadge: Function }) {
    const statusColorClass = {
        Pending: "border-yellow-500",
        Confirmed: "border-green-500",
        Completed: "border-blue-500",
        Cancelled: "border-red-500",
    }[event.status] || "border-gray-400";

  return (
    <div className={cn("p-2 bg-card rounded-md text-xs shadow-sm", statusColorClass, "border-l-4")}>
      <p className="font-semibold truncate text-foreground">{event.serviceName}</p>
      <p className="text-muted-foreground truncate">{event.clientName}</p>
      <p className="text-muted-foreground">{event.time}</p>
      <div className="mt-1">
        {getStatusBadge(event.status)}
      </div>
    </div>
  );
}
