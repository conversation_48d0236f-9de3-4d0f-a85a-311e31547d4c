"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import type { TutoringServiceDetails } from "@prisma/client";
import { useLanguage } from "@/contexts/language-context";
import { commonTranslations } from "@repo/translations";
import { BooleanDetailItem } from "../DetailItem";

interface TutoringDetailsProps {
    details: TutoringServiceDetails | null;
}

export function TutoringDetailsView({ details }: TutoringDetailsProps) {
    const { translate } = useLanguage();
    if (!details) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">
                    {translate(commonTranslations, 'tutoringTitle')}
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <BooleanDetailItem label={translate(commonTranslations, 'tutoringGrades_1_4')} value={details.Grades_1_4} />
                    <BooleanDetailItem label={translate(commonTranslations, 'tutoringGrades_5_8')} value={details.Grades_5_8} />
                    <BooleanDetailItem label={translate(commonTranslations, 'tutoringGrades_9_12')} value={details.Grades_9_12} />
                </div>
            </CardContent>
        </Card>
    );
}
