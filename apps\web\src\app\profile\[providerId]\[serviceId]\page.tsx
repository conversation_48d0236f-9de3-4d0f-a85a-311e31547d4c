
"use client";

import React, { useEffect, useState, useMemo } from 'react';
import Image from "next/image";
import Link from "next/link";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Star, MapPin, Phone, MessageSquare, CalendarDays, Loader2, Info, AlertTriangle, CheckCircle, XCircle, Download, FileText, ArrowLeft, PlusCircle } from "lucide-react";
import { ServiceCategorySlug, type AdvertisedService as PrismaAdvertisedService, type NannyServiceDetails, type ElderCareServiceDetails, type CleaningServiceDetails, type TutoringServiceDetails, type CookingServiceDetails, type Prisma, type User as PrismaUser } from '@prisma/client';
import { allMoldovaLocations, getFullLocationPathDisplayHelper } from "@/lib/locations";
import { commonTranslations } from "@repo/translations";
import { useLanguage } from '@/contexts/language-context';
import { BookingAvailabilityModal } from '@/components/profile/availability-modal';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useRouter, useParams } from 'next/navigation';
import { useSession } from "next-auth/react";
import { UserSilhouetteIcon } from '@/components/ui/user-silhouette-icon';

// Interface for the data we expect from the API
interface ProviderInfo {
  id: number;
  fullName: string | null;
  email: string;
  avatarUrl: string | null;
  bio: string | null;
  phone: string | null;
}

interface ServiceDetails {
  locationValue?: string | null;
  experienceYears?: number | null;
  availabilityWeekdays?: boolean | null;
  availabilityWeekends?: boolean | null;
  availabilityEvenings?: boolean | null;
  pricePerHour?: string | null;
  pricePerDay?: string | null;
  priceSubscriptionAmount?: string | null;
  priceSubscriptionUnit?: string | null;
  priceSubscriptionText?: string | null;
  subscriptionDetails?: string | null;
  docBuletinFileName?: string | null;
  docDiplomeFileNames?: string[] | null;
  docRecomandariFileNames?: string[] | null;
  [key: string]: any; 
}

interface AdvertisedServiceWithDetails {
  id: number;
  providerId: number;
  categoryId: number;
  serviceCategorySlug: string;
  serviceName: string;
  description: string;
  status: string;
  category?: { nameKey: string, slug: string } | null;
  nannyServiceDetails?: ServiceDetails | null;
  elderCareServiceDetails?: ServiceDetails | null;
  cleaningServiceDetails?: ServiceDetails | null;
  tutoringServiceDetails?: ServiceDetails | null;
  cookingServiceDetails?: ServiceDetails | null;
}

interface ProviderProfileDataForPage {
  providerInfo: ProviderInfo;
  services: AdvertisedServiceWithDetails[];
  averageRating: number;
  reviewsCount: number;
}


async function getProviderProfileData(id: string): Promise<ProviderProfileDataForPage | null> {
  try {
    const response = await fetch(`/api/proxy/services/${id}`);
    if (!response.ok) {
      if (response.status === 404) {
        console.warn(`[getProviderProfileData] Provider with ID ${id} not found (404).`);
        return null;
      }
      const errorData = await response.json().catch(() => ({ message: `Network response was not ok. Status: ${response.status}` }));
      throw new Error(errorData.message || 'Failed to fetch profile details');
    }
    return await response.json();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error fetching profile data';
    console.error(`[getProviderProfileData] Error fetching profile data for ID ${id}:`, error);
    throw new Error(`Server error: ${errorMessage}`);
  }
}

// Styled Helper Components
const DetailItem = ({ labelKey, value }: { labelKey: keyof typeof commonTranslations, value: string | React.ReactNode | null | undefined }) => {
  const { translate } = useLanguage();
  if (value === null || value === undefined || (typeof value === 'string' && !value.trim())) return null;
  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-1 py-2 border-b last:border-b-0">
      <dt className="font-medium text-muted-foreground">{translate(commonTranslations, labelKey)}</dt>
      <dd className="sm:col-span-2 text-foreground">{value}</dd>
    </div>
  );
};

const BooleanDetailItem = ({ labelKey, value }: { labelKey: keyof typeof commonTranslations, value: boolean | undefined | null }) => {
  const { translate } = useLanguage();
  if (value === undefined || value === null) return null;
  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-1 py-2 border-b last:border-b-0 items-center">
      <dt className="font-medium text-muted-foreground">{translate(commonTranslations, labelKey)}</dt>
      <dd className="sm:col-span-2 text-foreground flex items-center gap-2">
        {value ? <CheckCircle className="w-5 h-5 text-green-600" /> : <XCircle className="w-5 h-5 text-red-500" />}
        <span className={value ? 'text-green-700 font-medium' : 'text-red-600 font-medium'}>
          {value ? translate(commonTranslations, 'optionYes') : translate(commonTranslations, 'optionNo')}
        </span>
      </dd>
    </div>
  );
};

const MultiSelectDetailItem = ({ mainLabelKey, options, translateFn }: { mainLabelKey: keyof typeof commonTranslations, options: {flag?: boolean | null, labelKey: keyof typeof commonTranslations}[], translateFn: Function}) => {
    const selectedOptions = options.filter(opt => opt.flag).map(opt => translateFn(commonTranslations, opt.labelKey));
    if (selectedOptions.length === 0) return null;
    return (
       <div className="grid grid-cols-1 sm:grid-cols-3 gap-1 py-2 border-b last:border-b-0">
          <dt className="font-medium text-muted-foreground">{translateFn(commonTranslations, mainLabelKey)}</dt>
          <dd className="sm:col-span-2 text-foreground flex flex-wrap gap-2">
            {selectedOptions.map((opt, index) => <Badge key={index} variant="secondary">{opt}</Badge>)}
          </dd>
      </div>
    );
};


const DocumentItem = ({
  labelKey,
  fileNames,
  translate,
  toast,
}: {
  labelKey: keyof typeof commonTranslations;
  fileNames?: string | string[] | null; 
  translate: Function;
  toast: Function;
}) => {
  const displayFileNamesArray = Array.isArray(fileNames) ? fileNames : (fileNames ? [fileNames] : []);

  const handleDocumentClick = (fileName: string) => {
    toast({
      title: translate(commonTranslations, 'toastSuccessTitle'),
      description: translate(commonTranslations, 'documentDownloadStarted').replace('{fileName}', fileName),
    });
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-1 py-2 border-b last:border-b-0">
      <dt className="font-medium text-muted-foreground">{translate(commonTranslations, labelKey)}</dt>
      <dd className="sm:col-span-2 text-foreground">
        {displayFileNamesArray.length > 0 ? (
          displayFileNamesArray.map((fileName, index) => (
            <Button
              key={index}
              variant="link"
              size="sm"
              className="p-0 h-auto text-primary hover:underline flex items-center gap-1.5 mr-2 text-left break-all"
              onClick={() => handleDocumentClick(fileName)}
              title={translate(commonTranslations, 'downloadFileHint')}
            >
              <FileText className="w-4 h-4 shrink-0" />
              {fileName}
            </Button>
          ))
        ) : (
          <div className="flex items-center gap-2">
            <XCircle className="w-5 h-5 text-muted-foreground" />
            <span className="text-muted-foreground">{translate(commonTranslations, 'documentNotAttached')}</span>
          </div>
        )}
      </dd>
    </div>
  );
};


export default function ProfilePage() {
  const params = useParams();
  const providerId = params.providerId as string;
  const serviceIdFromParam = params.serviceId as string;
  const router = useRouter();

  const { translate } = useLanguage();
  const { toast } = useToast();
  const { data: session, status: sessionStatus } = useSession();

  const [profileData, setProfileData] = useState<ProviderProfileDataForPage | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);
  const [chatMessage, setChatMessage] = useState("");
  const [isSendingMessage, setIsSendingMessage] = useState(false);

  const currentUserId = (session?.user as any)?.id;

  const selectedServiceWithDetails = useMemo(() => {
    if (!profileData || !profileData.services || !serviceIdFromParam) return null;
    const serviceIdNum = parseInt(serviceIdFromParam, 10);
    if (isNaN(serviceIdNum)) return null;
    return profileData.services.find(s => s.id === serviceIdNum) || null;
  }, [profileData, serviceIdFromParam]);

  useEffect(() => {
    if (!providerId) {
      setError(translate(commonTranslations, 'profileIdMissingError'));
      setIsLoading(false);
      return;
    }
    if (!serviceIdFromParam) {
      setError(translate(commonTranslations, 'serviceIdMissingError'));
      setIsLoading(false);
      return;
    }

    async function fetchData() {
      setIsLoading(true);
      setError(null);
      try {
        const profile = await getProviderProfileData(providerId);
        
        if (profile === null || !profile.providerInfo) {
          setError(translate(commonTranslations, 'profileNotFoundError'));
        } else {
          setProfileData(profile);
          const serviceIdNum = parseInt(serviceIdFromParam, 10);
          if (isNaN(serviceIdNum) || !profile.services.find(s => s.id === serviceIdNum)) {
            setError(translate(commonTranslations, 'serviceNotFoundErrorForProvider'));
          }
        }
      } catch (err) {
         console.error("Detailed fetch error in ProfilePage:", err);
         const errorMessage = err instanceof Error ? err.message : translate(commonTranslations, 'profileFetchError');
         setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    }
    fetchData();
  }, [providerId, serviceIdFromParam, translate]);

  const handleInitiateChat = async () => {
    if (!chatMessage.trim() || !selectedServiceWithDetails || !profileData?.providerInfo.id) {
        toast({ variant: "destructive", title: translate(commonTranslations, 'toastErrorTitle'), description: translate(commonTranslations, 'chatMessageEmptyError') });
        return;
    }
    if (String(currentUserId) === String(profileData.providerInfo.id)) {
        toast({ variant: "destructive", title: translate(commonTranslations, 'toastErrorTitle'), description: translate(commonTranslations, 'chatCannotSelfMessageError') });
        return;
    }

    setIsSendingMessage(true);
    try {
        const response = await fetch('/api/proxy/chat/initiate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                providerId: profileData.providerInfo.id,
                serviceId: selectedServiceWithDetails.id,
                initialMessageContent: chatMessage,
            }),
        });
        const result = await response.json();
        if (!response.ok || !result.success) {
            throw new Error(result.message || translate(commonTranslations, 'chatInitiateError'));
        }
        toast({ title: translate(commonTranslations, 'toastSuccessTitle'), description: translate(commonTranslations, 'chatMessageSentSuccess') });
        setIsChatModalOpen(false);
        setChatMessage("");
        router.push(`/dashboard/chat/${result.chatRoomId}`);

    } catch (err) {
        const msg = err instanceof Error ? err.message : translate(commonTranslations, 'chatInitiateError');
        toast({ variant: "destructive", title: translate(commonTranslations, 'toastErrorTitle'), description: msg });
    } finally {
        setIsSendingMessage(false);
    }
  };

  const getCategoryDisplayName = (category: { nameKey: string, slug: string } | undefined | null): string => {
    if (!category) return "Necunoscută";
    return translate(commonTranslations, category.nameKey as keyof typeof commonTranslations) || category.nameKey;
  };
  
  const getServiceAvailabilityStringFromDetails = (details: any): string => {
    if (!details) return translate(commonTranslations, 'availabilityNotSpecified');
    const parts = [];
    if (details.availabilityWeekdays) parts.push(translate(commonTranslations, 'availabilityWeekdays'));
    if (details.availabilityWeekends) parts.push(translate(commonTranslations, 'availabilityWeekends'));
    if (details.availabilityEvenings) parts.push(translate(commonTranslations, 'availabilityEvenings'));
    return parts.length > 0 ? parts.join(', ') : translate(commonTranslations, 'availabilityNotSpecified');
  };

  const formatPriceFromDetails = (details: ServiceDetails | null): string => {
    if (!details) return translate(commonTranslations, 'priceContactFor');
    const parts: string[] = [];
    if (details.pricePerHour) parts.push(`${details.pricePerHour} MDL/oră`); 
    if (details.pricePerDay) parts.push(`${details.pricePerDay} MDL/zi`);
    
    if (details.pricePerMeal) {
        let mealPricePart = `${details.pricePerMeal} MDL/masă`;
        if (details.mealDetails) mealPricePart += ` (${details.mealDetails})`;
        parts.push(mealPricePart);
    }
    
    if (details.priceSubscriptionAmount && details.priceSubscriptionUnit) {
        parts.push(`${details.priceSubscriptionAmount} MDL/${details.priceSubscriptionUnit}`);
    } else if (details.priceSubscriptionText) {
        parts.push(details.priceSubscriptionText);
    }
    
    if (details.subscriptionDetails && (details.priceSubscriptionAmount || details.priceSubscriptionText)) {
      const lastPartIndex = parts.length -1;
      if (lastPartIndex >= 0) {
          parts[lastPartIndex] += ` (${details.subscriptionDetails})`;
      } else { 
          parts.push(details.subscriptionDetails);
      }
    }

    if (parts.length === 0) return translate(commonTranslations, 'priceContactFor');
    return parts.join(' / ');
  };
  
  const getServiceDetailsObject = (service: AdvertisedServiceWithDetails): ServiceDetails | null => {
    const slug = service.serviceCategorySlug;
    switch(slug) {
        case 'Nanny': return service.nannyServiceDetails ?? null;
        case 'ElderCare': return service.elderCareServiceDetails ?? null;
        case 'Cleaning': return service.cleaningServiceDetails ?? null;
        case 'Tutoring': return service.tutoringServiceDetails ?? null;
        case 'Cooking': return service.cookingServiceDetails ?? null;
        default: return null;
    }
  };

  if (isLoading) { 
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow flex flex-col items-center justify-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="mt-4 text-muted-foreground">{translate(commonTranslations, 'profileLoading')}</p>
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
     return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow container mx-auto py-12 px-4 flex flex-col items-center justify-center">
          <Card className="w-full max-w-md text-center">
            <CardHeader>
              <AlertTriangle className="mx-auto h-12 w-12 text-destructive" />
              <CardTitle className="mt-4">{translate(commonTranslations, 'profileNotFoundErrorTitle')}</CardTitle>
            </CardHeader>
            <CardFooter>
               <Button asChild className="w-full">
                <Link href="/search">{translate(commonTranslations, 'goToSearchPageButton')}</Link>
              </Button>
           </CardFooter>
          </Card>
        </main>
        <Footer />
      </div>
    );
 }

 if (!profileData || !profileData.providerInfo || !selectedServiceWithDetails) {
   return (
     <div className="flex flex-col min-h-screen">
       <Navbar />
       <main className="flex-grow container mx-auto py-12 px-4 flex flex-col items-center justify-center">
          <Card className="w-full max-w-md text-center">
           <CardHeader>
             <Info className="mx-auto h-12 w-12 text-primary" />
             <CardTitle className="mt-4">{translate(commonTranslations, 'serviceNotFoundErrorTitle')}</CardTitle>
           </CardHeader>
           <CardContent>
             <p className="text-muted-foreground">{translate(commonTranslations, 'serviceNotFoundErrorForProvider')}</p>
           </CardContent>
            <CardFooter>
               <Button asChild className="w-full">
                <Link href="/search">{translate(commonTranslations, 'goToSearchPageButton')}</Link>
              </Button>
           </CardFooter>
         </Card>
       </main>
       <Footer />
     </div>
   );
 }

  const { providerInfo, averageRating, reviewsCount } = profileData;
  const imageUrl = providerInfo.avatarUrl;
  const serviceDetails = getServiceDetailsObject(selectedServiceWithDetails);
  const canInteract = !!currentUserId && String(currentUserId) !== String(providerInfo.id);

  return (
    <div className="flex flex-col min-h-screen bg-muted/30">
      <Navbar />
      <main className="flex-grow container mx-auto py-8 px-4">
        <div className="mb-6">
            <Button variant="outline" asChild>
              <Link href="/search">
                <ArrowLeft className="w-4 h-4 mr-2" />
                {translate(commonTranslations, 'backToSearchButton')}
              </Link>
            </Button>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
          <div className="lg:col-span-1 space-y-6 lg:sticky lg:top-24">
            <Card className="shadow-lg">
                <CardContent className="p-6 text-center">
                     <div className="relative w-32 h-32 mx-auto rounded-full mb-4 overflow-hidden ring-4 ring-primary/20 bg-muted">
                        {imageUrl ? (
                            <Image
                                src={imageUrl}
                                alt={providerInfo.fullName || "Provider"}
                                fill
                                sizes="128px"
                                style={{objectFit: 'cover'}}
                                data-ai-hint={"portrait person"}
                                priority
                            />
                        ) : (
                            <UserSilhouetteIcon className="w-full h-full text-muted-foreground/30 p-4" />
                        )}
                    </div>
                    <CardTitle className="text-2xl font-headline">{providerInfo.fullName}</CardTitle>
                    <div className="flex items-center justify-center mt-2">
                        <Star className="w-5 h-5 text-yellow-400 fill-yellow-400 mr-1" />
                        <span className="font-semibold">{averageRating.toFixed(1)}</span>
                        <span className="ml-1 text-muted-foreground">({reviewsCount} {translate(commonTranslations, 'reviewsSuffix')})</span>
                    </div>
                    {providerInfo.phone && (
                    <div className="flex items-center justify-center mt-2 text-sm text-muted-foreground">
                        <Phone className="w-4 h-4 mr-1.5" />
                        <span>{providerInfo.phone}</span>
                    </div>
                    )}
                </CardContent>
            </Card>
            <div className="space-y-2">
                <Button size="lg" className="w-full" onClick={() => setIsBookingModalOpen(true)} disabled={!canInteract} title={!canInteract ? translate(commonTranslations, 'chatLoginToMessageOrCannotSelf') : ''}>
                    <PlusCircle className="mr-2 h-5 w-5" /> {translate(commonTranslations, 'requestBookingButton')}
                </Button>
                <Button 
                    size="lg" 
                    variant="outline" 
                    className="w-full" 
                    onClick={() => setIsChatModalOpen(true)}
                    disabled={!canInteract}
                    title={!canInteract ? translate(commonTranslations, 'chatLoginToMessageOrCannotSelf') : translate(commonTranslations, 'sendMessageButton')}
                >
                <MessageSquare className="mr-2 h-5 w-5" /> {translate(commonTranslations, 'sendMessageButton')}
                </Button>
            </div>
          </div>

          <div className="lg:col-span-2 space-y-6">
            {providerInfo.bio && (
              <Card className="shadow-md">
                <CardHeader>
                  <CardTitle className="text-xl font-headline">{translate(commonTranslations, 'aboutProviderLabel')} {providerInfo.fullName}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground/80 whitespace-pre-line">{providerInfo.bio}</p>
                </CardContent>
              </Card>
            )}

            <Card className="shadow-md">
              <CardHeader>
                <CardTitle className="text-xl font-headline">{selectedServiceWithDetails.serviceName}</CardTitle>
                <div className="text-md text-muted-foreground pt-2">
                  <Badge variant="outline" className="text-sm">{getCategoryDisplayName(selectedServiceWithDetails.category)}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-foreground/80 whitespace-pre-line mb-4">{selectedServiceWithDetails.description}</p>
                 <p className="text-lg font-semibold text-green-700">{formatPriceFromDetails(serviceDetails)}</p>
              </CardContent>
            </Card>

            {serviceDetails && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl font-headline">{translate(commonTranslations, 'operationalDetailsLabel')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <dl className="space-y-1">
                      <DetailItem labelKey="serviceLocationLabel" value={getFullLocationPathDisplayHelper(serviceDetails.locationValue || 'all', translate, commonTranslations)}/>
                      <DetailItem labelKey="serviceExperienceLabel" value={serviceDetails.experienceYears ? `${serviceDetails.experienceYears} ${translate(commonTranslations, 'yearsSuffix')}` : translate(commonTranslations, 'availabilityNotSpecified')} />
                      <DetailItem labelKey="serviceAvailabilityLabel" value={getServiceAvailabilityStringFromDetails(serviceDetails)}/>
                  </dl>
                </CardContent>
              </Card>
            )}

            {serviceDetails && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl font-headline">{translate(commonTranslations, 'categorySpecificDetailsLabel')}</CardTitle>
                </CardHeader>
                <CardContent>
                   <dl className="space-y-1">
                      <BooleanDetailItem labelKey="childcareFirstAidLabel" value={serviceDetails.firstAid} />
                      <MultiSelectDetailItem mainLabelKey="childcareActivitiesOfferedLabel" translateFn={translate} options={[
                          {flag: serviceDetails.activityWalks, labelKey: 'childcareActivityWalks'},
                          {flag: serviceDetails.activityGames, labelKey: 'childcareActivityGames'},
                      ]}/>
                      {/* Add more specific fields here using the new helper components */}
                   </dl>
                </CardContent>
              </Card>
            )}

            {serviceDetails && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl font-headline">{translate(commonTranslations, 'documentsTitle')}</CardTitle>
                </CardHeader>
                <CardContent>
                   <dl className="space-y-1">
                        <DocumentItem labelKey="docBuletinLabel" fileNames={serviceDetails.docBuletinFileName} translate={translate} toast={toast}/>
                        <DocumentItem labelKey="docDiplomeLabel" fileNames={serviceDetails.docDiplomeFileNames as string[] | null} translate={translate} toast={toast}/>
                        <DocumentItem labelKey="docRecomandariLabel" fileNames={serviceDetails.docRecomandariFileNames as string[] | null} translate={translate} toast={toast}/>
                        {(!serviceDetails.docBuletinFileName && (!serviceDetails.docDiplomeFileNames || (serviceDetails.docDiplomeFileNames as string[])?.length === 0) && (!serviceDetails.docRecomandariFileNames || (serviceDetails.docRecomandariFileNames as string[])?.length === 0)) && (
                            <p className="text-sm text-muted-foreground mt-2">{translate(commonTranslations, 'noDocumentsAttached')}</p>
                        )}
                   </dl>
                </CardContent>
              </Card>
            )}

          </div>
        </div>
      </main>
      <Footer />
      {profileData?.providerInfo && selectedServiceWithDetails && (
         <BookingAvailabilityModal
            isOpen={isBookingModalOpen}
            onClose={() => setIsBookingModalOpen(false)}
            providerId={profileData.providerInfo.id}
            providerName={providerInfo.fullName || "Provider"}
            serviceId={selectedServiceWithDetails.id}
        />
      )}
       <Dialog open={isChatModalOpen} onOpenChange={setIsChatModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{translate(commonTranslations, 'chatModalTitle').replace('{providerName}', profileData?.providerInfo.fullName || 'Furnizor')}</DialogTitle>
            <DialogDescription>
              {translate(commonTranslations, 'chatModalDescription').replace('{serviceName}', selectedServiceWithDetails?.serviceName || 'acest serviciu')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-2">
            <Label htmlFor="chatMessage" className="sr-only">{translate(commonTranslations, 'chatMessageLabel')}</Label>
            <Textarea
              id="chatMessage"
              value={chatMessage}
              onChange={(e) => setChatMessage(e.target.value)}
              placeholder={translate(commonTranslations, 'chatMessagePlaceholder')}
              rows={4}
            />
          </div>
          <DialogFooter className="sm:justify-between">
            <DialogClose asChild>
              <Button type="button" variant="outline">{translate(commonTranslations, 'cancelButton')}</Button>
            </DialogClose>
            <Button type="button" onClick={handleInitiateChat} disabled={isSendingMessage || !chatMessage.trim()}>
              {isSendingMessage && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {translate(commonTranslations, 'chatSendButton')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
