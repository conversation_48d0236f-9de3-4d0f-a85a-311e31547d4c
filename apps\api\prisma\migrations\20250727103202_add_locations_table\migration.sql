-- CreateEnum
CREATE TYPE "LocationType" AS ENUM ('Country', 'Municipality', 'City', 'Sector', 'Suburb', 'Town', 'Village');

-- CreateTable
CREATE TABLE "Locations" (
    "Id" SERIAL NOT NULL,
    "Key" TEXT NOT NULL,
    "Value" TEXT NOT NULL,
    "TranslationKey" TEXT NOT NULL,
    "DisplayRo" TEXT NOT NULL,
    "Type" "LocationType" NOT NULL,
    "ParentId" INTEGER,
    "SortOrder" INTEGER NOT NULL DEFAULT 0,
    "IsActive" BOOLEAN NOT NULL DEFAULT true,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Locations_pkey" PRIMARY KEY ("Id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Locations_Key_key" ON "Locations"("Key");

-- CreateIndex
CREATE UNIQUE INDEX "Locations_Value_key" ON "Locations"("Value");

-- AddForeignKey
ALTER TABLE "Locations" ADD CONSTRAINT "Locations_ParentId_fkey" FOREIGN KEY ("ParentId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;
