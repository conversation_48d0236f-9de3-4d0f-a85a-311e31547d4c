"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  MousePointer, 
  Clock, 
  Target,
  RefreshCw,
  Download
} from 'lucide-react';
import { useRoleToggleAnalytics } from '@/lib/analytics/role-toggle-analytics';
import { useLanguage } from '@/contexts/language-context';

interface AnalyticsSummary {
  roleSwitchEfficiency: {
    averageClicks: number;
    targetClicks: number;
    improvement: number;
  };
  dualRoleEngagement: {
    activeUsers: number;
    totalDualRoleUsers: number;
    engagementRate: number;
    targetRate: number;
  };
  taskCompletionRate: {
    completionRate: number;
    targetRate: number;
    improvement: number;
  };
  userSatisfaction: {
    currentScore: number;
    targetScore: number;
    improvement: number;
  };
  roleSwitchData: Array<{
    date: string;
    switches: number;
    uniqueUsers: number;
  }>;
  navigationData: Array<{
    page: string;
    clicks: number;
    role: 'client' | 'provider';
  }>;
  errorData: Array<{
    type: string;
    count: number;
    trend: 'up' | 'down' | 'stable';
  }>;
}

const analyticsTranslations = {
  analyticsTitle: { ro: "Analiză Role Toggle", ru: "Аналитика переключения ролей", en: "Role Toggle Analytics" },
  overview: { ro: "Prezentare generală", ru: "Обзор", en: "Overview" },
  metrics: { ro: "Metrici", ru: "Метрики", en: "Metrics" },
  trends: { ro: "Tendințe", ru: "Тренды", en: "Trends" },
  errors: { ro: "Erori", ru: "Ошибки", en: "Errors" },
  roleSwitchEfficiency: { ro: "Eficiența Comutării Rolurilor", ru: "Эффективность переключения ролей", en: "Role Switch Efficiency" },
  dualRoleEngagement: { ro: "Angajamentul Utilizatorilor Dual-Rol", ru: "Вовлеченность пользователей с двумя ролями", en: "Dual-Role User Engagement" },
  taskCompletion: { ro: "Rata de Finalizare a Sarcinilor", ru: "Уровень завершения задач", en: "Task Completion Rate" },
  userSatisfaction: { ro: "Satisfacția Utilizatorilor", ru: "Удовлетворенность пользователей", en: "User Satisfaction" },
  averageClicks: { ro: "Clicuri Medii", ru: "Средние клики", en: "Average Clicks" },
  target: { ro: "Țintă", ru: "Цель", en: "Target" },
  improvement: { ro: "Îmbunătățire", ru: "Улучшение", en: "Improvement" },
  refresh: { ro: "Actualizează", ru: "Обновить", en: "Refresh" },
  export: { ro: "Exportă", ru: "Экспорт", en: "Export" },
  last24Hours: { ro: "Ultimele 24 ore", ru: "Последние 24 часа", en: "Last 24 hours" },
  last7Days: { ro: "Ultimele 7 zile", ru: "Последние 7 дней", en: "Last 7 days" },
  last30Days: { ro: "Ultimele 30 zile", ru: "Последние 30 дней", en: "Last 30 days" },
};

export function RoleToggleAnalyticsDashboard() {
  const { translate } = useLanguage();
  const { getAnalyticsSummary, isEnabled } = useRoleToggleAnalytics();
  const [data, setData] = useState<AnalyticsSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d'>('7d');

  useEffect(() => {
    if (isEnabled) {
      loadAnalytics();
    }
  }, [timeRange, isEnabled]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      const summary = await getAnalyticsSummary(timeRange);
      setData(summary);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isEnabled) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Analytics is disabled</p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="w-6 h-6 animate-spin mr-2" />
            <span>Loading analytics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No analytics data available</p>
          <Button onClick={loadAnalytics} className="mt-4">
            <RefreshCw className="w-4 h-4 mr-2" />
            {translate(analyticsTranslations, 'refresh')}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">
          {translate(analyticsTranslations, 'analyticsTitle')}
        </h2>
        <div className="flex items-center gap-2">
          <Tabs value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <TabsList>
              <TabsTrigger value="24h">{translate(analyticsTranslations, 'last24Hours')}</TabsTrigger>
              <TabsTrigger value="7d">{translate(analyticsTranslations, 'last7Days')}</TabsTrigger>
              <TabsTrigger value="30d">{translate(analyticsTranslations, 'last30Days')}</TabsTrigger>
            </TabsList>
          </Tabs>
          <Button variant="outline" size="sm" onClick={loadAnalytics}>
            <RefreshCw className="w-4 h-4 mr-2" />
            {translate(analyticsTranslations, 'refresh')}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {translate(analyticsTranslations, 'export')}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title={translate(analyticsTranslations, 'roleSwitchEfficiency')}
          value={`${data.roleSwitchEfficiency.averageClicks}`}
          target={`${data.roleSwitchEfficiency.targetClicks}`}
          improvement={data.roleSwitchEfficiency.improvement}
          icon={MousePointer}
          suffix="clicks"
        />
        
        <MetricCard
          title={translate(analyticsTranslations, 'dualRoleEngagement')}
          value={`${data.dualRoleEngagement.engagementRate}%`}
          target={`${data.dualRoleEngagement.targetRate}%`}
          improvement={(data.dualRoleEngagement.engagementRate - data.dualRoleEngagement.targetRate)}
          icon={Users}
        />
        
        <MetricCard
          title={translate(analyticsTranslations, 'taskCompletion')}
          value={`${data.taskCompletionRate.completionRate}%`}
          target={`${data.taskCompletionRate.targetRate}%`}
          improvement={data.taskCompletionRate.improvement}
          icon={Target}
        />
        
        <MetricCard
          title={translate(analyticsTranslations, 'userSatisfaction')}
          value={`${data.userSatisfaction.currentScore}/5`}
          target={`${data.userSatisfaction.targetScore}/5`}
          improvement={data.userSatisfaction.improvement}
          icon={TrendingUp}
        />
      </div>

      {/* Charts */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">{translate(analyticsTranslations, 'overview')}</TabsTrigger>
          <TabsTrigger value="trends">{translate(analyticsTranslations, 'trends')}</TabsTrigger>
          <TabsTrigger value="errors">{translate(analyticsTranslations, 'errors')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Role Switch Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data.roleSwitchData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="switches" stroke="#8884d8" />
                    <Line type="monotone" dataKey="uniqueUsers" stroke="#82ca9d" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Navigation Usage by Role</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.navigationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="page" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="clicks" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends">
          <Card>
            <CardHeader>
              <CardTitle>Usage Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">↗ 23%</div>
                    <div className="text-sm text-muted-foreground">Role switches</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">↗ 15%</div>
                    <div className="text-sm text-muted-foreground">User engagement</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">↘ 8%</div>
                    <div className="text-sm text-muted-foreground">Error rate</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors">
          <Card>
            <CardHeader>
              <CardTitle>Error Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.errorData.map((error, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <div className="font-medium">{error.type}</div>
                      <div className="text-sm text-muted-foreground">{error.count} occurrences</div>
                    </div>
                    <Badge variant={error.trend === 'up' ? 'destructive' : 'secondary'}>
                      {error.trend === 'up' ? '↗' : error.trend === 'down' ? '↘' : '→'} {error.trend}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Metric card component
function MetricCard({ 
  title, 
  value, 
  target, 
  improvement, 
  icon: Icon, 
  suffix = '' 
}: {
  title: string;
  value: string;
  target: string;
  improvement: number;
  icon: any;
  suffix?: string;
}) {
  const isPositive = improvement > 0;
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline space-x-2">
              <p className="text-2xl font-bold">{value}</p>
              {suffix && <span className="text-sm text-muted-foreground">{suffix}</span>}
            </div>
            <p className="text-xs text-muted-foreground">Target: {target}</p>
          </div>
          <div className="flex flex-col items-end">
            <Icon className="w-8 h-8 text-muted-foreground" />
            <div className={`flex items-center text-xs ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {isPositive ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
              {Math.abs(improvement)}%
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
