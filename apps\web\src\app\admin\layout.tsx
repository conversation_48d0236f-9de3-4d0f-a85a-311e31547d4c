
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@repo/auth';
import type { ExtendedSession } from '@repo/auth';
import { AdminLayoutClient } from './admin-layout-client';

export default async function AdminDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // SERVER-SIDE AUTHENTICATION CHECK - This prevents admin content flash
  const session = await getServerSession(authOptions) as ExtendedSession | null;

  console.log('[AdminLayout] Server-side auth check:', {
    hasSession: !!session,
    userId: session?.user?.id,
    isAdmin: session?.user?.isAdmin
  });

  // Redirect non-authenticated users to login
  if (!session || !session.user) {
    console.log('[AdminLayout] No session found - redirecting to login');
    redirect('/login?callbackUrl=/admin');
  }

  // Redirect non-admin users to dashboard - NO ADMIN CONTENT WILL RENDER
  if (!session.user.isAdmin) {
    console.log('[AdminLayout] Non-admin user attempting admin access - redirecting to dashboard');
    redirect('/dashboard');
  }

  // Only admin users reach this point - safe to render admin content
  return (
    <AdminLayoutClient session={session}>
      {children}
    </AdminLayoutClient>
  );
}
