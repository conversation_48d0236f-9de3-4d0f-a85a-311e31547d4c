
import { type NextRequest, NextResponse } from 'next/server';
// The actual logout mechanism will primarily be handled by next-auth/react signOut on the client.
// This API route can serve as a server-side confirmation or for specific cleanup if needed,
// but next-auth handles its own cookie invalidation.

export async function POST(request: NextRequest) {
  try {
    // next-auth signOut on the client handles cookie clearing.
    // This API route is mostly for symmetry or if server-side actions were needed on logout.
    console.log('[Logout API] POST request received. Client-side signOut should handle session termination.');
    
    const response = NextResponse.json({ success: true, message: 'Deconectare inițiată. Sesiunea va fi invalidată de client.' });
    
    // Example of clearing custom cookies if they were still in use, though next-auth manages its own.
    // response.cookies.set('bonami-app-session', '', { maxAge: 0, path: '/' });
    // response.cookies.set('bonami-active-user-id', '', { maxAge: 0, path: '/' });

    return response;
  } catch (error) {
    console.error('Logout API error:', error);
    return NextResponse.json({ success: false, message: 'A apărut o eroare la procesarea cererii de deconectare.' }, { status: 500 });
  }
}
