"use client";

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useLanguage } from '@/contexts/language-context';
import { BooleanFilterGroup, FilterBadges, getActiveFilters } from './filter-components';

const cookingFilterTranslations = {
  cuisineTypes: { ro: "Tipuri Bucătărie", ru: "Типы кухни", en: "Cuisine Types" },
  traditional: { ro: "Tradițională", ru: "Традиционная", en: "Traditional" },
  vegetarian: { ro: "Vegetariană", ru: "Вегетарианская", en: "Vegetarian" },
  kids: { ro: "Pentru copii", ru: "Детская", en: "Kids-Friendly" },
  diet: { ro: "Dietetică", ru: "Диетическая", en: "Diet/Healthy" },

  serviceOptions: { ro: "Opț<PERSON><PERSON> Serviciu", ru: "Варианты услуг", en: "Service Options" },
  delivery: { ro: "Livrare", ru: "Доставка", en: "Delivery" },
  clientHome: { ro: "La clientul acasă", ru: "Дома у клиента", en: "At Client's Home" },
  ownHome: { ro: "La bucătarul acasă", ru: "Дома у повара", en: "At Cook's Home" },
  ownIngredients: { ro: "Ingrediente proprii", ru: "Собственные ингредиенты", en: "Own Ingredients" },
  weeklySubscription: { ro: "Abonament săptămânal", ru: "Еженедельная подписка", en: "Weekly Subscription" },
};

interface CookingFiltersState {
  // Cuisine types
  CuisineTypeTraditional: boolean;
  CuisineTypeVegetarian: boolean;
  CuisineTypeKids: boolean;
  CuisineTypeDiet: boolean;

  // Service options
  OffersDelivery: boolean;
  AtClientHome: boolean;
  AtOwnHome: boolean;
  cookingOwnProducts: boolean;
  WeeklySubscription: boolean;
}

export function CookingFilters() {
  const { translate } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<CookingFiltersState>({
    CuisineTypeTraditional: searchParams.get('CuisineTypeTraditional') === 'true',
    CuisineTypeVegetarian: searchParams.get('CuisineTypeVegetarian') === 'true',
    CuisineTypeKids: searchParams.get('CuisineTypeKids') === 'true',
    CuisineTypeDiet: searchParams.get('CuisineTypeDiet') === 'true',
    OffersDelivery: searchParams.get('OffersDelivery') === 'true',
    AtClientHome: searchParams.get('AtClientHome') === 'true',
    AtOwnHome: searchParams.get('AtOwnHome') === 'true',
    cookingOwnProducts: searchParams.get('cookingOwnProducts') === 'true',
    WeeklySubscription: searchParams.get('WeeklySubscription') === 'true',
  });

  // Update URL when filters change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams.toString());

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, 'true');
      } else {
        newParams.delete(key);
      }
    });

    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  }, [filters, router, pathname, searchParams]);

  const handleFilterChange = (key: string, checked: boolean) => {
    setFilters(prev => ({ ...prev, [key]: checked }));
  };

  const handleClearAll = () => {
    setFilters({
      CuisineTypeTraditional: false,
      CuisineTypeVegetarian: false,
      CuisineTypeKids: false,
      CuisineTypeDiet: false,
      OffersDelivery: false,
      AtClientHome: false,
      AtOwnHome: false,
      cookingOwnProducts: false,
      WeeklySubscription: false,
    });
  };

  const handleRemoveFilter = (key: string) => {
    setFilters(prev => ({ ...prev, [key]: false }));
  };

  // Create filter labels
  const filterLabels = {
    CuisineTypeTraditional: translate(cookingFilterTranslations, 'traditional'),
    CuisineTypeVegetarian: translate(cookingFilterTranslations, 'vegetarian'),
    CuisineTypeKids: translate(cookingFilterTranslations, 'kids'),
    CuisineTypeDiet: translate(cookingFilterTranslations, 'diet'),
    OffersDelivery: translate(cookingFilterTranslations, 'delivery'),
    AtClientHome: translate(cookingFilterTranslations, 'clientHome'),
    AtOwnHome: translate(cookingFilterTranslations, 'ownHome'),
    cookingOwnProducts: translate(cookingFilterTranslations, 'ownIngredients'),
    WeeklySubscription: translate(cookingFilterTranslations, 'weeklySubscription'),
  };

  // Get active filters for badges
  const activeFilters = getActiveFilters(filters, filterLabels, 'cooking');

  return (
    <div className="space-y-4">
      <FilterBadges
        activeFilters={activeFilters}
        onRemove={handleRemoveFilter}
        onClearAll={handleClearAll}
      />

      <BooleanFilterGroup
        title={translate(cookingFilterTranslations, 'cuisineTypes')}
        options={[
          { key: 'CuisineTypeTraditional', label: filterLabels.CuisineTypeTraditional, checked: filters.CuisineTypeTraditional },
          { key: 'CuisineTypeVegetarian', label: filterLabels.CuisineTypeVegetarian, checked: filters.CuisineTypeVegetarian },
          { key: 'CuisineTypeKids', label: filterLabels.CuisineTypeKids, checked: filters.CuisineTypeKids },
          { key: 'CuisineTypeDiet', label: filterLabels.CuisineTypeDiet, checked: filters.CuisineTypeDiet },
        ]}
        onChange={handleFilterChange}
      />

      <BooleanFilterGroup
        title={translate(cookingFilterTranslations, 'serviceOptions')}
        options={[
          { key: 'OffersDelivery', label: filterLabels.OffersDelivery, checked: filters.OffersDelivery },
          { key: 'AtClientHome', label: filterLabels.AtClientHome, checked: filters.AtClientHome },
          { key: 'AtOwnHome', label: filterLabels.AtOwnHome, checked: filters.AtOwnHome },
          { key: 'cookingOwnProducts', label: filterLabels.cookingOwnProducts, checked: filters.cookingOwnProducts },
          { key: 'WeeklySubscription', label: filterLabels.WeeklySubscription, checked: filters.WeeklySubscription },
        ]}
        onChange={handleFilterChange}
      />
    </div>
  );
}
