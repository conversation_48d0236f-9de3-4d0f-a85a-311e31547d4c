{"name": "bonami-monorepo", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev --workspace=apps/web\" \"npm run dev --workspace=apps/api\"", "build": "npm run build --workspace=apps/web && npm run build --workspace=apps/api", "start": "concurrently \"npm start --workspace=apps/web\" \"npm start --workspace=apps/api\"", "install:all": "npm install --workspaces"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5"}}