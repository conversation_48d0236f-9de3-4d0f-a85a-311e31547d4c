-- AlterTable
ALTER TABLE "Address" ADD COLUMN     "CityId" INTEGER,
ADD COLUMN     "CountryId" INTEGER,
ADD COLUMN     "RegionId" INTEGER,
ADD COLUMN     "SectorId" INTEGER,
ALTER COLUMN "City" DROP NOT NULL,
ALTER COLUMN "Country" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_CountryId_fkey" FOREIGN KEY ("CountryId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_RegionId_fkey" FOREIGN KEY ("RegionId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_CityId_fkey" FOREIGN KEY ("CityId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_SectorId_fkey" FOREIGN KEY ("SectorId") REFERENCES "Locations"("Id") ON DELETE SET NULL ON UPDATE CASCADE;
