// src/app/data-example/page.tsx
"use client";

import { useState, useEffect, type FormEvent } from 'react';
import { Navbar } from '@/components/layout/navbar';
import { Footer } from '@/components/layout/footer';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface Item {
  id: number;
  name: string;
  value: string;
}

export default function DataExamplePage() {
  const [itemName, setItemName] = useState('');
  const [itemValue, setItemValue] = useState('');
  const [items, setItems] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const fetchItems = async () => {
    setIsFetching(true);
    setError(null);
   
    console.warn("DB interaction in /data-example is commented out as items_table_placeholder might not exist after migrations.");
    setItems([]);
    setIsFetching(false);
  };

  useEffect(() => {
    fetchItems();
  }, []);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    console.warn("DB interaction in /data-example is commented out.");
    setSuccessMessage("Adăugare simulată. Interacțiunea cu BD este comentată.");
    setIsLoading(false);
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Navbar />
      <main className="flex-grow container mx-auto py-12 px-4">
        <Card className="max-w-2xl mx-auto mb-8">
          <CardHeader>
            <CardTitle className="text-2xl font-bold font-headline text-center">Adaugă Element Nou (Exemplu Comentat)</CardTitle>
            <CardDescription className="text-center">
              Completează formularul pentru a adăuga un element în baza de date. Interacțiunea cu BD este momentan comentată.
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="itemName">Nume Element</Label>
                <Input
                  id="itemName"
                  value={itemName}
                  onChange={(e) => setItemName(e.target.value)}
                  placeholder="Ex: Produs A"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="itemValue">Valoare Element</Label>
                <Input
                  id="itemValue"
                  value={itemValue}
                  onChange={(e) => setItemValue(e.target.value)}
                  placeholder="Ex: Detaliu important"
                  required
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se adaugă...
                  </>
                ) : (
                  "Adaugă Element"
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {error && (
          <Alert variant="destructive" className="mt-4 max-w-2xl mx-auto">
            <AlertTitle>Eroare</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {successMessage && (
          <Alert variant="default" className="mt-4 max-w-2xl mx-auto bg-green-100 border-green-300 text-green-800">
            <AlertTitle>Succes</AlertTitle>
            <AlertDescription>{successMessage}</AlertDescription>
          </Alert>
        )}

        <Card className="max-w-2xl mx-auto mt-8">
          <CardHeader>
            <CardTitle className="text-xl font-headline">Elemente Existente (Exemplu Comentat)</CardTitle>
          </CardHeader>
          <CardContent>
            {isFetching ? (
              <div className="flex justify-center items-center h-20">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="ml-2">Se încarcă elementele...</p>
              </div>
            ) : items.length > 0 ? (
              <ul className="space-y-3">
                {items.map((item) => (
                  <li key={item.id} className="p-3 border rounded-md shadow-sm bg-muted/30">
                    <p className="font-semibold">{item.name}</p>
                    <p className="text-sm text-muted-foreground">{item.value}</p>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-muted-foreground">Niciun element găsit sau interacțiune BD comentată.</p>
            )}
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
}
