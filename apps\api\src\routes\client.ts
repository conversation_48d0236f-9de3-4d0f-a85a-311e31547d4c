
import { Router, type Response } from 'express';
import prisma from '../lib/db';
import { type AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// This route is now redundant as it's handled in /bookings/client/:clientId
// Keeping it for now to avoid breaking changes, but should be deprecated.
router.get('/bookings', async (req: AuthenticatedRequest, res: Response) => {
  const clientIdStr = req.query.clientId as string | undefined;

  if (!clientIdStr) {
    return res.status(400).json({ message: 'Client ID is required' });
  }

  const clientId = parseInt(clientIdStr, 10);
  if (isNaN(clientId)) {
    return res.status(400).json({ message: 'Invalid Client ID format' });
  }

  try {
    const bookingsFromDb = await prisma.booking.findMany({
      where: {
        ClientId: clientId, 
      },
      include: {
        AdvertisedService: { 
          select: {
            ServiceName: true, 
            Category: { select: { NameKey: true }} 
          }
        },
        Provider: { 
          select: {
            Id: true, 
            FullName: true, 
            AvatarUrl: true, 
          }
        }
      },
      orderBy: {
        EventStartDateTime: 'desc', 
      },
    });

    const formattedBookings = bookingsFromDb.map(booking => ({
      id: booking.Id,
      clientId: booking.ClientId,
      providerId: booking.ProviderId,
      advertisedServiceId: booking.AdvertisedServiceId,
      eventStartDateTime: booking.EventStartDateTime?.toISOString(),
      eventEndDateTime: booking.EventEndDateTime?.toISOString(),
      status: booking.Status,
      clientNotes: booking.ClientNotes,
      providerNotes: booking.ProviderNotes,
      createdAt: booking.CreatedAt.toISOString(),
      updatedAt: booking.UpdatedAt.toISOString(),
      service: booking.AdvertisedService ? {
        serviceName: booking.AdvertisedService.ServiceName,
        category: booking.AdvertisedService.Category ? {
          nameKey: booking.AdvertisedService.Category.NameKey
        } : null
      } : null,
      provider: booking.Provider ? {
        id: booking.Provider.Id,
        fullName: booking.Provider.FullName,
        avatarUrl: booking.Provider.AvatarUrl
      } : null,
    }));

    return res.json({ bookings: formattedBookings });
  } catch (error) {
    console.error(`[API /client/bookings] Failed to fetch bookings for client ${clientId}:`, error);
    return res.status(500).json({ message: 'Failed to fetch bookings' });
  }
});

export default router;
