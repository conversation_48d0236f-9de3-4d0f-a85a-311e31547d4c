import Link from 'next/link';
import Image from 'next/image';
import type { AnchorHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

interface LogoProps extends AnchorHTMLAttributes<HTMLAnchorElement> {
    className?: string;
    showText?: boolean;
    size?: 'sm' | 'md' | 'lg';
}

export function Logo({ className, showText = true, size = 'md', ...props }: LogoProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  };

  return (
    <Link
      href="/"
      className={cn("flex items-center space-x-2 font-bold text-primary hover:opacity-80 transition-opacity", className)}
      {...props}
    >
      <Image
        src="/logo.png"
        alt="Bonami Logo"
        width={size === 'sm' ? 32 : size === 'md' ? 40 : 48}
        height={size === 'sm' ? 32 : size === 'md' ? 40 : 48}
        className={cn("object-contain", sizeClasses[size])}
        priority
      />
      {showText && (
        <span className={cn("font-bold text-primary", textSizeClasses[size])}>
          bonami
        </span>
      )}
    </Link>
  );
}
