"use client";

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, CheckCircle, MailO<PERSON> } from "lucide-react";
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { useRouter } from "next/navigation";
import { formatDistanceToNowStrict } from 'date-fns';
import { ro, ru, enUS as en } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import { useSession } from 'next-auth/react'; // Use NextAuth session
import Link from "next/link";

const dateFnsLocalesMap: Record<string, Locale> = { ro, ru, en };

// Interface for notification data received from API (can be camelCase)
interface DisplayNotification {
  id: number;
  message: string;
  link?: string | null;
  isRead: boolean;
  createdAt: string; // ISO date string
}

const notificationsPageTranslations = {
  pageTitle: { ro: "Notificările Mele", ru: "Мои уведомления", en: "My Notifications" },
  pageDescription: { ro: "Vezi toate notificările tale aici.", ru: "Просматривайте все ваши уведомления здесь.", en: "View all your notifications here." },
  markAllAsRead: { ro: "Marchează Toate ca Citite", ru: "Отметить все как прочитанные", en: "Mark All as Read" },
  noNotifications: { ro: "Nu ai nicio notificare momentan.", ru: "У вас пока нет уведомлений.", en: "You have no notifications at the moment." },
  errorFetching: { ro: "Eroare la preluarea notificărilor.", ru: "Ошибка при загрузке уведомлений.", en: "Error fetching notifications." },
  errorMarkingRead: { ro: "Eroare la marcarea notificărilor ca citite.", ru: "Ошибка при отметке уведомлений как прочитанных.", en: "Error marking notifications as read." },
  errorNetwork: { ro: "A apărut o problemă de rețea.", ru: "Возникла проблема с сетью.", en: "A network problem occurred." },
  allNotificationsRead: { ro: "Toate notificările au fost marcate ca citite.", ru: "Все уведомления были отмечены как прочитанные.", en: "All notifications have been marked as read." },
  notificationMarkedRead: { ro: "Notificare marcată ca citită.", ru: "Уведомление отмечено как прочитанное.", en: "Notification marked as read." },
};

export default function NotificationsPage() {
  const { translate, currentLanguage } = useLanguage();
  const { data: session, status: sessionStatus } = useSession();
  const { toast } = useToast();
  const router = useRouter();
  const selectedDateLocale = dateFnsLocalesMap[currentLanguage.code] || ro;

  const [notifications, setNotifications] = useState<DisplayNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // const [currentUserId, setCurrentUserId] = useState<number | null>(null); // No longer needed with NextAuth

  // useEffect(() => {
  //   // Old logic for custom auth
  //   const userIdFromStorage = localStorage.getItem('bonamiUserId');
  //   if (userIdFromStorage) {
  //     setCurrentUserId(parseInt(userIdFromStorage, 10));
  //   } else {
  //     setError(translate(commonTranslations, 'errorLoadingUserData'));
  //     setIsLoading(false);
  //   }
  // }, [translate]);

  const fetchNotifications = useCallback(async () => {
    if (sessionStatus !== "authenticated") {
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // API now needs to use NextAuth session to determine user
      const response = await fetch('/api/proxy/notifications');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || translate(notificationsPageTranslations, 'errorFetching'));
      }
      const data = await response.json();
      setNotifications(data.notifications || []); // API should return camelCase
    } catch (err) {
      setError(err instanceof Error ? err.message : translate(notificationsPageTranslations, 'errorFetching'));
    } finally {
      setIsLoading(false);
    }
  }, [sessionStatus, translate]);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications, sessionStatus]);

  const handleMarkAsRead = async (notificationId: number) => {
    if (sessionStatus !== "authenticated") return;
    try {
      // API now needs to use NextAuth session
      const response = await fetch('/api/proxy/notifications/mark-as-read', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notificationId }), // notificationId is camelCase from DisplayNotification
      });
      if (!response.ok) {
        throw new Error(translate(notificationsPageTranslations, 'errorMarkingRead'));
      }
      setNotifications(prev =>
        prev.map(n => (n.id === notificationId ? { ...n, isRead: true } : n))
      );
      toast({
        title: translate(commonTranslations, 'toastSuccessTitle'),
        description: translate(notificationsPageTranslations, 'notificationMarkedRead'),
      });
    } catch (err) {
      toast({
        variant: "destructive",
        title: translate(commonTranslations, 'toastErrorTitle'),
        description: err instanceof Error ? err.message : translate(notificationsPageTranslations, 'errorNetwork'),
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    if (sessionStatus !== "authenticated") return;
    const unreadNotifications = notifications.filter(n => !n.isRead);
    if (unreadNotifications.length === 0) return;

    try {
      // API now needs to use NextAuth session
      const response = await fetch('/api/proxy/notifications/mark-as-read', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ markAll: true }),
      });
      if (!response.ok) {
        throw new Error(translate(notificationsPageTranslations, 'errorMarkingRead'));
      }
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      toast({
        title: translate(commonTranslations, 'toastSuccessTitle'),
        description: translate(notificationsPageTranslations, 'allNotificationsRead'),
      });
    } catch (err) {
      toast({
        variant: "destructive",
        title: translate(commonTranslations, 'toastErrorTitle'),
        description: err instanceof Error ? err.message : translate(notificationsPageTranslations, 'errorNetwork'),
      });
    }
  };

  const handleNotificationClick = async (notification: DisplayNotification) => {
    if (!notification.isRead) {
      await handleMarkAsRead(notification.id);
    }
    if (notification.link) {
      router.push(notification.link);
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (sessionStatus === "loading") {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div>
            <CardTitle className="text-xl font-headline flex items-center">
              <Bell className="w-6 h-6 mr-2 text-primary" />
              {translate(notificationsPageTranslations, 'pageTitle')}
            </CardTitle>
            <CardDescription>{translate(notificationsPageTranslations, 'pageDescription')}</CardDescription>
          </div>
          {notifications.length > 0 && unreadCount > 0 && (
            <Button onClick={handleMarkAllAsRead} variant="outline" size="sm">
              <MailOpen className="w-4 h-4 mr-2" />
              {translate(notificationsPageTranslations, 'markAllAsRead')}
            </Button>
          )}
        </CardHeader>
      </Card>

      {isLoading && sessionStatus === "authenticated" && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="ml-3">{translate(commonTranslations, 'loading')}</p>
        </div>
      )}

      {error && (
        <Card>
          <CardContent className="py-10 text-center text-destructive">
            <AlertTriangle className="mx-auto h-8 w-8 mb-2" />
            <p>{error}</p>
          </CardContent>
        </Card>
      )}

      {!isLoading && !error && notifications.length === 0 && sessionStatus === "authenticated" && (
        <Card>
          <CardContent className="text-center py-12">
            <Bell className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{translate(notificationsPageTranslations, 'noNotifications')}</p>
          </CardContent>
        </Card>
      )}
      
      {!isLoading && !error && notifications.length > 0 && sessionStatus === "authenticated" && (
        <Card>
          <CardContent className="p-0">
            <ul className="divide-y divide-border">
              {notifications.map(notification => (
                <li
                  key={notification.id}
                  className={cn(
                    "p-4 hover:bg-muted/50 transition-colors cursor-pointer",
                    !notification.isRead && "bg-primary/5"
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={cn("mt-1 flex-shrink-0", !notification.isRead ? "text-primary" : "text-muted-foreground")}>
                      {!notification.isRead ? <Bell className="w-5 h-5" /> : <CheckCircle className="w-5 h-5" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className={cn("text-sm text-foreground", !notification.isRead && "font-semibold")}>
                        {notification.message}
                      </p>
                      <p className="text-xs text-muted-foreground mt-0.5">
                        {formatDistanceToNowStrict(new Date(notification.createdAt), { addSuffix: true, locale: selectedDateLocale })}
                      </p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {sessionStatus === "unauthenticated" && !isLoading && (
         <Card>
          <CardContent className="text-center py-12">
             <AlertTriangle className="mx-auto h-8 w-8 mb-2 text-destructive" />
            <p className="text-muted-foreground">Trebuie să fii autentificat pentru a vedea notificările.</p>
             <Button asChild className="mt-4"><Link href="/login">Mergi la autentificare</Link></Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
